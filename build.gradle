apply from: "config.gradle"
buildscript {
    ext.kotlin_version = "1.4.10"
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url "https://www.jitpack.io" }
        maven { url 'https://raw.githubusercontent.com/saki4510t/libcommon/master/repository/' }
        google()
        mavenCentral()

    }
    dependencies {//4.0.2
        classpath 'com.android.tools.build:gradle:4.1.1'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"


    }
}

allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url "https://www.jitpack.io" }
        maven { url 'https://raw.githubusercontent.com/saki4510t/libcommon/master/repository/' }
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
