apply plugin: 'com.android.library'
apply plugin: 'org.greenrobot.greendao'
android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
    }
    dataBinding {
        enabled true
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    //support
    api rootProject.ext.support["support-v4"]
    api rootProject.ext.support["appcompat-v7"]
    api rootProject.ext.support["recyclerview-v7"]
    //rxjava
    api rootProject.ext.dependencies.rxjava
    api rootProject.ext.dependencies.rxandroid
    //rx管理View的生命周期
    api(rootProject.ext.dependencies.rxlifecycle) {
        exclude group: 'com.android.support'
    }
    api(rootProject.ext.dependencies["rxlifecycle-components"]) {
        exclude group: 'com.android.support'
    }
    //rxbinding
    api(rootProject.ext.dependencies.rxbinding) {
        exclude group: 'com.android.support'
    }
    //rx权限请求
    api(rootProject.ext.dependencies.rxpermissions) {
        exclude group: 'com.android.support'
    }
    //network
    api rootProject.ext.dependencies.okhttp
    api rootProject.ext.dependencies.retrofit
    api rootProject.ext.dependencies["converter-gson"]
    api rootProject.ext.dependencies["adapter-rxjava"]
    //json解析
    api rootProject.ext.dependencies.gson
    //material-dialogs
    api(rootProject.ext.dependencies["material-dialogs-core"]) {
        exclude group: 'com.android.support'
    }
    api(rootProject.ext.dependencies["material-dialogs-commons"]) {
        exclude group: 'com.android.support'
    }
    //glide图片加载库
    api (rootProject.ext.dependencies.glide){
        exclude group: 'com.android.support'
    }
    annotationProcessor rootProject.ext.dependencies["glide-compiler"]
    //recyclerview的databinding套装
    api(rootProject.ext.dependencies.bindingcollectionadapter) {
        exclude group: 'com.android.support'
    }
    api(rootProject.ext.dependencies["bindingcollectionadapter-recyclerview"]) {
        exclude group: 'com.android.support'
    }
    api(rootProject.ext.dependencies["bindingcollectionadapter-viewpager2"]) {
        exclude group: 'com.android.support'
    }
    //Google LiveData和ViewModel组件
    api rootProject.ext.dependencies["lifecycle-extensions"]
    annotationProcessor rootProject.ext.dependencies["lifecycle-compiler"]

    api 'androidx.activity:activity:1.2.0-beta01'
    api 'androidx.fragment:fragment:1.3.0-beta01'

    api 'com.lcodecorex:tkrefreshlayout:1.0.7'
    api rootProject.ext.dependencies.greendao
    api rootProject.ext.dependencies.GreenDaoUpgradeHelper
    api rootProject.ext.dependencies.Toasty
    api rootProject.ext.dependencies.pdfview
    api rootProject.ext.dependencies.pickview
    api rootProject.ext.dependencies.arouter
    implementation 'org.bouncycastle:bcpkix-jdk15on:1.68'

//    kapt  rootProject.ext.dependencies.arouter-compiler


}
