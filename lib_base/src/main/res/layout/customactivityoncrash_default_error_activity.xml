<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:ignore="UselessParent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingBottom="@dimen/customactivityoncrash_activity_vertical_margin"
            android:paddingLeft="@dimen/customactivityoncrash_activity_horizontal_margin"
            android:paddingRight="@dimen/customactivityoncrash_activity_horizontal_margin"
            android:paddingTop="@dimen/customactivityoncrash_activity_vertical_margin">

            <ImageView
                android:id="@+id/customactivityoncrash_error_activity_image"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@null"
                android:src="@drawable/customactivityoncrash_error_image" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/customactivityoncrash_activity_vertical_margin"
                android:gravity="center"
                android:text="@string/customactivityoncrash_error_activity_error_occurred_explanation"
                android:textSize="18sp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/customactivityoncrash_error_activity_restart_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/customactivityoncrash_activity_vertical_margin"
                android:text="@string/customactivityoncrash_error_activity_close_app" />

            <Button
                android:id="@+id/customactivityoncrash_error_activity_more_info_button"
                style="?borderlessButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/customactivityoncrash_error_activity_error_details"
                android:textColor="?colorPrimary" />
        </LinearLayout>
    </ScrollView>
</RelativeLayout>