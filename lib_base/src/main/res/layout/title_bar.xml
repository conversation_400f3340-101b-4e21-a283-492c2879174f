<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin_title"
    android:layout_width="match_parent"
    android:layout_height="70dp"
    android:orientation="vertical">

    <View
        android:id="@+id/space"
        android:layout_width="match_parent"
        android:layout_height="25dp"
        android:background="@android:color/transparent" />

    <RelativeLayout
        android:layout_below="@+id/space"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginLeft="30dp"
        android:layout_centerVertical="true">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:drawableLeft="@drawable/logo"
            android:drawablePadding="5dp"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:text="title"
            android:textColor="@color/color3333"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_shenban"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_toLeftOf="@+id/tv_home"
            android:background="@drawable/shape_corner5_white"
            android:drawableLeft="@drawable/explain"
            android:drawablePadding="10dp"
            android:gravity="center"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp"
            android:src="@drawable/icon_home"
            android:text="notarization_desc"
            android:textColor="@color/color3333"
            android:textSize="16sp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_home"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginHorizontal="10dp"
            android:background="@drawable/shape_corner5_white"
            android:drawableLeft="@drawable/icon_home"
            android:drawablePadding="10dp"
            android:gravity="center"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp"
            android:text="home"
            android:textColor="@color/color3333"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:gravity="center"
            android:paddingLeft="20dp"
            android:paddingTop="5dp"
            android:paddingRight="20dp"
            android:paddingBottom="5dp"
            android:text=""
            android:textColor="@color/color3333"
            android:textSize="18sp" />

    </RelativeLayout>


</RelativeLayout>