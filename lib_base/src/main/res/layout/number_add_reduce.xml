<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center">

    <TextView
        android:id="@+id/btn_reduce"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:gravity="center"
        android:onClick="onViewClicked"
        android:background="@drawable/reduce" />

    <TextView
        android:id="@+id/tv_count"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="1"
        android:textColor="#000"
        android:textSize="20sp" />

    <TextView
        android:id="@+id/btn_add"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:gravity="center"
        android:onClick="onViewClicked"
        android:background="@drawable/add"/>
</LinearLayout>
