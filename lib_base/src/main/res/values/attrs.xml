<resources>

    <!-- require boolean value to decide whether requestFocus for view. -->
    <attr name="requestFocus" format="boolean" />
    <!-- require ItemView {@link me.tatarka.bindingcollectionadapter.ItemView} or ItemViewSelector {{@link me.tatarka.bindingcollectionadapter.ItemViewSelector}.} -->
    <attr name="itemView" format="reference" />
    <!-- require List<ViewModel> bind to ItemView to presentation.-->
    <attr name="items" format="reference" />
    <!-- require a adapter which type of BindingRecyclerViewAdapter<T> to AdapterView-->
    <attr name="adapter" format="reference" />

    <attr name="onScrollChangeCommand" format="reference" />
    <attr name="onScrollStateChangedCommand" format="reference" />
    <attr name="url" format="string" />
    <attr name="onTouchCommand" format="reference" />

    <!-- require BindingCommand {@link com.kelin.mvvmlight.command.BindingCommand } to deal with view click event. -->
    <attr name="onClickCommand" format="reference" />
    <attr name="onLongClickCommand" format="reference" />
    <!-- require BindingCommand<Boolean> {@link com.kelin.mvvmlight.command.BindingCommand } to deal with view focus change event.
     BindingCommand would has params which means if view hasFocus.-->
    <attr name="onFocusChangeCommand" format="reference" />
    <attr name="isThrottleFirst" format="boolean" />
    <attr name="currentView" format="reference" />
    <attr name="isVisible" format="boolean" />
    <!-- require boolean value to decide whether requestFocus for view. -->
    <declare-styleable name="View">
        <!-- require BindingCommand {@link com.kelin.mvvmlight.command.BindingCommand } to deal with view click event. -->
        <attr name="onClickCommand" />
        <attr name="onLongClickCommand" />
        <!-- require BindingCommand<Boolean> {@link com.kelin.mvvmlight.command.BindingCommand } to deal with view focus change event.
         BindingCommand would has params which means if view hasFocus.-->
        <attr name="onFocusChangeCommand" />
        <!-- require BindingCommand<MotionEvent> -->
        <attr name="onTouchCommand" />
        <attr name="isThrottleFirst" />
        <attr name="currentView" />

    </declare-styleable>


    <declare-styleable name="AdapterView">
        <!-- require ItemView {@link me.tatarka.bindingcollectionadapter.ItemView} or ItemViewSelector {{@link me.tatarka.bindingcollectionadapter.ItemViewSelector}.} -->
        <attr name="itemView" />
        <!-- require List<ViewModel> bind to ItemView to presentation.-->
        <attr name="items" />
        <!-- require a adapter which type of BindingRecyclerViewAdapter<T> to AdapterView-->
        <attr name="adapter" />
        <attr name="dropDownItemView" format="reference" />
        <attr name="itemIds" format="reference" />
        <attr name="itemIsEnabled" format="reference" />
        <!-- require BindingCommand<Integer> -->
        <attr name="onScrollStateChangedCommand" />
        <!-- require BindingCommand<ListViewScrollDataWrapper> -->
        <attr name="onScrollChangeCommand" />
        <!-- require BindingCommand<Integer> count of list items-->
        <attr name="onLoadMoreCommand" format="reference" />
    </declare-styleable>

    <declare-styleable name="TextView">
        <!--require BindingCommand<TextChangeDataWrapper> -->
        <attr name="beforeTextChangedCommand" format="reference" />
        <!--require BindingCommand<TextChangeDataWrapper> -->
        <attr name="onTextChangedCommand" format="reference" />
        <!--require BindingCommand<String> -->
        <attr name="afterTextChangedCommand" format="reference" />
        <attr name="textChanged" format="reference" />
    </declare-styleable>


    <declare-styleable name="ImageView">
        <!--  load bitmap from uri(string type) -->
        <attr name="url" />
        <attr name="imageUrl" format="string|reference"/>
        <!--width for ResizeOptions (use Fresco to load bitmap). -->
        <attr name="request_width" format="integer" />
        <!--height for ResizeOptions (use Fresco to load bitmap). -->
        <attr name="request_height" format="integer" />
        <attr name="placeholderRes" format="reference|color" />
        <!--  require BindingCommand<Bitmap> See {@link @link com.kelin.mvvmlight.command.BindingCommand} -->
        <attr name="onSuccessCommand" format="reference" />
        <!--require BindingCommand<CloseableReference<CloseableImage>> See {@link com.kelin.mvvmlight.command.BindingCommand} -->
        <attr name="onFailureCommand" format="reference" />

    </declare-styleable>


    <declare-styleable name="ViewGroup">
        <!-- require ItemView {@link me.tatarka.bindingcollectionadapter.ItemView} or ItemViewSelector {{@link me.tatarka.bindingcollectionadapter.ItemViewSelector}.} -->
        <attr name="itemView" />
        <!-- require List<ViewModel> bind to ItemView to presentation.-->
        <attr name="observableList" format="reference" />

    </declare-styleable>

    <declare-styleable name="RecyclerView" parent="AdapterView">
        <attr name="lineManager" format="reference" />
        <attr name="itemBinding" format="reference" />
        <attr name="layoutManager" format="reference" />
        <attr name="itemAnimator" format="reference" />
    </declare-styleable>
    <declare-styleable name="RadioGroup">
        <attr name="onCheckedChangedCommand" format="reference" />
    </declare-styleable>

    <declare-styleable name="Spinner">
        <attr name="itemDatas" format="reference" />
        <attr name="valueReply" format="string" />
        <attr name="resource" format="integer" />
        <attr name="dropDownResource" format="integer" />
        <attr name="onItemSelectedCommand" format="reference" />
    </declare-styleable>

    <declare-styleable name="Switch">
        <attr name="onCheckedChangeCommand" format="reference" />
        <attr name="switchState" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ListView" parent="AdapterView">
        <!--require BindingCommand<Integer> integer mean to position where is clicked! -->
        <attr name="onItemClickCommand" format="reference" />
    </declare-styleable>

    <declare-styleable name="ViewPager">
        <!-- require ItemView {@link me.tatarka.bindingcollectionadapter.ItemView} or ItemViewSelector {{@link me.tatarka.bindingcollectionadapter.ItemViewSelector}.} -->
        <attr name="itemView" />
        <!-- require List<ViewModel> bind to ItemView to presentation.-->
        <attr name="items" />
        <!-- require a adapter which type of BindingRecyclerViewAdapter<T> to AdapterView-->
        <attr name="adapter" />
        <!-- require PageTitles<T>-->
        <attr name="pageTitles" format="reference" />
        <!--require BindingCommand<ViewPagerDataWrapper> -->
        <attr name="onPageScrolledCommand" format="reference" />
        <!--require BindingCommand<Integer> -->
        <attr name="onPageSelectedCommand" format="reference" />
        <!--require BindingCommand<Integer> -->
        <attr name="onPageScrollStateChangedCommand" format="reference" />

    </declare-styleable>

    <declare-styleable name="NestedScrollView">
        <!-- require BindingCommand<NestScrollDataWrapper> -->
        <attr name="onScrollChangeCommand" />
    </declare-styleable>

    <declare-styleable name="SimpleDraweeView">
        <!-- require String to load Image"-->
        <attr name="url" />
    </declare-styleable>

    <declare-styleable name="ScrollView">
        <!-- require BindingCommand<ScrollDataWrapper> -->
        <attr name="onScrollChangeCommand" />
    </declare-styleable>

    <declare-styleable name="SwipeRefreshLayout">
        <!-- require BindingCommand -->
        <attr name="onRefreshCommand" format="reference" />
        <attr name="refreshing" format="boolean" />
    </declare-styleable>

    <declare-styleable name="WebView">
        <!-- require String render to html show in webview-->
        <attr name="render" format="string" />
    </declare-styleable>
    <!-- 自定义控制事件分发的LinearLayout -->
    <declare-styleable name="ControlDistributeLinearLayout">
        <attr name="distribute_event" format="boolean" />
    </declare-styleable>
</resources>