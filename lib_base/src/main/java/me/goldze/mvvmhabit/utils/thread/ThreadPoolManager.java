package me.goldze.mvvmhabit.utils.thread;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * ****************************************************************<br>
 * 文件名称 : ThreadPoolManager.java.java<br>
 * 文件描述 : 线程池管理类<br>
 * ****************************************************************
 */
public class ThreadPoolManager {

    private ExecutorService mExecutorService;

    public void startThread(Runnable runnable) {
        if (null == mExecutorService) {
            //开辟一个核心线程为2且任务队列无界且有优先级的线程池
            mExecutorService = new ThreadPoolExecutor(2, Integer.MAX_VALUE, 60, TimeUnit.SECONDS,
                    new PriorityBlockingQueue<>());
        }
        mExecutorService.execute(runnable);
    }

    public static ThreadPoolManager getInstance() {
        return SingleTask.mThreadPoolManager;
    }

    private static final class SingleTask {
        private static final ThreadPoolManager mThreadPoolManager = new ThreadPoolManager();
    }

}
