package me.goldze.mvvmhabit.utils;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.Log;
import android.util.TimeUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.FrameLayout;

import com.bigkoo.pickerview.builder.OptionsPickerBuilder;
import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.OnTimeSelectChangeListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.OptionsPickerView;
import com.bigkoo.pickerview.view.TimePickerView;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * GCSWRW4897
 */
public class TimePickUtil {
    private static SimpleDateFormat formatter;
    private TimePickerView timePickerView;
    private Activity activity;
    private OptionsPickerView<Object> pvOptions;
    private String s;

    public TimePickUtil(Activity activity) {
        this.activity = activity;
    }

    //时间选择器弹框
    public void initTimePickerDialog(String selectTime, boolean isDialog) {
        Calendar selectedDate = Calendar.getInstance();
        if (!TextUtils.isEmpty(selectTime)) {
            Date date = strToDate(selectTime);
            if (date != null) {
                selectedDate.setTime(date);
            }
        } else {
            selectedDate.setTimeInMillis(System.currentTimeMillis());
        }
        Calendar startDate = Calendar.getInstance();
        startDate.set(1993, 0, 1);
        Calendar endDate = Calendar.getInstance();
        endDate.set(2099, 11, 0);
        timePickerView = new TimePickerBuilder(activity, new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {
                if (timeListener != null) {
                    timeListener.setTime(getTime(date));
                }
            }
        })
                .setTimeSelectChangeListener(new OnTimeSelectChangeListener() {
                    @Override
                    public void onTimeSelectChanged(Date date) {
                        Log.i("pvTime", "onTimeSelectChanged");
                    }
                })
                .setCancelText("取消")//取消按钮文字
                .setSubmitText("确定")//确认按钮文字
                .setTitleText("请选择时间")//标题文字
                .setTitleSize(20)//标题文字大小
                .setOutSideCancelable(true)//点击屏幕，点在控件外部范围时，是否取消显示
                .isCyclic(false)//是否循环滚动
                .setLineSpacingMultiplier(2.0f)
                .setTextColorCenter(Color.BLACK)//设置选中项的颜色
                .setTitleColor(Color.BLACK)//标题文字颜色
                .setSubmitColor(Color.BLUE)//确定按钮文字颜色
                .setCancelColor(Color.BLUE)//取消按钮文字颜色
                .setType(new boolean[]{true, true, true, true, false, false})//年月日时分秒
                .isDialog(isDialog)
                .setRangDate(startDate, endDate)
                .setDate(selectedDate)
                .build();

        if (isDialog) {
            initDialog();
        }


    }

    public void showDialog() {
        timePickerView.show();
    }

    //获取时间 年 月 日 时
    public static String getTime(Date date) {//可根据需要自行截取数据显示
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH");
        return format.format(date);
    }

    //获取时间 年 月 日 时 long
    public static long getTime(String date) {//可根据需要自行截取数据显示
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH");
        try {
            return format.parse(date).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }

    //dialog从底部弹出,设置其弹框位置
    private void initDialog() {
        Dialog mDialog = timePickerView.getDialog();
        if (mDialog != null) {
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    Gravity.BOTTOM);
            params.leftMargin = 0;
            params.rightMargin = 0;
            timePickerView.getDialogContainerLayout().setLayoutParams(params);

            Window dialogWindow = mDialog.getWindow();
            if (dialogWindow != null) {
                dialogWindow.setWindowAnimations(com.bigkoo.pickerview.R.style.picker_view_slide_anim);//修改动画样式
                dialogWindow.setGravity(Gravity.BOTTOM);//改成Bottom,底部显示
            }

        }
    }

    //单项选择
    public void showSelectDialog(List<String> bankNameList, String title, int pos) {
        OptionsPickerView pvOptions = new OptionsPickerBuilder(activity, (options1, options2, options3, v) -> {
            String strBankName = bankNameList.get(options1);
            if (itemClickListener != null) {
                itemClickListener.setText(strBankName, options1);
            }
        })
                .setDividerColor(Color.BLACK)
                .setTitleText(title)
                .setSelectOptions(pos)
                .setTextColorCenter(Color.BLACK)
                .setContentTextSize(20)
                .setLineSpacingMultiplier(2.0f)
                .setOutSideCancelable(true)
                .build();
        pvOptions.setPicker(bankNameList);
        pvOptions.show();
    }

    //城市区的选择
    public void setAddress(Context context, List<Object> options1Items, List<List<Object>> options2Items, List<List<List<Object>>> options3Items,
                           int pos1, int pos2, int pos3) {
        pvOptions = new OptionsPickerBuilder(context, (options1, options2, options3, v) -> {
            if (addressListener != null) {
                addressListener.setAddress(options1, options2, options3, v);
            }
        })
                //分隔线颜色。
                .setDividerColor(Color.parseColor("#BBBBBB"))
                //滚轮背景颜色
                .setBgColor(Color.parseColor("#F5F5F5"))
                //设置两横线之间的间隔倍数
                .setLineSpacingMultiplier(3.0f)
                //设置选中项的颜色
                .setTextColorCenter(Color.parseColor("#333333"))
                .isCenterLabel(true)
                // 设置选择的三级单位
                .setLabels("", "", "")
                //标题文字
                .setTitleText("地址选择")
                .setSubmitText("确定")//确定按钮文字
                .setCancelText("取消")//取消按钮文字
                .setTitleText("城市选择")//标题
//                .setSubCalSize(18)//确定和取消文字大小
//                .setTitleSize(20)//标题文字大小
                .setTitleColor(Color.BLACK)//标题文字颜色
                .setSubmitColor(Color.BLUE)//确定按钮文字颜色
                .setCancelColor(Color.BLUE)//取消按钮文字颜色
                //默认选中项
                .setSelectOptions(pos1, pos2, pos3)
                .build();
        pvOptions.setPicker(options1Items, options2Items, options3Items);//三级选择器
        pvOptions.show();

    }


    public static Date strToDate(String strDate) {
        if (strDate.length() > 11) {
            formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        } else {
            formatter = new SimpleDateFormat("yyyy-MM-dd");
        }
        Date date = null;
        try {
            date = formatter.parse(strDate);
            return date;
        } catch (ParseException e) {


        }
        return date;

    }

    public interface TimeListener {
        void setTime(String text);
    }

    private TimeListener timeListener;

    public TimeListener getTimeListener() {
        return timeListener;
    }

    public void setTimeListener(TimeListener timeListener) {
        this.timeListener = timeListener;
    }

    public interface ItemClickListener {
        void setText(String text, int position);
    }

    private ItemClickListener itemClickListener;


    public void setItemClickListener(ItemClickListener itemClickListener) {
        this.itemClickListener = itemClickListener;
    }

    public interface AddressListener {
        void setAddress(int options1, int options2, int options3, View view);
    }

    public void setAddressListener(AddressListener addressListener) {
        this.addressListener = addressListener;
    }

    private AddressListener addressListener;
}
