package me.goldze.mvvmhabit.utils;

import android.util.Log;

import java.net.URL;
import java.net.URLConnection;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;

public class TimeUtil {
    static final String TAG = "TimeUtil";
    static SimpleDateFormat mformat = new SimpleDateFormat("HH:mm:ss");
    static SimpleDateFormat format = new SimpleDateFormat("HH:mm");
    static SimpleDateFormat formatYM = new SimpleDateFormat("yyyy-MM");
    static SimpleDateFormat formatYMD = new SimpleDateFormat("yyyy-MM-dd");
    static SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    static SimpleDateFormat formatdate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    static SimpleDateFormat format2 = new SimpleDateFormat("HH小时mm分钟ss秒");

    public static String gettime() {

        return mformat.format(System.currentTimeMillis());
    }

    public static String gettimelong(long time) {

        return mformat.format(time);
    }

    /**
     * 把毫秒数转换成时分秒  00:00:00
     */
    public static String getlong(long time) {
        String hh = "";
        String mm = "";
        String ss = "";
        long date = time / 1000;
        long h = date / 3600;
        long m = date % 3600 / 60;
        long s = date % 3600 % 60;
        if (h < 10) {
            hh = "0" + h + ":";
        } else {
            hh = h + ":";
        }
        if (m < 10) {
            mm = "0" + m + ":";
        } else {
            mm = m + ":";
        }
        if (s < 10) {
            ss = "0" + s;
        } else {
            ss = s + "";
        }

        return hh + mm + ss;
    }

    public static String gettimeYM(long time) {

        return formatYM.format(time);
    }

    public static String gettimeInt(int time) {

        String hh = time / 3600 + "";
        String mm = (time % 3600) / 60 + "";
        String ss = (time % 3600) % 60 % 60 + "";
        if (hh.length() < 2) {
            hh = "0" + hh;
        }
        if (mm.length() < 2) {
            mm = "0" + mm;
        }
        if (ss.length() < 2) {
            ss = "0" + ss;
        }
        return hh + ":" + mm + ":" + ss;
    }

    private void getNetTime() {
        URL url = null;//取得资源对象
        try {
            url = new URL("http://www.baidu.com");
            //url = new URL("http://www.ntsc.ac.cn");//中国科学院国家授时中心
            //url = new URL("http://www.bjtime.cn");
            URLConnection uc = url.openConnection();//生成连接对象
            uc.connect(); //发出连接
            long ld = uc.getDate(); //取得网站日期时间
            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(ld);
            final String format = formatter.format(calendar.getTime());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String addTime(long data, String time) {
        try {

            if (time.length() == 5)
                time = "00:" + time;

            String[] aa = time.split(":");
            long h = Integer.parseInt(aa[0]) * 60 * 60 * 1000L;
            long m = Integer.parseInt(aa[1]) * 60 * 1000L;
            long s = Integer.parseInt(aa[2]) * 1000L;
            return getTime_six(data + h + m + s);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * @param date
     * @return
     */
    public static String getDateToString(Date date) {
        return formatYMD.format(date);
    }

    /**
     * @param date
     * @return
     */
    public static String getDateToDay(Date date) {
        String day = formatYMD.format(date);
        return day.split("-")[0] + "年" + day.split("-")[1] + "月" + day.split("-")[2] + "日";
    }

    /**
     * @param date
     * @return
     */
    public static String getDateToStringOne(Date date) {
        return formatdate.format(date);
    }

    /**
     * @param date
     * @return
     */
    public static String getDateToStringdata(Date date) {
        return formatdate.format(date);
    }

    /**
     * 时间转换  yyyy-MM-dd HH:mm:ss
     */
    public static String getTime_Date(Date time) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(time);
    }

    /**
     * @param date 总秒数
     * @return
     */
    public static String getDateToStringTwo(long date) {
        String hh = "";
        String mm = "";
        String ss = "";

        int h = (int) (date / 3600);
        int m = (int) (date % 3600 / 60);
        int s = (int) (date % 3600 % 60);
        if (h < 10) {
            hh = "0" + h + "小时";
        } else {
            hh = h + "小时";
        }
        if (m < 10) {
            mm = "0" + m + "分钟";
        } else {
            mm = m + "分钟";
        }
        if (s < 10) {
            ss = "0" + s + "秒";
        } else {
            ss = s + "秒";
        }

        return hh + mm + ss;
    }

    /**
     * int转秒  HH:mm:ss
     * 暂不考虑超过10小时的情况
     * time  单位秒
     *
     * @return
     */
    public static String intToFormat(int time) {
        SimpleDateFormat format = new SimpleDateFormat("mm:ss");
//        String times = "00:00:00";
        if (time < 60 * 60 * 1000) {
            return "00:" + format.format(new Date(time * 1000));
        } else {
            int h = time / (60 * 60);
            return "0" + h + ":" + format.format(new Date((time - h * 3600) * 1000));
        }

    }

    /**
     * 当前时间是否在范围内
     */
    public static boolean CurrTimeCompare(String startTime, String endTime) {
        try {
            long start = format.parse(startTime).getTime();
            long end = format.parse(endTime).getTime();
            long current = format.parse(format.format(System.currentTimeMillis())).getTime();
            if (current > start && current < end)
                return true;
            return false;
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 时间转换  yyyy-MM-dd HH:mm:ss
     */
    public static String getTime_six(long time) {
        try {
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return format.format(time);
        } catch (Exception e) {
            Log.e(TAG, "易存链 " + e.getMessage());
        }
        return "";
    }

    /**
     * 时间转换  yyyy-MM-dd HH:mm:ss
     */
    public static String getCurrentTime() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date(System.currentTimeMillis());
        Log.e("Test","simpleDateFormat.format(date)==="+simpleDateFormat.format(date));
        return simpleDateFormat.format(date);
    }


    /**
     * 时间转换  yyyy-MM-dd HH:mm:ss
     */
    public static String getCurrentTimeMinute() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date date = new Date(System.currentTimeMillis());
        Log.e("Test","simpleDateFormat.format(date)==="+simpleDateFormat.format(date));
        return simpleDateFormat.format(date);
    }

    /**
     * 两个时间字符串相减 获取   时 分 秒
     */
    public static String getTime(String befo, String after) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String h = "";
        String m = "";
        String d = "";
        try {
            long a = format.parse(befo).getTime();
            long b = format.parse(after).getTime();
            long totalTime = b - a;
            int ss = (int) (totalTime / 1000);//总秒数
            int HH = ss / 3600;
            int MM = (ss % 3600) / 60;
            int DD = (ss % 3600) % 60;
            if (HH >= 23) {
                return "";
            }
//            if (HH < 10) {
//                h = "0" + HH;
//            }
//            if (MM < 10) {
//                m = "0" + MM;
//            }
//            if (DD < 10) {
//                d = "0" + DD;
//            }
            return HH + "-" + MM + "-" + DD;
        } catch (ParseException e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 两个时间字符串相减 获取   时 分 秒  int值
     */
    public static ArrayList<Integer> getTimeList(String befo, String after) {
        ArrayList<Integer> list = new ArrayList<>();
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            long a = format.parse(befo).getTime();
            long b = format.parse(after).getTime();
            long totalTime = b - a;
            int ss = (int) (totalTime / 1000);//总秒数
            int HH = ss / 3600;
            int MM = (ss % 3600) / 60;
            int DD = (ss % 3600) % 60;
            if (HH >= 23) {
                return null;
            }
            list.add(0, HH);
            list.add(0, MM);
            list.add(0, DD);
            return list;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 时间对比
     */
    public static boolean CompareTime(String startTime, String endTime) {
        try {
            long start = format.parse(startTime).getTime();
            long end = format.parse(endTime).getTime();
            if (start < end)
                return true;
            return false;
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 时间转化00:00格式
     */
    public static String to2length(int context) {
        String result = context + "";
        if (result.length() < 2) {
            return "0" + context;
        }
        return result;
    }

    /*
     * 将时分秒转为秒数
     * */
    public static long formatTurnSecond(String time) {
        String s = time;
        int index1 = s.indexOf(":");
        int index2 = s.indexOf(":", index1 + 1);
        int hh = Integer.parseInt(s.substring(0, index1));
        int mi = Integer.parseInt(s.substring(index1 + 1, index2));
        int ss = Integer.parseInt(s.substring(index2 + 1));

        Log.e("zpzp", "formatTurnSecond: 时间== " + hh * 60 * 60 + mi * 60 + ss);
        return hh * 60 * 60 + mi * 60 + ss;
    }
}
