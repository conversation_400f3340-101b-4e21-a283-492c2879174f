package me.goldze.mvvmhabit.utils;

import android.content.Context;
import android.content.SharedPreferences;

import java.security.MessageDigest;

public class MD5Util {

//    public static String getMD5Str(String str) {
//        byte[] digest = null;
//        try {
//            MessageDigest md5 = MessageDigest.getInstance("md5");
//            digest  = md5.digest(str.getBytes("utf-8"));
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }
//        //16是表示转换为16进制数
//        String md5Str = new BigInteger(1, digest).toString(16);
//        return md5Str;
//    }
    /**
     * @param str
     * @Description: 32位小写MD5
     */
    public static String getMD5Str(String str) {
        String reStr = null;
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");

            byte[] bytes = md5.digest(str.getBytes());
            StringBuffer stringBuffer = new StringBuffer();
            for (byte b : bytes) {
                int bt = b & 0xff;
                if (bt < 16) {
                    stringBuffer.append(0);
                }
                stringBuffer.append(Integer.toHexString(bt));
            }
            reStr = stringBuffer.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "null";
        }
        return reStr;
    }
    public static void saveFromInfo(Context context, String from){
        //获取SharedPreferences对象
        SharedPreferences sharedPre=context.getSharedPreferences("isFrom", context.MODE_PRIVATE);
        //获取Editor对象
        SharedPreferences.Editor editor=sharedPre.edit();
        //设置参数
        editor.putString("from", from);
        //提交
        editor.commit();
    }

    public static void saveETag(Context context, String from){
        //获取SharedPreferences对象
        SharedPreferences sharedPre=context.getSharedPreferences("eTag", context.MODE_PRIVATE);
        //获取Editor对象
        SharedPreferences.Editor editor=sharedPre.edit();
        //设置参数
        editor.putString("from", from);
        //提交
        editor.commit();
    }

    public static void saveToken(Context context, String idCard, String name, String imgFaceStr){
        //获取SharedPreferences对象
        SharedPreferences sharedPre=context.getSharedPreferences("configTokenQz", context.MODE_PRIVATE);
        //获取Editor对象
        SharedPreferences.Editor editor=sharedPre.edit();
        //设置参数
        editor.putString("idCard", idCard);
        editor.putString("name", name);
        editor.putString("imgFaceStr", imgFaceStr);
        //提交
        editor.commit();
    }

    public static void saveUserINfo(Context context, String roleId, String notaryId){
        //获取SharedPreferences对象
        SharedPreferences sharedPre=context.getSharedPreferences("configUserInfoQz", context.MODE_PRIVATE);
        //获取Editor对象
        SharedPreferences.Editor editor=sharedPre.edit();
        //设置参数
        editor.putString("roleId", roleId);
        editor.putString("notaryId", notaryId);
        //提交
        editor.commit();
    }

}