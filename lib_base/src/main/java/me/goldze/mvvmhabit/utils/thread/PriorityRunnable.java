package me.goldze.mvvmhabit.utils.thread;

/**
 * ****************************************************************<br>
 * 文件名称 : PriorityRunnable.java.java<br>
 * 文件描述 : 可管控优先级的线程队列<br>
 * ****************************************************************
 */
public abstract class PriorityRunnable implements Runnable, Comparable<PriorityRunnable> {

    private final int priority;

    public PriorityRunnable(int priority) {
        if (priority < 0) {
            throw new IllegalArgumentException();
        }
        this.priority = priority;
    }

    public PriorityRunnable() {
        this.priority = 0;
    }

    public int getPriority() {
        return priority;
    }

    @Override
    public int compareTo(PriorityRunnable priorityRunnable) {
        int me = this.priority;
        int anotherPri = priorityRunnable.getPriority();
        return me == anotherPri ? 0 : me < anotherPri ? 1 : -1;
    }

    @Override
    public void run() {
        doInBackground();
    }


    protected abstract void doInBackground();
}
