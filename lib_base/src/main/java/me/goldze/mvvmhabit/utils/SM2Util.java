package me.goldze.mvvmhabit.utils;

import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.RequiresApi;

import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Hex;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.util
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/7/1
 */
public class SM2Util {

    private static final String secretKey = "1234567890abcdef"; // 密钥
    private static final String sm2p256v1 = "sm2p256v1";

    static {
        double version = Security.getProvider(BouncyCastleProvider.PROVIDER_NAME).getVersion();
        Log.i("Test", "generateKey1: " + version);
        Security.removeProvider(BouncyCastleProvider.PROVIDER_NAME);
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Log.i("Test", "运行环境没有BouncyCastleProvider");
            Security.addProvider(new BouncyCastleProvider());
        }
        version = Security.getProvider(BouncyCastleProvider.PROVIDER_NAME).getVersion();
        Log.i("Test", "generateKey2: " + version);
    }

    public static String encrypt(String plainText, String secretKey) {
//        try {
//            SecretKeySpec key = new SecretKeySpec(secretKey.getBytes(), "SM4");
//            Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", "BC");
//            cipher.init(Cipher.ENCRYPT_MODE, key);
//            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
//            return Base64.getEncoder().encodeToString(encryptedBytes);
//        } catch (Exception e) {
//            Log.e("Test", "Encryption error: " + e.getMessage());
//            return null;
//        }
        byte[] key = hexTobytes(secretKey);
        byte[] output = new byte[0];
        try {
            output = SM4Util.encryptEcbPkcs5Padding(plainText.getBytes(StandardCharsets.UTF_8), key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String hex = Hex.toHexString(output);
        return hex;
    }

    /**
     * Hex转byte[]，两种情况，Hex长度为奇数最后一个字符会被舍去
     */
    public static byte[] hexTobytes(String hex) {
        if (hex.length() < 1) {
            return null;
        } else {
            byte[] result = new byte[hex.length() / 2];
            int j = 0;
            for (int i = 0; i < hex.length(); i += 2) {
                result[j++] = (byte) Integer.parseInt(hex.substring(i, i + 2), 16);
            }
            return result;
        }
    }


    public static String decrypt(String encryptedText, String secretKey) {
//        try {
//            SecretKeySpec key = new SecretKeySpec(secretKey.getBytes(), "SM4");
//            Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", "BC");
//            cipher.init(Cipher.DECRYPT_MODE, key);
//            byte[] encryptedBytes = Base64.getEncoder().encode(encryptedText.getBytes());
//            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
//            return new String(decryptedBytes);
//        } catch (Exception e) {
//            Log.e("Test", "Decryption error: " + e.getMessage());
//            return null;
//        }
        byte[] input = Hex.decode(encryptedText);
        byte[] key = hexTobytes(secretKey);
        byte[] output = new byte[0];
        try {
            output = SM4Util.decryptEcbPkcs5Padding(input, key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String s = new String(output, StandardCharsets.UTF_8);
        return s;
    }


    /**
     * SM2加密算法
     *
     * @param publicKey 公钥
     * @param data      数据
     * @return
     */
    public static String encryptSM2(String publicKey, String data) {
        if (publicKey == null) {
            return "";
        }
        // 获取一条SM2曲线参数
        X9ECParameters sm2ECParameters = GMNamedCurves.getByName(sm2p256v1);
        // 构造domain参数
        ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
        //提取公钥点
        ECPoint pukPoint = sm2ECParameters.getCurve().decodePoint(Hex.decode(publicKey));
        // 公钥前面的02或者03表示是压缩公钥，04表示未压缩公钥, 04的时候，可以去掉前面的04
        ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(pukPoint, domainParameters);
        SM2Engine sm2Engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
        sm2Engine.init(true, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));

        byte[] arrayOfBytes = null;
        if (!TextUtils.isEmpty(data)) {
            try {
                byte[] in = data.getBytes("utf-8");
                arrayOfBytes = sm2Engine.processBlock(in, 0, in.length);
            } catch (Exception e) {
                e.printStackTrace();
            }
            Log.i("http", "encrypt: " + Hex.toHexString(arrayOfBytes));
            return Hex.toHexString(arrayOfBytes);
        } else {
            return "";
        }


    }

    /**
     * SM2解密算法
     *
     * @param privateKey 私钥
     * @param cipherData 密文数据
     * @return
     */
    public static String decryptSM2(String privateKey, String cipherData) {
        String result = "解密失败";
        try {
            byte[] cipherDataByte = Hex.decode(cipherData);
            //获取一条SM2曲线参数
            X9ECParameters sm2ECParameters = GMNamedCurves.getByName(sm2p256v1);
            //构造domain参数
            ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());

            BigInteger privateKeyD = new BigInteger(privateKey, 16);
            ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(privateKeyD, domainParameters);

            SM2Engine sm2Engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            sm2Engine.init(false, privateKeyParameters);

            byte[] arrayOfBytes = sm2Engine.processBlock(cipherDataByte, 0, cipherDataByte.length);
            return new String(arrayOfBytes, "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


//    /**
//     * SM2 生成密钥 16进制
//     *
//     * @return
//     */
//    public static SM2KeyPair generateSM2Key() {
//        SM2 sm2 = new SM2();
//        String privateKey = HexUtil.encodeHexStr(BCUtil.encodeECPrivateKey(sm2.getPrivateKey()));
//        String publicKey = HexUtil.encodeHexStr(((BCECPublicKey) sm2.getPublicKey()).getQ().getEncoded(false));
//        return new SM2KeyPair(publicKey, privateKey);
//    }
//
//    public static class SM2KeyPair {
//        private String publicKey;
//        private String privateKey;
//
//        SM2KeyPair(String publicKey, String privateKey) {
//            this.publicKey = publicKey;
//            this.privateKey = privateKey;
//        }
//
//        public String getPublicKey() {
//            return publicKey;
//        }
//
//        public void setPublicKey(String publicKey) {
//            this.publicKey = publicKey;
//        }
//
//        public String getPrivateKey() {
//            return privateKey;
//        }
//
//        public void setPrivateKey(String privateKey) {
//            this.privateKey = privateKey;
//        }
//    }
//
//    /**
//     * SM2 私钥解密
//     *
//     * @param privateKey
//     * @param encryptedText
//     * @return
//     */
//    public static String sm2Decode(String privateKey, String encryptedText) {
//        SM2 sm2 = SmUtil.sm2(privateKey, null);
//        byte[] decryptBytes = sm2.decrypt(encryptedText, KeyType.PrivateKey);
//        return new String(decryptBytes);
//    }
//
//    /**
//     * SM2 公钥加密
//     *
//     * @param publicKey
//     * @param text
//     * @return
//     */
//    public static String sm2Encode(String publicKey, String text) {
//        SM2 sm2 = SmUtil.sm2(null, publicKey);
//        byte[] encryptBytes = sm2.encrypt(text.getBytes(), KeyType.PublicKey);
//        return HexUtil.encodeHexStr(encryptBytes);
//    }


}
