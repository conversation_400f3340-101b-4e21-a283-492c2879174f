package me.goldze.mvvmhabit.base;


import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.text.Html;
import android.text.Spannable;
import android.util.SparseArray;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.IdRes;
import androidx.annotation.IntRange;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * ****************************************************************<br>
 * 文件名称 : BaseViewHolder.java<br>
 * ****************************************************************
 */
public class BaseViewHolder extends RecyclerView.ViewHolder {
    private SparseArray<View> mViewSparseArray;

    public BaseViewHolder(View itemView, int viewType) {
        super(itemView);
    }

    public BaseViewHolder(View itemView) {
        super(itemView);
    }

    public interface OnItemClickListener<T> {
        void onItemClick(BaseViewHolder viewHolder, int position, T itemData, Object type);

        void onItemLongClick(BaseViewHolder viewHolder, int position, T itemData, Object type);
    }

    public interface OnItemViewClickListener<T> {
        void onItemViewClick(BaseViewHolder viewHolder, View view, int position, T itemData, Object type);

        void onItemViewLongClick(BaseViewHolder viewHolder, View view, int position, T itemData, Object type);
    }

    /**
     * 获取Item里的控件
     *
     * @param viewId
     * @param <V>
     * @return
     */
    public <V extends View> V getView(@IdRes int viewId) {
        if (mViewSparseArray == null) {
            mViewSparseArray = new SparseArray<>();
        }
        View view = mViewSparseArray.get(viewId);
        if (view == null) {
            view = itemView.findViewById(viewId);
            mViewSparseArray.put(viewId, view);
        }
        return (V) view;
    }

    /****************************************各种设置方法******************************************************/
    public void setText(@IdRes int viewId, String text, boolean isSpecial) {
        if (isSpecial) {
            this.<TextView>getView(viewId).setText(Html.fromHtml(text));
        } else {
            this.<TextView>getView(viewId).setText(text);
        }

    }

    public void setText(@IdRes int viewId, Object object) {

        this.<TextView>getView(viewId).setText(object == null ? "" : object.toString());
    }

    public void setText(@IdRes int viewId, Spannable object) {

        this.<TextView>getView(viewId).setText(object);
    }


    public void setCompoundDrawables(@IdRes int viewId, Drawable left, Drawable top, Drawable right, Drawable bottom) {
        this.<TextView>getView(viewId).setCompoundDrawables(left, top, right, bottom);
    }

    public void setImageBitmap(@IdRes int viewId, Bitmap bitmap) {
        this.<ImageView>getView(viewId).setImageBitmap(bitmap);
    }

    public void setImageResource(@IdRes int viewId, @DrawableRes int resId) {
        this.<ImageView>getView(viewId).setImageResource(resId);
    }

    public void setBackgroundResource(@IdRes int viewId, @DrawableRes int resId) {
        this.getView(viewId).setBackgroundResource(resId);
    }

    public void setTextColor(@IdRes int viewId, int color) {
        this.<TextView>getView(viewId).setTextColor(color);
    }

    public void setBackgroundColor(@IdRes int viewId, int color) {
        this.<TextView>getView(viewId).setBackgroundColor(color);
    }

    public void setLinearLayoutBackgroundColor(@IdRes int viewId, int color) {
        this.<LinearLayout>getView(viewId).setBackgroundColor(color);
    }

    public void setRelativeLayoutBackgroundColor(@IdRes int viewId, int color) {
        this.<RelativeLayout>getView(viewId).setBackgroundColor(color);
    }

    public void setLayoutParams(@IdRes int viewId, int width, int height) {
        View temporaryView = this.getView(viewId);
        ViewGroup.LayoutParams maskParams = temporaryView.getLayoutParams();
        maskParams.width = width;
        maskParams.height = height;
        temporaryView.setLayoutParams(maskParams);
    }

//    public void setBackgroud(@IdRes int viewId, String url) {
//        ImageLoaderManager.getInstance().displayImage(this.<ImageView>getView(viewId), url);
//    }

    public void setBackgroundAlpha(@IdRes int viewId, @IntRange(from = 0, to = 255) int alpha) {
        this.getView(viewId).getBackground().mutate().setAlpha(alpha);
    }

    public void setVisibility(@IdRes int viewId, boolean isShow) {
        if (isShow) {
            this.getView(viewId).setVisibility(View.VISIBLE);
        } else {
            this.getView(viewId).setVisibility(View.GONE);
        }
    }

    public void setVisibility(@IdRes int viewId, int visibility) {
        this.getView(viewId).setVisibility(visibility);
    }

    public void setLayoutManager(@IdRes int viewId, Context context) {
        ((RecyclerView) this.getView(viewId)).setLayoutManager(new LinearLayoutManager(context));
    }

    public void setLayoutManager(@IdRes int viewId, Context context, int spanCount) {
        ((RecyclerView) this.getView(viewId)).setLayoutManager(new GridLayoutManager(context, spanCount));
    }

    public void setAdapter(@IdRes int viewId, BaseAdapter adapter) {
        ((RecyclerView) this.getView(viewId)).setAdapter(adapter);
    }
}
