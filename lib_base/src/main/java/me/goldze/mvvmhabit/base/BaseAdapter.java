package me.goldze.mvvmhabit.base;

import android.content.Context;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.IntRange;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ****************************************************************<br>
 * 文件名称 : BaseAdapter.java<br>

 * ****************************************************************
 */
public abstract class BaseAdapter<T> extends RecyclerView.Adapter<BaseViewHolder> {

    protected List<T> mDataList;
    protected Context mContext;
    @LayoutRes
    protected int mItemLayoutRes;
    protected Map<Integer, Integer> mItemLayouts;
    /**
     * 为了在一个Activity/Fragment有多个Adapter时区分不同类型
     */
    protected Object mType;
    private BaseViewHolder.OnItemClickListener<T> mOnItemClickListener;
    private BaseViewHolder.OnItemViewClickListener<T> mOnItemViewClickListener;
    protected LayoutInflater mLayoutInflater;

    public BaseAdapter(Context context, @LayoutRes int itemLayoutRes, @Nullable Object type) {
        mContext = context;
        mItemLayoutRes = itemLayoutRes;
        mType = type;
        mDataList = new ArrayList<>();
        mLayoutInflater = LayoutInflater.from(context);
    }

    public BaseAdapter(Context context, Map<Integer, Integer> itemLayouts, @Nullable Object type) {
        mContext = context;
        mItemLayouts = itemLayouts;
        mType = type;
        mDataList = new ArrayList<>();
        mLayoutInflater = LayoutInflater.from(context);
    }

    @Override
    public int getItemViewType(int position) {
        T itemData = mDataList.get(position);
        return viewType(itemData);
    }

    /**
     * 设置监听
     *
     * @param onItemClickListener
     */
    public void setOnItemClickListener(BaseViewHolder.OnItemClickListener<T> onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public void setOnItemViewClickListener(BaseViewHolder.OnItemViewClickListener<T> onItemViewClickListener) {
        mOnItemViewClickListener = onItemViewClickListener;
    }

    /**
     * 分页加载
     *
     * @param dataList
     * @param isFirst
     */
    public void refreshAdapter(@NonNull List<T> dataList, boolean isFirst) {
        if (isFirst) {//第一次添加
            int previousSize = mDataList.size();
            mDataList.clear();
            notifyItemRangeRemoved(0, previousSize);
            mDataList.addAll(dataList);
            notifyItemRangeInserted(0, mDataList.size());
        } else {
            int preSize = mDataList.size();
            mDataList.addAll(dataList);
            notifyItemRangeInserted(preSize, dataList.size());
        }
    }

    public void refreshAdapter(List<T> dataList, boolean isFirst, boolean isRefresh) {
        if (isFirst) {//第一次添加
            int previousSize = mDataList.size();
            mDataList.clear();
            notifyItemRangeRemoved(0, previousSize);
            mDataList.addAll(dataList);
            notifyItemRangeInserted(0, mDataList.size());
        } else {
            int preSize = mDataList.size();
            if (isRefresh) {
                mDataList.addAll(0, dataList);
                notifyItemRangeChanged(0, dataList.size() < 10 ? 10 : dataList.size());
            } else {
                mDataList.addAll(dataList);
                notifyItemRangeInserted(preSize, dataList.size());
            }
        }
    }

    /**
     * 初始加载
     *
     * @param dataList
     */
    public void refreshAdapter(@NonNull List<T> dataList) {
        if (null == dataList) {
            return;
        }
        mDataList.clear();
        mDataList.addAll(dataList);
        notifyDataSetChanged();
    }

    public void removeItem(int position) {
        mDataList.remove(position);
        notifyItemRemoved(position);
        notifyItemRangeChanged(position, mDataList.size());
    }

    public void clearAdapter() {
        mDataList.clear();
        notifyDataSetChanged();
    }

    public void refreshAdapter() {
        notifyDataSetChanged();
    }

    public boolean isNonEmpty() {
        return !mDataList.isEmpty();
    }

    public List<T> getDataList() {
        return mDataList;
    }

    public void refreshAdapter(int position) {
        notifyItemChanged(position);
    }

    public T getItemDataByPosition(@IntRange(from = 0) int position) {
        return mDataList.get(position);
    }

    public int getScreenWidth() {
        DisplayMetrics dm = mContext.getApplicationContext().getResources().getDisplayMetrics();
        return dm.widthPixels;
    }

    public void refreshAdapter(T itemData) {
        if (null != mDataList) {
            mDataList.add(mDataList.size() - 1 == -1 ? 0 : mDataList.size(), itemData);
            notifyItemRangeChanged(mDataList.size() - 1, 2);
        }
    }

    public void refreshAdapter(T itemData, boolean toListFirst) {
        if (toListFirst) {
            mDataList.add(0, itemData);
            notifyItemRangeChanged(0, mDataList.size());
        } else {
            refreshAdapter(itemData);
        }

    }

    public void replaceAdapter(T itemData, int position) {
        mDataList.set(position, itemData);
    }

    public Context getContext() {
        return mContext;
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        View layout;
        if (null != mItemLayouts) {
            layout = mLayoutInflater.inflate(mItemLayouts.get(viewType), viewGroup, false);
        } else {
            layout = mLayoutInflater.inflate(mItemLayoutRes, viewGroup, false);
        }

        return new BaseViewHolder(layout);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseViewHolder baseViewHolder, int position) {
        T itemData = mDataList.get(position);
        setOnItemClickListener(baseViewHolder, itemData, position);
        bind(baseViewHolder, itemData, position);
    }


    protected void setOnItemClickListener(@NonNull final BaseViewHolder viewHolder, final T itemData, final int position) {
        if (mContext instanceof BaseViewHolder.OnItemClickListener) {
            mOnItemClickListener = (BaseViewHolder.OnItemClickListener<T>) mContext;
        }
        if (mOnItemClickListener != null) {
            viewHolder.itemView.setOnClickListener(view -> mOnItemClickListener.onItemClick(viewHolder, position, itemData, mType));
            viewHolder.itemView.setOnLongClickListener(view -> {
                mOnItemClickListener.onItemLongClick(viewHolder, position, itemData, mType);
                return true;
            });
        }
    }

    protected void setOnItemViewClickListener(@NonNull final BaseViewHolder viewHolder, View view, final T itemData, final int position) {
        if (mContext instanceof BaseViewHolder.OnItemViewClickListener) {
            mOnItemViewClickListener = (BaseViewHolder.OnItemViewClickListener<T>) mContext;
        }
        if (mOnItemViewClickListener != null) {
            view.setOnClickListener(view1 -> mOnItemViewClickListener.onItemViewClick(viewHolder, view1, position, itemData, mType));
            view.setOnLongClickListener(view12 -> {
                mOnItemViewClickListener.onItemViewLongClick(viewHolder, view12, position, itemData, mType);
                return true;
            });

        }
    }

    @Override
    public int getItemCount() {
        return mDataList.size();
    }

    public abstract void bind(@NonNull BaseViewHolder viewHolder, T itemData, int position);

    public abstract int viewType(T itemData);
}
