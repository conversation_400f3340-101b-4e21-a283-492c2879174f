package me.goldze.mvvmhabit.greendao;

import android.content.Context;
import android.content.ContextWrapper;
import android.database.DatabaseErrorHandler;
import android.database.sqlite.SQLiteDatabase;
import android.os.Environment;
import android.util.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class FileDatabaseContext extends ContextWrapper {

    public FileDatabaseContext(Context base) {
        super(base);
    }

    @Override
    public Context getApplicationContext() {
        return super.getApplicationContext();
    }

    @Override
    public File getDatabasePath(String name) {
        File file = createDatabasePath(name);
        if (null != file && file.exists()) {
            return file;
        }
        return super.getDatabasePath(name);
    }

    /**
     * 创建数据库路径
     *
     * @param name
     */
    public File createDatabasePath(String name) {

        Log.i("filedatabase", "name---" + name);

        //将数据库从app的包文件夹里面迁移出来，确保数据库不会因为app的卸载而被删除
        String DB_PATH = Environment.getExternalStorageDirectory().getPath() + File.separator + "" + File.separator + "DB";
        String dbPath = DB_PATH + File.separator + name;//数据库路径
        //判断目录是否存在，不存在则创建该目录
        File dirFile = new File(DB_PATH);//暂时屏蔽
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }

        //判断数据库文件是否存在，不存在则创建该文件
        File dbFile = new File(dbPath);
        if (dbFile.exists()) { //如果数据库文件已经存在，就直接返回
            return dbFile;
        } else {
            //去判断app文件夹里面是否有数据库
            String originDbDir;
            //获取sd卡路径
//            if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
//                // 有SD卡则使用SD - PS:没SD卡但是有外部存储器，会使用外部存储器
//                // SD\Android\data\包名\files\
//                originDbDir = getApplicationContext().getExternalFilesDir("DB").getPath() + "/";
//            } else {
                // 没有SD卡或者外部存储器，使用内部存储器
                // \data\data\包名\files\
                originDbDir = getApplicationContext().getFilesDir().getPath() + "/DB/";
//            }

            String originDbPath = originDbDir + File.separator + name;//数据库路径

            //判断目录是否存在，不存在则创建该目录
            File originDirFile = new File(originDbDir);//暂时屏蔽
            if (originDirFile.exists()) {
                //判断app文件夹里面是否有数据库文件
                File originDbFile = new File(originDbPath);
                if (originDbFile.exists()) {
                    try {
                        copyFileUsingFileStreams(originDbFile, dbFile);
                        return dbFile; //返回已经复制好的文件
                    } catch (Exception e) {
                        Log.e("HSJ-Exception ==", "HSJ-Exception ==" + e.getMessage());
                    }
                }
            }

            try {
                dbFile.createNewFile();
                return dbFile;//创建文件
            } catch (IOException e) {
                Log.e("HSJ-Exception ==", "HSJ-Exception ==" + e.getMessage());
                return getApplicationContext().getDatabasePath(name);
            }
        }
    }

    //重载这个方法，是用来打开SD卡上的数据库的，android 2.3及以下会调用这个方法。
    @Override
    public SQLiteDatabase openOrCreateDatabase(String name, int mode, SQLiteDatabase.CursorFactory factory) {
        SQLiteDatabase result = SQLiteDatabase.openOrCreateDatabase(getDatabasePath(name), factory);
        return result;
    }

    //Android 4.0会调用此方法获取数据库。
    @Override
    public SQLiteDatabase openOrCreateDatabase(String name, int mode, SQLiteDatabase.CursorFactory factory,
                                               DatabaseErrorHandler errorHandler) {
        SQLiteDatabase result = SQLiteDatabase.openOrCreateDatabase(getDatabasePath(name), factory);
        return result;
    }

    /**
     * 将文件从一个文件夹里面复制到另一个文件夹里面
     *
     * @param source
     * @param dest
     * @throws IOException
     */
    private static void copyFileUsingFileStreams(File source, File dest)
            throws IOException {
        InputStream input = null;
        OutputStream output = null;
        try {
            input = new FileInputStream(source);
            output = new FileOutputStream(dest);
            byte[] buf = new byte[1024];
            int bytesRead;
            while ((bytesRead = input.read(buf)) > 0) {
                output.write(buf, 0, bytesRead);
            }
        } finally {
            if (null != input) {
                input.close();
            }

            if (null != output) {
                output.close();
            }
        }
    }
}