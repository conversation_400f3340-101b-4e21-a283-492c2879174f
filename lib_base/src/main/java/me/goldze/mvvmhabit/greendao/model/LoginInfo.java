package me.goldze.mvvmhabit.greendao.model;

import android.os.Parcel;
import android.os.Parcelable;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Index;


@Entity(indexes = {@Index(value = "employeeID DESC", unique = true)})
public class LoginInfo implements Parcelable {
    @Id(autoincrement = true)
    public Long id;

    private String employeeID;

    private String employeeName;


    @Generated(hash = 1717645532)
    public LoginInfo(Long id, String employeeID, String employeeName) {
        this.id = id;
        this.employeeID = employeeID;
        this.employeeName = employeeName;
    }

    @Generated(hash = 1911824992)
    public LoginInfo() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmployeeID() {
        return employeeID;
    }

    public void setEmployeeID(String employeeID) {
        this.employeeID = employeeID;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {

    }
}