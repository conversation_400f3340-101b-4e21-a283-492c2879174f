package me.goldze.mvvmhabit.greendao;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;

import com.github.yuweiguocn.library.greendao.MigrationHelper;

import org.greenrobot.greendao.database.Database;

import me.goldze.mvvmhabit.greendao.model.DaoMaster;
import me.goldze.mvvmhabit.greendao.model.LoginInfoDao;

public class GreenDaoDBOpenHelper extends DaoMaster.DevOpenHelper {

    public GreenDaoDBOpenHelper(Context context, String name) {
        super(context, name);
    }

    public GreenDaoDBOpenHelper(Context context, String name, SQLiteDatabase.CursorFactory factory) {
        super(context, name, factory);
    }

    @Override
    public void onCreate(Database db) {
        super.onCreate(db);
        MigrationHelper.migrate(db, new MigrationHelper.ReCreateAllTableListener() {

            @Override
            public void onCreateAllTables(Database db, boolean ifNotExists) {
                DaoMaster.createAllTables(db, ifNotExists);
            }

            @Override
            public void onDropAllTables(Database db, boolean ifExists) {
                DaoMaster.dropAllTables(db, ifExists);
            }
        }, LoginInfoDao.class);
    }

    @Override
    public void onUpgrade(Database db, int oldVersion, int newVersion) {
        super.onUpgrade(db, oldVersion, newVersion);

        if (newVersion > oldVersion) {

            MigrationHelper.migrate(db, new MigrationHelper.ReCreateAllTableListener() {

                        @Override
                        public void onCreateAllTables(Database db, boolean ifNotExists) {
                            DaoMaster.createAllTables(db, ifNotExists);
                        }

                        @Override
                        public void onDropAllTables(Database db, boolean ifExists) {
                            DaoMaster.dropAllTables(db, ifExists);
                        }
                    }, LoginInfoDao.class);
        }
    }
}
