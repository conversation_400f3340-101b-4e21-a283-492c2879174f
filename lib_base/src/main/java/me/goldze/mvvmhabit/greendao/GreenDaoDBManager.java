package me.goldze.mvvmhabit.greendao;

import android.content.Context;


import java.util.List;

import me.goldze.mvvmhabit.greendao.model.DaoMaster;
import me.goldze.mvvmhabit.greendao.model.DaoSession;
import me.goldze.mvvmhabit.greendao.model.LoginInfo;
import me.goldze.mvvmhabit.greendao.model.LoginInfoDao;

public class GreenDaoDBManager {
    private static final String DB_NAME = "shenggx_db.db";

    private static Context context;

    public static Context getContext() {
        return context;
    }

    public void setContext(Context context) {
        this.context = context.getApplicationContext();
    }

    private DaoMaster daoMaster;
    private DaoSession daoSession;
    private GreenDaoDBOpenHelper devOpenHelper;

    private LoginInfoDao loginInfoDao;
    private static GreenDaoDBManager mGreenDaoDBManager;

    private GreenDaoDBManager() {
        initDao();
    }

    public static GreenDaoDBManager getInstance() {
        if (null == mGreenDaoDBManager) {
            synchronized (GreenDaoDBManager.class) {
                if (null == mGreenDaoDBManager) {
                    mGreenDaoDBManager = new GreenDaoDBManager();
                }
            }
        }
        return mGreenDaoDBManager;
    }

    public void initDao() {
        devOpenHelper = new GreenDaoDBOpenHelper(context, DB_NAME, null);
        if (devOpenHelper != null) {
            daoMaster = new DaoMaster(devOpenHelper.getWritableDatabase());
        }
        daoSession = daoMaster.newSession();
        loginInfoDao = daoSession.getLoginInfoDao();
    }

    public void saveLoginInfo(LoginInfo loginInfo) {
        if (null == loginInfoDao) {
            return;
        }
        loginInfoDao.insertOrReplace(loginInfo);
    }

    /**
     * 查询所有的登录用户信息
     *
     * @param
     * @return
     */
    public List<LoginInfo> queryLoginInfoList(String name) {
        if (null != loginInfoDao) {
            List<LoginInfo> list = loginInfoDao.queryBuilder()
                    .where(LoginInfoDao.Properties.EmployeeName.like(name))
                    .list();
            return list;
        }
        return null;
    }


}


