package me.goldze.mvvmhabit.greendao;



import java.util.List;

import me.goldze.mvvmhabit.greendao.model.LoginInfo;
import me.goldze.mvvmhabit.greendao.model.LoginInfoDao;

/**
 * 实际增删改查操作类
 */
public class DBManagerUtil {

    private static DBManagerUtil managerUtil;

    public static DBManagerUtil getInstance() {
        if (null == managerUtil) {
            synchronized (DBManagerUtil.class) {
                if (null == managerUtil) {
                    managerUtil = new DBManagerUtil();
                }
            }
        }
        return managerUtil;
    }

//    /**
//     * 获取单例
//     */
//    public static DaoSession getInstance() {
//        if (mDaoSession == null) {
//            mDaoSession = CliectApp.getmDaoSession();
//        }
//        return mDaoSession;
//    }

    /**
     * 查询所有的登录用户信息
     *
     * @param
     * @return
     */
    public List<LoginInfo> queryLoginInfoList(LoginInfoDao loginInfoDao, String name) {
        if (null != loginInfoDao) {
            List<LoginInfo> list = loginInfoDao.queryBuilder()
                    .where(LoginInfoDao.Properties.EmployeeName.like(name))
                    .list();
            return list;
        }
        return null;
    }




}
