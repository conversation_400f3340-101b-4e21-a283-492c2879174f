package me.goldze.mvvmhabit.binding.viewadapter.image;

import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import androidx.databinding.BindingAdapter;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;

import me.goldze.mvvmhabit.R;
import me.goldze.mvvmhabit.utils.GlideRoundedCornersTransform;

/**
 * Created by goldze on 2017/6/18.
 */
public final class ViewAdapter {
    @BindingAdapter(value = {"url", "placeholderRes",}, requireAll = false)
    public static void setImageUri(ImageView imageView, String url, int placeholderRes) {
//        if (!TextUtils.isEmpty(url)) {
            //使用Glide框架加载图片
//            Glide.with(imageView.getContext())
//                    .load(url)
//                    .apply(new RequestOptions().placeholder(placeholderRes))
//                    .into(imageView);

            RequestOptions options = new RequestOptions();
            //圆角
            options = options.transform(new
                    GlideRoundedCornersTransform(imageView.getContext(), 999f, GlideRoundedCornersTransform.CornerType.ALL));
//            if (!TextUtils.isEmpty(itemData.getName())) {
            Glide.with(imageView.getContext())
                    .load(url)
                    .apply(options).placeholder(R.drawable.person_empty).error(R.drawable.person_empty)
                    .into(imageView);
//        }
    }

    @BindingAdapter(value = {"imageUrl", "placeholderRes",}, requireAll = false)
    public static void setImageUri(ImageView imageView, String imageUrl, Drawable placeholderRes) {
//        if (!TextUtils.isEmpty(url)) {
        //使用Glide框架加载图片
//            Glide.with(imageView.getContext())
//                    .load(url)
//                    .apply(new RequestOptions().placeholder(placeholderRes))
//                    .into(imageView);

        RequestOptions options = new RequestOptions();
        //圆角
        options = options.transform(new
                GlideRoundedCornersTransform(imageView.getContext(), 8f, GlideRoundedCornersTransform.CornerType.ALL));
//            if (!TextUtils.isEmpty(itemData.getName())) {
        Glide.with(imageView.getContext())
                .load(imageUrl)
                .apply(options).placeholder(placeholderRes).error(placeholderRes)
                .into(imageView);
//        }
    }
}

