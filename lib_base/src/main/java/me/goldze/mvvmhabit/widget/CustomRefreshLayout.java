package me.goldze.mvvmhabit.widget;

import android.content.Context;

import com.lcodecore.tkrefreshlayout.IBottomView;
import com.lcodecore.tkrefreshlayout.IHeaderView;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;
import com.lcodecore.tkrefreshlayout.footer.LoadingView;
import com.lcodecore.tkrefreshlayout.header.SinaRefreshView;

import me.goldze.mvvmhabit.R;

/**
 * @ProjectName:
 * @Package: me.goldze.mvvmhabit.widget
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2023/5/25
 */
public class CustomRefreshLayout extends TwinklingRefreshLayout {

    public CustomRefreshLayout(Context context) {
        super(context);
        // 设置头部
        SinaRefreshView
                sinaRefreshView = new SinaRefreshView(getContext());
        sinaRefreshView.setArrowResource(com.lcodecore.tkrefreshlayout.R.drawable.anim_loading_view);
        setHeaderView(sinaRefreshView);
        // 设置尾部
        LoadingView ballPulseView = new LoadingView(getContext());
        setBottomView(ballPulseView);

    }
//
//    @Override
//    public void setHeaderView(IHeaderView headerView) {
//        super.setHeaderView(headerView);
//        // 设置头部
//        SinaRefreshView
//                sinaRefreshView = new SinaRefreshView(getContext());
//        sinaRefreshView.setArrowResource(R.drawable.anim_loading_view);
//    }
//
//    @Override
//    public void setBottomView(IBottomView bottomView) {
//        super.setBottomView(bottomView);
//    }
}
