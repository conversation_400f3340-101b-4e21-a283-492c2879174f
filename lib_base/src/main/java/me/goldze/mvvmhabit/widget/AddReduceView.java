package me.goldze.mvvmhabit.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import me.goldze.mvvmhabit.R;

/**
 * @ProjectName:
 * @Package: me.goldze.mvvmhabit.widget
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2023/5/30
 */
public class AddReduceView extends LinearLayout implements View.OnClickListener {
    private int value = 1;
    private int minValue = 1;
    private int maxValue = 10;
    private TextView tvCount;

    public AddReduceView(Context context, AttributeSet attrs) {
        super(context, attrs);
        View view = View.inflate(context, R.layout.number_add_reduce, this);
        TextView btn_reduce = (TextView) view.findViewById(R.id.btn_reduce);
        tvCount = (TextView) view.findViewById(R.id.tv_count);
        TextView btn_add = (TextView) view.findViewById(R.id.btn_add);
        btn_reduce.setOnClickListener(this);
        btn_add.setOnClickListener(this);
        //设置默认值
        int value = getValue();
        setValue(value);
    }

//    @OnClick({R.id.btn_reduce, R.id.btn_add})
    public void onViewClicked(View view) {
        int id = view.getId();
        if (id == R.id.btn_reduce) {//减
            reduce();
        } else if (id == R.id.btn_add) {//加
            add();
        }
    }

    /**
     * 如果当前值大于最小值   减
     */
    private void reduce() {
        if (value > minValue) {
            value--;
        }
        setValue(value);
        if (onValueChangeListene != null) {
            onValueChangeListene.onValueChange(value);
        }
    }

    /**
     * 如果当前值小于最小值  加
     */
    private void add() {
        if (value < maxValue) {
            value++;
        }
        setValue(value);
        if (onValueChangeListene != null) {
            onValueChangeListene.onValueChange(value);
        }
    }

    //获取具体值
    public int getValue() {
        String countStr = tvCount.getText().toString().trim();
        if (countStr != null) {
            value = Integer.valueOf(countStr);
        }
        return value;
    }

    public void setValue(int value) {
        this.value = value;
        tvCount.setText(value + "");
    }

    public int getMinValue() {
        return minValue;
    }

    public void setMinValue(int minValue) {
        this.minValue = minValue;
    }

    public int getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(int maxValue) {
        this.maxValue = maxValue;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_reduce) {//减
            reduce();
        } else if (id == R.id.btn_add) {//加
            add();
        }
    }


    //监听回调
    public interface OnValueChangeListener {
        public void onValueChange(int value);
    }

    private OnValueChangeListener onValueChangeListene;

    public void setOnValueChangeListener(OnValueChangeListener onValueChangeListene) {
        this.onValueChangeListene = onValueChangeListene;
    }
}
