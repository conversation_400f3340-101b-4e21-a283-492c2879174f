package me.goldze.mvvmhabit.widget;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

public class DrawView extends View {
    // 定义记录前一个拖动事件发生点的坐标
    float preX;
    float preY;
    private Path path;
    public Paint paint = null;
    // 定义一个内存中的图片，该图片将作为缓冲区
    Bitmap cacheBitmap = null;
    // 定义cacheBitmap上的Canvas对象
    Canvas cacheCanvas = null;
    int with = 0;


    public DrawView(Context context) {
        super(context);
    }

    public DrawView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DrawView(Context context, int width, int height) {
        super(context);
        path = new Path();
        // 设置cacheCanvas将会绘制到内存中的cacheBitmap上

        // 设置画笔的颜色
        paint = new Paint(Paint.DITHER_FLAG);
        paint.setColor(Color.BLACK);
        // 设置画笔风格
        paint.setStyle(Paint.Style.STROKE);
        //画笔粗细
        paint.setStrokeWidth(14);//18
        paint.setTextSize(25);//30
        // 抗锯齿
        paint.setAntiAlias(true);
        paint.setDither(true);

    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        // 创建一个与该View相同大小的缓存区
        cacheBitmap = Bitmap.createBitmap(getWidth(), getHeight(), Bitmap.Config.ARGB_8888);
        cacheCanvas = new Canvas();
        cacheCanvas.setBitmap(cacheBitmap);
//        cacheCanvas.drawColor(Color.parseColor("#f5f5f7"));
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // 获取拖动事件的发生位置
        float x = event.getX() + with;
        float y = event.getY();
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 从前一个点绘制到当前点之后，把当前点定义成下次绘制的前一个点
                path.moveTo(x, y);
                preX = x;
                preY = y;
                break;
            case MotionEvent.ACTION_MOVE:
                // 从前一个点绘制到当前点之后，把当前点定义成下次绘制的前一个点
//                path.quadTo(preX, preY, x, y);
//                preX = x;
//                preY = y;
                final float previousX = preX;
                final float previousY = preY;
                final float dx = Math.abs(x - previousX);
                final float dy = Math.abs(y - previousY);
                if (dx >= 3 || dy >= 3){
                    float cx = (x + previousX) / 2;
                    float cy = (y + previousY) / 2;
                    path.quadTo(previousX, previousY, cx, cy);
                    preX = x;
                    preY = y;
                }
                break;
            case MotionEvent.ACTION_UP:
                cacheCanvas.drawPath(path, paint); // ①
                path.reset();
                break;
        }
        invalidate();
        // 返回true表明处理方法已经处理该事件
        return true;
    }

    @Override
    public void onDraw(Canvas canvas) {
        Paint bmpPaint = new Paint();
        // 将cacheBitmap绘制到该View组件上
        canvas.drawBitmap(cacheBitmap, 0, 0, bmpPaint);
        // 沿着path绘制
        canvas.drawPath(path, paint);
    }

    //获取我们绘制成功后的图片

    public Bitmap getPaintBitmap(int lenth) {
        Log.d("zpzp", cacheBitmap.getWidth() + "  " + cacheBitmap.getHeight());

        return resizeImage(cacheBitmap,400, 230);
    }


    // 缩放
    public static Bitmap resizeImage(Bitmap bitmap, int width, int height) {
        //获取图片的宽高
        int originWidth = bitmap.getWidth();
        int originHeight = bitmap.getHeight();

        //这里缩放我们的尺寸，缩放多少自己去定义
        float scaleWidth = ((float) width) / originWidth;

        //进行缩放
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleWidth);
        return Bitmap.createScaledBitmap(bitmap, 400,Math.round(scaleWidth*originHeight), true);
    }


    //清除画板
    public void clear() {
        if (cacheBitmap != null) {
            path.reset();
            cacheCanvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
            invalidate();
        }
    }

    public boolean isCanvasEmpty() {
        // Get the pixel data from the canvas
        // Iterate over the pixels and check if any of them are non-transparent
        for (int x = 0; x < cacheBitmap.getWidth(); x++) {
            for (int y = 0; y < cacheBitmap.getHeight(); y++) {
                int pixel = cacheBitmap.getPixel(x, y);
                if (pixel != Color.TRANSPARENT) {
                    return false;
                }
            }
        }

        // If no non-transparent pixels were found, the canvas is empty
        return true;
    }

    public void setwith(int withs) {
        with = withs;
    }
}

