package me.goldze.mvvmhabit.widget;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.widget
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/1/17
 */

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import me.goldze.mvvmhabit.R;


public class TitleBar extends RelativeLayout {
    private Context context;

    private OnTitleAndLeftAndRightClickListener listener;//监听点击事件

    private LinearLayout li_life, li_right, li_title;//布局控件，设置响应监听事件用

    private TextView tvShenban, tvTitle, tv_title;//左边文字，右边文字，标题；文本控件，设置标题，文字用

    private ImageView iv_life, iv_title, iv_right;//图片控件，设置图标用

    public TitleBar(Context context) {
        super(context);
        this.context = context;
    }

    /**
     * 注意这个构造方法是Context context, AttributeSet attrs两个参数
     *
     * @param context
     * @param attrs
     */
    public TitleBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
    }

    public void setTitle() {
        LayoutInflater.from(context).inflate(R.layout.title_bar, this);//获取布局xml
        //初始化LinearLayout控件
        tvShenban = (TextView) findViewById(R.id.tv_shenban);
//        li_right = (TextView) findViewById(R.id.li_right);
        tvTitle = (TextView) findViewById(R.id.tv_title);

        tvShenban.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (listener != null)
                    listener.onLeftLinearLayoutClick();
            }
        });
        tvTitle.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (listener != null)
                    listener.onRightLinearLayoutClick();
            }
        });


        //通过context.obtainStyledAttributes方法，将AttributeSet和属性的类型传递过来
//        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.TitleBar);


//        //初始化ImageView控件
//        iv_right = (ImageView) findViewById(R.id.iv_right_image);
//        iv_title = (ImageView) findViewById(R.id.iv_center_image);
//        iv_life = (ImageView) findViewById(R.id.iv_life_image);
//
//        Drawable drawable = typedArray.getDrawable(R.styleable.TitleBar_imRightSrc);//获得自定义属性并赋值
//        iv_right.setImageDrawable(drawable);
//        iv_life.setImageDrawable(typedArray.getDrawable(R.styleable.TitleBar_imLifeSrc));
//        iv_title.setImageDrawable(typedArray.getDrawable(R.styleable.TitleBar_imCenterSrc));
    }

    //设置监听器
    public void setOnTitleAndLeftAndRightClickListener(OnTitleAndLeftAndRightClickListener listener) {
        this.listener = listener;
    }

    /**
     * 使用java代码设置标题
     *
     * @param titleString
     */
    public void setTitle(String titleString) {
        this.tv_title.setText(titleString);
    }

    /**
     * 使用java代码左面标题，中间标题，右边标题
     *
     * @param leftString
     * @param titleString
     * @param rightString
     */
    public void setLeftAndTitleAndRight(String leftString, String titleString, String rightString) {
        this.tv_title.setText(leftString);
        this.tv_title.setText(titleString);
        this.tvShenban.setText(rightString);
    }

    /**
     * 回调接口
     */
    public interface OnTitleAndLeftAndRightClickListener {
        public void onLeftLinearLayoutClick();

        public void onRightLinearLayoutClick();

    }
}


