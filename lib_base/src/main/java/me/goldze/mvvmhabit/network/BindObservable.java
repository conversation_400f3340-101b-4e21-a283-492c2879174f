package me.goldze.mvvmhabit.network;

import android.content.Context;

import io.reactivex.Observable;

public class BindObservable {

    private static BindObservable mBindObservable;

    public static BindObservable getInstance() {
        if (mBindObservable == null) {
            synchronized (BindObservable.class) {
                if (mBindObservable == null) {
                    mBindObservable = new BindObservable();
                }
                return mBindObservable;
            }
        }
        return mBindObservable;
    }

    public void bindObservable(Observable observable,MyObserver observer) {
        observable.compose(RxHelper.observableIO2Main()).subscribe(observer);
    }
}
