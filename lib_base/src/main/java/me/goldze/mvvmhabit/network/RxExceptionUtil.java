package me.goldze.mvvmhabit.network;

import org.json.JSONException;

import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.text.ParseException;

import me.goldze.mvvmhabit.http.NetworkUtil;
import me.goldze.mvvmhabit.utils.Utils;
import retrofit2.HttpException;

/**
 * ****************************************************************
 * 文件名称 : RxExceptionUtil.java<br>
 * 文件描述 : 异常处理 <br>
 * ****************************************************************
 */
public class RxExceptionUtil {

    public static String exceptionHandler(Throwable e) {
        String errorMsg = "获取数据失败";
        if (e instanceof UnknownHostException) {
            errorMsg = "网络异常";
        } else if (e instanceof SocketTimeoutException) {
            errorMsg = "请求网络超时";
        } else if (e instanceof HttpException) {
            HttpException httpException = (HttpException) e;
            errorMsg = convertStatusCode(httpException);
        } else if (e instanceof ParseException || e instanceof JSONException
                || e instanceof JSONException) {
            errorMsg = "数据解析错误";
        } else if (!NetworkUtil.isNetworkAvailable(Utils.getContext())) {
            errorMsg = "当前无网络";
        }
        return errorMsg;
    }

    public static String convertStatusCode(HttpException httpException) {
        String msg;
//        if (httpException.code() >= 501 && httpException.code() < 600) {
//            msg = "服务器处理请求出错";
//        } else if (httpException.code() == 500) {
//            msg = "服务器处理请求出错,请稍后再试";
//        } else if (httpException.code() == 404) {
//            msg = "服务正在维护中,请稍后...";
//        }else if (httpException.code() == 401) {
//            msg = "无权限";
//        }
//        else if (httpException.code() >= 405 && httpException.code() < 500) {
//            msg = "服务器无法处理请求";
//        } else if (httpException.code() >= 300 && httpException.code() < 400) {
//            msg = "请求被重定向到其他页面";
//        } else {
//            msg = httpException.message();
//        }

        if (httpException.code() == 401) {
            msg = "无权限";
        } else {
            msg = "服务器处理请求出错";
        }
        return msg;
    }
}
