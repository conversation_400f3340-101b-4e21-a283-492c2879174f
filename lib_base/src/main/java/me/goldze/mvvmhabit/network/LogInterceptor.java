package me.goldze.mvvmhabit.network;


import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;

import org.w3c.dom.Text;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Locale;

import me.goldze.mvvmhabit.utils.CommonUtil;
import me.goldze.mvvmhabit.utils.SM2Util;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okio.Buffer;

/**
 * ****************************************************************<br>
 * 文件名称 : LogInterceptor.java<br>
 * 文件描述 : 网络请求Log截取 <br>
 * ****************************************************************
 */
public class LogInterceptor implements Interceptor {

    @Override
    public okhttp3.Response intercept(Chain chain) throws IOException {
        Request request = chain.request();

        try {
            RequestBody requestBody = request.body();
            String body = null;
            if (requestBody != null) {
                Buffer buffer = new Buffer();
                requestBody.writeTo(buffer);
                Charset charset = Charset.forName("UTF-8");
                MediaType contentType = requestBody.contentType();
                if (contentType != null) {
                    charset = contentType.charset(Charset.defaultCharset());
                }
                body = buffer.readString(charset);
            }

            if (CommonUtil.IS_DEBUG) {
                Log.d("okhttp", "发送请求: method：" + request.method()
                        + "\nurl：" + request.url()
                        + "\n请求头：" + request.headers()
                        + "\n请求参数: " + body);
            }

        } catch (Exception e) {
            e.printStackTrace();
            Log.e("okhttp", e.getMessage());
        }

        long t1 = System.nanoTime();
        okhttp3.Response response = chain.proceed(chain.request());
        String originEncryptKey = response.headers().get("encrypt");
        String encryptKey="";
        if (originEncryptKey != null) {
            encryptKey = SM2Util.decryptSM2(CommonUtil.ENCRY_PRIVATE_SCRET, "04" + originEncryptKey);
            Log.e("Test", "encryptKey==" + encryptKey + "originEncryptKey==" + originEncryptKey);
        }


        long t2 = System.nanoTime();
        okhttp3.MediaType mediaType = response.body().contentType();
        String content = response.body().string();
        if (CommonUtil.IS_DEBUG) {
            Log.d("http", String.format(Locale.getDefault(), "Received response for %s in %.1fms%n%s",
                    response.request().url(), (t2 - t1) / 1e6d, response.headers()) + "response body ==" + content);
        }
        //这里可以统一拦截401等接口
        if (CommonUtil.IS_DEBUG) {
            Log.e("http", "原始出参paramJson==" + content);
        }
        String responseStr = content;
        if (!TextUtils.isEmpty(content) && !content.contains("{") && !request.url().toString().endsWith("/public") && !request.url().toString().endsWith("/code")
                && !request.url().toString().endsWith("/check") && !TextUtils.isEmpty(encryptKey)) {
            responseStr = SM2Util.decrypt(content,encryptKey);
            if (CommonUtil.IS_DEBUG) {
                Log.e("http", request.url() + "解密后出参==" + responseStr);
            }
        }

        return response.newBuilder()
                .body(okhttp3.ResponseBody.create(mediaType, responseStr))
                .build();
    }
}
