package me.goldze.mvvmhabit.network;

import android.util.Log;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import me.goldze.mvvmhabit.http.BaseResponse;
import retrofit2.HttpException;

/**
 * ****************************************************************<br>
 * 文件名称 : BaseObserver.java<br>
 * 文件描述 : 数据返回统一处理<br>
 * ****************************************************************
 */
public abstract class BaseObserver<T> implements Observer<BaseResponse<T>> {
    private static final String TAG = "BaseObserver";

    @Override
    public void onNext(BaseResponse<T> response) {
        if (response.getCode() == 200 || response.getCode() == 0 || response.getCode() ==0000) {
            onSuccess(response.getResult());
        } else {
            Log.e(TAG, String.valueOf(response.getMessage()) + "===========" + response.getCode());
//            ToastUtils.showShort(response.getMessage());
            onFailure(null, response.getMessage());
        }
    }

    @Override
    public void onError(Throwable e) {//服务器错误信息处理
        Log.e(TAG, String.valueOf(e.getMessage()));
        String message;
        String errorMsg = "未知错误";
        if (e instanceof HttpException) {
            HttpException httpException = (HttpException) e;
            errorMsg = RxExceptionUtil.convertStatusCode(httpException);
        } else {
            errorMsg = RxExceptionUtil.exceptionHandler(e);
        }
//        ToastUtils.showShort(errorMsg);
        onFailure(e, errorMsg);
    }

    @Override
    public void onComplete() {
//

    }


    @Override
    public void onSubscribe(Disposable d) {

    }

    public abstract void onSuccess(T result);

    public abstract void onFailure(Throwable e, String errorMsg);
}
