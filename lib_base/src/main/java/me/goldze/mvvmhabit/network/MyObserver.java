package me.goldze.mvvmhabit.network;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;

import io.reactivex.disposables.Disposable;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * @ProjectName:
 * @Package: me.goldze.mvvmhabit.network
 * @Description:
 * @Author: xuh<PERSON>feng
 * @CreateDate: 2024/1/31
 */
public abstract class MyObserver<T> extends BaseObserver<T> {
    private Disposable mDisposable;

    protected MyObserver() {
    }

    @Override
    public void onSubscribe(Disposable d) {
        this.mDisposable = d;
        if (!isConnected(Utils.getContext())) {
            Log.e("miniTest", "网络异常");
            ToastUtils.showShort("网络异常");
            if (d.isDisposed()) {
                d.dispose();
            }
        }
    }

    @Override
    public void onError(Throwable e) {
        if (mDisposable.isDisposed()) {
            mDisposable.dispose();
        }
        super.onError(e);
    }

    @Override
    public void onComplete() {
        if (mDisposable.isDisposed()) {
            mDisposable.dispose();
        }
        super.onComplete();
    }


    /**
     * 是否有网络连接，不管是wifi还是数据流量
     */
    private static boolean isConnected(Context context) {
        boolean connectedState = false;
        try {
            if (null != context) {
                ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
                if (null != connectivityManager) {
                    NetworkInfo wifi = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
                    NetworkInfo mobile = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_MOBILE);
                    assert wifi != null;
                    assert mobile != null;
                    if (wifi.isAvailable() || mobile.isAvailable()) {
                        connectedState = true;
                    }
                }
            }
        } catch (Exception ex) {
            ex.getMessage();
        }
        return connectedState;
    }

    /**
     * 取消订阅
     */
    public void cancleRequest() {
        if (mDisposable != null && mDisposable.isDisposed()) {
            mDisposable.dispose();
        }

    }
}

