package me.goldze.mvvmhabit.network;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.gson.Gson;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import me.goldze.mvvmhabit.common.BaseAppConfig;
import me.goldze.mvvmhabit.utils.CommonUtil;
import me.goldze.mvvmhabit.utils.SM2Util;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * ****************************************************************
 * 文件名称 : RetrofitUtils.java<br>
 * 文件描述 : 初始化相关 <br>
 * ****************************************************************
 */
public class NoHeaderRetrofitUtils {

    private static final String TAG = "RetrofitUtils";

    private static NoHeaderRetrofitUtils mRetrofitUtils;

    private static Context mContext;

    //设置默认超时时间
    public static int DEFAULT_TIME = 20;

    public static NoHeaderRetrofitUtils getInstance() {
        if (mRetrofitUtils == null) {
            synchronized (NoHeaderRetrofitUtils.class) {
                if (mRetrofitUtils == null) {
                    mRetrofitUtils = new NoHeaderRetrofitUtils();
                }
                return mRetrofitUtils;
            }
        }
        return mRetrofitUtils;
    }

    public static void resetOvertime(int overTime) {
        DEFAULT_TIME = overTime;
    }

    /**
     * 初始化Retrofit
     */
    @NonNull
    public Retrofit initRetrofit() {
        return new Retrofit.Builder()
                .baseUrl(BaseAppConfig.SERVICE_PATH)
                .client(initOkHttp())
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .addConverterFactory(GsonConverterFactory.create())
                .build();
    }

    public <T> T generateReq(Class<T> destClass) {
        return initRetrofit().create(destClass);
    }

    /**
     * 初始化okhttp
     */
    @NonNull
    private OkHttpClient initOkHttp() {
        return new OkHttpClient().newBuilder()
                .readTimeout(DEFAULT_TIME, TimeUnit.SECONDS)//设置读取超时时间
                .connectTimeout(DEFAULT_TIME, TimeUnit.SECONDS)//设置请求超时时间
                .writeTimeout(DEFAULT_TIME, TimeUnit.SECONDS)//设置写入超时时间
                .addInterceptor(new Interceptor() {
                    @Override
                    public Response intercept(Chain chain) throws IOException {
                        Request request = null;
                        String sm4Key = CommonUtil.getSM4SCRET();
                        request = chain.request().newBuilder()
                                .addHeader("encrypt", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, sm4Key))
                                .addHeader("source", "2.0mini")
                                .addHeader("client",CommonUtil.clientId)
                                .addHeader("clientscrt",SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, CommonUtil.clientId))
                                .build();

//                        RequestBody requestBody = request.body();
//                        String body = null;
//                        if (requestBody != null && requestBody.contentType() != null) {
//                            if (!requestBody.contentType().toString().contains("multipart/form-data")) {
//                                Buffer buffer = new Buffer();
//                                requestBody.writeTo(buffer);
//                                Charset charset = Charset.forName("UTF-8");
//                                MediaType contentType = requestBody.contentType();
//                                if (contentType != null) {
//                                    charset = contentType.charset(Charset.defaultCharset());
//                                }
//                                body = buffer.readString(charset);
//                            }
//                        }
//                        //post
//                        if (!TextUtils.isEmpty(body)) {
//                            //针对mac=** post类型formurlencoder这种要转换为json
//                            Gson gson = new Gson();
//                            if (request.method().contains("GET") && body.contains("=")) {
//                                String[] paramArray = body.split("=");
//                                HashMap tmpMap = new HashMap();
//                                tmpMap.put(paramArray[0], paramArray[1]);
//                                body = gson.toJson(tmpMap);
//                            }
//                            String sm2Str = SM2Util.encrypt(body, sm4Key);
//                            HashMap<String, Object> param = new HashMap<>();
//                            param.put("encryptStr", sm2Str);
//                            if(CommonUtil.IS_DEBUG)
//                            Log.e("http", "加密后入参sm2Str==" + sm2Str + "==requestBody.contentType()" + requestBody.contentType());
//                            RequestBody newrequestBody = RequestBody.create(requestBody.contentType(), gson.toJson(param));
//                            request = request.newBuilder().post(newrequestBody).build();
//                        } else {
//                            if (CommonUtil.IS_DEBUG) {
//                                Log.e("http", "加密前原始url==" + request.url());
//                            }
//                            if (!TextUtils.isEmpty(request.url().toString()) && request.url().toString().contains("?")) {
//                                String paramStr = request.url().toString().substring(request.url().toString().lastIndexOf("?") + 1, request.url().toString().length());
//                                String newUrl = "";
//                                if (!TextUtils.isEmpty(paramStr)) {
//                                    String jsonParam = handlerStrToHashMap(paramStr);
//                                    String sm2Str = SM2Util.encrypt(jsonParam, sm4Key);
//
//                                    if (CommonUtil.IS_DEBUG) {
//                                        Log.d("http", "加密前get url==" + request.url().toString());
//                                    }
//                                    newUrl = request.url().toString().substring(0, request.url().toString().lastIndexOf("?") + 1).concat("encryptStr=").concat(sm2Str);
//                                    if (CommonUtil.IS_DEBUG) {
//                                        Log.d("http", "加密后get newUrl==" + newUrl);
//                                    }
//                                }
//                                if (!TextUtils.isEmpty(newUrl)) {
//                                    request = request.newBuilder().url(newUrl).build();
//                                }
//                            }
//                        }

                        return chain.proceed(request);
                    }
                })
                .addInterceptor(new LogInterceptor())//添加打印拦截器
                .retryOnConnectionFailure(true)//设置出现错误进行重新连接。
                .build();
    }

    private String handlerStrToHashMap(String strURl) {
        Map<String, String> resultMap = new HashMap<>();
        if (strURl != null) {
            String[] pairs = strURl.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length == 2) {
                    resultMap.put(keyValue[0], keyValue[1]);
                }
            }
        }
        return new Gson().toJson(resultMap);

    }
}

