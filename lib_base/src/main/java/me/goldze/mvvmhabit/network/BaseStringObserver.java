package me.goldze.mvvmhabit.network;

import android.util.Log;

import com.alibaba.android.arouter.utils.TextUtils;

import org.w3c.dom.Text;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import me.goldze.mvvmhabit.http.BaseResponse;
import me.goldze.mvvmhabit.utils.ToastUtils;
import retrofit2.HttpException;

/**
 * ****************************************************************<br>
 * 文件名称 : BaseObserver.java<br>
 * 文件描述 : 数据返回统一处理<br>
 * ****************************************************************
 */
public abstract class BaseStringObserver<T> implements Observer<String> {
    private static final String TAG = "BaseStringObserver";

    @Override
    public void onNext(String response) {
        Log.e(TAG, String.valueOf(response+"0000000000"));
        if (!TextUtils.isEmpty(response) && response.contains("access")) {
            onSuccess(response);
        } else {
            onFailure(null, response);
        }
    }

    @Override
    public void onError(Throwable e) {//服务器错误信息处理
        Log.e(TAG, String.valueOf(e.getMessage()));
        String message;
        String errorMsg = "未知错误";
        if (e instanceof HttpException) {
            HttpException httpException = (HttpException) e;
            errorMsg = RxExceptionUtil.convertStatusCode(httpException);
        } else {
            errorMsg = RxExceptionUtil.exceptionHandler(e);
        }
        ToastUtils.showShort(errorMsg);
        onFailure(e, errorMsg);
    }

    @Override
    public void onComplete() {
//

    }


    @Override
    public void onSubscribe(Disposable d) {

    }

    public abstract void onSuccess(String result);

    public abstract void onFailure(Throwable e, String errorMsg);
}
