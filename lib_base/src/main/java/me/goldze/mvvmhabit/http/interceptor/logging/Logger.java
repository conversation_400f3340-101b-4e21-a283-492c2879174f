package me.goldze.mvvmhabit.http.interceptor.logging;

import okhttp3.internal.platform.Platform;

/**
 * <AUTHOR> on 11/07/2017.
 */
@SuppressWarnings({"WeakerAccess", "unused"})
public interface Logger {
    void log(int level, String tag, String msg);

    Logger DEFAULT = new Logger() {
        @Override
        public void log(int level, String tag, String message) {
            Platform.get().log(level, message, null);
        }
    };
}
