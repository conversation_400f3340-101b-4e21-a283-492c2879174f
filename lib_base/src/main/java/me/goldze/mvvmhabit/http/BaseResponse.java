package me.goldze.mvvmhabit.http;

import com.alibaba.android.arouter.utils.TextUtils;

/**
 * Created by gold<PERSON> on 2017/5/10.
 * 该类仅供参考，实际业务返回的固定字段, 根据需求来定义，
 */
public class BaseResponse<T> {
    private int code;//0:成功
    private String msg;
    private T data;
    public boolean isSuccess = false;

    public boolean isSuccess() {
        return isSuccess;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public T getResult() {
        return data;
    }

    public void setResult(T result) {
        this.data = result;
    }

    public boolean isOk() {
        return code == 0;
    }

    public String getMessage() {
        return !TextUtils.isEmpty(msg) ? msg : "未知错误";
    }

    public void setMessage(String message) {
        this.msg = message;
    }
}
