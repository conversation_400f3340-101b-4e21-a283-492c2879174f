<manifest xmlns:android="http://schemas.android.com/apk/res/android"

    package="me.goldze.mvvmhabit">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <application>
        <activity
            android:name=".base.ContainerActivity"
            android:configChanges="orientation|keyboardHidden"></activity>
        <activity
            android:name=".crash.DefaultErrorActivity"
            android:process=":error_activity" />

        <provider
            android:name=".crash.CaocInitProvider"
            android:authorities="${applicationId}.customactivityoncrashinitprovider"
            android:exported="false"
            android:initOrder="101" />
    </application>

</manifest>
