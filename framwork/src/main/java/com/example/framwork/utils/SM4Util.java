package com.example.framwork.utils;

import android.util.Log;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

import java.security.AlgorithmParameters;
import java.security.Key;
import java.security.SecureRandom;
import java.security.Security;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * @ProjectName:
 * @Package: com.example.framwork.utils
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/7/1
 */
public class SM4Util {

    private static String TAG = SM4Util.class.getSimpleName();
    public static final int DEFAULT_KEY_SIZE = 128;
    public static final String ALGORITHM_NAME = "SM4";

    private static final String ALGORITHM_ECB_PKCS5PADDING = "SM4/ECB/PKCS5Padding";


    static {
        double version = Security.getProvider(BouncyCastleProvider.PROVIDER_NAME).getVersion();
        Log.i("Test", "generateKey1: " + version);
        Security.removeProvider(BouncyCastleProvider.PROVIDER_NAME);
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Log.i("sys", "运行环境没有BouncyCastleProvider");
            Security.addProvider(new BouncyCastleProvider());
        }
        version = Security.getProvider(BouncyCastleProvider.PROVIDER_NAME).getVersion();
        Log.i("Test", "generateKey2: " + version);
    }


    /**
     * 生成密钥
     *
     * @return
     * @explain
     */
    public static String generateKey() {
        // 创建一个安全的随机数生成器
        SecureRandom secureRandom = new SecureRandom();
        // 生成一个 128 位（16 字节）的随机密钥
        byte[] keyBytes = new byte[16];
        secureRandom.nextBytes(keyBytes);
        return Hex.toHexString(keyBytes);
    }


    /**
     * 生成初始化向量iv
     */
    public static AlgorithmParameters generateIV() throws Exception {
        SecureRandom secureRandom = new SecureRandom();
        byte[] iv = new byte[16];
        //随机生成bytes数组
        secureRandom.nextBytes(iv);
        AlgorithmParameters params = AlgorithmParameters.getInstance(ALGORITHM_NAME);
        IvParameterSpec spec = new IvParameterSpec(iv);
        params.init(spec);
        Log.i("Test", "generateKey: iv:" + iv);
        return params;
    }

    /**
     * @param algorithmName 算法名称
     * @param key           密钥
     * @param iv            初始向量
     * @param data          加密/解密数据 Hex.decode()
     * @return 加密后数据
     * @throws Exception
     * @description 加密
     */
    public static byte[] encrypt(String algorithmName, byte[] key, byte[] iv, byte[] data) throws Exception {
        return sm4Core(algorithmName, Cipher.ENCRYPT_MODE, key, iv, data);
    }

    /**
     * @param algorithmName 算法名称
     * @param key           密钥
     * @param iv            初始向量
     * @param data          加密数据
     * @return 解密结果
     * @throws Exception
     * @description 解密
     */
    public static byte[] decrypt(String algorithmName, byte[] key, byte[] iv, byte[] data) throws Exception {
        return sm4Core(algorithmName, Cipher.DECRYPT_MODE, key, iv, data);
    }

    /**
     * 加密/解密
     *
     * @param algorithmName 算法名称
     * @param type          加密/解密
     * @param key           密钥
     * @param iv            初始向量
     * @param data          加密/解密数据
     * @return 结果
     * @throws Exception
     */
    private static byte[] sm4Core(String algorithmName, int type, byte[] key, byte[] iv, byte[] data)
            throws Exception {
        //指定加密算法
        Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
        Key sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);// 初始化加密器，设置加密模式和初始向量
        //ecb不需要设置初始向量
        if (algorithmName.contains("/ECB/")) {
            cipher.init(type, sm4Key);
        } else {
            // 创建初始向量参数
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            //// 初始化加密器，设置加密模式和初始向量
            cipher.init(type, sm4Key, ivParameterSpec);
        }
        return cipher.doFinal(data);
    }

//    public static String encryptSM4()
//    {
//        try {
//            ECBEncodeByte = SM4Util.encrypt("SM4/ECB/" + chooseMode, Hex.decode(createKey), ivBytes, paramStr);
//            cipher = Hex.toHexString(ECBEncodeByte);
//            System.out.println(cipher);
//        } catch (Exception e) {
//            cipher = "加密失败：" + e.getMessage();
//            e.printStackTrace();
//        }

//    }

    /**
     * 加密，SM4-ECB-PKCS5Padding
     *
     * @param data 要加密的明文
     * @param key  密钥16字节，使用Sm4Util.generateKey()生成
     * @return 加密后的密文
     * @throws Exception 加密异常
     */
    public static byte[] encryptEcbPkcs5Padding(byte[] data, byte[] key) throws Exception {
        return sm4(data, key, ALGORITHM_ECB_PKCS5PADDING, null, Cipher.ENCRYPT_MODE);
    }

    /**
     * 解密，SM4-ECB-PKCS5Padding
     *
     * @param data 要解密的密文
     * @param key  密钥16字节，使用Sm4Util.generateKey()生成
     * @return 解密后的明文
     * @throws Exception 解密异常
     */
    public static byte[] decryptEcbPkcs5Padding(byte[] data, byte[] key) throws Exception {
        return sm4(data, key, ALGORITHM_ECB_PKCS5PADDING, null, Cipher.DECRYPT_MODE);
    }

    /**
     * SM4对称加解密
     *
     * @param input   明文（加密模式）或密文（解密模式）
     * @param key     密钥
     * @param sm4mode sm4加密模式
     * @param iv      初始向量(ECB模式下传NULL)
     * @param mode    Cipher.ENCRYPT_MODE - 加密；Cipher.DECRYPT_MODE - 解密
     * @return 密文（加密模式）或明文（解密模式）
     * @throws Exception 加解密异常
     */
    private static byte[] sm4(byte[] input, byte[] key, String sm4mode, byte[] iv, int mode)
            throws Exception {
        IvParameterSpec ivParameterSpec = null;
        if (null != iv) {
            ivParameterSpec = new IvParameterSpec(iv);
        }
        SecretKeySpec sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);
        Cipher cipher = Cipher.getInstance(sm4mode, BouncyCastleProvider.PROVIDER_NAME);
        if (null == ivParameterSpec) {
            cipher.init(mode, sm4Key);
        } else {
            cipher.init(mode, sm4Key, ivParameterSpec);
        }
        return cipher.doFinal(input);
    }

    /**
     * parseByte2HexStr
     */
    public static String parseByte2HexStr(byte buf[]) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }


}
