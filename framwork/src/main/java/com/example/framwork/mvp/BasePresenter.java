package com.example.framwork.mvp;

import android.content.Context;

import com.alibaba.fastjson.JSON;
import com.example.framwork.noHttp.OnRequestListener;
import com.example.framwork.utils.MyLogUtils;
import com.yanzhenjie.nohttp.RequestMethod;

import java.util.HashMap;
import java.util.List;


/**
 * Created by wa<PERSON><PERSON><PERSON> on 2016/10/8.
 */

public abstract class BasePresenter<Entity> {
    protected CustomRequest request;
    protected Context context;
    protected HashMap<String, Object> requestInfo;

    public BasePresenter(Context context, Class<Entity> clazz, EntityType type) {
        request = new CustomRequest(clazz, type);
        this.context = context;
    }

    public BasePresenter(Context context) {
        request = new CustomRequest(null, EntityType.ENTITY);
        this.context = context;
    }


    public void postNoToast(String loadtxt, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel(context, RequestMethod.POST, requestInfo, true, true, false, loadtxt, requestListener, false);
    }

    public void post(String loadtxt, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel(context, RequestMethod.POST, requestInfo, true, true, true, loadtxt, requestListener, false);
    }
    public void post(boolean isloading,String loadtxt, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel(context, RequestMethod.POST, requestInfo, true, isloading, true, loadtxt, requestListener, false);
    }

    public void post(boolean isloading,String loadtxt,boolean isShowToast, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel(context, RequestMethod.POST, requestInfo, true, isloading, isShowToast, loadtxt, requestListener, false);
    }


    public void postNoLoad(OnRequestListener<Entity> requestListener, boolean isGetAppId) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel(context, RequestMethod.POST, requestInfo, true, false, true, "", requestListener, isGetAppId);
    }


    public void postQuiet(OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel(context, RequestMethod.POST, requestInfo, false, false, false, "", requestListener, false);
    }
    public void getFromQuite(String loadtxt, OnRequestListener<Entity> requestListener) {
        request.resultModel2(context, RequestMethod.GET, requestInfo, true, true, false, loadtxt, requestListener);
    }
    //未发现使用
    public void postImageRemote(String loadTxt, HashMap<String, Object> imgMap, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo) + JSON.toJSONString(imgMap));
        request.resultPostImageModelRemote(context, imgMap, requestInfo, false, true, true, loadTxt, requestListener);
    }

    //fastupload上传图片不需要加密
    public void postBitmap(String loadTxt, HashMap<String, Object> imgMap, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo) + JSON.toJSONString(imgMap));
        request.resultPostBitmapModel(context, imgMap, requestInfo, false, true, true, loadTxt, requestListener);
    }

    public void postBitmap(boolean isLoading, HashMap<String, Object> imgMap, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo) + JSON.toJSONString(imgMap));
        request.resultPostBitmapModel(context, imgMap, requestInfo, false, isLoading, true, "", requestListener);
    }

    public void postFrom(String loadtxt, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel2(context, RequestMethod.POST, requestInfo, true, true, true, loadtxt, requestListener);
    }
    public void postNotShowToast(String loadtxt, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel2(context, RequestMethod.POST, requestInfo, true, false, false, loadtxt, requestListener);
    }
    public void postShowToast(String loadtxt, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel2(context, RequestMethod.POST, requestInfo, true, true, true, loadtxt, requestListener);
    }

    public void postFromNoLoad(OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel2(context, RequestMethod.POST, requestInfo, true, false, true, "", requestListener);
    }


    /**
     * 安静请求
     *
     * @param requestListener
     */
    public void postFromQuiet(OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModel2(context, RequestMethod.POST, requestInfo, false, false, false, "", requestListener);
    }

    public void get(String url,String token, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", url);
        request.resultGet(context, url,token, requestListener);
    }
    public void getFrom(String loadtxt, OnRequestListener<Entity> requestListener) {
        request.resultModel2(context, RequestMethod.GET, requestInfo, true, true, true, loadtxt, requestListener);
    }
    public void getFrom(String loadtxt,boolean isshowToast ,OnRequestListener<Entity> requestListener) {
        request.resultModel2(context, RequestMethod.GET, requestInfo, true, true, isshowToast, loadtxt, requestListener);
    }
    public void postRemote(String loadtxt, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo));
        request.resultModelRemote(context, RequestMethod.POST, requestInfo, true, true, true, loadtxt, requestListener);
    }
    public void postImageNoLoad(String loadTxt, HashMap<String, Object> imgMap, OnRequestListener<Entity> requestListener) {
        MyLogUtils.i("BasePresenter", JSON.toJSONString(requestInfo) + JSON.toJSONString(imgMap));
        request.resultPostImageModel(context, imgMap, requestInfo, false, false, true, loadTxt, requestListener);
    }
}
