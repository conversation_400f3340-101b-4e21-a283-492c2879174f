package com.example.framwork.mvp;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.android.arouter.launcher.ARouter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.example.framwork.base.QuickActivity;
import com.example.framwork.baseapp.BaseAppConfig;
import com.example.framwork.noHttp.Bean.AppIdAndTokenEntity;
import com.example.framwork.noHttp.Bean.BaseResponseBean;
import com.example.framwork.noHttp.CallServer;
import com.example.framwork.noHttp.FastJsonRequest;
import com.example.framwork.noHttp.HttpCallBack;
import com.example.framwork.noHttp.NetworkConfig;
import com.example.framwork.noHttp.OnRequestListener;
import com.example.framwork.utils.AppInfoUtil;
import com.example.framwork.utils.CommonUtil;
import com.example.framwork.utils.DLog;
import com.example.framwork.utils.DateUtil;
import com.example.framwork.utils.EncryptUtil;
import com.example.framwork.utils.MyLogUtils;
import com.example.framwork.utils.SM2Util;
import com.google.gson.Gson;
import com.yanzhenjie.nohttp.BitmapBinary;
import com.yanzhenjie.nohttp.FileBinary;
import com.yanzhenjie.nohttp.RequestMethod;
import com.yanzhenjie.nohttp.rest.Request;
import com.yanzhenjie.nohttp.rest.StringRequest;


import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import es.dmoral.toasty.Toasty;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;


/**
 * Created by wanjingyu on 2016/10/8.
 */

public class CustomRequest<Entity> {
    public final String TAG = "http";
    private static NetworkConfig sConfig;
    private String methodName;
    private Class<Entity> clazz;
    private EntityType type;

    public CustomRequest(Class<Entity> clazz, EntityType type) {
        this.clazz = clazz;
        this.type = type;

    }

    public static void setConfig(NetworkConfig config) {
        if (sConfig == null) {
            synchronized (NetworkConfig.class) {
                if (sConfig == null)
                    sConfig = config == null ? NetworkConfig.newBuilder().build() : config;
                else DLog.w("Kalle", "Only allowed to configure once.");
            }
        }
    }

    public static NetworkConfig getConfig() {
        setConfig(null);
        return sConfig;
    }

    private String handlerStrToHashMap(String strURl) {
        Map<String, String> resultMap = new HashMap<>();
        if (strURl != null) {
            String[] pairs = strURl.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length == 2) {
                    resultMap.put(keyValue[0], keyValue[1]);
                }
            }
        }
        return new Gson().toJson(resultMap);

    }

    public void resultGet(Context context, String url, String token, OnRequestListener<String> requestListener) {
        try {
            int index = url.lastIndexOf("/");
            String paramStr = "";
            if (!TextUtils.isEmpty(url) && url.contains("?")) {
                this.methodName = url.substring(index + 1, url.lastIndexOf("?"));
                paramStr = url.substring(url.lastIndexOf("?") + 1, url.length());
            }

            String sm4Key = CommonUtil.getSM4SCRET();
            if (!TextUtils.isEmpty(paramStr)) {
                String jsonParam = handlerStrToHashMap(paramStr);
                String sm2Str = SM2Util.encrypt(jsonParam, sm4Key);

                if (CommonUtil.isDebug) {
                    Log.d("http", "加密前get url==" + url);
                }
                url = url.substring(0, url.lastIndexOf("?") + 1).concat("encryptStr=").concat(sm2Str);
                if (CommonUtil.isDebug) {
                    Log.d("http", "加密后get url==" + url);
                }
            }
//            this.methodName = url.split("cgz")[1];
            Request<String> request = new StringRequest(url,
                    RequestMethod.GET);
            request.addHeader("token", token);
            request.addHeader("Content-Type", " ");
            request.addHeader("encrypt", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, sm4Key));
            request.addHeader("secret", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, CommonUtil.secret));
            request.addHeader("clientId", CommonUtil.clientId);
            if(CommonUtil.isDebug){
                Log.e("http","headers==="+request.getHeaders().toJSONString());
            }
            //TODO
//            request.addHeader("token", "cd0e5c54-7cd2-4ada-85a0-e892754930b4");
            executeGet(context, request, requestListener);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void resultModel(Context context, RequestMethod requestMethod, HashMap<String, Object> info, boolean isDLogin, boolean isShowLoading, boolean isShowToast, String loadingText, OnRequestListener<Object> requestListener, boolean isGetAppId) {
        if (info.containsKey("methodName")) {
            this.methodName = info.get("methodName").toString();
            info.remove("methodName");
        }

        Request<String> request;
        String jsonString = JSON.toJSONString(info);
        String sm4Key = CommonUtil.getSM4SCRET();
        if (getConfig().isEncryption()) {
            request = new FastJsonRequest(BaseAppConfig.SERVICE_PATH + (TextUtils.isEmpty(methodName) ? "" : methodName),
                    requestMethod, jsonString);
        } else {
            request = new StringRequest(BaseAppConfig.SERVICE_PATH + (TextUtils.isEmpty(methodName) ? "" : methodName),
                    requestMethod);
            String paramJson = new Gson().toJson(info);
            if (CommonUtil.isDebug) {
                Log.d("http", methodName + "原始入参paramJson==" + paramJson);
            }
            HashMap<String, Object> params = new HashMap<>();
            if (!methodName.contains("appedition/selectNewBy")) {
                String sm2Str = SM2Util.encrypt(paramJson, sm4Key);
                if (CommonUtil.isDebug) {
                    Log.d("http", methodName + "加密后入参sm2Str==" + sm2Str);
                }

                if (!TextUtils.isEmpty(sm2Str)) {
                    params.put("encryptStr", sm2Str);
                }
            } else {
                params = info;
            }

            request.setDefineRequestBody(JSON.toJSONString(params), "application/json;charset=UTF-8");
        }
        if (!methodName.contains("cgz/user/register"))
            request.addHeader("token", TextUtils.isEmpty((String) info.get("token")) ? CommonUtil.TOKEN : info.get("token").toString());
        //TODO XHF
//        request.addHeader("token","5c17c896-f393-43ce-b030-aacecfd7db70");
        request.addHeader("source", "app-shenggongxieMini");
        request.addHeader("encrypt", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, sm4Key));
        request.addHeader("secret", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, CommonUtil.secret));
        request.addHeader("clientId", CommonUtil.clientId);
        request.addHeader("version", AppInfoUtil.getAppVersionName(context));
        request.addHeader("packageName", AppInfoUtil.getCurrentPkgName(context));
        if(CommonUtil.isDebug){
            Log.e("http","headers==="+request.getHeaders().toJSONString());
        }

        execute(context, request, isDLogin, isShowLoading, isShowToast, loadingText, requestListener, isGetAppId);
    }


    public void resultModel2(Context context, RequestMethod requestMethod, HashMap<String, Object> info, boolean isDLogin, boolean isShowLoading, boolean isShowToast, String loadingText, OnRequestListener<Object> requestListener) {
        if (!info.containsKey("methodName")) {
            DLog.e("设置方法");
            return;
        }
        this.methodName = info.get("methodName").toString();
        info.remove("methodName");
        Request<String> request;
        String jsonString = JSON.toJSONString(info);
        String sm4Key = CommonUtil.getSM4SCRET();
        if (getConfig().isEncryption()) {
            request = new FastJsonRequest(BaseAppConfig.SERVICE_PATH + methodName,
                    requestMethod, jsonString);
            DLog.d("http", "isEncryption: url==" + BaseAppConfig.SERVICE_PATH + methodName);

        } else {
            request = new StringRequest(BaseAppConfig.SERVICE_PATH + methodName,
                    requestMethod);
            DLog.d("http", "noEncryption: url==" + BaseAppConfig.SERVICE_PATH + methodName);
            String paramJson = new Gson().toJson(info);
            if (CommonUtil.isDebug)
                Log.d("http", methodName + "原始入参paramJson==" + paramJson);

            String sm2Str = SM2Util.encrypt(paramJson, sm4Key);
            if (CommonUtil.isDebug) {
                Log.d("http", methodName + "加密后入参sm2Str==" + sm2Str);
            }
//        request.removeAll();
            HashMap<String, Object> params = new HashMap<>();
            if (!TextUtils.isEmpty(sm2Str)) {
                params.put("encryptStr", sm2Str);
                request.add(params);
            }
//            request.set("encryptStr", sm2Str);
//            request.setDefineRequestBody(JSON.toJSONString(params), "application/x-www-form-urlencoded");
        }
        request.addHeader("token", TextUtils.isEmpty((String) info.get("token")) ? CommonUtil.TOKEN : info.get("token").toString());
        request.addHeader("Content-Type", "application/x-www-form-urlencoded");
        request.addHeader("encrypt", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, sm4Key));
        request.addHeader("secret", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, CommonUtil.secret));
        request.addHeader("clientId", CommonUtil.clientId);
        request.addHeader("version", AppInfoUtil.getAppVersionName(context));
        request.addHeader("packageName", AppInfoUtil.getCurrentPkgName(context));
        if(CommonUtil.isDebug){
            Log.e("http","headers==="+request.getHeaders().toJSONString());
        }
        execute(context, request, isDLogin, isShowLoading, isShowToast, loadingText, requestListener, false);
    }

    public void resultModelRemote(Context context, RequestMethod requestMethod, HashMap<String, Object> info, boolean isDLogin, boolean isShowLoading, boolean isShowToast, String loadingText, OnRequestListener<Object> requestListener) {
        if (!info.containsKey("methodName")) {
            DLog.e("设置方法");
            return;
        }
        this.methodName = info.get("methodName").toString();
        info.remove("methodName");
        Request<String> request;
        String jsonString = JSON.toJSONString(info);
        String sm4Key = CommonUtil.getSM4SCRET();
        if (getConfig().isEncryption()) {
            request = new FastJsonRequest(BaseAppConfig.SERVICE_PATH + methodName,
                    requestMethod, jsonString);

        } else {
            request = new StringRequest(BaseAppConfig.SERVICE_PATH + methodName,
                    requestMethod);
            String paramJson = new Gson().toJson(info);
            if (CommonUtil.isDebug)
                Log.d("http", methodName + "原始入参paramJson==" + paramJson);

            String sm2Str = SM2Util.encrypt(paramJson, sm4Key);
            if (CommonUtil.isDebug) {
                Log.d("http", methodName + "加密后入参sm2Str==" + sm2Str);
            }
            HashMap<String, Object> params = new HashMap<>();
            if (!TextUtils.isEmpty(sm2Str)) {
                params.put("encryptStr", sm2Str);
                request.add(params);
            }
        }
        request.addHeader("token", TextUtils.isEmpty((String) info.get("token")) ? CommonUtil.TOKEN : info.get("token").toString());
        request.addHeader("source", "app-shenggongxieMini");
        request.addHeader("Content-Type", "application/x-www-form-urlencoded");
        request.addHeader("encrypt", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, sm4Key));
        request.addHeader("secret", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, CommonUtil.secret));
        request.addHeader("clientId", CommonUtil.clientId);
        request.addHeader("version", AppInfoUtil.getAppVersionName(context));
        request.addHeader("packageName", AppInfoUtil.getCurrentPkgName(context));
        if(CommonUtil.isDebug){
            Log.e("http"," "+request.getHeaders().toJSONString());
        }
        executeRemote(context, request, isDLogin, isShowLoading, isShowToast, loadingText, requestListener);
    }

    public void executeRemote(Context context, Request request, boolean isDLogin, boolean isShowLoading, boolean isShowToast, String loadingText, OnRequestListener<Object> requestListener) {
        if (context instanceof QuickActivity) {
            if (isShowLoading) {
                QuickActivity b = (QuickActivity) context;
                if (TextUtils.isEmpty(loadingText))
                    b.showProgress();
                else b.showProgress(loadingText);
            }
        }
        addRemote(context, isDLogin, isShowLoading, isShowToast, request, requestListener);
    }

    //formdata 类型（不加密）
    private void addRemote(final Context context, final boolean isDLogin, final boolean isShowLoading, final boolean isShowToast, final Request<String> request, final OnRequestListener<Object> requestListener) {

        CallServer.getRequestInstance().add(context, request, new HttpCallBack<String>() {
            @Override
            public void onSucceed(int what, String response) {
                if (CommonUtil.isDebug)
                    Log.d(TAG, methodName + "原始出参：" + response);
                if (!TextUtils.isEmpty(response) && !response.contains("{")) {
                    response = SM2Util.decryptSM2(CommonUtil.ENCRY_PRIVATE_SCRET, "04" + response);
                    if (CommonUtil.isDebug)
                        Log.d(TAG, request.url() + "解密后原始出参：" + response);
                }
                requestListener.requestSuccess(response);
            }

            @Override
            public void onFailed(int what, Exception exception, String error) {
                if (CommonUtil.isDebug) {
                    Log.d(TAG, methodName + "原始出参：" + error + what);
                }
                if (isShowToast) {
                    Toasty.error(context, error).show();
                }
                requestListener.requestFailed(what, null, exception, error);
            }

            @Override
            public void onFinish() {
                if (context instanceof QuickActivity && isShowLoading) {
                    QuickActivity activity = (QuickActivity) context;
                    if (activity.isFinishing())
                        return;
                    activity.hideProgress();
                }
                requestListener.requestFinish();
            }
        }, 0);
    }


    public void resultPostImageModel(Context context, HashMap<String, String> imgInfo, HashMap<String, Object> info, boolean isDLogin, boolean isShowLoading, boolean isShowToast, String loadingText, OnRequestListener<Object> requestListener) {
        if (!info.containsKey("methodName")) {
            DLog.e("设置方法");
            return;
        }
        this.methodName = info.get("methodName").toString();
        info.remove("methodName");
        Request<String> request;
        String jsonString = JSON.toJSONString(info);
        String sm4Key = CommonUtil.getSM4SCRET();
        if (getConfig().isEncryption()) {
            request = new FastJsonRequest(BaseAppConfig.SERVICE_PATH + methodName,
                    RequestMethod.POST, jsonString);
        } else {
            request = new StringRequest(BaseAppConfig.SERVICE_PATH + methodName,
                    RequestMethod.POST);
            request.add(info);
        }
        if (imgInfo.size() != 0) {
            StringBuilder imageDLog = new StringBuilder();

            for (Map.Entry<String, String> entry : imgInfo.entrySet()) {
                imageDLog.append("需要上传图片信息：").append(entry.getKey()).append(":").append(entry.getValue());
                if (entry.getValue() != null && !TextUtils.isEmpty(entry.getValue()) && !entry.getValue().startsWith("http")) {
//                    RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), new File(entry.getValue()));
//                    MultipartBody.Part body = MultipartBody.Part.createFormData("file", new File(entry.getValue()).getName(), requestFile);
                    request.add(entry.getKey(), new FileBinary(new File(entry.getValue()), new File(entry.getValue()).getName(), "image/jpeg"));
                }
            }
            DLog.d(TAG, imageDLog.toString());
        }
        request.addHeader("token", TextUtils.isEmpty((String) info.get("token")) ? CommonUtil.TOKEN : info.get("token").toString());
        request.addHeader("Content-Type", "multipart/form-data");
        request.addHeader("source", "app-shenggongxieMini");
        request.addHeader("encrypt", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, sm4Key));
        request.addHeader("secret", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, CommonUtil.secret));
        request.addHeader("clientId", CommonUtil.clientId);
        request.addHeader("version", AppInfoUtil.getAppVersionName(context));
        request.addHeader("packageName", AppInfoUtil.getCurrentPkgName(context));
        execute(context, request, isDLogin, isShowLoading, isShowToast, loadingText, requestListener, false);
    }

    public void resultPostImageModelRemote(Context context, HashMap<String, String> imgInfo, HashMap<String, Object> info, boolean isDLogin, boolean isShowLoading, boolean isShowToast, String loadingText, OnRequestListener<Object> requestListener) {
        if (!info.containsKey("methodName")) {
            DLog.e("设置方法");
            return;
        }
        this.methodName = info.get("methodName").toString();
        info.remove("methodName");
        Request<String> request;
        String jsonString = JSON.toJSONString(info);
        String sm4Key = CommonUtil.getSM4SCRET();
        if (getConfig().isEncryption()) {
            request = new FastJsonRequest(BaseAppConfig.SERVICE_PATH + methodName,
                    RequestMethod.POST, jsonString);
        } else {
            request = new StringRequest(BaseAppConfig.SERVICE_PATH + methodName,
                    RequestMethod.POST);
            request.add(info);
        }
        if (imgInfo.size() != 0) {
            StringBuilder imageDLog = new StringBuilder();
            for (Map.Entry<String, String> entry : imgInfo.entrySet()) {
                imageDLog.append("需要上传图片信息：").append(entry.getKey()).append(":").append(entry.getValue());
                if (entry.getValue() != null && !TextUtils.isEmpty(entry.getValue()) && !entry.getValue().startsWith("http")) {
                    request.add(entry.getKey(), new FileBinary(new File(entry.getValue()), new File(entry.getValue()).getName(), "image/jpeg"));
                }
            }
            DLog.d(TAG, imageDLog.toString());
        }
        request.addHeader("token", TextUtils.isEmpty((String) info.get("token")) ? CommonUtil.TOKEN : info.get("token").toString());
        request.addHeader("source", "app-shenggongxieMini");
        request.addHeader("Content-Type", "multipart/form-data");
        request.addHeader("encrypt", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, sm4Key));
        request.addHeader("secret", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, CommonUtil.secret));
        request.addHeader("clientId", CommonUtil.clientId);
        request.addHeader("version", AppInfoUtil.getAppVersionName(context));
        request.addHeader("packageName", AppInfoUtil.getCurrentPkgName(context));
        executeRemote(context, request, isDLogin, isShowLoading, isShowToast, loadingText, requestListener);
    }

    public void resultPostBitmapModel(Context context, HashMap<String, Bitmap> imgInfo, HashMap<String, Object> info, boolean isDLogin, boolean isShowLoading, boolean isShowToast, String loadingText, OnRequestListener<Object> requestListener) {
        if (!info.containsKey("methodName")) {
            DLog.e("设置方法");
            return;
        }
        this.methodName = info.get("methodName").toString();
        info.remove("methodName");
        Request<String> request;
        String jsonString = JSON.toJSONString(info);
        String sm4Key = CommonUtil.getSM4SCRET();
        request = new StringRequest(BaseAppConfig.SERVICE_PATH + methodName,
                RequestMethod.POST);
        request.add(info);
        if (imgInfo.size() != 0) {
            StringBuilder imageDLog = new StringBuilder();

            for (Map.Entry<String, Bitmap> entry : imgInfo.entrySet()) {
                if (entry.getValue() != null) {
//                    request.add(entry.getKey(), new FileBinary(bitmapToFile(context,entry.getValue()))) ;
                    request.add(entry.getKey(), new BitmapBinary(entry.getValue(), DateUtil.getInstance().getCurTime() + ".jpg"));
//                    try {
//                        request.setDefineRequestBody(new FileInputStream(bitmapToFile(context, entry.getValue())), "multipart/form-data");
//                    } catch (FileNotFoundException e) {
//                        e.printStackTrace();
//                    }
                }
            }
            DLog.d(TAG, imageDLog.toString());
        }
//
        request.addHeader("token", TextUtils.isEmpty((String) info.get("token")) ? CommonUtil.TOKEN : info.get("token").toString());
        request.addHeader("source", "app-shenggongxieMini");
//        request.addHeader("Content-Type", "multipart/form-data;boundary----WebKitFormBoundary7MA4YWxkTrZu0gW");
        request.addHeader("encrypt", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, sm4Key));
        request.addHeader("secret", SM2Util.encryptSM2(CommonUtil.SM2_PUBLIC_SCRET, CommonUtil.secret));
        request.addHeader("clientId", CommonUtil.clientId);
        request.addHeader("version", AppInfoUtil.getAppVersionName(context));
        request.addHeader("packageName", AppInfoUtil.getCurrentPkgName(context));
        request.setMultipartFormEnable(true);
        execute(context, request, isDLogin, isShowLoading, isShowToast, loadingText, requestListener, false);
    }

    public static File bitmapToFile(Context context, Bitmap bitmap) {
        File file = new File(context.getFilesDir().getAbsolutePath() + File.separator + DateUtil.getInstance().getCurTime() + ".jpg");
        try {
            file.createNewFile();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.PNG, 0, bos);
            byte[] bitmapData = bos.toByteArray();
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(bitmapData);
            fos.flush();
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }

    public void executeGet(Context context, Request<String> request, OnRequestListener<String> requestListener) {
        addGetRequest(context, request, requestListener);
    }

    public void execute(Context context, Request request, boolean isDLogin, boolean isShowLoading, boolean isShowToast, String loadingText, OnRequestListener<Object> requestListener, boolean isGetAppId) {
        if (context instanceof QuickActivity) {
            if (isShowLoading) {
                QuickActivity b = (QuickActivity) context;
                if (TextUtils.isEmpty(loadingText))
                    b.showProgress();
                else b.showProgress(loadingText);
            }
        }
        addRequest(context, isDLogin, isShowLoading, isShowToast, request, requestListener, isGetAppId);
    }

    private void addRequest(final Context context, final boolean isDLogin, final boolean isShowLoading, final boolean isShowToast, final Request<String> request, final OnRequestListener<Object> requestListener, final boolean isGetAppId) {
        CallServer.getRequestInstance().add(context, request, new HttpCallBack<String>() {
            @Override
            public void onSucceed(int what, String response) {
                if (CommonUtil.isDebug)
                    Log.d(TAG, request.url() + "header==" + request.getHeaders().toJSONString() + "原始出参：" + response);
                if (!TextUtils.isEmpty(response) && !response.contains("{")) {
                    response = SM2Util.decryptSM2(CommonUtil.ENCRY_PRIVATE_SCRET, "04" + response);
                    if (CommonUtil.isDebug)
                        Log.d(TAG, request.url() + "解密后原始出参：" + response);
                }

                if (!TextUtils.isEmpty(response.trim())) {
                    try {
                        if (methodName.equals("/notarization/cgz/notaryorder/doSetSignNameJSCA") ||
                                methodName.equals("/enforcement-api/block-order/getList") ||
//                                methodName.equals("/pay-api/zr/zf/queyOrderInfo") ||
//                                methodName.equals("/notarization/cgz/notaryorder/placeOrder") ||
                                methodName.equals("/easy-enforcement-api/easy-block-order/getList") ||
                                methodName.equals("/easy-enforcement-api/easy-block-order/getPartyDetails") ||
                                methodName.equals("/enforcement-api/block-order/launchNotarization") ||
                                methodName.equals("/easy-enforcement-api/easy-block-order/launchNotarization") ||
                                methodName.equals("/enforcement-api/block-order/getPartyDetails") ||
                                methodName.equals("/user-api/sys/dict/selectAllTop") ||
                                methodName.equals("/user-api/sys/dict/selectPage") ||
                                methodName.equals("/enforcement-api/block-order/padOverGetDetails") ||
                                methodName.equals("/notarization/cgz/camera/queryMacBinding") ||
                                methodName.equals("/notarization/cgz/notaryPublic/listNotaryStatus") ||
                                methodName.equals("/notarization/sys/ordervideo/app/videoNotarizationWaitHandleRemind") ||
                                methodName.equals("/easy-enforcement-api/easy-block-order/padOverGetDetails")) {
                            if (getConfig().isEncryption()) {
                                String responseS = EncryptUtil.getInstance().decodeValue(response);
                                DLog.d(TAG, request.url() + "解密出参：" + responseS);
                                requestListener.requestSuccess(responseS);
                            } else {
                                BaseResponseBean bean = (BaseResponseBean) BaseResponseBean.parseObj(response, getConfig().getReponseC());
                                if (bean.getCode() == 401) {
                                    Toasty.error(context, "Token已过期,请重新登录").show();
//                                AppManager.getAppManager().finishAllActivity(MainActivity.class);
                                } else {
                                    requestListener.requestSuccess(response);
                                }
                            }
                        } else if (methodName.contains("office/config/info")) {
                            requestListener.requestSuccess(response);
                        } else if (!isGetAppId) {
                            BaseResponseBean bean;
                            if (getConfig().isEncryption()) {
                                String responseS = EncryptUtil.getInstance().decodeValue(response);
                                DLog.d(TAG, request.url() + "解密出参：" + responseS);
                                bean = (BaseResponseBean) BaseResponseBean.parseObj(responseS, getConfig().getReponseC());
                            } else {
                                bean = (BaseResponseBean) BaseResponseBean.parseObj(response, getConfig().getReponseC());
                            }
                            if (bean.isSuccess()) {
                                if (clazz == null) {
                                    requestListener.requestSuccess(bean);
                                } else {
                                    if (type == EntityType.ENTITY) {
                                        requestListener.requestSuccess(bean.parseObject(clazz));
                                    } else if (type == EntityType.LIST) {
                                        requestListener.requestSuccess(bean.parseList(clazz));
                                    }
                                }
                            } else {
                                if (isShowToast) {
                                    Toasty.error(context, bean.getMessage() == null ? "获取数据失败" : bean.getMessage()).show();
                                }
                                requestListener.requestFailed(bean.getCode(), bean, null, bean.getMessage() == null ? "获取数据失败" : bean.getMessage());
                            }
                        } else {
                            AppIdAndTokenEntity bean;
                            JSONObject json = JSONObject.parseObject(response);
                            bean = JSON.toJavaObject(json, AppIdAndTokenEntity.class);
                            if (bean.code == 200) {
                                requestListener.requestSuccess(bean);
                            }
                        }
                    } catch (Exception e1) {
                        e1.printStackTrace();
                        onFailed(1, null, "解析数据异常");
                    }
                }
            }

            @Override
            public void onFailed(int what, Exception exception, String error) {
                if (CommonUtil.isDebug)
                    Log.d(TAG, request.url() + "原始出参：" + error + what);
                if (isShowToast && what != 401) {
                    Toasty.error(context, error).show();
                }
                if (what == 401) {
                    Toasty.error(context, "会话已失效，即将跳转到首页").show();
                    ARouter.getInstance().build("/app/mainactivity").navigation();
                } else {
                    requestListener.requestFailed(what, null, exception, error);
                }

            }

            @Override
            public void onFinish() {
                if (context instanceof QuickActivity && isShowLoading) {
                    QuickActivity activity = (QuickActivity) context;
                    if (activity.isFinishing())
                        return;
                    activity.hideProgress();
                }
                requestListener.requestFinish();
            }
        }, 0);
    }

    private void addGetRequest(final Context context, final Request<String> request, final OnRequestListener<String> requestListener) {
        CallServer.getRequestInstance().add(context, request, new HttpCallBack<String>() {
            @Override
            public void onSucceed(int what, String response) {
                if (CommonUtil.isDebug)
                    Log.d(TAG, request.url() + "原始出参：" + response);
                if (!request.url().contains("accessPublicKey") && !TextUtils.isEmpty(response) && !response.contains("{")) {
                    response = SM2Util.decryptSM2(CommonUtil.ENCRY_PRIVATE_SCRET, "04" + response);
                    if (CommonUtil.isDebug)
                        Log.d(TAG, request.url() + "解密后原始出参：" + response);
                }

                if (!TextUtils.isEmpty(response.trim())) {
                    requestListener.requestSuccess(response);
                }
            }

            @Override
            public void onFailed(int what, Exception exception, String error) {
                if (CommonUtil.isDebug)
                    Log.d(TAG, request.url() + "原始出参：" + error + what);
                if (what == 401) {
                    Toasty.error(context, "会话已失效，即将跳转到首页").show();
                    ARouter.getInstance().build("/app/mainactivity").navigation();
                } else {
                    requestListener.requestFailed(what, null, exception, error);
                }

            }

            @Override
            public void onFinish() {
                if (context instanceof QuickActivity) {
                    QuickActivity activity = (QuickActivity) context;
                    if (activity.isFinishing())
                        return;
                    activity.hideProgress();
                }
                requestListener.requestFinish();
            }
        }, 0);
    }


}
