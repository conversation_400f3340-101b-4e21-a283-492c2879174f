<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.example.framwork.widget.limitededittext.LimitedEditText
        android:id="@+id/et_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:gravity="left|top"
        android:hint=""
        android:inputType="text|textMultiLine"
        android:minLines="5"
        android:textColor="@color/gray_98"
        android:textSize="14sp"
        app:formatter="%s/%s"
        app:limitCount="500"
        app:wordCountTextView="@+id/countTextView" />


    <TextView
        android:id="@+id/countTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_below="@+id/et_content"
        android:layout_marginTop="10dp"
        android:textColor="@color/gray_98"
        android:textSize="13sp" />
</RelativeLayout>