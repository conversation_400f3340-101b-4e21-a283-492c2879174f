<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_new_tag"
            android:layout_width="35dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="5dp"
            android:background="@drawable/border_new_tag"
            android:gravity="center"
            android:minHeight="@dimen/space_15"
            android:text="最新"
            android:textColor="@color/color_red"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/tv_new_one"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/yellow_ca7b42"
            android:textSize="13sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_new_tag_two"
            android:layout_width="35dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="5dp"
            android:background="@drawable/border_new_tag"
            android:gravity="center"
            android:minHeight="@dimen/space_15"
            android:text="最新"
            android:textColor="@color/color_red"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/tv_new_two"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/yellow_ca7b42"
            android:textSize="13sp" />
    </LinearLayout>

</LinearLayout>