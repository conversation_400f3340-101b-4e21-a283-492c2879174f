<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">

    <ImageView
        android:id="@+id/iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/tv_jump"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_margin="30dp"
        android:background="@color/white"
        android:paddingBottom="8dp"
        android:paddingLeft="30dp"
        android:paddingRight="30dp"
        android:paddingTop="8dp"
        android:textColor="@color/gray_98"
        android:textSize="15sp" />

</RelativeLayout>