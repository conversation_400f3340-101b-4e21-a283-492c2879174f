<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:orientation="horizontal"
    android:paddingBottom="15dp"
    android:paddingTop="15dp">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <Button
            android:id="@+id/photo_btn"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_centerVertical="true"
            android:background="@drawable/photo_drawable_btn"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/photo_btn"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp"
            android:text="相册"
            android:textColor="#ffffff"
            android:textSize="18sp"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <Button
            android:id="@+id/btn_flash"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_centerInParent="true"
            android:background="@drawable/flash_drawable_btn"/>

        <TextView
            android:id="@+id/tv_flash"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/btn_flash"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp"
            android:text="开灯"
            android:visibility="gone"
            android:textColor="#ffffff"
            android:textSize="18sp"/>
    </LinearLayout>

</LinearLayout>