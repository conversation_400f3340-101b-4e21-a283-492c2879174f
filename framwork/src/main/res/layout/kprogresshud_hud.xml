<?xml version="1.0" encoding="utf-8"?>
<com.example.framwork.widget.kprogresshud.BackgroundLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/background"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="16dp">

    <FrameLayout
        android:id="@+id/container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:layout_marginTop="8dp"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/details_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:textSize="13sp"
        android:visibility="gone" />
</com.example.framwork.widget.kprogresshud.BackgroundLayout>