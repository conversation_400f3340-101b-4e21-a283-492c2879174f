<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#555555"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv"
        android:layout_width="wrap_content"
        android:layout_height="35dp"
        android:gravity="center_vertical"
        android:paddingLeft="25dp"
        android:singleLine="true"
        android:textColor="#ffffff"/>

</LinearLayout>