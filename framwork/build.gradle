apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    //解决library中BuildConfig.DEBUG始终为false的问题
    publishNonDefault true
    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [moduleName: project.getName(), AROUTER_MODULE_NAME: project.getName()]
            }
        }
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    ndkVersion "20.0.5594570"
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}
allprojects {
    repositories {
        jcenter()
    }
}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api rootProject.ext.dependencies["appcompatv7"]
    api rootProject.ext.dependencies["design"]
    api rootProject.ext.dependencies["transition"]
    api rootProject.ext.dependencies["exifinterface"]
    api 'androidx.constraintlayout:constraintlayout:1.1.3'
    //-------一下自己添加
    api 'com.flyco.dialog:FlycoDialog_Lib:1.3.2@aar'
    api 'com.nineoldandroids:library:2.4.0'
    //轮播广告
    api 'com.flyco.banner:FlycoBanner_Lib:2.0.2@aar'
    //网络请求
    api 'com.yanzhenjie.nohttp:nohttp:1.1.11'
    api 'com.yanzhenjie.nohttp:okhttp:1.1.11'
    api 'com.alibaba:fastjson:1.1.70.android'
    api 'org.greenrobot:eventbus:3.2.0'
    //android  时间工具类
    api 'net.danlew:android.joda:2.9.9'
    //pop窗口
    api 'com.github.zyyoona7:EasyPopup:1.1.2'
    //相册选择
    api 'com.github.LuckSiege.PictureSelector:picture_library:v2.5.8'
    api 'jp.wasabeef:glide-transformations:3.3.0'
    api 'com.google.zxing:core:3.3.0'
    api 'com.yanzhenjie:permission:2.0.3'
    //刷新和加载更多
    api 'com.scwang.smartrefresh:SmartRefreshLayout:1.1.0-alpha-27'
    implementation 'com.jakewharton:butterknife:10.2.1'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.1'
    api 'com.github.GrenderG:Toasty:1.3.0'
    api 'com.just.agentweb:agentweb:4.1.3'
    api 'xyz.zpayh:hdimageview:2.0.0'
    api 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.2'
    api 'me.grantland:autofittextview:0.2.+'
    api 'de.hdodenhof:circleimageview:2.2.0'
    api 'com.contrarywind:Android-PickerView:4.1.9'
    //圆角 imageview
    api 'com.makeramen:roundedimageview:2.3.0'
    api 'com.github.bumptech.glide:glide:4.11.0'
    implementation "com.squareup.okhttp3:okhttp:3.6.0"
    annotationProcessor 'com.github.bumptech.glide:compiler:4.11.0'
    api 'com.google.code.gson:gson:2.8.6'
    api 'com.alibaba:arouter-api:1.5.1'
    annotationProcessor  'com.alibaba:arouter-compiler:1.5.1'
//    api 'org.bouncycastle:bcpkix-jdk15on:1.68'
}
