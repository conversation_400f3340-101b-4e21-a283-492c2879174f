ext {
    commonLibVersion = '4.1.1'
    kotlinVersion = '1.4.20'
    javaSourceCompatibility = JavaVersion.VERSION_1_8
    javaTargetCompatibility = JavaVersion.VERSION_1_8
    android = [
            compileSdkVersion: 29,
            buildToolsVersion: "29.0.2",
            minSdkVersion    : 23,
            targetSdkVersion : 29,
            versionCode      : 83,
            versionName      : "1.19.3",
    ]

    dependencies = [
            serenegiant  : 'com.serenegiant:common:4.1.1',
            appcompatv7  : 'androidx.appcompat:appcompat:1.1.0-beta01',
            design       : 'com.google.android.material:material:1.0.0',
            transition   : 'androidx.transition:transition:1.2.0-rc01',
            exifinterface: 'androidx.exifinterface:exifinterface:1.1.0-beta01'
    ]
    COMMON_MODULE_DIR_PATH = projectDir.getPath() + "/framwork/libs"
}


