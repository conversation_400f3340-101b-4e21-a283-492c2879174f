ext {
    commonLibVersion = '4.1.1'
    kotlinVersion = '1.4.20'
    javaSourceCompatibility = JavaVersion.VERSION_1_8
    javaTargetCompatibility = JavaVersion.VERSION_1_8
    android = [
            compileSdkVersion: 30,
//            buildToolsVersion: "29.0.2",
            minSdkVersion    : 23,
            targetSdkVersion : 30,
            versionCode      : 27,
            versionName      : "1.3.2"
    ]

    //version配置
    versions = [
            "support-version": "1.0.0",
            "junit-version"  : "4.12",
    ]
    //support配置
    support = [
            'support-v4'     : "androidx.legacy:legacy-support-v4:${versions["support-version"]}",
            'appcompat-v7'   : "androidx.appcompat:appcompat:${versions["support-version"]}",
            'recyclerview-v7': "androidx.recyclerview:recyclerview:1.2.1",
//            'support-v13'             : "androidx.legacy:legacy-support-v13:${versions["support-version"]}",
//            'support-fragment'        : "androidx.fragment:fragment:${versions["support-version"]}",
            'design'         : "com.google.android.material:material:${versions["support-version"]}",
//            'animated-vector-drawable': "androidx.vectordrawable:vectordrawable-animated:${versions["support-version"]}",
//            'junit'                   : "junit:junit:${versions["junit-version"]}",
    ]

    dependencies = [

            serenegiant                            : 'com.serenegiant:common:4.1.1',
            appcompatv7                            : 'androidx.appcompat:appcompat:1.1.0-beta01',
            design                                 : 'com.google.android.material:material:1.0.0',
            transition                             : 'androidx.transition:transition:1.2.0-rc01',
            exifinterface                          : 'androidx.exifinterface:exifinterface:1.1.0-beta01',
            MVVMHabit                              : 'com.github.goldze:MVVMHabit:4.0.0',
            autosize                               : 'me.jessyan:autosize:1.2.1',
            greendao                               : "org.greenrobot:greendao:3.3.0",
            GreenDaoUpgradeHelper                  : "io.github.yuweiguocn:GreenDaoUpgradeHelper:v2.2.1",
            //rxjava
            "rxjava"                               : "io.reactivex.rxjava2:rxjava:2.2.3",
            "rxandroid"                            : "io.reactivex.rxjava2:rxandroid:2.1.0",
            //rx系列与View生命周期同步
            "rxlifecycle"                          : "com.trello.rxlifecycle2:rxlifecycle:2.2.2",
            "rxlifecycle-components"               : "com.trello.rxlifecycle2:rxlifecycle-components:2.2.2",
            //rxbinding
            "rxbinding"                            : "com.jakewharton.rxbinding2:rxbinding:2.1.1",
            //rx 6.0权限请求
            "rxpermissions"                        : "com.github.tbruyelle:rxpermissions:0.10.2",
            //network
            "okhttp"                               : "com.squareup.okhttp3:okhttp:3.10.0",
            "retrofit"                             : "com.squareup.retrofit2:retrofit:2.4.0",
            "converter-gson"                       : "com.squareup.retrofit2:converter-gson:2.4.0",
            "adapter-rxjava"                       : "com.squareup.retrofit2:adapter-rxjava2:2.4.0",
            //glide图片加载
            "glide"                                : "com.github.bumptech.glide:glide:4.11.0",
            "glide-compiler"                       : "com.github.bumptech.glide:compiler:4.11.0",
            //json解析
            "gson"                                 : "com.google.code.gson:gson:2.8.6",
            //material-dialogs
            "material-dialogs-core"                : "com.afollestad.material-dialogs:core:0.9.6.0",
            "material-dialogs-commons"             : "com.afollestad.material-dialogs:commons:0.9.6.0",
            //recyclerview的databinding套装
            "bindingcollectionadapter"             : "me.tatarka.bindingcollectionadapter2:bindingcollectionadapter:4.0.0",
            "bindingcollectionadapter-recyclerview": "me.tatarka.bindingcollectionadapter2:bindingcollectionadapter-recyclerview:4.0.0",
            "bindingcollectionadapter-viewpager2"  : "me.tatarka.bindingcollectionadapter2:bindingcollectionadapter-viewpager2:4.0.0",
            //Google AAC
            "lifecycle-extensions"                 : "androidx.lifecycle:lifecycle-extensions:2.0.0",
            "lifecycle-compiler"                   : "androidx.lifecycle:lifecycle-compiler:2.0.0",
            //MVVMHabit
            "MVVMHabit"                            : "com.github.goldze:MVVMHabit:4.0.0",
            "Toasty"                               : "com.github.GrenderG:Toasty:1.3.0",
            "pickview"                             : "com.contrarywind:Android-PickerView:4.1.4",
            "pdfview"                              : "com.github.barteksc:android-pdf-viewer:2.8.2",
            "fastjson"                             : "com.alibaba:fastjson:1.1.70.android",
            "titlebar"                             : "com.hjq:titlebar:5.0",
            "serenegiant"                          : 'com.serenegiant:common:4.1.1',
            "trtc"                                 : 'com.tencent.liteav:LiteAVSDK_TRTC:11.4.0.13270',
            "arouter"                              : "com.alibaba:arouter-api:1.5.0",
            "arouter-compiler"                     : "com.alibaba:arouter-compiler:1.2.2",
            "sentry"                               : "io.sentry:sentry-android:6.13.1",
            "xxpermission"                         : 'com.hjq:xxpermissions:6.2',
            "Toasty"                               : 'com.github.GrenderG:Toasty:1.3.0',
            "rtc"                                  : "io.agora.rtc:full-sdk:3.3.2",
            "mqtt"                                 : 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.0',
            "mqttService"                          : 'org.eclipse.paho:org.eclipse.paho.android.service:1.1.1',
            "pdfView"                              : 'com.github.barteksc:android-pdf-viewer:2.8.2',
            "prdownloader"                         : 'com.mindorks.android:prdownloader:0.6.0',
            "eventbus"                             : 'org.greenrobot:eventbus:3.2.0',
            "tbssdk"                               : 'com.tencent.tbs:tbssdk:44286'



    ]
}