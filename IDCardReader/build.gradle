apply plugin: 'com.android.library'
tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}


android {
    compileSdkVersion 29
    buildToolsVersion "29.0.3"

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 29
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    productFlavors {
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    ndkVersion "20.0.5594570"
//    android{compileOptions.encoding="GBK"}
}

repositories {
    jcenter()
    google()
}
dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation 'junit:junit:4.12'
    api files('libs/zkandroididcardreader.jar')
    api files('libs/zkandroidcore.jar')
}