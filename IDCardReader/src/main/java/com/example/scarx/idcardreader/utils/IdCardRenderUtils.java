package com.example.scarx.idcardreader.utils;

import android.app.Activity;
import android.util.Log;

import com.example.scarx.idcardreader.utils.imp.MyCallBack;
import com.zkteco.android.IDReader.IDPhotoHelper;
import com.zkteco.android.IDReader.WLTService;
import com.zkteco.android.biometric.core.utils.LogHelper;
import com.zkteco.android.biometric.module.idcard.IDCardReader;
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo;

import java.util.concurrent.CountDownLatch;


public class IdCardRenderUtils {
    static boolean bSamStatus = false;
    static IDCardInfo idCardInfo = new IDCardInfo();
    static boolean ret = false;
    boolean bStoped;

    public boolean isbStoped() {
        return bStoped;
    }

    public void setbStoped(boolean bStoped) {
        this.bStoped = bStoped;
    }

    public void readerIdCard(final IDCardReader idCardReader, final CountDownLatch countdownLatch, final MyCallBack callBack){
        try {
            if(null==idCardReader){
                return;
            }
            callBack.onRequestDevicePermission();
            idCardReader.open(0);
            new Thread(new Runnable() {
                public void run() {
                    while (true) {
                        try {
                            Thread.sleep(500);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        if(!bStoped) {
                            try {
                                bSamStatus = idCardReader.getStatus(0);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            if (!bSamStatus) {
                                try {
                                    idCardReader.reset(0);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                            try {
                                idCardReader.findCard(1);
                                idCardReader.selectCard(1);
                            } catch (Exception e) {
           //                     Log.e("zzkong", "111111111111");
//                            LogHelper.e("errcode:" + e.getErrorCode() + ",internalerrorcode:" + e.getInternalErrorCode());
//                            //continue;
//                            callBack.onFail("errcode:" + e.getErrorCode() + ",internalerrorcode:" + e.getInternalErrorCode());
                            }
                            try {
                                Thread.sleep(50);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            try {
                                ret = idCardReader.readCard(0, 0, idCardInfo);
                            } catch (Exception e) {
                               // Log.e("zzkong", "读卡失败:" + e.getMessage());
                            }
                            if (ret) {
                                callBack.onSuccess(idCardInfo);
                            } else {
                                callBack.onNoCards();
                            }
                        }
                        countdownLatch.countDown();
                    }

                }
            }).start();
        }catch (Exception e)
        {
            callBack.onFail("连接设备失败");
            Log.e("zzkong", "开始读卡失败，错误码：" + e.getMessage() + "\n错误信息：" + e.getMessage() + "\n内部代码=" + e.getLocalizedMessage());
//            writeLogToFile("连接设备失败");
//            textView.setText("连接失败");
//            textView.setText("开始读卡失败，错误码：" + e.getErrorCode() + "\n错误信息：" + e.getMessage() + "\n内部代码=" + e.getInternalErrorCode());
        }
    }
}
