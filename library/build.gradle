apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 29
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

configurations.all {
    resolutionStrategy {
        force 'com.google.android.material:material:1.0.0'
        force 'androidx.core:core-ktx:1.2.0'
        force 'androidx.media:media:1.0.0'
        force 'androidx.core:core:1.3.2'
        force 'androidx.appcompat:appcompat:1.1.0'
        force 'androidx.recyclerview:recyclerview:1.2.1'

    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'com.google.android.material:material:1.0.0'
    implementation 'androidx.exifinterface:exifinterface:1.3.5'
    implementation "androidx.core:core-ktx:1.2.0"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    // glide
    implementation 'com.github.bumptech.glide:glide:4.11.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.11.0'
    implementation 'com.github.bumptech.glide:okhttp3-integration:4.11.0'
    implementation "com.github.zjupure:webpdecoder:2.0.4.11.0"

}