group 'io.agora.agora_rtc_engine'
version '1.0-SNAPSHOT'

def safeExtGet(prop, fallback) {
  rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

buildscript {
  def kotlin_version = rootProject.ext.has('kotlin_version') ? rootProject.ext.get('kotlin_version') : '1.3.72'

  repositories {
    mavenCentral()
    google()
  }

  dependencies {
    classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
  }
}

rootProject.allprojects {
  repositories {
    mavenCentral()
    google()
    maven { url 'https://www.jitpack.io' }
  }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
  compileSdkVersion safeExtGet('compileSdkVersion', 28)
  buildToolsVersion safeExtGet('buildToolsVersion', '28.0.3')

  defaultConfig {
    minSdkVersion safeExtGet('minSdkVersion', 16)
    targetSdkVersion safeExtGet('targetSdkVersion', 28)

    consumerProguardFiles 'consumer-rules.pro'
  }

  sourceSets {
    main.java.srcDirs += 'src/main/kotlin'
  }

  lintOptions {
    disable 'InvalidPackage'
  }
}

dependencies {
//  api 'com.github.agorabuilder:native-full-sdk:3.4.6'
  implementation 'io.agora.rtc:full-sdk:3.4.6'
  implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${safeExtGet('kotlin_version', '1.3.72')}"
}
