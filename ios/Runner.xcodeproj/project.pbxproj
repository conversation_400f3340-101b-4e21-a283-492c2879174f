// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		46CCA9312853661200E29BA6 /* ReplayKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 46CCA90F28535C1C00E29BA6 /* ReplayKit.framework */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		EA9241E0CC64F3B6B35A5A29 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 77E8BEFE78F21D39052F3E78 /* Pods_Runner.framework */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		46CCA91828535C1C00E29BA6 /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 12;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		2CFA640091F3F6175B28BE3A /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		373C27E226AB7897D8B7ED33 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		4344B4E01EC5C26193BCBF9D /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		4697B77E28850507001826CC /* RunnerDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerDebug.entitlements; sourceTree = "<group>"; };
		469C00FD2646697C0030F8F2 /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		46CCA90F28535C1C00E29BA6 /* ReplayKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ReplayKit.framework; path = System/Library/Frameworks/ReplayKit.framework; sourceTree = SDKROOT; };
		46CCA92F28535DA100E29BA6 /* TXLiteAVSDK_ReplayKitExt.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TXLiteAVSDK_ReplayKitExt.framework; sourceTree = "<group>"; };
		46CCA93C2856C55800E29BA6 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		46CCA93E2856C56F00E29BA6 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		46CCA9402856C58300E29BA6 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		77E8BEFE78F21D39052F3E78 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				46CCA9312853661200E29BA6 /* ReplayKit.framework in Frameworks */,
				EA9241E0CC64F3B6B35A5A29 /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		58A638CF4D3A2DBA1E13B019 /* Pods */ = {
			isa = PBXGroup;
			children = (
				4344B4E01EC5C26193BCBF9D /* Pods-Runner.debug.xcconfig */,
				2CFA640091F3F6175B28BE3A /* Pods-Runner.release.xcconfig */,
				373C27E226AB7897D8B7ED33 /* Pods-Runner.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		69FDA83FFF3C2CF645EBFA79 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				46CCA9402856C58300E29BA6 /* Accelerate.framework */,
				46CCA93E2856C56F00E29BA6 /* libz.tbd */,
				46CCA93C2856C55800E29BA6 /* libc++.tbd */,
				46CCA90F28535C1C00E29BA6 /* ReplayKit.framework */,
				46CCA92F28535DA100E29BA6 /* TXLiteAVSDK_ReplayKitExt.framework */,
				77E8BEFE78F21D39052F3E78 /* Pods_Runner.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				58A638CF4D3A2DBA1E13B019 /* Pods */,
				69FDA83FFF3C2CF645EBFA79 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				4697B77E28850507001826CC /* RunnerDebug.entitlements */,
				469C00FD2646697C0030F8F2 /* Runner.entitlements */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				AF4A38A180703742B65ACBDE /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				46CCA91828535C1C00E29BA6 /* Embed App Extensions */,
				F7CD803669C3EA5F3F6D9E68 /* [CP] Embed Pods Frameworks */,
				6D93E21E89582B6CE68E3C1F /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1330;
				LastUpgradeCheck = 1020;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin\n";
		};
		6D93E21E89582B6CE68E3C1F /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		AF4A38A180703742B65ACBDE /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F7CD803669C3EA5F3F6D9E68 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 26243ALJF7;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=*]" = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MARKETING_VERSION = 2.4.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld64",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"bz2\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"sqlite3\"",
					"-l\"sqlite3.0\"",
					"-l\"z\"",
					"-framework",
					"\"AMapFoundationKit\"",
					"-framework",
					"\"AMapLocationKit\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AgoraAIDenoiseExtension\"",
					"-framework",
					"\"AgoraCore\"",
					"-framework",
					"\"AgoraDav1dExtension\"",
					"-framework",
					"\"AgoraJNDExtension\"",
					"-framework",
					"\"AgoraRtcKit\"",
					"-framework",
					"\"AgoraSoundTouch\"",
					"-framework",
					"\"Agorafdkaac\"",
					"-framework",
					"\"Agoraffmpeg\"",
					"-framework",
					"\"AlipaySDK\"",
					"-framework",
					"\"AssetsLibrary\"",
					"-framework",
					"\"AudioToolbox\"",
					"-framework",
					"\"BSGridCollectionViewLayout\"",
					"-framework",
					"\"BSImagePicker\"",
					"-framework",
					"\"BSImageView\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreServices\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"ExternalAccessory\"",
					"-framework",
					"\"FMDB\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Mantle\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MetalKit\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Reachability\"",
					"-framework",
					"\"ReplayKit\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SDWebImageWebPCoder\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"TOCropViewController\"",
					"-framework",
					"\"TXCustomBeautyProcesserPlugin\"",
					"-framework",
					"\"TXFFmpeg\"",
					"-framework",
					"\"TXLiteAVSDK_Professional\"",
					"-framework",
					"\"TXSoundTouch\"",
					"-framework",
					"\"Toast\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"VideoToolbox\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"agora_rtc_engine\"",
					"-framework",
					"\"amap_core_fluttify\"",
					"-framework",
					"\"amap_location_fluttify\"",
					"-framework",
					"\"audio_session\"",
					"-framework",
					"\"audioplayers\"",
					"-framework",
					"\"camera\"",
					"-framework",
					"\"connectivity\"",
					"-framework",
					"\"core_location_fluttify\"",
					"-framework",
					"\"device_info\"",
					"-framework",
					"\"flutter_downloader\"",
					"-framework",
					"\"flutter_image_compress\"",
					"-framework",
					"\"flutter_native_image\"",
					"-framework",
					"\"flutter_network_speed\"",
					"-framework",
					"\"fluttertoast\"",
					"-framework",
					"\"foundation_fluttify\"",
					"-framework",
					"\"image_cropper\"",
					"-framework",
					"\"image_picker\"",
					"-framework",
					"\"just_audio\"",
					"-framework",
					"\"multi_image_picker\"",
					"-framework",
					"\"open_file\"",
					"-framework",
					"\"package_info\"",
					"-framework",
					"\"path_provider\"",
					"-framework",
					"\"pdf_flutter\"",
					"-framework",
					"\"permission_handler\"",
					"-framework",
					"\"replay_kit_launcher\"",
					"-framework",
					"\"shared_preferences\"",
					"-framework",
					"\"sqflite\"",
					"-framework",
					"\"tencent_trtc_cloud\"",
					"-framework",
					"\"url_launcher\"",
					"-framework",
					"\"video_compress\"",
					"-framework",
					"\"video_player\"",
					"-framework",
					"\"wakelock\"",
					"-framework",
					"\"webview_flutter\"",
				);
				PLIST_FILE_OUTPUT_FORMAT = "same-as-input";
				PRODUCT_BUNDLE_IDENTIFIER = com.jiangsu.qingtongzhihe;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = "";
				STRINGS_FILE_OUTPUT_ENCODING = "UTF-16";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 26243ALJF7;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=*]" = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MARKETING_VERSION = 2.4.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld64",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"bz2\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"sqlite3\"",
					"-l\"sqlite3.0\"",
					"-l\"z\"",
					"-framework",
					"\"AMapFoundationKit\"",
					"-framework",
					"\"AMapLocationKit\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AgoraAIDenoiseExtension\"",
					"-framework",
					"\"AgoraCore\"",
					"-framework",
					"\"AgoraDav1dExtension\"",
					"-framework",
					"\"AgoraJNDExtension\"",
					"-framework",
					"\"AgoraRtcKit\"",
					"-framework",
					"\"AgoraSoundTouch\"",
					"-framework",
					"\"Agorafdkaac\"",
					"-framework",
					"\"Agoraffmpeg\"",
					"-framework",
					"\"AlipaySDK\"",
					"-framework",
					"\"AssetsLibrary\"",
					"-framework",
					"\"AudioToolbox\"",
					"-framework",
					"\"BSGridCollectionViewLayout\"",
					"-framework",
					"\"BSImagePicker\"",
					"-framework",
					"\"BSImageView\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreServices\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"ExternalAccessory\"",
					"-framework",
					"\"FMDB\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Mantle\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MetalKit\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Reachability\"",
					"-framework",
					"\"ReplayKit\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SDWebImageWebPCoder\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"TOCropViewController\"",
					"-framework",
					"\"TXCustomBeautyProcesserPlugin\"",
					"-framework",
					"\"TXFFmpeg\"",
					"-framework",
					"\"TXLiteAVSDK_Professional\"",
					"-framework",
					"\"TXSoundTouch\"",
					"-framework",
					"\"Toast\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"VideoToolbox\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"agora_rtc_engine\"",
					"-framework",
					"\"amap_core_fluttify\"",
					"-framework",
					"\"amap_location_fluttify\"",
					"-framework",
					"\"audio_session\"",
					"-framework",
					"\"audioplayers\"",
					"-framework",
					"\"camera\"",
					"-framework",
					"\"connectivity\"",
					"-framework",
					"\"core_location_fluttify\"",
					"-framework",
					"\"device_info\"",
					"-framework",
					"\"flutter_downloader\"",
					"-framework",
					"\"flutter_image_compress\"",
					"-framework",
					"\"flutter_native_image\"",
					"-framework",
					"\"flutter_network_speed\"",
					"-framework",
					"\"fluttertoast\"",
					"-framework",
					"\"foundation_fluttify\"",
					"-framework",
					"\"image_cropper\"",
					"-framework",
					"\"image_picker\"",
					"-framework",
					"\"just_audio\"",
					"-framework",
					"\"multi_image_picker\"",
					"-framework",
					"\"open_file\"",
					"-framework",
					"\"package_info\"",
					"-framework",
					"\"path_provider\"",
					"-framework",
					"\"pdf_flutter\"",
					"-framework",
					"\"permission_handler\"",
					"-framework",
					"\"replay_kit_launcher\"",
					"-framework",
					"\"shared_preferences\"",
					"-framework",
					"\"sqflite\"",
					"-framework",
					"\"tencent_trtc_cloud\"",
					"-framework",
					"\"url_launcher\"",
					"-framework",
					"\"video_compress\"",
					"-framework",
					"\"video_player\"",
					"-framework",
					"\"wakelock\"",
					"-framework",
					"\"webview_flutter\"",
				);
				PLIST_FILE_OUTPUT_FORMAT = "same-as-input";
				PRODUCT_BUNDLE_IDENTIFIER = com.jiangsu.qingtongzhihe;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = "";
				STRINGS_FILE_OUTPUT_ENCODING = "UTF-16";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 26243ALJF7;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=*]" = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MARKETING_VERSION = 2.4.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld64",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"bz2\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"sqlite3\"",
					"-l\"sqlite3.0\"",
					"-l\"z\"",
					"-framework",
					"\"AMapFoundationKit\"",
					"-framework",
					"\"AMapLocationKit\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AgoraAIDenoiseExtension\"",
					"-framework",
					"\"AgoraCore\"",
					"-framework",
					"\"AgoraDav1dExtension\"",
					"-framework",
					"\"AgoraJNDExtension\"",
					"-framework",
					"\"AgoraRtcKit\"",
					"-framework",
					"\"AgoraSoundTouch\"",
					"-framework",
					"\"Agorafdkaac\"",
					"-framework",
					"\"Agoraffmpeg\"",
					"-framework",
					"\"AlipaySDK\"",
					"-framework",
					"\"AssetsLibrary\"",
					"-framework",
					"\"AudioToolbox\"",
					"-framework",
					"\"BSGridCollectionViewLayout\"",
					"-framework",
					"\"BSImagePicker\"",
					"-framework",
					"\"BSImageView\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreServices\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"ExternalAccessory\"",
					"-framework",
					"\"FMDB\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Mantle\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MetalKit\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Reachability\"",
					"-framework",
					"\"ReplayKit\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SDWebImageWebPCoder\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"TOCropViewController\"",
					"-framework",
					"\"TXCustomBeautyProcesserPlugin\"",
					"-framework",
					"\"TXFFmpeg\"",
					"-framework",
					"\"TXLiteAVSDK_Professional\"",
					"-framework",
					"\"TXSoundTouch\"",
					"-framework",
					"\"Toast\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"VideoToolbox\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"agora_rtc_engine\"",
					"-framework",
					"\"amap_core_fluttify\"",
					"-framework",
					"\"amap_location_fluttify\"",
					"-framework",
					"\"audio_session\"",
					"-framework",
					"\"audioplayers\"",
					"-framework",
					"\"camera\"",
					"-framework",
					"\"connectivity\"",
					"-framework",
					"\"core_location_fluttify\"",
					"-framework",
					"\"device_info\"",
					"-framework",
					"\"flutter_downloader\"",
					"-framework",
					"\"flutter_image_compress\"",
					"-framework",
					"\"flutter_native_image\"",
					"-framework",
					"\"flutter_network_speed\"",
					"-framework",
					"\"fluttertoast\"",
					"-framework",
					"\"foundation_fluttify\"",
					"-framework",
					"\"image_cropper\"",
					"-framework",
					"\"image_picker\"",
					"-framework",
					"\"just_audio\"",
					"-framework",
					"\"multi_image_picker\"",
					"-framework",
					"\"open_file\"",
					"-framework",
					"\"package_info\"",
					"-framework",
					"\"path_provider\"",
					"-framework",
					"\"pdf_flutter\"",
					"-framework",
					"\"permission_handler\"",
					"-framework",
					"\"replay_kit_launcher\"",
					"-framework",
					"\"shared_preferences\"",
					"-framework",
					"\"sqflite\"",
					"-framework",
					"\"tencent_trtc_cloud\"",
					"-framework",
					"\"url_launcher\"",
					"-framework",
					"\"video_compress\"",
					"-framework",
					"\"video_player\"",
					"-framework",
					"\"wakelock\"",
					"-framework",
					"\"webview_flutter\"",
				);
				PLIST_FILE_OUTPUT_FORMAT = "same-as-input";
				PRODUCT_BUNDLE_IDENTIFIER = com.jiangsu.qingtongzhihe;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = "";
				STRINGS_FILE_OUTPUT_ENCODING = "UTF-16";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
