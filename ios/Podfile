# Uncomment this line to define a global platform for your project
# platform :ios, '11.0'
platform :ios, '12.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_builåd_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
end

# post_install do |installer|
#   installer.pods_project.targets.each do |target|
#     flutter_additional_ios_build_settings(target)
#   end
# end

 post_install do |installer|
   installer.pods_project.targets.each do |target|
     flutter_additional_ios_build_settings(target)
     target.build_configurations.each do |config|
       config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
     end
   end
   bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
     def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
       framework_path = File.join(Dir.pwd, framework_relative_path)
       command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
       puts "Stripping bitcode: #{command}"
       system(command)
     end
     framework_paths = [
       "Runner.app/Frameworks/Flutter.framework/Flutter",
       "Pods/AgoraIrisRTC_iOS/AgoraRtcWrapper.xcframework/ios-arm64_armv7/AgoraRtcWrapper.framework/AgoraRtcWrapper",
       "Pods/AgoraRtcEngine_iOS/AgoraAIDenoiseExtension.xcframework/ios-arm64_armv7/AgoraAIDenoiseExtension.framework/AgoraAIDenoiseExtension",
       "Pods/AgoraRtcEngine_iOS/AgoraCIExtension.xcframework/ios-arm64_armv7/AgoraCIExtension.framework/AgoraCIExtension",
       "Pods/AgoraRtcEngine_iOS/AgoraCore.xcframework/ios-arm64_armv7/AgoraCore.framework/AgoraCore",
       "Pods/AgoraRtcEngine_iOS/AgoraDav1dExtension.xcframework/ios-arm64_armv7/AgoraDav1dExtension.framework/AgoraDav1dExtension",
       "Pods/AgoraRtcEngine_iOS/AgoraFDExtension.xcframework/ios-arm64_armv7/AgoraFDExtension.framework/AgoraFDExtension",
       "Pods/AgoraRtcEngine_iOS/AgoraFullAudioFormatExtension.xcframework/ios-arm64_armv7/AgoraFullAudioFormatExtension.framework/AgoraFullAudioFormatExtension",
       "Pods/AgoraRtcEngine_iOS/AgoraReplayKitExtension.xcframework/ios-arm64_armv7/AgoraReplayKitExtension.framework/AgoraReplayKitExtension",
       "Pods/AgoraRtcEngine_iOS/AgoraRtcKit.xcframework/ios-arm64_armv7/AgoraRtcKit.framework/AgoraRtcKit",
       "Pods/AgoraRtcEngine_iOS/AgoraSoundTouch.xcframework/ios-arm64_armv7/AgoraSoundTouch.framework/AgoraSoundTouch",
       "Pods/AgoraRtcEngine_iOS/AgoraSpatialAudioExtension.xcframework/ios-arm64_armv7/AgoraSpatialAudioExtension.framework/AgoraSpatialAudioExtension",
       "Pods/AgoraRtcEngine_iOS/AgoraSuperResolutionExtension.xcframework/ios-arm64_armv7/AgoraSuperResolutionExtension.framework/AgoraSuperResolutionExtension",
       "Pods/AgoraRtcEngine_iOS/AgoraVideoProcessExtension.xcframework/ios-arm64_armv7/AgoraVideoProcessExtension.framework/AgoraVideoProcessExtension",
       "Pods/AgoraRtcEngine_iOS/AgoraVideoSegmentationExtension.xcframework/ios-arm64_armv7/AgoraVideoSegmentationExtension.framework/AgoraVideoSegmentationExtension",
       "Pods/AgoraRtcEngine_iOS/Agorafdkaac.xcframework/ios-arm64_armv7/Agorafdkaac.framework/Agorafdkaac",
       "Pods/AgoraRtcEngine_iOS/Agoraffmpeg.xcframework/ios-arm64_armv7/Agoraffmpeg.framework/Agoraffmpeg",
    ]
     framework_paths.each do |framework_relative_path|
       strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
     end
 end
