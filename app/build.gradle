apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
android {
    signingConfigs {
        relealse {
            keyAlias 'notrization'
            keyPassword '123456'
            storePassword '123456'
            storeFile file("$rootDir/notrization.jks")
        }
    }
//    buildToolsVersion rootProject.ext.android.buildToolsVersion
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    defaultConfig {
        applicationId "com.gc.mininotarization"
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        ndk {
            abiFilters "armeabi-v7a"
        }

    }
    ndkVersion "20.0.5594570"
    buildTypes {
        debug {
            buildConfigField "boolean", "LOG_DEBUG", "true"
            minifyEnabled false
            zipAlignEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.relealse
        }

        release {
            minifyEnabled false
            zipAlignEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            buildConfigField "boolean", "LOG_DEBUG", "false"
            signingConfig signingConfigs.relealse
        }
    }
    flavorDimensions "type" //这个是必须的
    productFlavors {
        server_test {
            manifestPlaceholders = [channel: "test"]
            buildConfigField "int", "SERVER_TYPE", "1"
            dimension "type"  //并且必须使用这个dimension
        }
        server_online {
            manifestPlaceholders = [channel: "online"]
            buildConfigField "int", "SERVER_TYPE", "2"
            dimension "type"  //并且必须使用这个dimension
        }
    }
    dexOptions {
        preDexLibraries = false
    }
    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def outputFile = output.outputFile
            if (outputFile != null && outputFile.name.endsWith('.apk')) {
                def fileName
                def versionName
                def versionCode
                if (variant.productFlavors[0].versionName != null) {
                    versionName = variant.productFlavors[0].versionName
                    versionCode = variant.productFlavors[0].versionCode
                } else {
                    versionName = defaultConfig.versionName
                    versionCode = defaultConfig.versionCode
                }
                def tempName

                fileName = "com.gc.mininotarization${versionName}_${versionCode}_${variant.productFlavors[0].name}.apk"
                outputFileName = fileName
            }
        }
    }
    lintOptions {
        disable 'GoogleAppIndexingWarning'
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
//    configurations.all {
//        resolutionStrategy {
//            force 'androidx.core:core:1.5.0'
//        }
//    }


    dataBinding {
        enabled true
    }

}

configurations.all {
    resolutionStrategy {
        force 'com.google.android.material:material:1.0.0'
        force 'androidx.core:core-ktx:1.2.0'
        force 'androidx.media:media:1.0.0'
        force 'androidx.core:core:1.3.2'
        force 'androidx.appcompat:appcompat:1.1.0'
        force 'androidx.recyclerview:recyclerview:1.2.1'

    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation rootProject.ext.support["design"]
//    implementation rootProject.ext.dependencies.autosize
    implementation rootProject.ext.dependencies.fastjson
    implementation "com.serenegiant:common:2.12.4"
    implementation project(':lib_base')
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    // 根据拼音获取首字母
    implementation 'com.github.promeg:tinypinyin:2.0.3'
    //底部tabBar
    implementation('me.majiajie:pager-bottom-tab-strip:2.2.5') {
        exclude group: 'com.android.support'
    }
    implementation project(path: ':library')

    //内存泄漏测试
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:1.6.3'
    debugImplementation 'com.squareup.leakcanary:leakcanary-support-fragment:1.6.3'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'com.hjq:titlebar:5.0'
    implementation project(':FaceRec')
    implementation project(':IDCardReader')
    implementation project(':customcapture')
    implementation rootProject.ext.dependencies.trtc
    implementation rootProject.ext.dependencies.arouter
    implementation rootProject.ext.dependencies.xxpermission
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.3.2"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.3.2"
    implementation rootProject.ext.dependencies.Toasty
    implementation rootProject.ext.dependencies.rtc
    implementation rootProject.ext.dependencies.mqttService
    implementation rootProject.ext.dependencies.mqtt
    implementation rootProject.ext.dependencies.prdownloader
    implementation rootProject.ext.dependencies.pdfView
    implementation rootProject.ext.dependencies.eventbus
    implementation 'io.github.lucksiege:pictureselector:v3.11.0'
    implementation 'com.github.bumptech.glide:okhttp3-integration:4.11.0'
    compile 'com.contrarywind:Android-PickerView:4.1.9'
    implementation 'com.umeng.umsdk:common:9.6.5'// 必选
    implementation 'com.umeng.umsdk:asms:1.8.2'// 必选
    implementation 'com.umeng.umsdk:apm:1.9.3' // U-APM包依赖(必选)
//    implementation 'com.github.lys8829:sm4:1.0.8'
//    implementation 'org.bouncycastle:bcpkix-jdk15on:1.68'
//    implementation rootProject.ext.dependencies.tbssdk
//    implementation "com.github.SherlockGougou:BigImageViewPager:androidx-7.0.4"
//    implementation 'com.github.SherlockGougou:BigImageViewPager:androidx-7.2.0'
}