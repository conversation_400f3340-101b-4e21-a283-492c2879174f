apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
android {
    signingConfigs {
        relealse {
            keyAlias 'notrization'
            keyPassword '123456'
            storePassword '123456'
            storeFile file("$rootDir/notrization.jks")
        }
    }
    buildToolsVersion rootProject.ext.android.buildToolsVersion
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    defaultConfig {
        applicationId "com.gc.notarizationpc"
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
//        javaCompileOptions {
//            annotationProcessorOptions {
//                arguments = [moduleName: project.getName(), AROUTER_MODULE_NAME: project.getName()]
//            }
//        }
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        ndk {
            abiFilters  "armeabi-v7a"
        }
    }
    ndkVersion "20.0.5594570"
    buildTypes {
        debug {
            buildConfigField "boolean", "LOG_DEBUG", "true"
            minifyEnabled false
            zipAlignEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.relealse
        }

        release {
            minifyEnabled false
            zipAlignEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            buildConfigField "boolean", "LOG_DEBUG", "false"
            signingConfig signingConfigs.relealse
        }
    }
    flavorDimensions "type" //这个是必须的
    productFlavors {
        server_test {
            manifestPlaceholders = [channel: "test"]
            buildConfigField "int", "SERVER_TYPE", "1"
            dimension "type"  //并且必须使用这个dimension
        }
        server_online {
            manifestPlaceholders = [channel: "online"]
            buildConfigField "int", "SERVER_TYPE", "2"
            dimension "type"  //并且必须使用这个dimension
        }
    }
    dexOptions {
        preDexLibraries = false
    }
    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def outputFile = output.outputFile
            if (outputFile != null && outputFile.name.endsWith('.apk')) {
                def fileName
                def versionName
                def versionCode
                if (variant.productFlavors[0].versionName != null) {
                    versionName = variant.productFlavors[0].versionName
                    versionCode = variant.productFlavors[0].versionCode
                } else {
                    versionName = defaultConfig.versionName
                    versionCode = defaultConfig.versionCode
                }
                def tempName
                fileName = "${defaultConfig.applicationId}_${versionName}_${versionCode}_${variant.productFlavors[0].name}.apk"
                outputFileName = fileName
            }
        }
    }
    lintOptions {
        disable 'GoogleAppIndexingWarning'
    }
//    compileOptions {
//        sourceCompatibility rootProject.ext.javaSourceCompatibility
//        targetCompatibility rootProject.ext.javaTargetCompatibility
//    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
//    ndkVersion '21.4.7075529'

}
//sentry {
//    autoProguardConfig false
//    autoUpload true
//    uploadNativeSymbols false
//    includeNativeSources false
//}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.3.0-alpha02'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation files('libs\\AMap3DMap_9.4.0_AMapSearch_9.4.0_AMapLocation_6.1.0_20220808.jar')
    implementation files('libs\\BaiduLBS_Android.jar')
    testImplementation 'junit:junit:4.12'

    implementation project(':framwork')
    implementation project(':FaceRec')
    implementation project(':IDCardReader')
    implementation project(':customcapture')
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    implementation 'io.reactivex.rxjava3:rxjava:3.0.0'
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.3.0-alpha02"
    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.2.0"
    implementation 'io.agora.rtc:full-sdk:3.3.2'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation project(path: ':lib-screensharing')
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.3.2"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.3.2"
    implementation 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.0'
    implementation 'org.eclipse.paho:org.eclipse.paho.android.service:1.1.1'
    implementation 'com.tencent.liteav:LiteAVSDK_TRTC:11.4.0.13270'
//    implementation 'com.tencent.liteav:LiteAVSDK_TRTC:9.3.10765'
//    implementation 'com.amap.api:location:4.7.0'
    //动态权限
    implementation 'com.hjq:xxpermissions:6.2'
    implementation "com.squareup.okhttp3:okhttp:3.6.0"
    implementation 'com.ldoublem.loadingview:loadingviewlib:1.0'
    implementation 'com.shuhart.stepview:stepview:1.5.1'
    implementation 'com.youth.banner:banner:2.1.0'
    implementation 'com.zzhoujay.richtext:richtext:3.0.8'
    implementation 'io.fotoapparat:facedetector:1.0.0'
    implementation 'com.github.bumptech.glide:glide:3.7.0'
    implementation 'com.github.barteksc:android-pdf-viewer:2.8.2'
    implementation 'com.mindorks.android:prdownloader:0.6.0'
    //Rx权限
    implementation 'com.tbruyelle.rxpermissions2:rxpermissions:0.9.5@aar'
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.4'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.0'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.3'
    implementation "com.tencent.tbs.tbssdk:sdk:43697"

    implementation 'com.lovedise:permissiongen:0.0.6'

    implementation 'com.contrarywind:Android-PickerView:4.1.4'
    implementation 'com.wanjian:cockroach:0.0.5'
    implementation 'org.slf4j:slf4j-nop:1.7.25'
    implementation 'com.google.code.gson:gson:2.8.2'
    implementation 'com.umeng.umsdk:common:9.6.5'// 必选
    implementation 'com.umeng.umsdk:asms:1.8.2'// 必选
    implementation 'com.umeng.umsdk:apm:1.9.3' // U-APM包依赖(必选)
    implementation 'com.alibaba:arouter-api:1.5.1'
    kapt   'com.alibaba:arouter-compiler:1.5.1'
}
