<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <div class="box-right" style="width: 100vw;height: 100vh;">
      <div id="vabOnlyOffice"></div>
    </div>
  </body>
  <script>
    function loadJs(src) {
      return new Promise((resolve, reject) => {
        let script = document.createElement("script");
        script.type = "text/javascript";
        script.src = src;
        document.body.appendChild(script);

        script.onload = () => {
          resolve();
        };
        script.onerror = () => {
          reject();
        };
      });
    }
    window.onload = () => {
      console.log(111)
      loadJs("{api}").then(
        () => {
          new DocsAPI.DocEditor("vabOnlyOffice", {ConttentMap});
        }
      );
    };
  </script>
</html>
