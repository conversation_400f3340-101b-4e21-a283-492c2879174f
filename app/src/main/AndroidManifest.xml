<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.gc.notarizationpc">

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- 允许写入数据到SD卡  -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- 允许读取SD卡 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission
        android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 用于访问GPS定位 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 用于提高GPS定位速度 -->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <uses-feature
        android:name="android.hardware.usb.host"
        android:required="true"
        android:sharedUserId="android.uid.system" />

    <protected-broadcast android:name="android.intent.action.USB_CAMERA" />

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <!--允许写设备缓存，用于问题排查-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission
        android:name="android.permission.CHANGE_CONFIGURATION"
        tools:ignore="ProtectedPermissions" />
    <!-- 硬件加速对X5视频播放非常重要，建议开启 -->
    <uses-permission android:name="android.permission.GET_TASKS" />

    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />


    <uses-permission android:name="android.permission.READ_SETTINGS" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />


    <application
        android:name=".AppApplication"
        android:allowBackup="true"
        android:hardwareAccelerated="false"
        android:icon="@mipmap/logo"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

        <activity android:name=".ui.OrderSucessActivity" />
        <activity
            android:name=".ui.NotarizationSelfActivity"
            android:windowSoftInputMode="adjustPan|stateHidden"></activity>
        <activity
            android:name=".ui.SelfNotarizationSelectRoleActivity"
            android:windowSoftInputMode="adjustPan|stateHidden"></activity>

        <activity android:name=".ui.ShowCertreprotActivity" />
        <activity android:name=".ui.RemoteModelActivity" />
        <activity android:name=".ui.RemoteDetailsActivity"></activity>
        <activity android:name=".ui.RemoteNotarizationActivity" />
        <activity android:name=".ui.RemoteEmpowermentActivity" />
        <activity
            android:name=".ui.RemoteTwoActivity"
            android:hardwareAccelerated="true" />
        <activity android:name=".ui.JinRongActivity" />
        <activity android:name=".ui.VideoConsultActivity" />
        <activity
            android:name=".ui.VideoConsultNotarizationActivity"
            android:exported="false"
            android:hardwareAccelerated="true" />

        <receiver android:name=".broadcast.BootBroadcastReceiver">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.media.VOLUME_CHANGED_ACTION" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
            android:resource="@xml/device_filter" />
        <meta-data
            android:name="android.max_aspect"
            android:value="2.1" />

        <service
            android:name="com.baidu.location.f"
            android:enabled="true"
            android:process=":remote"></service>
        <!--    百度定位    -->
        <meta-data
            android:name="com.baidu.lbsapi.API_KEY"
            android:value="Bv9AS8VsHTXFNzH3rZAsFfZvxgliHfUp" />
        <!--            android:value="IPZolmGyVwGDkiGGKzXxGGfh9SEOEuQu" />-->
        <!--            android:value="Bv9AS8VsHTXFNzH3rZAsFfZvxgliHfUp" />-->
        <!--    高德定位    -->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="5b883e7539073aee4dac8dd85c314d1d" />
        <service
            android:name="com.amap.api.location.APSService"
            android:foregroundServiceType="location" />

        <activity
            android:name=".ui.MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape">
                        <intent-filter>
                            <action android:name="android.intent.action.MAIN" />

                            <category android:name="android.intent.category.LAUNCHER" />
                        </intent-filter>
        </activity>
        <activity
            android:name=".ui.PayActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape">
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->

<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
        </activity>

        <!-- 人证比对 -->
        <activity
            android:name=".ui.ComparisonActivity"
            android:hardwareAccelerated="true" />
        <activity
            android:name=".ui.ComparisonIDcardActivity"
            android:hardwareAccelerated="true"></activity>
        <activity
            android:name=".ui.MobileActivity"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.VideoConsultMobileActivity"
            android:windowSoftInputMode="stateHidden|stateUnchanged" />
        <activity android:name=".ui.ModelSelectActivity" />
        <activity android:name=".ui.PersonSelectActivity" />
        <activity android:name=".ui.DescriptionActivity" />
        <activity android:name=".ui.NotarizationConfirmActivity" />
        <activity
            android:name=".ui.RemoteOneActivity"
            android:hardwareAccelerated="true" />
        <activity
            android:name=".ui.VideoNotarizationActivity"
            android:exported="false"
            android:hardwareAccelerated="true" />
        <activity
            android:name=".ui.VideoNotarizationUSBActivity"
            android:exported="false"
            android:hardwareAccelerated="true" />
        <activity
            android:name=".ui.TakePhotoActivity"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"></activity>
        <activity
            android:name=".ui.ConsultTakePhotoActivity"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"></activity>
        <activity android:name=".ui.CustomServiceActivity" />
        <activity android:name=".ui.SelectNotary" />
        <activity
            android:name="com.tencent.rtmp.video.TXScreenCapture$TXScreenCaptureAssistantActivity"
            android:theme="@android:style/Theme.Translucent" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.gc.notarizationpc.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <service android:name="org.eclipse.paho.android.service.MqttService" /> <!--MqttService-->
        <service android:name="com.gc.notarizationpc.ui.service.CheckTokenService" />
    </application>

</manifest>