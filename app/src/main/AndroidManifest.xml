<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.gc.mininotarization">

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- 允许写入数据到SD卡  -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- 允许读取SD卡 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission
        android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 用于访问GPS定位 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 用于提高GPS定位速度 -->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <uses-feature
        android:name="android.hardware.usb.host"
        android:required="true"
        android:sharedUserId="android.uid.system" />

    <protected-broadcast android:name="android.intent.action.USB_CAMERA" />

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <!--允许写设备缓存，用于问题排查-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission
        android:name="android.permission.CHANGE_CONFIGURATION"
        tools:ignore="ProtectedPermissions" />
    <!-- 硬件加速对X5视频播放非常重要，建议开启 -->
    <uses-permission android:name="android.permission.GET_TASKS" />

    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />


    <uses-permission android:name="android.permission.READ_SETTINGS" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />


    <application
        android:name="com.gc.notarizationpc.MainApplication"
        android:allowBackup="true"
        android:icon="@mipmap/logo"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

        <meta-data
            android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
            android:resource="@xml/device_filter" />
        <meta-data
            android:name="android.max_aspect"
            android:value="2.1" />

        <!--    高德定位    -->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="787552fc764661c79c8e39a082cb9e6e" />
        <service
            android:name="com.amap.api.location.APSService"
            android:foregroundServiceType="location" />

        <activity
            android:name="com.gc.notarizationpc.ui.video.VideoConnectionActivity"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.selfservicecertificate.DomesticEconomyNotarizationActivity"
            android:screenOrientation="landscape"></activity>

        <activity
            android:name="com.gc.notarizationpc.ui.inquirycertification.InquiryTypeHomeActivity"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.ViewPagerActivity"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.inquirycertification.SelfServiceOrderDetailActivity"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.selfservicecertificate.DomesticEconomyTakePhotoActivity"
            android:screenOrientation="landscape"></activity>


        <activity
            android:name="com.gc.notarizationpc.ui.HomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape">
                        <intent-filter>
                            <action android:name="android.intent.action.MAIN" />
                            <category android:name="android.intent.category.LAUNCHER" />
                        </intent-filter>

        </activity>

        <service
            android:name="com.tencent.smtt.export.external.DexClassLoaderProviderService"
            android:label="dexopt"
            android:process=":dexopt" >
        </service>

<!--        <activity-->
<!--            android:name="com.gc.notarizationpc.ui.TestActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="landscape">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->

<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
<!--        </activity>-->

        <activity
            android:name="com.gc.notarizationpc.ui.SelectNotaryActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"></activity>

        <activity
            android:name="com.gc.notarizationpc.ui.ReservationActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"></activity>

        <activity
            android:name="com.gc.notarizationpc.ui.inquirycertification.InquiryCertificationHomeActivity"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"></activity>

        <activity
            android:name="com.gc.notarizationpc.ui.selfservicecertificate.SelfServiceCertificateHomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"></activity>

        <activity
            android:name="com.gc.notarizationpc.ui.IdentityCheckActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.video.CounselingRoomActivity"
            android:screenOrientation="landscape">

        </activity>
        <activity
            android:name="com.gc.notarizationpc.ui.inquirycertification.InquiryOrderListHomeActivity"
            android:screenOrientation="landscape"></activity>

        <activity
            android:name="com.gc.notarizationpc.ui.common.ElectronicRulesActivity"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.inquirycertification.OrderDetailActivity"
            android:screenOrientation="landscape"></activity>

        <activity
            android:name="com.gc.notarizationpc.ui.video.TakePhotoActivity"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:screenOrientation="landscape">

        </activity>

        <activity
            android:name="com.gc.notarizationpc.ui.video.IDCardCompariseMobileActivity"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.common.OnLineRulesActivity"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.WebViewActivity"
            android:screenOrientation="landscape"></activity>

        <activity
            android:name="com.gc.notarizationpc.ui.DeviceRegisterActivity"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.selfservicecertificate.CompariseAcitivity"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.selfservicecertificate.MobileActivity"
            android:screenOrientation="landscape"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.selfservicecertificate.SelfNotarizationActivity"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="adjustPan"></activity>
        <activity
            android:name="com.gc.notarizationpc.ui.selfservicecertificate.SelfTakePhotoActivity"
            android:screenOrientation="landscape"></activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.gc.mininotarization.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <service android:name="org.eclipse.paho.android.service.MqttService" /> <!--MqttService-->


    </application>
</manifest>