package com.gc.notarizationpc.adapter;


import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.SectionIndexer;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.SerachBean;
import com.gc.notarizationpc.myview.FlowLayout;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.List;

import es.dmoral.toasty.Toasty;

/**
 * 按照名称首字母进行排序的adapter
 */
public class MyAdapter extends BaseAdapter implements SectionIndexer {

    private List<SerachBean> list = null;
    private Context mContext;
    private TextView choosedView = null;//被选中的textview
    private List<SerachBean.Bean> valuelist = new ArrayList<>();
    private int type;

    private PopwindowUtil.ResultListener<SerachBean.Bean> mResultListener;

    public MyAdapter(Context mContext, List<SerachBean> list, int type,PopwindowUtil.ResultListener<SerachBean.Bean> resultListener) {
        this.mContext = mContext;
        this.list = list;
        this.type = type;
        // 这段代码是为了外部参数和内部参数联动保持一致
        valuelist.clear();
        getSelectedItem();
        mResultListener = resultListener;
        if (choosedView != null)
            choosedView = null;
    }


    public int getCount() {
        return this.list.size();
    }

    public Object getItem(int position) {
        return list.get(position);
    }

    public long getItemId(int position) {
        return position;
    }

    @Override
    public Object[] getSections() {
        return null;
    }


    final static class ViewHolder {
        TextView tv_fistletters;
        FlowLayout tv_info;

    }

    // 更新ListView
    public void updateListView(List<SerachBean> list) {
        this.list = list;
        if (choosedView != null)
            choosedView = null;
        notifyDataSetChanged();
    }

    public View getView(final int position, View view, ViewGroup arg2) {
        List<SerachBean.Bean> itemData = new ArrayList<>();
        itemData.addAll(list.get(position).getBean());
//        for (int i = 0; i < list.get(position).getBean().size(); i++) {
//            itemData.add(list.get(position).getBean().get(i).getName());
//        }

        ViewHolder viewHolder = null;
//        if (view == null) {
        viewHolder = new ViewHolder();
        view = LayoutInflater.from(mContext).inflate(R.layout.my_list_item, null);
        viewHolder.tv_fistletters = (TextView) view.findViewById(R.id.tv_fistletters);
        viewHolder.tv_info = (FlowLayout) view.findViewById(R.id.item_info);
        view.setTag(viewHolder);
//        } else {
//            viewHolder = (ViewHolder) view.getTag();
//        }
        viewHolder.tv_fistletters.setVisibility(View.VISIBLE);
        viewHolder.tv_fistletters.setText(list.get(position).getKey());

        viewHolder.tv_info.setAdapter(itemData, R.layout.flow_item, new FlowLayout.ItemView<SerachBean.Bean>() {
            @Override
            public void getCover(SerachBean.Bean item, FlowLayout.ViewHolder holder, View inflate, int pos) {
                try {
                    holder.setText(R.id.label_name, item.getName());
                    final TextView textView = holder.getView(R.id.label_name);
                    textView.setText(item.getName());
                    if (item.getSelected()){
                        textView.setBackground(mContext.getResources().getDrawable(R.drawable.shape_corner4_2568ff));
                        textView.setTextColor(Color.WHITE);
                    }else{
                        textView.setBackground(mContext.getResources().getDrawable(R.drawable.shape_corner4_white_all));
                        textView.setTextColor(mContext.getResources().getColor(R.color.date_picker_text_dark));
                    }
                    textView.setOnClickListener(new MyOnclickClickListener() {
                        @RequiresApi(api = Build.VERSION_CODES.N)
                        @Override
                        public void mOnClick(View v) {
                            //单选
                            if (type == 1) {
                                if (valuelist != null)
                                    valuelist.clear();
                                //点击不同按钮
                                if (!item.getSelected()) {
                                    textView.setBackground(mContext.getResources().getDrawable(R.drawable.shape_corner4_2568ff));
                                    textView.setTextColor(Color.WHITE);
                                      for (SerachBean searchBean : list) {
                                          for (SerachBean.Bean bean : searchBean.getBean()) {
                                              bean.setSelected(false);
                                          }
                                    }
                                    list.get(position).getBean().get(pos).setSelected(true);
//                                        choosedView.setBackground(mContext.getResources().getDrawable(R.drawable.shape_corner4_white_all));
//                                        choosedView.setTextColor(mContext.getResources().getColor(R.color.color3333));
//                                        choosedView = textView;
//                                    valuelist.add(item);
                                    getSelectedItem();
                                } else {
                                    //点击相同按钮
                                    textView.setBackground(mContext.getResources().getDrawable(R.drawable.shape_corner4_white_all));
                                    textView.setTextColor(mContext.getResources().getColor(R.color.date_picker_text_dark));
                                    List<SerachBean.Bean> tempList = new ArrayList<>();
                                    tempList.addAll(valuelist);
                                    for (SerachBean.Bean bean: tempList ) {
                                        if (bean.getName().equals(item.getName())){
                                            valuelist.remove(bean);
                                        }
                                    }
                                    list.get(position).getBean().get(pos).setSelected(false);
                                    getSelectedItem();
                                }
                            } else {
                                //多选
                                //点击相同按钮
                                if (item.getSelected()) {
                                    textView.setBackground(mContext.getResources().getDrawable(R.drawable.shape_corner4_white_all));
                                    textView.setTextColor(mContext.getResources().getColor(R.color.date_picker_text_dark));
                                    List<SerachBean.Bean> tempList = new ArrayList<>();
                                    tempList.addAll(valuelist);
                                    for (SerachBean.Bean bean: tempList ) {
                                        if (bean.getName().equals(item.getName())){
                                            valuelist.remove(bean);
                                        }
                                    }
                                    mResultListener.result(item);
                                    list.get(position).getBean().get(pos).setSelected(false);
                                    getSelectedItem();
                                } else if (valuelist.size() >= type) {
                                    //超过选择数量
                                    Toasty.warning(mContext, "最多只能选择" + type + "个").show();
                                } else if (!item.getSelected()) {
                                    //点击不同按钮
                                    textView.setBackground(mContext.getResources().getDrawable(R.drawable.shape_corner4_2568ff));
                                    textView.setTextColor(Color.WHITE);
//                                    valuelist.add(item);
                                    mResultListener.result(item);
                                    list.get(position).getBean().get(pos).setSelected(true);
                                    getSelectedItem();
                                }

                            }
                            notifyDataSetChanged();
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    MobclickAgent.reportError(mContext, e);
                }
            }
        });
        return view;

    }

    /**
     * 获取选中的item
     */
    private void getSelectedItem(){
        valuelist.clear();
        for (SerachBean serachBean: list) {
            for (SerachBean.Bean tempBean: serachBean.getBean()) {
                if (tempBean.getSelected()){
                    valuelist.add(tempBean);
                }
            }

        }
    }


    /**
     * 根据ListView的当前位置获取分类的首字母的Char ascii值
     */
    public int getSectionForPosition(int position) {
        return list.get(position).getKey().charAt(0);
    }

    /**
     * 获取第一次出现该首字母的List所在的位置
     */
    public int getPositionForSection(int section) {
        for (int i = 0; i < getCount(); i++) {
            String sortStr = list.get(i).getKey();
            char firstChar = sortStr.toUpperCase().charAt(0);
            if (firstChar == section) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 根据ListView的位置获取对应的首字母
     */
    public String getAlpha(int position) {
        return list.get(position).getKey();
    }

    public boolean compare(String content) {
        boolean is = false;
        for (int i = 0; i < valuelist.size(); i++) {
            if (valuelist.get(i).equals(content)) {
                is = true;
                break;
            }
        }
        return is;
    }

    public List<SerachBean.Bean> getInfo() {
        return valuelist;
    }
}
