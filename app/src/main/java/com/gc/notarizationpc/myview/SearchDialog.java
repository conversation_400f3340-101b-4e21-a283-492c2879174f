package com.gc.notarizationpc.myview;


import android.annotation.TargetApi;
import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.adapter.MyAdapter;
import com.gc.notarizationpc.bean.SerachBean;
import com.gc.notarizationpc.util.CharacterParser;
import com.gc.notarizationpc.util.DialogUtils;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopwindowUtil;

import java.util.ArrayList;
import java.util.List;

public class SearchDialog {

    private final static String TAG = "SearchDialog";

    private SearchInterface searchInterface = null;
    private MyAdapter mAlphabetAadpter;

    private List<SerachBean> serachBeans = new ArrayList<>();
    // 中文转拼音工具栏（字母列表实现的核心工具栏）
    private CharacterParser mCharacterParser;
    private Dialog mDialog = null;
    private TextView tv_dialog;
    private EditText et_searchview;
    private TextView tv_search;
    private ListView sortListView;
    private SideBarView sideBarView;
    private LinearLayout make_sure_layout;
    private Button search_cancel;
    private Button search_sure;
    private Context context;
    private boolean needbotom;
    private String hint = "";
    private int type;
    private List<String> namelist = new ArrayList<>();//记录多选时当前已选中的信息


    /**
     * @param context
     * @param context   搜索里的hint值，不传则用默认
     * @param needbotom 下方的取消 确认按钮需要传boolean值
     * @param type      单选多选 单选传1 多选传具体个数（比如允许多选最多3个 值传3）
     */
    @RequiresApi(api = Build.VERSION_CODES.M)
    public SearchDialog(Context context, List<SerachBean> serachBeans, boolean needbotom, @Nullable String hint, int type) {
        this.context = context;
        this.serachBeans = serachBeans;
        this.needbotom = needbotom;
        this.hint = hint;
        this.type = type;
        //搜索的hint需要传入，下方的取消 确认按钮需要传boolean值，单选多选 单选传“1” 多选传具体个数（比如允许多选最多3个 值传“3”）全部都可选传“all”
        mDialog = DialogUtils.getInstance().getCenterDialog(context, false, R.layout.serach_dialog);

        initViews();
        initValues();
        initListeners();
    }

    @TargetApi(Build.VERSION_CODES.M)
    private void initViews() {
        sortListView = mDialog.findViewById(R.id.name_listview);
        sideBarView = mDialog.findViewById(R.id.sidrbar);
        tv_dialog = mDialog.findViewById(R.id.tv_dialog);
        et_searchview = mDialog.findViewById(R.id.et_searchview);
        tv_search = mDialog.findViewById(R.id.tv_search);
        if (!TextUtils.isEmpty(hint))
            et_searchview.setHint(hint);
        make_sure_layout = mDialog.findViewById(R.id.make_sure_layout);
        search_cancel = mDialog.findViewById(R.id.search_cancel);
        search_sure = mDialog.findViewById(R.id.search_sure);
        if (needbotom) {
            make_sure_layout.setVisibility(View.VISIBLE);
        }

    }

    private void initValues() {
        mCharacterParser = new CharacterParser();
        mAlphabetAadpter = new MyAdapter(context, serachBeans, type, new PopwindowUtil.ResultListener<SerachBean.Bean>() {
            @Override
            public void result(SerachBean.Bean value) {
                if (value !=null && value.getName()!=null){
                    for (SerachBean seraBean:serachBeans) {
                        int index = serachBeans.indexOf(seraBean);
                        for (SerachBean.Bean bean:seraBean.getBean()) {
                            if (bean.getName() == value.getName()){
                                int index1 = serachBeans.get(index).getBean().indexOf(bean);
                                if (value.getSelected()){
                                    serachBeans.get(index).getBean().get(index1).setSelected(false);
                                }else{
                                    serachBeans.get(index).getBean().get(index1).setSelected(true);
                                }

                            }
                        }
                    }
                }

            }
        });
        sortListView.setAdapter(mAlphabetAadpter);
        List<String> keys = new ArrayList<>();
        for (int i = 0; i < serachBeans.size(); i++) {
            keys.add(serachBeans.get(i).getKey());
        }
        sideBarView.setCharacters(keys.toArray(new String[keys.size()]));
    }


    @RequiresApi(api = Build.VERSION_CODES.M)
    private void initListeners() {

        search_sure.setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
//                if (mAlphabetAadpter.getInfo() == null || mAlphabetAadpter.getInfo().size() == 0) {
//                    dismiss();
//                    return;
//                }
                searchInterface.GetChoosedMsg(mAlphabetAadpter.getInfo());
                dismiss();
            }
        });
        search_cancel.setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                dismiss();
            }
        });

        mDialog.findViewById(R.id.btn_close).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                dismiss();
            }
        });

        // 设置右侧触摸监听
        sideBarView.setOnTouchingLetterChangedListener(s -> {
            // 该字母首次出现的位置
            Log.d(TAG, "onTouchingLetterChanged:" + s);
            int position = mAlphabetAadpter.getPositionForSection(s.charAt(0));
            tv_dialog.setText(s);
            showDialog();
            if (position != -1) {
                sortListView.setSelection(position);
            }
        });

        // 根据输入框输入值的改变来过滤搜索
        et_searchview.addTextChangedListener(new TextWatcher() {

            @Override
            public void onTextChanged(CharSequence s, int start, int before,
                                      int count) {
                // 当输入框里面的值为空，更新为原来的列表，否则为过滤数据列表
                if (s.toString().isEmpty() && s.toString().length() == 0)
                    startSearch(s.toString());
            }

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });

        tv_search.setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                String text = et_searchview.getText().toString().replace(" ", "");
                if (!TextUtils.isEmpty(text)) {
                    startSearch(text);
                }
            }
        });
    }

    /**
     * 当输入框里面的值为空，更新为原来的列表，否则为过滤数据列表
     */
    private void startSearch(String filterStr) {
        //调接口搜索
        List<SerachBean> startSearchList = new ArrayList<>();
        if (TextUtils.isEmpty(filterStr)) {
            startSearchList = serachBeans;
        } else {
            startSearchList.clear();
            for (int i = 0; i < serachBeans.size(); i++) {
                SerachBean bean = new SerachBean();
                bean.setKey(serachBeans.get(i).getKey());
                List<SerachBean.Bean> beanList = new ArrayList<>();
                for (int j = 0; j < serachBeans.get(i).getBean().size(); j++) {

                    SerachBean.Bean itembean = new SerachBean.Bean();

                    String name = serachBeans.get(i).getBean().get(j).getName();
                    //英文、中文匹配
                    if (name.toUpperCase().indexOf(filterStr.toString().toUpperCase()) != -1
                            || mCharacterParser.getPinYin(name).toUpperCase().contains(filterStr.toString().toUpperCase())) {
                        itembean.setName(name);
                        itembean.setId(serachBeans.get(i).getBean().get(j).getId());
                        beanList.add(itembean);
                    }
                }
                if (beanList != null && beanList.size() > 0) {
                    bean.setBean(beanList);
                    startSearchList.add(bean);
                }
            }
        }
        mAlphabetAadpter.updateListView(startSearchList);
    }


    // 只显示 1.5 秒的字母对话框
    private void showDialog() {
        if (tv_dialog == null)
            return;
        tv_dialog.setVisibility(View.VISIBLE);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                tv_dialog.setVisibility(View.GONE);
            }
        }, 1500);
    }

    public void show() {
        if (mDialog != null && !mDialog.isShowing())
            mDialog.show();
    }

    public void dismiss() {
        if (mDialog != null && mDialog.isShowing()) {
            mDialog.dismiss();
            mDialog = null;
        }
    }

    public void hide() {
        if (mDialog != null) {
            mDialog.hide();
        }
    }

    public void setInterface(SearchInterface searchInterface) {
        this.searchInterface = searchInterface;
    }

    public interface SearchInterface {
        void GetChoosedMsg(List<SerachBean.Bean> o);
    }
}

