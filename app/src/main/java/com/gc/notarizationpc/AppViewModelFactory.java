package com.gc.notarizationpc;

import android.annotation.SuppressLint;
import android.app.Application;

import androidx.annotation.NonNull;
import androidx.annotation.VisibleForTesting;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.gc.notarizationpc.ui.login.LoginViewModel;
import com.gc.notarizationpc.ui.viewmodel.CompariseViewModel;
import com.gc.notarizationpc.ui.viewmodel.CounselingRoomViewModel;
import com.gc.notarizationpc.ui.viewmodel.DeviceRegisterViewModel;
import com.gc.notarizationpc.ui.viewmodel.DomesticEconomyApplyInfoViewModel;
import com.gc.notarizationpc.ui.viewmodel.DomesticEconomyNotarizationViewModel;
import com.gc.notarizationpc.ui.viewmodel.DomesticEconomyTakePhotoViewModel;
import com.gc.notarizationpc.ui.viewmodel.ElectronicRulesViewModel;
import com.gc.notarizationpc.ui.viewmodel.FragmentDomesticEconomyReadAndSignViewModel;
import com.gc.notarizationpc.ui.viewmodel.FragmentDomesticEconomyUploadMaterialViewModel;
import com.gc.notarizationpc.ui.viewmodel.FragmentGridViewModel;
import com.gc.notarizationpc.ui.viewmodel.FragmentMyAppointmentGridViewModel;
import com.gc.notarizationpc.ui.viewmodel.FragmentOrderDetailApplyInformationViewModel;
import com.gc.notarizationpc.ui.viewmodel.HomeViewModel;
import com.gc.notarizationpc.ui.viewmodel.IDCardCompariseMobileViewModel;
import com.gc.notarizationpc.ui.viewmodel.IdentityCheckViewModel;
import com.gc.notarizationpc.ui.viewmodel.InquiryCertificationHomeViewModel;
import com.gc.notarizationpc.ui.viewmodel.InquiryOrderListHomeViewModel;
import com.gc.notarizationpc.ui.viewmodel.InquiryTypeViewModel;
import com.gc.notarizationpc.ui.viewmodel.MobileViewModel;
import com.gc.notarizationpc.ui.viewmodel.OnLineRulesViewModel;
import com.gc.notarizationpc.ui.viewmodel.OrderDetailViewModel;
import com.gc.notarizationpc.ui.viewmodel.ReservationViewModel;
import com.gc.notarizationpc.ui.viewmodel.SelectNotaryViewModel;
import com.gc.notarizationpc.ui.viewmodel.SelfApplyInfoViewModel;
import com.gc.notarizationpc.ui.viewmodel.SelfNotarizationViewModel;
import com.gc.notarizationpc.ui.viewmodel.SelfServiceCertificateHomeViewModel;
import com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderDetailViewModel;
import com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderGridViewModel;
import com.gc.notarizationpc.ui.viewmodel.SelfTakePhotoViewModel;
import com.gc.notarizationpc.ui.viewmodel.TakePhotoViewModel;
import com.gc.notarizationpc.ui.viewmodel.VideoConnectionViewModel;
import com.gc.notarizationpc.ui.viewmodel.VideoNotarizationViewModel;

/**
 * Created by goldze on 2019/3/26.
 */
public class AppViewModelFactory extends ViewModelProvider.NewInstanceFactory {
    @SuppressLint("StaticFieldLeak")
    private static volatile AppViewModelFactory INSTANCE;
    private final Application mApplication;


    public static AppViewModelFactory getInstance(Application application) {
        if (INSTANCE == null) {
            synchronized (AppViewModelFactory.class) {
                if (INSTANCE == null) {
                    INSTANCE = new AppViewModelFactory(application);
                }
            }
        }
        return INSTANCE;
    }

    @VisibleForTesting
    public static void destroyInstance() {
        INSTANCE = null;
    }

    private AppViewModelFactory(Application application) {
        this.mApplication = application;
    }

    @NonNull
    @Override
    public <T extends ViewModel> T create(@NonNull Class<T> modelClass) {
        if (modelClass.isAssignableFrom(LoginViewModel.class)) {
            return (T) new LoginViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(HomeViewModel.class)) {
            return (T) new HomeViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(SelectNotaryViewModel.class)) {
            return (T) new SelectNotaryViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(VideoNotarizationViewModel.class)) {
            return (T) new VideoNotarizationViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(ReservationViewModel.class)) {
            return (T) new ReservationViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(IdentityCheckViewModel.class)) {
            return (T) new IdentityCheckViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(VideoConnectionViewModel.class)) {
            return (T) new VideoConnectionViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(CounselingRoomViewModel.class)) {
            return (T) new CounselingRoomViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(InquiryCertificationHomeViewModel.class)) {
            return (T) new InquiryCertificationHomeViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(InquiryTypeViewModel.class)) {
            return (T) new InquiryTypeViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(InquiryOrderListHomeViewModel.class)) {
            return (T) new InquiryOrderListHomeViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(FragmentGridViewModel.class)) {
            return (T) new FragmentGridViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(SelfServiceOrderGridViewModel.class)) {
            return (T) new SelfServiceOrderGridViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(FragmentMyAppointmentGridViewModel.class)) {
            return (T) new FragmentMyAppointmentGridViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(OrderDetailViewModel.class)) {
            return (T) new OrderDetailViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(FragmentOrderDetailApplyInformationViewModel.class)) {
            return (T) new FragmentOrderDetailApplyInformationViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(TakePhotoViewModel.class)) {
            return (T) new TakePhotoViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(OnLineRulesViewModel.class)) {
            return (T) new OnLineRulesViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(IDCardCompariseMobileViewModel.class)) {
            return (T) new IDCardCompariseMobileViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(DeviceRegisterViewModel.class)) {
            return (T) new DeviceRegisterViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(CompariseViewModel.class)) {
            return (T) new CompariseViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(MobileViewModel.class)) {
            return (T) new MobileViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(SelfApplyInfoViewModel.class)) {
            return (T) new SelfApplyInfoViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(SelfNotarizationViewModel.class)) {
            return (T) new SelfNotarizationViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(SelfServiceCertificateHomeViewModel.class)) {
            return (T) new SelfServiceCertificateHomeViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(ElectronicRulesViewModel.class)) {
            return (T) new ElectronicRulesViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(SelfTakePhotoViewModel.class)) {
            return (T) new SelfTakePhotoViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(SelfServiceOrderDetailViewModel.class)) {
            return (T) new SelfServiceOrderDetailViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(DomesticEconomyNotarizationViewModel.class)) {
            return (T) new DomesticEconomyNotarizationViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(DomesticEconomyApplyInfoViewModel.class)) {
            return (T) new DomesticEconomyApplyInfoViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(FragmentDomesticEconomyUploadMaterialViewModel.class)) {
            return (T) new FragmentDomesticEconomyUploadMaterialViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(FragmentDomesticEconomyReadAndSignViewModel.class)) {
            return (T) new FragmentDomesticEconomyReadAndSignViewModel(mApplication);
        } else if (modelClass.isAssignableFrom(DomesticEconomyTakePhotoViewModel.class)) {
            return (T) new DomesticEconomyTakePhotoViewModel(mApplication);
        }
        throw new IllegalArgumentException("Unknown ViewModel class: " + modelClass.getName());
    }
}
