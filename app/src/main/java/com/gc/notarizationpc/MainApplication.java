package com.gc.notarizationpc;


import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.os.Build;
import android.util.Log;

import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.util.CrashHandler;
import com.umeng.commonsdk.UMConfigure;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import me.goldze.mvvmhabit.base.BaseApplication;
import me.goldze.mvvmhabit.utils.KLog;
//import me.jessyan.autosize.AutoSizeConfig;

/**
 * @ProjectName:
 * @Package: com.gc.shenggx
 * @Description:
 * @Author: xuh<PERSON>feng
 * @CreateDate: 2023/5/23
 */
public class MainApplication extends BaseApplication {

//    public AmazonS3 oosClient;

//    public AmazonS3 getOosClient() {
//        return oosClient;
//    }

    @Override
    public void onCreate() {
        super.onCreate();

//        File internalStorage = this.getFilesDir();
//        String path = internalStorage.getAbsolutePath();
//        Log.e("Test", QbSdk.getTbsVersion(this)+"");
//        if (QbSdk.getTbsVersion(this) <= 0) {
//            copyAssetsToSDCard(this, "tbs", "../storage/emulated/0/Android/data/com.gc.mininotarization/files/tbs");
//        }

        //是否开启日志打印
        KLog.init(true);
        //初始化服务请求地址
        AppConfig.initServerSpServices(this);
        UMConfigure.submitPolicyGrantResult(getApplicationContext(), true);
        UMConfigure.preInit(this, "66545be1940d5a4c495fa33b", "2.0mini");
        UMConfigure.init(this, "66545be1940d5a4c495fa33b", "2.0mini", UMConfigure.DEVICE_TYPE_PHONE, "");
        CrashHandler.getInstance().init(this);
        String webViewVersion = "";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            PackageInfo packageInfo = null;
            try {
                packageInfo = getPackageManager().getPackageInfo("com.android.webview", 0);
                webViewVersion = packageInfo.versionName;
                Log.e("Test", "webViewVersion" + webViewVersion);
            } catch (PackageManager.NameNotFoundException e) {
                e.printStackTrace();
            }
        }

//        QbSdk.reset(getApplicationContext());
//        QbSdk.setTbsListener(new TbsListener() {
//
//            /**
//             * @param stateCode 用户可处理错误码请参考{@link com.tencent.smtt.sdk.TbsCommonCode}
//             */
//            @Override
//            public void onDownloadFinish(int stateCode) {
//                Log.i("Test", "onDownloadFinished: " + stateCode);
//            }
//
//            /**
//             * @param stateCode 用户可处理错误码请参考{@link com.tencent.smtt.sdk.TbsCommonCode}
//             */
//            @Override
//            public void onInstallFinish(int stateCode) {
//                Log.i("Test", "onInstallFinished: " + stateCode);
//                //"安装x5内核完成 重启app
////                CommonUtil.restartApp(MainApplication.this);
////                SPUtils.getInstance().put("");
//            }
//
//            /**
//             * 首次安装应用，会触发内核下载，此时会有内核下载的进度回调。
//             * @param progress 0 - 100
//             */
//            @Override
//            public void onDownloadProgress(int progress) {
//                Log.i("Test", "Core Downloading: " + progress);
//            }
//        });


//        if (QbSdk.getTbsVersion(this) <= 0) {
//            QbSdk.reset(getApplicationContext());
//                    QbSdk.installLocalTbsCore(MainApplication.this, 46007, path + "/tbs" + "/046007_x5.tbs.apk");
//        }

//        /* 此过程包括X5内核的下载、预初始化，接入方不需要接管处理x5的初始化流程，希望无感接入 */
//        QbSdk.initX5Environment(this, new QbSdk.PreInitCallback() {
//            @Override
//            public void onCoreInitFinished() {
//                // 内核初始化完成，可能为系统内核，也可能为系统内核
//            }
//
//            /**
//             * 预初始化结束
//             * 由于X5内核体积较大，需要依赖wifi网络下发，所以当内核不存在的时候，默认会回调false，此时将会使用系统内核代替
//             * 内核下发请求发起有24小时间隔，卸载重装、调整系统时间24小时后都可重置
//             * 调试阶段建议通过 WebView 访问 debugtbs.qq.com -> 安装线上内核 解决
//             * @param isX5 是否使用X5内核
//             */
//            @Override
//            public void onViewInitFinished(boolean isX5) {
//                Log.i("Test", "onViewInitFinished: " + isX5);
//                // hint: you can use QbSdk.getX5CoreLoadHelp(context) anytime to get help.
//            }
//        });


/**********************************在线方案******************************/

        /* [new] 独立Web进程演示 */
//        if (!startX5WebProcessPreinitService()) {
//            return;
//        }

        /* 设置允许移动网络下进行内核下载。默认不下载，会导致部分一直用移动网络的用户无法使用x5内核 */
//        QbSdk.setDownloadWithoutWifi(true);

//        QbSdk.setCoreMinVersion(QbSdk.CORE_VER_ENABLE_202207);
        /* SDK内核初始化周期回调，包括 下载、安装、加载 */


        // 设置语言
//        GreenDaoDBManager.getInstance().setContext(this);

        //创建选项对象
//        S3ClientOptions options = new S3ClientOptions();
//        //可以使用 V4 签名，也可以使用 V2 签名，false 为 V2 签名，使用系统属性来设置
//        System.setProperty(SDKGlobalConfiguration.ENABLE_S3_SIGV4_SYSTEM_PROPERTY, "false");
//        //V4 签名可以用负载参与签名、本例设置负载不参与签名
//        options.setPayloadSigningEnabled(false);
        //使用 AK 和 SK 创建密码以及配置对象创建 client
//        oosClient = new AmazonS3Client(
//                new PropertiesCredentials(AppConfig.AccessKey, AppConfig.SecretKey));
//        //设置 endpoint
//        oosClient.setEndpoint(AppConfig.endPoint);
//        //设置选项
//        oosClient.setS3ClientOptions(options);

        //内存泄漏检测
//        if (!LeakCanary.isInAnalyzerProcess(this)) {
//            LeakCanary.install(this);
//        }

        //在需要字体不随系统字体放大缩小而变化时，需要设置为 true
//        AutoSizeConfig.getInstance()
//                .setUseDeviceSize(true)//是否使用设备的实际尺寸,默认为false
//                .setCustomFragment(true)//是否激活fragment的自适应能力，默认为false
//                .setExcludeFontScale(true);//设置是否使用系统字体大小，即不随系统字体放大缩小而变化时，需要设置为true

    }

    @Override
    public void onTerminate() {
        super.onTerminate();
    }

    /**
     * 启动X5 独立Web进程的预加载服务。优点：
     * 1、后台启动，用户无感进程切换
     * 2、启动进程服务后，有X5内核时，X5预加载内核
     * 3、Web进程Crash时，不会使得整个应用进程crash掉
     * 4、隔离主进程的内存，降低网页导致的App OOM概率。
     * <p>
     * 缺点：
     * 进程的创建占用手机整体的内存，demo 约为 150 MB
     */
//    private boolean startX5WebProcessPreinitService() {
//        String currentProcessName = QbSdk.getCurrentProcessName(this);
//        // 设置多进程数据目录隔离，不设置的话系统内核多个进程使用WebView会crash，X5下可能ANR
//        WebView.setDataDirectorySuffix(QbSdk.getCurrentProcessName(this));
//        Log.i("Test", currentProcessName);
//        if (currentProcessName.equals(this.getPackageName())) {
//            this.startService(new Intent(this, X5ProcessInitService.class));
//            return true;
//        }
//        return false;
//    }
    public static void copyAssetsToSDCard(Context context, String sourceFolder, String destinationFolder) {
        AssetManager assetManager = context.getAssets();
        String[] files;
        try {
            // 获取assets文件夹下的所有文件和子文件夹
            files = assetManager.list(sourceFolder);
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }

        // 创建目标文件夹
        File destFolder = new File(destinationFolder);
        if (!destFolder.exists()) {
            destFolder.mkdirs();
        }

        for (String filename : files) {
            InputStream in = null;
            OutputStream out = null;
            try {
                // 从assets中打开文件
                in = assetManager.open(sourceFolder + "/" + filename);
                // 指定输出目标文件
                File outFile = new File(destinationFolder, filename);
                out = new FileOutputStream(outFile);
                // 将文件内容复制到目标文件
                Log.i("Qbsdk", "copy 开始");
                copyFile(in, out);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (in != null) {
                        in.close();
                    }
                    if (out != null) {
                        out.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private static void copyFile(InputStream in, OutputStream out) throws IOException {
        try {
            byte[] buffer = new byte[1024];
            int read;
            Log.e("Qbsdk", "copy 进行中...");
            while ((read = in.read(buffer)) != -1) {
                out.write(buffer, 0, read);
            }
            Log.e("Qbsdk", "copy 文件成功");
        } catch (Exception e) {
            e.printStackTrace();
            Log.e("Qbsdk", "copy 文件失败" + e.getMessage());
        }

    }

}
