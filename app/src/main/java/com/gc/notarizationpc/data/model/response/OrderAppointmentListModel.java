package com.gc.notarizationpc.data.model.response;

/**
 * applicantId	申办人Id	string
 * caseInfoId	案件ID	string
 * caseRemarks	备注	string
 * createdTime	创建时间	string
 * endTime	预约结束时间	string
 * id	订单编号	string
 * notarialId	公证处Id	string
 * notarialName	公证处名称	string
 * officeId	公证员ID	string
 * officeName	公证员姓名	string
 * roomId	房间ID	string
 * startTime	预约开始时间	string
 * status	状态;本系统中：-1-待认领，1-已认领	integer(int32)
 * userName	申办人名称	string
 */

public class OrderAppointmentListModel {

    public String getApplicantId() {
        return applicantId;
    }

    public void setApplicantId(String applicantId) {
        this.applicantId = applicantId;
    }

    public String getCaseInfoId() {
        return caseInfoId;
    }

    public void setCaseInfoId(String caseInfoId) {
        this.caseInfoId = caseInfoId;
    }

    public String getCaseRemarks() {
        return caseRemarks;
    }

    public void setCaseRemarks(String caseRemarks) {
        this.caseRemarks = caseRemarks;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNotarialId() {
        return notarialId;
    }

    public void setNotarialId(String notarialId) {
        this.notarialId = notarialId;
    }

    public String getNotarialName() {
        return notarialName;
    }

    public void setNotarialName(String notarialName) {
        this.notarialName = notarialName;
    }

    public String getOfficeId() {
        return officeId;
    }

    public void setOfficeId(String officeId) {
        this.officeId = officeId;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    private String applicantId;
    private String caseInfoId;
    private String caseRemarks;
    private String createdTime;
    private String endTime;
    private String id;
    private String notarialId;
    private String notarialName;
    private String officeId;
    private String officeName;
    private String roomId;
    private String startTime;
    private Integer status;
    private String userName;
    private String statusString;

    public String getStatusString() {
        return statusString;
    }

    public void setStatusString(String statusString) {
        this.statusString = statusString;
    }
}
