package com.gc.notarizationpc.data.model.response;

import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName:文书
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2024/4/10
 */
public class DocInfoResponse implements Serializable {
    private String applicantId;
    private String applicantName;
    private Integer applicantType;// 1，2代理人，申请人  3：企业
    private String credentialNum;
    private Integer credentialType;
    private boolean choose;

    public boolean isChoose() {
        return choose;
    }

    public void setChoose(boolean choose) {
        this.choose = choose;
    }

    private List<DocTypeListDTO> docTypeList;

    public String getApplicantId() {
        return applicantId;
    }

    public void setApplicantId(String applicantId) {
        this.applicantId = applicantId;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public Integer getApplicantType() {
        return applicantType;
    }

    public void setApplicantType(Integer applicantType) {
        this.applicantType = applicantType;
    }

    public String getCredentialNum() {
        return credentialNum;
    }

    public void setCredentialNum(String credentialNum) {
        this.credentialNum = credentialNum;
    }

    public Integer getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(Integer credentialType) {
        this.credentialType = credentialType;
    }

    public List<DocTypeListDTO> getDocTypeList() {
        return docTypeList;
    }

    public void setDocTypeList(List<DocTypeListDTO> docTypeList) {
        this.docTypeList = docTypeList;
    }

    public static class DocTypeListDTO {
        private List<DocumentVOListDTO> documentVOList;
        private String typeName;

        public List<DocumentVOListDTO> getDocumentVOList() {
            return documentVOList;
        }

        public void setDocumentVOList(List<DocumentVOListDTO> documentVOList) {
            this.documentVOList = documentVOList;
        }

        public String getTypeName() {
            return typeName;
        }

        public void setTypeName(String typeName) {
            this.typeName = typeName;
        }

        public static class DocumentVOListDTO {
            private Integer canSign;//canSign表示这个人能不能签字
            private String documentName;
            private String id;
            private Integer initSign;
            private Integer minReadTime;
            private Integer personSignStatus;//表示如果这人能签字的话，那这个人的签署状态是签了还没没钱
            private String signDocumentFile;
            private List<String> signDocumentFileUrl;
            private Integer signStatus;
            private String unsignDocumentFile;
            private List<String> unsignDocumentFileUrl;
            private boolean choose;

            public boolean isChoose() {
                return choose;
            }

            public void setChoose(boolean choose) {
                this.choose = choose;
            }

            public List<String> getSignDocumentFileUrl() {
                return signDocumentFileUrl;
            }

            public void setSignDocumentFileUrl(List<String> signDocumentFileUrl) {
                this.signDocumentFileUrl = signDocumentFileUrl;
            }

            public List<String> getUnsignDocumentFileUrl() {
                return unsignDocumentFileUrl;
            }

            public void setUnsignDocumentFileUrl(List<String> unsignDocumentFileUrl) {
                this.unsignDocumentFileUrl = unsignDocumentFileUrl;
            }

            public Integer getCanSign() {
                return canSign;
            }

            public void setCanSign(Integer canSign) {
                this.canSign = canSign;
            }

            public String getDocumentName() {
                return documentName;
            }

            public void setDocumentName(String documentName) {
                this.documentName = documentName;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public Integer getInitSign() {
                return initSign;
            }

            public void setInitSign(Integer initSign) {
                this.initSign = initSign;
            }

            public Integer getMinReadTime() {
                return minReadTime == null ? 0 : minReadTime;
            }

            public void setMinReadTime(Integer minReadTime) {
                this.minReadTime = minReadTime;
            }

            public Integer getPersonSignStatus() {
                return personSignStatus;
            }

            public void setPersonSignStatus(Integer personSignStatus) {
                this.personSignStatus = personSignStatus;
            }

            public String getSignDocumentFile() {
                return signDocumentFile;
            }

            public void setSignDocumentFile(String signDocumentFile) {
                this.signDocumentFile = signDocumentFile;
            }

            public Integer getSignStatus() {
                return signStatus;
            }

            public void setSignStatus(Integer signStatus) {
                this.signStatus = signStatus;
            }

            public String getUnsignDocumentFile() {
                return unsignDocumentFile;
            }

            public void setUnsignDocumentFile(String unsignDocumentFile) {
                this.unsignDocumentFile = unsignDocumentFile;
            }
        }
    }
}
