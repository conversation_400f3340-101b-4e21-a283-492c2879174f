package com.gc.notarizationpc.data.model.response;


public class OrderRecordPredetermineListModel {


    public String getApplicantId() {
        return applicantId;
    }

    public void setApplicantId(String applicantId) {
        this.applicantId = applicantId;
    }

    public String getCaseInfoId() {
        return caseInfoId;
    }

    public void setCaseInfoId(String caseInfoId) {
        this.caseInfoId = caseInfoId;
    }

    public String getCaseRemarks() {
        return caseRemarks;
    }

    public void setCaseRemarks(String caseRemarks) {
        this.caseRemarks = caseRemarks;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNotarialId() {
        return notarialId;
    }

    public void setNotarialId(String notarialId) {
        this.notarialId = notarialId;
    }

    public String getNotarialName() {
        return notarialName;
    }

    public void setNotarialName(String notarialName) {
        this.notarialName = notarialName;
    }

    public String getOfficeId() {
        return officeId;
    }

    public void setOfficeId(String officeId) {
        this.officeId = officeId;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    private String applicantId;
    private String caseInfoId;
    private String caseRemarks;
    private String createdTime;
    private String endTime;
    private String id;
    private String notarialId;
    private String notarialName;
    private String officeId;
    private String officeName;
    private String startTime;
    private Integer status;
    private String userName;
}
