package com.gc.notarizationpc.data.model.request;

import android.os.Parcel;
import android.os.Parcelable;

import java.io.Serializable;

public class ImageBean implements Serializable {
    public String unitGuid;//44cdcd0f-9f06-4741-b080-6e1504644792,
    public String userId;//null,
    public String createDate;//2020-11-25 10;//52;//52,
    public String fileName;//2020-11-25 10;//52;//39.jpg,
    public String fileSize;//62,
    public String fileType;//jpg,
    public String groupId;//null,
    public String filePath;//group1/M00/01/0A/CgUFPF-9xwSAVPtoAAD5pJwhdwM650.jpg,
    public String code;//null,
    public String statusInfo;//


    /**
     * @Author:WeJeson
     * @Date:2024/1/31
     * @CreateTime: 10:00
     * @PackageName:com.gc.notarizationpc.data.model
     */
    public static class OfficeListRequest implements Parcelable {

        private String appId;
        private String city;
        private String mechanismName;
        private String province;
        private Integer searchType;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getMechanismName() {
            return mechanismName;
        }

        public void setMechanismName(String mechanismName) {
            this.mechanismName = mechanismName;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public Integer getSearchType() {
            return searchType;
        }

        public void setSearchType(Integer searchType) {
            this.searchType = searchType;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {

        }
    }
}