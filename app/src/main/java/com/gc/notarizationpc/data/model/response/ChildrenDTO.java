package com.gc.notarizationpc.data.model.response;

import java.util.ArrayList;
import java.util.List;

public class ChildrenDTO {

    @com.google.gson.annotations.SerializedName("children")
    private java.util.List<ChildrenDTO> children;
    /**
     * dtlId
     */
    @com.google.gson.annotations.SerializedName("dtlId")
    private Integer dtlId;
    /**
     * haveSublevel
     */
    @com.google.gson.annotations.SerializedName("haveSublevel")
    private Boolean haveSublevel;
    /**
     * id
     */
    @com.google.gson.annotations.SerializedName("id")
    private Integer id;
    /**
     * materialsListsName
     */
    @com.google.gson.annotations.SerializedName("materialsListsName")
    private String materialsListsName;
    /**
     * mattersCode
     */
    @com.google.gson.annotations.SerializedName("mattersCode")
    private String mattersCode;
    /**
     * mattersName
     */
    @com.google.gson.annotations.SerializedName("mattersName")
    private String mattersName;
    /**
     * notarizationFee
     */
    @com.google.gson.annotations.SerializedName("notarizationFee")
    private Integer notarizationFee;
    /**
     * parentMattersCode
     */
    @com.google.gson.annotations.SerializedName("parentMattersCode")
    private String parentMattersCode;


    public List<ChildrenDTO> getChildren() {
        return children;
    }

    public void setChildren(List<ChildrenDTO> children) {
        this.children = (children == null) ? new ArrayList<ChildrenDTO>() : children;
    }

    public Integer getDtlId() {
        return dtlId;
    }

    public void setDtlId(Integer dtlId) {
        this.dtlId = dtlId == null ? 0 : dtlId;
    }

    public Boolean getHaveSublevel() {
        return haveSublevel;
    }

    public void setHaveSublevel(Boolean haveSublevel) {
        this.haveSublevel = haveSublevel == null ? false : haveSublevel;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id == null ? 0 : id;
    }

    public String getMaterialsListsName() {
        return materialsListsName;
    }

    public void setMaterialsListsName(String materialsListsName) {
        this.materialsListsName = materialsListsName == null ? "" : materialsListsName;
    }

    public String getMattersCode() {
        return mattersCode;
    }

    public void setMattersCode(String mattersCode) {
        this.mattersCode = mattersCode == null ? "" : mattersCode;
    }

    public String getMattersName() {
        return mattersName;
    }

    public void setMattersName(String mattersName) {
        this.mattersName = mattersName ==   null ? "" : mattersName;
    }

    public Integer getNotarizationFee() {
        return notarizationFee;
    }

    public void setNotarizationFee(Integer notarizationFee) {
        this.notarizationFee = notarizationFee == null ? 0 : notarizationFee;
    }

    public String getParentMattersCode() {
        return parentMattersCode;
    }

    public void setParentMattersCode(String parentMattersCode) {
        this.parentMattersCode = parentMattersCode == null ? "" : parentMattersCode;
    }
}
