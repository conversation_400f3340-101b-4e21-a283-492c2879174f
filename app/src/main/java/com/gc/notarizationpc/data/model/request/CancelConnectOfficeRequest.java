package com.gc.notarizationpc.data.model.request;

import java.io.Serializable;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.request
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/4/2
 */
public class CancelConnectOfficeRequest implements Serializable {

    private String officeId;
    private String recordId;
    private Integer recordType;
    private String userId;

    public String getOfficeId() {
        return officeId;
    }

    public void setOfficeId(String officeId) {
        this.officeId = officeId;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public Integer getRecordType() {
        return recordType;
    }

    public void setRecordType(Integer recordType) {
        this.recordType = recordType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
