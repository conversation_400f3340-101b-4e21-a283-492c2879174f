package com.gc.notarizationpc.data;

import com.gc.notarizationpc.bean.CaptchaCheckIt;
import com.gc.notarizationpc.bean.CaptchaGetIt;
import com.gc.notarizationpc.data.model.request.AddSelfServiceRequest;
import com.gc.notarizationpc.data.model.request.CancelConnectOfficeRequest;
import com.gc.notarizationpc.data.model.request.ConnectNotaryRequest;
import com.gc.notarizationpc.data.model.request.LineOfficeRequest;
import com.gc.notarizationpc.data.model.request.LoginOnRequest;
import com.gc.notarizationpc.data.model.request.MacAddressRequest;
import com.gc.notarizationpc.data.model.request.MakeAppointmentRequest;
import com.gc.notarizationpc.data.model.request.OfficeListRequest;
import com.gc.notarizationpc.data.model.request.QzfLoginInfo;
import com.gc.notarizationpc.data.model.request.SignDocRequest;
import com.gc.notarizationpc.data.model.request.StartApplyRequest;
import com.gc.notarizationpc.data.model.request.UpdateSelfServiceMaterialRequest;
import com.gc.notarizationpc.data.model.request.VideoAddRequest;
import com.gc.notarizationpc.data.model.response.ApplyInfoResponse;
import com.gc.notarizationpc.data.model.response.AreaModel;
import com.gc.notarizationpc.data.model.response.BindingNotaryOfficeModel;
import com.gc.notarizationpc.data.model.response.CfgResponse;
import com.gc.notarizationpc.data.model.response.DocInfoResponse;
import com.gc.notarizationpc.data.model.response.EnterpriseCertificateTypeResponse;
import com.gc.notarizationpc.data.model.response.FeeInfoResponse;
import com.gc.notarizationpc.data.model.response.FileInformationModel;
import com.gc.notarizationpc.data.model.response.HomeModuleFunctionResponse;
import com.gc.notarizationpc.data.model.response.Input;
import com.gc.notarizationpc.data.model.response.LanguageModel;
import com.gc.notarizationpc.data.model.response.ListDocumentResponse;
import com.gc.notarizationpc.data.model.response.ListRequiredMaterialResponse;
import com.gc.notarizationpc.data.model.response.LoginResponse;
import com.gc.notarizationpc.data.model.response.MachineBindAreaResponse;
import com.gc.notarizationpc.data.model.response.MaterialListResponse;
import com.gc.notarizationpc.data.model.response.NotarialOfficeListBean;
import com.gc.notarizationpc.data.model.response.NotaryAffairResponseModel;
import com.gc.notarizationpc.data.model.response.NotaryItemModel;
import com.gc.notarizationpc.data.model.response.NotaryMatterItem;
import com.gc.notarizationpc.data.model.response.NotaryOfficeModel;
import com.gc.notarizationpc.data.model.response.OrderAppointmentListModel;
import com.gc.notarizationpc.data.model.response.PersonDocumentType;
import com.gc.notarizationpc.data.model.response.SelfServiceOrderDetailModel;
import com.gc.notarizationpc.data.model.response.SelfServiceOrderListModel;
import com.gc.notarizationpc.data.model.response.SimilarityResponse;
import com.gc.notarizationpc.data.model.response.UpgradeResponse;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.data.model.response.VideoOrderDetailModel;
import com.gc.notarizationpc.data.model.response.VideoOrderListModel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import me.goldze.mvvmhabit.http.BaseResponse;
import me.goldze.mvvmhabit.utils.CommonUtil;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Part;
import retrofit2.http.Path;
import retrofit2.http.Query;

/**
 * 网络接口层
 */
public interface ApiService {

    class HttpConstant {
        private static final String USER_URL = "/user-api/";

        private static final String ANNEX = "/annex/";

        private static final String ADMIN = "/admin/";

        private static final String NTRZS = "/ntrzs/";
        private static final String LOGIN_ON = USER_URL + "sys/login/loginOn";

        private static final String uploadFile = ANNEX + "sys/annex/fastDFSUpload";

        private static final String LINE_OFFICE = ADMIN + "v1/institutional/mobile/line/office";

        //公证处公证员列表
        private static final String LIST_WECHAT_NOTARY_OFFICE_BY_REGION = ADMIN + "v1/integratedMachine/listNotaryOfficeByRegion";
        //登录
        private static final String LOGIN_BY_MAC = "/auth/oauth/token?grant_type=machine";
        //首页快捷入口
        private static final String GET_HOME_MODULE = ADMIN + "v1/device/cfg/getDeviceByMac";
        // 获取公证处和语言接口
        private static final String GET_NOTARY_AREA_LIST = NTRZS + "nation/nationList";
        private static final String GET_NOTARY_LANGUAGE_LIST = ADMIN + "dict/type/language";
        // 预约办证
        private static final String ADD_NOTARY_MATTERS = NTRZS + "v1/integratedMachine/predetermine/add";
        // 获取短信验证码
        private static final String GET_MESSAGE_CODE = NTRZS + "v1/integratedMachine/requireSmsCode/";
        // 获取公证处列表
        private static final String GET_MACHINE_BY_MAC = ADMIN + "v1/device/getMachineByMac/";
        // 获取公证事项
        private static final String GET_NOTARY_LIST_MATTERS = NTRZS + "v1/recordPredetermine/machine/listmatters";

        // 案件详情
        private static final String GET_CASE_DETAIL = NTRZS + "v1/caseInfo/appOrderGetById";

        // 案件视频订单列表
        private static final String GET_VIDEO_ORDER_LIST = NTRZS + "v1/integratedMachine/listCaseListPage";

        // 自助订单列表
        private static final String SELF_PAGE = NTRZS + "v1/integratedMachine/self/page";

        // 预约列表
        private static final String GET_APPOINTMENT_LIST = NTRZS + "v1/integratedMachine/predetermine/page";

        //视频受理创建订单
        private static final String ADD_VIDEO_ORDER = NTRZS + "v1/integratedMachine/add/videoAuth/record";
        //视频咨询连线创建订单
        private static final String ADD_VIDEO_LINE_ORDER = NTRZS + "v1/integratedMachine/videoGuidance/add/record";

        //匹配公证员
        private static final String MATCH_NOTARY = ADMIN + "v1/integratedMachine/matchNotary";
        //文件上传
        private static final String UPLOAD = NTRZS + "v1/file/upload2";
        //开始申办
        private static final String START_APPLY = ADMIN + "v1/integratedMachine/apply";
        //根据mac地址查询关联的所属区域
        private static final String FIND_INTEGRATED_MACHINE_BIND_AREA = ADMIN + "v1/integratedMachine/findIntegratedMachineBindArea";
        //根据公证人员ID查询公证人员状态
        private static final String REFRESH_OFFICE_STATE_BY_OFFICE_ID = ADMIN + "v1/integratedMachine/refreshOfficeStateByOfficeId";
        //指定连线公证人员
        private static final String LINE_NOTARY = ADMIN + "v1/integratedMachine/line/office";
        //视频咨询指定连线公证人员
        private static final String CONSULT_LINE_NOTARY = ADMIN + "v1/integratedMachine/videoGuidance/line/office";
        //取消链接公证员
        private static final String CANCEL_CONNECT_OFFICER = NTRZS + "v1/integratedMachine/cancelConnectOfficer";
        //取消公证处匹配
        private static final String CANCEL_NOTARY_MATCH = NTRZS + "v1/integratedMachine/cancelNotaryMatch";
        //查询申请信息
        private static final String GET_CASE_INFO = NTRZS + "v1/integratedMachine/getCaseInfo/";
        //查询缴费清单
        private static final String GET_PAYMENT_LIST = NTRZS + "v1/integratedMachine/getPaymentList";
        //查询文书列表(当事人及文书)
        private static final String LIST_APPLICANTS_AND_DOCUMENTS = NTRZS + "v1/integratedMachine/listApplicantsAndDocuments";
        //公证文书签名(批量)
        private static final String SIGN_MULTI_DOCUMENT = NTRZS + "v1/integratedMachine/signMultiDocument";
        // 上传材料模块  查询所需材料
        private static final String LIST_MATERIAL_BY_CASE_INFO_ID = NTRZS + "v1/integratedMachine/listMaterialByCaseInfoId/";
        // 视频订单详情
        private static final String GET_VIDEO_ORDER_DETAIL = NTRZS + "v1/integratedMachine/getCaseInfoDtl/";
        // 公证用途
        private static final String GET_NOTARY_USE = ADMIN + "dict/type/";
        // 终止案件
        private static final String TERMINATE_CASE = NTRZS + "v1/integratedMachine/terminationCase";
        // 上传材料模块  上传材料
        private static final String UPLOAD_MATERIAL = NTRZS + "v1/integratedMachine/uploadMaterial";//todo
        // 清空上传
        private static final String CLEAR_MATERIAL_AND_RE_UPLOAD_BY_USER = NTRZS + "v1/integratedMachine/clearMaterialAndReUploadByUser";
        // 获得设备配置管理
        private static final String GET_CFG = ADMIN + "v1/integratedMachine/getCfg";
        // 视频咨询视频连线成功之后更新咨询记录状态
        private static final String JUST_STATE = NTRZS + "v1/integratedMachine/videoGuidance/upd/justState";
        // 视频咨询查询其他材料列表
        private static final String LIST_OTHER_MATERIAL = NTRZS + "v1/integratedMachine/videoGuidance/listOtherMaterial";
        // 视频咨询上传材料
        private static final String UPLOAD_OTHER_MATERIAL_BIND_RECORD = NTRZS + "v1/integratedMachine/videoGuidance/uploadOtherMaterialBindRecord";
        // 视频咨询删除材料
        private static final String DEL_OTHER_MATERIAL = NTRZS + "v1/integratedMachine/videoGuidance/delOtherMaterial";
        // 获取用户的userID
        private static final String GET_USER_INFORMATION = ADMIN + "v1/integratedMachine/userInfo";
        // 验证手机号收到的短信验证码
        private static final String VALIDATE_CODE = NTRZS + "v1/integratedMachine/validateCode";

        // 受理室定位报告
        private static final String LOCATION_REPORT = NTRZS + "v1/integratedMachine/locationReport";
        // app升级
        private static final String UPGRADE_APP = ADMIN + "v1/integratedMachine/upgradeApp";
        // 人脸对比相似度，生成pdf
        private static final String UNIVERSAL_SIMILARITY = NTRZS + "v1/face/universalSimilarity";//todo
        // 根据用途获取事项 useType 出国留学--1->学习 定居移民--2->定居 探亲旅游--3->探亲 商务劳务--5-->劳务 境外其他--4-->其他（废弃）
        private static final String GET_MATTERS_BY_USE_TYPE = NTRZS + "v1/integratedMachine/self/getMattersByUseType";
        // 公证处列表
        private static final String LIST_NOTARY_BY_REGION = ADMIN + "v1/integratedMachine/listNotaryByRegion";
        //个人证件类型
        private static final String DOCUMENT_TYPE_PERSON = ADMIN + "dict/type/document_type_person";
        //企业证件类型
        private static final String ENTERPRISE_CERTIFICATE_TYPE = ADMIN + "dict/type/enterprise_certificate_type";
        //自助办证--新增自助办理记录
        private static final String ADD_SELF_SERVICE = NTRZS + "v1/integratedMachine/self/addSelfService";
        //自助办证--小一体机新增用户
        private static final String USER_ADD = ADMIN + "v1/integratedMachine/userAdd";
        //自助办证-查询所需材料
        private static final String LIST_REQUIRED_MATERIAL = NTRZS + "v1/integratedMachine/self/listRequiredMaterial/";
        //案件预受理-小一体机-查询所需材料
        private static final String LIST_REQUIRED_MATERIAL_RECORD_PRE_PROCESS = NTRZS + "v1/recordPreProcess/integratedMachine/listRequiredMaterial/";
        //自助办证-更新材料
        private static final String UPDATE_SELF_SERVICE_MATERIAL = NTRZS + "v1/integratedMachine/self/updateSelfServiceMaterial";//todo
        //案件预受理-小一体机-更新材料
        private static final String UPDATE_RECORD_PRE_PROCESS_MATERIAL = NTRZS + "v1/recordPreProcess/integratedMachine/updateMaterial";
        //自助办证-获取文书
        private static final String LIST_DOCUMENT = NTRZS + "v1/integratedMachine/self/listDocument";
        //案件预受理-小一体机-获取文书
        private static final String RECORD_PRE_LIST_DOCUMENT = NTRZS + "v1/recordPreProcess/integratedMachine/listDocument";
        // 自助办证-文书签字
        private static final String SIGN_DOCUMENT = NTRZS + "v1/integratedMachine/self/signDocument";
        // 自助办证-删除自助订单
        private static final String SELF_DELETE = NTRZS + "v1/integratedMachine/self/delete";
        // 自助办证-自助详情查询
        private static final String SELF_SERVICE_ORDER_DETAIL = NTRZS + "v1/integratedMachine/self/dtl/";
        // 自助办证-终止申办
        private static final String END_APPLY = NTRZS + "v1/integratedMachine/self/endApply";
        // 获取公证事项（废弃）
        private static final String NOTARY_ITEM_BY_USER = ADMIN + "/dict/type/user";
        //自助办证--新增预受理办理记录
        private static final String ADD_PRE_PROCESS = NTRZS + "v1/recordPreProcess/integratedMachine/add";
        // 案件预受理-小一体机-完成提交
        private static final String RECORD_PRE_PROCESS_SUBMIT = NTRZS + "v1/recordPreProcess/integratedMachine/submit/";
        // 案件预受理-小一体机--查询所有事项
        private static final String RECORD_PRE_PROCESS_LIST_MATTERS = NTRZS + "v1/recordPreProcess/integratedMachine/listMatters";
        // 获取sm2公钥
        private static final String GET_PUBLIC_KEY = "/accessKey";
        // 视频受理室-缴费
        private static final String PAY_MENT_RECORDS = NTRZS + "v1/paymentRecord/integratedMachine/payMentRecords";
        // 自助办证获取办证用途
        private static final String GET_PURPOSE = ADMIN + "/dict/type/usage_classification";
        // 自助办证获取事项
        private static final String GET_SELF_MATTERS = NTRZS + "/matters/info/self/getSelfMatters";

        // 人脸对比相似度，生成pdf(新)
        private static final String FACE_DETECT_TO_PDF = NTRZS + "v1/face/machine/faceDetectToPDF";

        // 获取图形验证码
        private static final String GET_GRAPH_CODE = "/code/";

        // 校验图形验证码
        private static final String CHECK_CODE = "/code/check";

    }

    @POST(HttpConstant.LOGIN_ON)
    Observable<BaseResponse<QzfLoginInfo>> LoginOn(@Body LoginOnRequest request);


    /**
     * 单个文件上传接口
     */
    @Multipart
    @POST(HttpConstant.uploadFile)
    Observable<BaseResponse<String[]>> uploadSingleFile(@Part MultipartBody.Part imageFile);

    /**
     * 获取公证员列表
     */
    @POST(HttpConstant.LIST_WECHAT_NOTARY_OFFICE_BY_REGION)
    Observable<BaseResponse<List<NotarialOfficeListBean>>> getAllOfficerListAPI(@Body OfficeListRequest request);


    /**
     * 匹配公证员
     */
    @POST(HttpConstant.LINE_OFFICE)
    Observable<BaseResponse<String[]>> lineOffice(@Body LineOfficeRequest request);


    /**
     * 根据mac地址登录
     */
    @Headers({"Authorization:Basic bWFjaGluZTptYWNoaW5l", "Tenant-Id:1"})
    @GET(HttpConstant.LOGIN_BY_MAC)
    Observable<BaseResponse<LoginResponse>> loginByMac(@Query("macAddress") String grantType);

    /**
     * 首页快捷入口
     */
    @FormUrlEncoded
    @POST(HttpConstant.GET_HOME_MODULE)
    Observable<BaseResponse<HomeModuleFunctionResponse>> getHomeModule(@Field("macAddress") String macAddress);

    /**
     * 获取公证地区列表
     */
    @GET(HttpConstant.GET_NOTARY_AREA_LIST)
    Observable<BaseResponse<List<AreaModel>>> getNotaryAreaList();

    /**
     * 获取公证语言列表
     */
    @GET(HttpConstant.GET_NOTARY_LANGUAGE_LIST)
    Observable<BaseResponse<List<LanguageModel>>> getNotaryLanguageList();

    /**
     * 预约办证
     */
    @POST(HttpConstant.ADD_NOTARY_MATTERS)
    Observable<BaseResponse> addNotaryMatters(@Body MakeAppointmentRequest request);

    /**
     * 获取短信验证码
     */
    @POST(HttpConstant.GET_MESSAGE_CODE)
    Observable<BaseResponse> getMessageCode(@Body HashMap<String, String> mobile);

    /**
     * 视频订单详情
     */
    @GET(HttpConstant.GET_VIDEO_ORDER_DETAIL + "{caseInfoId}")
    Observable<BaseResponse<VideoOrderDetailModel>> getVideoOrderDetail(@Path("caseInfoId") String caseInfoId);

    /**
     * 获取公证处列表
     */
    @GET(HttpConstant.GET_MACHINE_BY_MAC)
    Observable<BaseResponse<NotaryOfficeModel>> getMachineByMac(HashMap<String, String> macAddress);

    /**
     * 获取公证事项
     */
    @GET(HttpConstant.GET_NOTARY_LIST_MATTERS)
    Observable<BaseResponse<List<NotaryAffairResponseModel>>> getNotaryMatters();

    /**
     * 案件预受理-小一体机--查询所有事项
     */
    @GET(HttpConstant.RECORD_PRE_PROCESS_LIST_MATTERS)
    Observable<BaseResponse<List<NotaryMatterItem>>> recordPreProcessListMatters(@Query("mechanismId") String mechanismId);

    /**
     * 获取案件详情
     */
    @GET(HttpConstant.GET_CASE_DETAIL)
    Observable<BaseResponse<VideoOrderDetailModel>> getCaseDetail(@Path("id") String id);

    /**
     * 案件订单列表
     */

//    @FormUrlEncoded
    @POST(HttpConstant.GET_VIDEO_ORDER_LIST)
//    @Headers({"content-type:application/x-www-form-urlencoded"})
    Observable<BaseResponse<VideoOrderListModel>> getOrderList(@Body Map<String, Object> dataSource);

    /**
     * 自助订单列表
     */
    @POST(HttpConstant.SELF_PAGE)
    Observable<BaseResponse<SelfServiceOrderListModel>> getSelfOrderList(@Body Map<String, Object> dataSource);

    /**
     * 预约列表
     */
    @POST(HttpConstant.GET_APPOINTMENT_LIST)
    Observable<BaseResponse<List<OrderAppointmentListModel>>> getAppointmentList(@Body Map<String, Object> dataSource);


    /**
     * 视频受理室新增
     */
    @POST(HttpConstant.ADD_VIDEO_ORDER)
    Observable<BaseResponse<String>> addVideoOrder(@Body VideoAddRequest request);

    /**
     * 视频咨询连线新增
     */
    @POST(HttpConstant.ADD_VIDEO_LINE_ORDER)
    Observable<BaseResponse<String>> addVideoLineOrder(@Body VideoAddRequest request);


    /**
     * 单个文件上传接口
     */
    @Multipart
    @POST(HttpConstant.UPLOAD)
    Observable<BaseResponse<UploadFileBean>> uploadFile(@Part MultipartBody.Part file);

    /**
     * 开始申办
     */
    @POST(HttpConstant.START_APPLY)
    Observable<BaseResponse> startApply(@Body StartApplyRequest request);

    /**
     * 根据mac地址查询关联的所属区域
     */
    @POST(HttpConstant.FIND_INTEGRATED_MACHINE_BIND_AREA)
    Observable<BaseResponse<MachineBindAreaResponse>> findIntegratedMachineBindArea(@Body MacAddressRequest request);

    /**
     * 指定连线公证人员
     */
    @POST(HttpConstant.LINE_NOTARY)
    Observable<BaseResponse<Boolean>> lineNotary(@Body ConnectNotaryRequest request);

    /**
     * 视频咨询指定连线公证人员
     */
    @POST(HttpConstant.CONSULT_LINE_NOTARY)
    Observable<BaseResponse<Boolean>> consultLineNotary(@Body ConnectNotaryRequest request);

    /**
     * 根据公证人员ID查询公证人员状态
     */
    @GET(HttpConstant.REFRESH_OFFICE_STATE_BY_OFFICE_ID)
    Observable<BaseResponse<String>> refreshOfficeStateByOfficeId(@Query("notaryId") String notaryId, @Query("officeId") String officeId);

    /**
     * 取消指定连线公证人员
     */
    @POST(HttpConstant.CANCEL_CONNECT_OFFICER)
    Observable<BaseResponse<Boolean>> cancelConnectOffice(@Body CancelConnectOfficeRequest request);

    /**
     * 取消公证处匹配
     */
    @POST(HttpConstant.CANCEL_NOTARY_MATCH)
    Observable<BaseResponse<Boolean>> cancelNotaryMatch(@Body CancelConnectOfficeRequest request);

    /**
     * 取消公证处匹配
     */
    @POST(HttpConstant.MATCH_NOTARY)
    Observable<BaseResponse<Boolean>> matchNotary(@Body ConnectNotaryRequest request);

    /**
     * 获取公证用途
     */
    @GET(HttpConstant.GET_NOTARY_USE + "{type}")
    Observable<BaseResponse> getNotaryUse(@Path("type") String type);

    /**
     * 查看申请信息
     */
    @GET(HttpConstant.GET_CASE_INFO + "{caseInfoId}")
    Observable<BaseResponse<ApplyInfoResponse>> getCaseInfo(@Path("caseInfoId") String caseInfoId);

    /**
     * 查询缴费清单
     */
    @GET(HttpConstant.GET_PAYMENT_LIST)
    Observable<BaseResponse<FeeInfoResponse>> getPaymentList(@Query("caseInfoId") String caseInfoId);

    /**
     * 查阅文书
     */
    @GET(HttpConstant.LIST_APPLICANTS_AND_DOCUMENTS)
    Observable<BaseResponse<List<DocInfoResponse>>> listApplicantsAndDocuments(@Query("caseInfoId") String caseInfoId);

    /**
     * 公证文书签名(批量)
     */
    @POST(HttpConstant.SIGN_MULTI_DOCUMENT)
    Observable<BaseResponse> signMultiDocument(@Body SignDocRequest request);

    /**
     * 查询所需材料
     */
    @GET(HttpConstant.LIST_MATERIAL_BY_CASE_INFO_ID + "{caseInfoId}")
    Observable<BaseResponse<List<MaterialListResponse>>> listMaterialByCaseInfoId(@Path("caseInfoId") String caseInfoId);

    /**
     * 终止案件
     */
    @POST(HttpConstant.TERMINATE_CASE)
    Observable<BaseResponse> terminateCase(@Body Map<String, String> map);

    /**
     * 上传材料
     */

    @Multipart
    @POST(HttpConstant.UPLOAD_MATERIAL)
    Observable<BaseResponse<FileInformationModel>> uploadMaterial(@Part("encryptStr") RequestBody caseInfoId, @Part MultipartBody.Part file);

    /**
     * 清空上传
     */
    @POST(HttpConstant.CLEAR_MATERIAL_AND_RE_UPLOAD_BY_USER)
    Observable<BaseResponse> clearMaterialAndReUploadByUser(@Body Map<String, Object> data);

    /**
     * 获得设备配置管理
     */
    @POST(HttpConstant.GET_CFG)
    Observable<BaseResponse<CfgResponse>> getCfg(@Body Map<String, String> data);

    /**
     * 视频咨询视频连线成功之后更新咨询记录状态
     */
    @POST(HttpConstant.JUST_STATE)
    Observable<BaseResponse> justState(@Body Map<String, String> data);

    /**
     * 视频咨询查询其他材料列表
     */
    @GET(HttpConstant.LIST_OTHER_MATERIAL)
    Observable<BaseResponse<List<MaterialListResponse.MaterialVoListDTO>>> listOtherMaterial(@Query("recordId") String recordId);

    /**
     * 获取用户的信息
     */
    @POST(HttpConstant.GET_USER_INFORMATION)
    Observable<BaseResponse> getUserInformation(@Body Map<String, Object> data);

    /**
     * 验证手机号收到的短信验证码
     */
    @POST(HttpConstant.VALIDATE_CODE)
    Observable<BaseResponse> validateCode(@Body Map<String, Object> data);

    /**
     * 视频咨询删除上传材料
     */
    @GET(HttpConstant.DEL_OTHER_MATERIAL)
    Observable<BaseResponse> delOtherMaterial(@Query("recordId") String recordId, @Query("fileId") String fileId);

    /**
     * 视频咨询上传材料
     */
    @Multipart
    @POST(HttpConstant.UPLOAD_OTHER_MATERIAL_BIND_RECORD)
    Observable<BaseResponse<FileInformationModel>> uploadOtherMaterialBindRecord(@Part("encryptStr") RequestBody recordId, @Part MultipartBody.Part file);

    /**
     * APP升级
     */
    @POST(HttpConstant.UPGRADE_APP)
    Observable<BaseResponse<UpgradeResponse>> upgradeApp(@Body Map<String, String> data);

    /**
     * 定位报告
     */
    @POST(HttpConstant.LOCATION_REPORT)
    Observable<BaseResponse> locationReport(@Body Map<String, Object> data);

    /**
     * 获取公证处列表
     */
    @POST(HttpConstant.LIST_NOTARY_BY_REGION)
    Observable<BaseResponse<List<BindingNotaryOfficeModel>>> getListNotaryByRegion(@Body OfficeListRequest request);

    /**
     * 人脸对比相似度，生成pdf
     */
    @Multipart
    @POST(HttpConstant.UNIVERSAL_SIMILARITY)
    Observable<BaseResponse<SimilarityResponse>> universalSimilarity(@Part("encryptStr") RequestBody idCard, @Part MultipartBody.Part file);

    /**
     * 根据用途获取事项 useType 出国留学--1->学习 定居移民--2->定居 探亲旅游--3->探亲 商务劳务--5-->劳务 境外其他--4-->其他
     */
    @GET(HttpConstant.GET_MATTERS_BY_USE_TYPE)
    Observable<BaseResponse<List<NotaryMatterItem>>> getMattersByUseType(@Query("useType") String useType, @Query("mechanismId") String mechanismId);

    /**
     * 获取个人证件类型
     */
    @GET(HttpConstant.DOCUMENT_TYPE_PERSON)
    Observable<BaseResponse<List<PersonDocumentType>>> getTypePerson();

    /**
     * 获取个人证件类型
     */
    @GET(HttpConstant.ENTERPRISE_CERTIFICATE_TYPE)
    Observable<BaseResponse<List<EnterpriseCertificateTypeResponse>>> getEnterpriseCertificateType();

    /**
     * 新增自助办理记录
     */
    @POST(HttpConstant.ADD_SELF_SERVICE)
    Observable<BaseResponse> addSelfService(@Body AddSelfServiceRequest request);

    /**
     * 新增预受理办理记录
     */
    @POST(HttpConstant.ADD_PRE_PROCESS)
    Observable<BaseResponse> addPreProcess(@Body AddSelfServiceRequest request);

    /**
     * 小一体机新增用户
     */
    @POST(HttpConstant.USER_ADD)
    Observable<BaseResponse> userAdd(@Body Map<String, Object> data);

    /**
     * 自助办证-初始化费用
     */
    @GET(HttpConstant.LIST_REQUIRED_MATERIAL + "{mechanismId}" + "/{recordId}")
    Observable<BaseResponse<ListRequiredMaterialResponse>> listRequiredMaterial(@Path("mechanismId") String mechanismId, @Path("recordId") String recordId);

    /**
     * 案件预受理-小一体机-查询所需材料
     */
    @GET(HttpConstant.LIST_REQUIRED_MATERIAL_RECORD_PRE_PROCESS + "{mechanismId}" + "/{recordId}")
    Observable<BaseResponse<ListRequiredMaterialResponse>> listRequiredRecordPreProcessMaterial(@Path("mechanismId") String mechanismId, @Path("recordId") String recordId);

    /**
     * 自助办证-更新材料
     */
    @POST(HttpConstant.UPDATE_SELF_SERVICE_MATERIAL)
    Observable<BaseResponse> updateSelfServiceMaterial(@Body UpdateSelfServiceMaterialRequest request);

    /**
     * 案件预受理-小一体机-更新材料
     */
    @POST(HttpConstant.UPDATE_RECORD_PRE_PROCESS_MATERIAL)
    Observable<BaseResponse> updateRecordPreProcessMaterial(@Body UpdateSelfServiceMaterialRequest request);

    /**
     * 自助 获取文书
     */
    @POST(HttpConstant.LIST_DOCUMENT)
    Observable<BaseResponse<List<ListDocumentResponse>>> listDocument(@Body Map<String, Object> data);

    /**
     * 案件预受理-小一体机-获取文书
     */
    @POST(HttpConstant.RECORD_PRE_LIST_DOCUMENT)
    Observable<BaseResponse<List<ListDocumentResponse>>> recordPreListDocument(@Body Map<String, Object> data);

    /**
     * 自助办证-文书签字
     */
    @POST(HttpConstant.SIGN_DOCUMENT)
    Observable<BaseResponse> signDocument(@Body Map<String, Object> data);

    /**
     * 自助办证-删除自助订单
     */

    @DELETE(HttpConstant.SELF_DELETE)
    Observable<BaseResponse> selfDelete(@Query("orderId") String orderId);

    /**
     * 自助办证-自助详情查询
     */
    @GET(HttpConstant.SELF_SERVICE_ORDER_DETAIL + "{recordId}")
    Observable<BaseResponse<SelfServiceOrderDetailModel>> selfServiceOrderDetail(@Path("recordId") String recordId);

    /**
     * 自助办证-终止申办
     */
    @PUT(HttpConstant.END_APPLY)
    Observable<BaseResponse> endApply(@Query("id") String id);

    /**
     * 获取配置的公证事项
     */
    @GET(HttpConstant.NOTARY_ITEM_BY_USER)
    Observable<BaseResponse<List<NotaryItemModel>>> getNotaryItemByUser();

    /**
     * 案件预受理-小一体机-完成提交
     */
    @PUT(HttpConstant.RECORD_PRE_PROCESS_SUBMIT + "{recordId}")
    Observable<BaseResponse> recordPreProcessSubmit(@Path("recordId") String recordId);

    /**
     * 获取sm2公钥
     */
    @GET(HttpConstant.GET_PUBLIC_KEY)
    Observable<BaseResponse<String>> getPublicKey();

    /**
     * 获取视频手里是缴费列表
     */
    @POST(HttpConstant.PAY_MENT_RECORDS)
    Observable<BaseResponse<List<FeeInfoResponse>>> payMentRecords(@Body Map<String, Object> data);

    /**
     * 获取配置的公证用途
     */
    @GET(HttpConstant.GET_PURPOSE)
    Observable<BaseResponse<List<NotaryItemModel>>> getPurPoseList();

    /**
     * 获取自助办证公证事项
     */
    @GET(HttpConstant.GET_SELF_MATTERS)
    Observable<BaseResponse<List<NotaryMatterItem>>> getSelfMatters();

    /**
     * 人脸对比相似度，生成pdf（新）
     */
    @POST(HttpConstant.FACE_DETECT_TO_PDF)
    Observable<BaseResponse<SimilarityResponse>> faceDetectToPDF(@Body Map<String, Object> params);

    /**
     * 获取图形验证码
     */
    @POST(HttpConstant.GET_GRAPH_CODE)
    Observable<BaseResponse<Input<CaptchaGetIt>>> getGraphCode(@Body Map<String, Object> params);

    /**
     * 校验图形验证码
     */
    @POST(HttpConstant.CHECK_CODE)
    Observable<BaseResponse<Input<CaptchaCheckIt>>> checkCode(@Query("captchaType") String captchaType, @Query("pointJson") String pointJson, @Query("token") String token);
//

}
