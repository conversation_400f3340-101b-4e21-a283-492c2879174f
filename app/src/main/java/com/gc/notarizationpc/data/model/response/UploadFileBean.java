package com.gc.notarizationpc.data.model.response;

import java.io.Serializable;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/3/29
 */
public class UploadFileBean implements Serializable {

    private String fileName;
    private String fileSize;
    private String id;
    private String original;
    private String sharedUrl;
    private String type;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOriginal() {
        return original;
    }

    public void setOriginal(String original) {
        this.original = original;
    }

    public String getSharedUrl() {
        return sharedUrl;
    }

    public void setSharedUrl(String sharedUrl) {
        this.sharedUrl = sharedUrl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
