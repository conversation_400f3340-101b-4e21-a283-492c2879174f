package com.gc.notarizationpc.data.model.request;

import com.gc.notarizationpc.bean.SpinnerItemData;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import me.goldze.mvvmhabit.binding.viewadapter.spinner.IKeyAndValue;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2024/5/24
 */
public class AddSelfServiceRequest implements Serializable {
    private String applicationItem;
    private String createdOrgan;
    private String dictUserPurpose;
    private List<RecordAgentApplicantsDTO> recordAgentApplicants;
    private List<RecordApplicantsDTO> recordApplicants;
    private List<RecordCorpApplicantsDTO> recordCorpApplicants;
    private String selfId;
    private Integer source;
    private Integer substitute;
    private String sysUserId;
    private String translateLanguage;
    private String usedPlace;
    private String usedPurpose;

    public String getApplicationItem() {
        return applicationItem;
    }

    public void setApplicationItem(String applicationItem) {
        this.applicationItem = applicationItem;
    }

    public String getCreatedOrgan() {
        return createdOrgan;
    }

    public void setCreatedOrgan(String createdOrgan) {
        this.createdOrgan = createdOrgan;
    }

    public String getDictUserPurpose() {
        return dictUserPurpose;
    }

    public void setDictUserPurpose(String dictUserPurpose) {
        this.dictUserPurpose = dictUserPurpose;
    }

    public List<RecordAgentApplicantsDTO> getRecordAgentApplicants() {
        return recordAgentApplicants;
    }

    public void setRecordAgentApplicants(List<RecordAgentApplicantsDTO> recordAgentApplicants) {
        this.recordAgentApplicants = recordAgentApplicants;
    }

    public List<RecordApplicantsDTO> getRecordApplicants() {
        return recordApplicants;
    }

    public void setRecordApplicants(List<RecordApplicantsDTO> recordApplicants) {
        this.recordApplicants = recordApplicants;
    }

    public List<RecordCorpApplicantsDTO> getRecordCorpApplicants() {
        return recordCorpApplicants;
    }

    public void setRecordCorpApplicants(List<RecordCorpApplicantsDTO> recordCorpApplicants) {
        this.recordCorpApplicants = recordCorpApplicants;
    }

    public String getSelfId() {
        return selfId;
    }

    public void setSelfId(String selfId) {
        this.selfId = selfId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getSubstitute() {
        return substitute;
    }

    public void setSubstitute(Integer substitute) {
        this.substitute = substitute;
    }

    public String getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(String sysUserId) {
        this.sysUserId = sysUserId;
    }

    public String getTranslateLanguage() {
        return translateLanguage;
    }

    public void setTranslateLanguage(String translateLanguage) {
        this.translateLanguage = translateLanguage;
    }

    public String getUsedPlace() {
        return usedPlace;
    }

    public void setUsedPlace(String usedPlace) {
        this.usedPlace = usedPlace;
    }

    public String getUsedPurpose() {
        return usedPurpose;
    }

    public void setUsedPurpose(String usedPurpose) {
        this.usedPurpose = usedPurpose;
    }

    public static class RecordAgentApplicantsDTO {
        private String address;
        private String agentName;
        private String birthday;
        private String contactNum;
        private String credentialNum;
        private Integer credentialType;
        private Integer gender;

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getAgentName() {
            return agentName;
        }

        public void setAgentName(String agentName) {
            this.agentName = agentName;
        }

        public String getBirthday() {
            return birthday;
        }

        public void setBirthday(String birthday) {
            this.birthday = birthday;
        }

        public String getContactNum() {
            return contactNum;
        }

        public void setContactNum(String contactNum) {
            this.contactNum = contactNum;
        }

        public String getCredentialNum() {
            return credentialNum;
        }

        public void setCredentialNum(String credentialNum) {
            this.credentialNum = credentialNum;
        }

        public Integer getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(Integer credentialType) {
            this.credentialType = credentialType;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }
    }

    public static class RecordApplicantsDTO {
        private String address;
        private String applicantName;
        private String birthday;
        private String contactNum;
        private String credentialNum;
        private int credentialType;
        private int gender;
        private int substitute;//是否代办 1:是 -1:否
        private String substituteStr;
        private boolean canOperater = true;//是否能操作删除，编辑
        public List<IKeyAndValue> data = new ArrayList<>();

        public RecordApplicantsDTO() {
            if (data.size() == 0) {
                data.add(new SpinnerItemData("-1", "申请人"));
                data.add(new SpinnerItemData("1", "代理人"));
            }

        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            RecordApplicantsDTO that = (RecordApplicantsDTO) o;
            return credentialNum.equals(that.credentialNum);
        }

        @Override
        public int hashCode() {
            return Objects.hash(credentialNum);
        }

        public boolean isCanOperater() {
            return canOperater;
        }

        public void setCanOperater(boolean canOperater) {
            this.canOperater = canOperater;
        }

        public String getSubstituteStr() {
            return substituteStr;
        }

        public void setSubstituteStr(String substituteStr) {
            this.substituteStr = substituteStr;
        }

        public Integer getSubstitute() {
            return substitute;
        }

        public void setSubstitute(int substitute) {
            this.substitute = substitute;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getApplicantName() {
            return applicantName;
        }

        public void setApplicantName(String applicantName) {
            this.applicantName = applicantName;
        }

        public String getBirthday() {
            return birthday;
        }

        public void setBirthday(String birthday) {
            this.birthday = birthday;
        }

        public String getContactNum() {
            return contactNum;
        }

        public void setContactNum(String contactNum) {
            this.contactNum = contactNum;
        }

        public String getCredentialNum() {
            return credentialNum;
        }

        public void setCredentialNum(String credentialNum) {
            this.credentialNum = credentialNum;
        }

        public int getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(int credentialType) {
            this.credentialType = credentialType;
        }

        public int getGender() {
            return gender;
        }

        public void setGender(int gender) {
            this.gender = gender;
        }
    }

    public static class RecordCorpApplicantsDTO {
        private String contactNum;
        private String corporationCredentialNum;
        private int corporationCredentialType;
        private String corporationName;
        private String credentialNum;
        private int credentialType;
        private String legalRepresentative;
        private String role;

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getContactNum() {
            return contactNum;
        }

        public void setContactNum(String contactNum) {
            this.contactNum = contactNum;
        }

        public String getCorporationCredentialNum() {
            return corporationCredentialNum;
        }

        public void setCorporationCredentialNum(String corporationCredentialNum) {
            this.corporationCredentialNum = corporationCredentialNum;
        }

        public int getCorporationCredentialType() {
            return corporationCredentialType;
        }

        public void setCorporationCredentialType(int corporationCredentialType) {
            this.corporationCredentialType = corporationCredentialType;
        }

        public String getCorporationName() {
            return corporationName;
        }

        public void setCorporationName(String corporationName) {
            this.corporationName = corporationName;
        }

        public String getCredentialNum() {
            return credentialNum;
        }

        public void setCredentialNum(String credentialNum) {
            this.credentialNum = credentialNum;
        }

        public int getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(int credentialType) {
            this.credentialType = credentialType;
        }

        public String getLegalRepresentative() {
            return legalRepresentative;
        }

        public void setLegalRepresentative(String legalRepresentative) {
            this.legalRepresentative = legalRepresentative;
        }
    }
}
