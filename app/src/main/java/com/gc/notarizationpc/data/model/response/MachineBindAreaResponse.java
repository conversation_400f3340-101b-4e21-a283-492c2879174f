package com.gc.notarizationpc.data.model.response;

import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: xuh<PERSON>feng
 * @CreateDate: 2024/4/1
 */
public class MachineBindAreaResponse implements Serializable {

    private String macAddress;
    private ProvinceDTO province;

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public ProvinceDTO getProvince() {
        return province;
    }

    public void setProvince(ProvinceDTO province) {
        this.province = province;
    }

    public static class ProvinceDTO {
        private String name;
        private String code;
        private List<GroupDTO> group;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public List<GroupDTO> getGroup() {
            return group;
        }

        public void setGroup(List<GroupDTO> group) {
            this.group = group;
        }

        public static class GroupDTO {
            private String firstChar;
            private List<CityListDTO> cityList;

            public String getFirstChar() {
                return firstChar;
            }

            public void setFirstChar(String firstChar) {
                this.firstChar = firstChar;
            }

            public List<CityListDTO> getCityList() {
                return cityList;
            }

            public void setCityList(List<CityListDTO> cityList) {
                this.cityList = cityList;
            }

            public static class CityListDTO {
                private String name;
                private String code;
                private List<AreasDTO> areas;

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public String getCode() {
                    return code;
                }

                public void setCode(String code) {
                    this.code = code;
                }

                public List<AreasDTO> getAreas() {
                    return areas;
                }

                public void setAreas(List<AreasDTO> areas) {
                    this.areas = areas;
                }

                public static class AreasDTO {
                    private String name;
                    private String code;

                    public String getName() {
                        return name;
                    }

                    public void setName(String name) {
                        this.name = name;
                    }

                    public String getCode() {
                        return code;
                    }

                    public void setCode(String code) {
                        this.code = code;
                    }
                }
            }
        }
    }
}
