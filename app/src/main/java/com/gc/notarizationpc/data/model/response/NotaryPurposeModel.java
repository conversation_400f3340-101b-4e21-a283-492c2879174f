package com.gc.notarizationpc.data.model.response;


import java.util.Map;

public class NotaryPurposeModel {

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDictId() {
        return dictId;
    }

    public void setDictId(String dictId) {
        this.dictId = dictId;
    }

    public Integer getDictKind() {
        return dictKind;
    }

    public void setDictKind(Integer dictKind) {
        this.dictKind = dictKind;
    }

    public String getDictType() {
        return dictType;
    }

    public void setDictType(String dictType) {
        this.dictType = dictType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getMechanismId() {
        return mechanismId;
    }

    public void setMechanismId(String mechanismId) {
        this.mechanismId = mechanismId;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    private String createBy;
    private String createTime;
    private String delFlag;
    private String description;
    private String dictId;
    private Integer dictKind;
    private String dictType;
    private String id;
    private String label;
    private String mechanismId;
    private String remarks;
    private String sortOrder;
    private String updateBy;
    private String updateTime;
    private String value;

    public void fromJson(Map<String,Object> data){
        createBy =  data.get("createBy") == null ? "" : data.get("createBy").toString();
        createTime =  data.get("createTime") == null ? "" : data.get("createTime").toString();
        delFlag =  data.get("delFlag") == null ? "" : data.get("delFlag").toString();
        description =  data.get("description") == null ? "" : data.get("description").toString();
        dictId =  data.get("dictId") == null ? "" : data.get("dictId").toString();
        dictKind =  data.get("dictKind") == null ? 0 : (Integer)data.get("dictKind");
        dictType =  data.get("dictType") == null ? "" : data.get("dictType").toString();
        id = data.get("id") == null ? "" : data.get("id").toString();
        label = data.get("label") == null ? "" : data.get("label").toString();
        mechanismId = data.get("mechanismId") == null ? "" : data.get("mechanismId").toString();
        remarks = data.get("remarks") == null ? "" : data.get("remarks").toString();
        sortOrder = data.get("sortOrder") == null ? "": data.get("sortOrder").toString();
        updateBy = data.get("updateBy") == null ? "" : data.get("updateBy").toString();
        updateTime = data.get("updateTime") == null ? "" : data.get("updateTime").toString();
        value = data.get("value") == null ? "" : data.get("value").toString();
    }
}
