package com.gc.notarizationpc.data.model.response;


import java.io.Serializable;

public class BindingNotaryOfficeModel implements Serializable {
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBusiTime() {
        return busiTime;
    }

    public void setBusiTime(String busiTime) {
        this.busiTime = busiTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMechanismName() {
        return mechanismName;
    }

    public void setMechanismName(String mechanismName) {
        this.mechanismName = mechanismName;
    }

    /**
     * address	地址	string
     * busiTime	时段	string
     * id	机构管理主键	string
     * mechanismName	公证处名称	string
     */

    private String address;
    private String busiTime;
    private String id;
    private String mechanismName;

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    private boolean isSelected;
}
