package com.gc.notarizationpc.data.model.response;

import java.io.Serializable;
import java.util.List;

public class SelfServiceOrderListModel implements Serializable {


    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public List<DataListDTO> getDataList() {
        return dataList;
    }

    public void setDataList(List<DataListDTO> dataList) {
        this.dataList = dataList;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    private Integer currentPage;
    private List<DataListDTO> dataList;
    private Integer pageSize;
    private Integer total;


    public static class DataListDTO implements Serializable{
        public String getApplicantName() {
            return applicantName;
        }

        public void setApplicantName(String applicantName) {
            this.applicantName = applicantName;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getOrderName() {
            return orderName;
        }

        public void setOrderName(String orderName) {
            this.orderName = orderName;
        }

        public Integer getProcessState() {
            return processState;
        }

        public void setProcessState(Integer processState) {
            this.processState = processState;
        }

        private String applicantName;
        private String createTime;
        private String id;
        private String orderName;
        private Integer processState;
        private String applicationItem;
        public String getApplicationItem() {
            return applicationItem;
        }

        public void setApplicationItem(String applicationItem) {
            this.applicationItem = applicationItem;
        }

        public String getUsedPurpose() {
            return usedPurpose;
        }

        public void setUsedPurpose(String usedPurpose) {
            this.usedPurpose = usedPurpose;
        }

        private String usedPurpose;
    }
}
