package com.gc.notarizationpc.data.model.request;

import java.io.Serializable;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.request
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/3/26
 */
public class VideoAddRequest implements Serializable {

    private Integer dispatchMode;//记录分发模式(1-指定公证员，2-匹配公证处)
    private String mobile;
    private String officeId;
    private String sourceType;//案件来源;系统中状态为：1-青桐智盒app，2-小程序，3-大一体机，4-小一体机，5-公证员业务后台，6-第三方同步，7-公证员小程序端
    private String userId;
    private String userName;

    public Integer getDispatchMode() {
        return dispatchMode;
    }

    public void setDispatchMode(Integer dispatchMode) {
        this.dispatchMode = dispatchMode;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOfficeId() {
        return officeId;
    }

    public void setOfficeId(String officeId) {
        this.officeId = officeId;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
