package com.gc.notarizationpc.data.model.response;

import com.alibaba.android.arouter.utils.TextUtils;

import org.w3c.dom.Text;

import java.util.List;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/6/7
 */
public class ListDocumentResponse {

    private String documentTypeName;
    private String firstDocUrl;
    private List<SignDocumentVOListDTO> signDocumentVOList;

    public String getFirstDocUrl() {
        return TextUtils.isEmpty(firstDocUrl)?"":firstDocUrl;
    }

    public void setFirstDocUrl(String firstDocUrl) {
        this.firstDocUrl = firstDocUrl;
    }

    public String getDocumentTypeName() {
        return documentTypeName;
    }

    public void setDocumentTypeName(String documentTypeName) {
        this.documentTypeName = documentTypeName;
    }

    public List<SignDocumentVOListDTO> getSignDocumentVOList() {
        return signDocumentVOList;
    }

    public void setSignDocumentVOList(List<SignDocumentVOListDTO> signDocumentVOList) {
        this.signDocumentVOList = signDocumentVOList;
    }

    public static class SignDocumentVOListDTO {
        private String documentId;
        private List<String> documentImages;
        private String documentName;
        private String documentType;
        private Boolean sign;

        public String getDocumentId() {
            return documentId;
        }

        public void setDocumentId(String documentId) {
            this.documentId = documentId;
        }

        public List<String> getDocumentImages() {
            return documentImages;
        }

        public void setDocumentImages(List<String> documentImages) {
            this.documentImages = documentImages;
        }

        public String getDocumentName() {
            return documentName;
        }

        public void setDocumentName(String documentName) {
            this.documentName = documentName;
        }

        public String getDocumentType() {
            return documentType;
        }

        public void setDocumentType(String documentType) {
            this.documentType = documentType;
        }

        public Boolean getSign() {
            return sign;
        }

        public void setSign(Boolean sign) {
            this.sign = sign;
        }
    }
}
