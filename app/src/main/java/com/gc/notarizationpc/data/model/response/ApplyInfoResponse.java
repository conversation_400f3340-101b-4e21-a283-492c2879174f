package com.gc.notarizationpc.data.model.response;

import java.util.List;

/**
 * 申办
 *
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2024/3/20
 */
public class ApplyInfoResponse {
    private List<ApplicantListDTO> applicantList;
    private String applicationItem;
    private Integer manufactureNum;
    private String remark;
    private String translateLanguage;
    private String translateLanguageStr;
    private String usedPlace;
    private String usedPlaceStr;
    private String usedPurpose;
    private String usedPurposeStr;
    private String applyDate;
    private String applicationItemStr;

    public String getApplicationItemStr() {
        return applicationItemStr;
    }

    public void setApplicationItemStr(String applicationItemStr) {
        this.applicationItemStr = applicationItemStr;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public List<ApplicantListDTO> getApplicantList() {
        return applicantList;
    }

    public void setApplicantList(List<ApplicantListDTO> applicantList) {
        this.applicantList = applicantList;
    }

    public String getApplicationItem() {
        return applicationItem;
    }

    public void setApplicationItem(String applicationItem) {
        this.applicationItem = applicationItem;
    }

    public Integer getManufactureNum() {
        return manufactureNum;
    }

    public void setManufactureNum(Integer manufactureNum) {
        this.manufactureNum = manufactureNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTranslateLanguage() {
        return translateLanguage;
    }

    public void setTranslateLanguage(String translateLanguage) {
        this.translateLanguage = translateLanguage;
    }

    public String getTranslateLanguageStr() {
        return translateLanguageStr;
    }

    public void setTranslateLanguageStr(String translateLanguageStr) {
        this.translateLanguageStr = translateLanguageStr;
    }

    public String getUsedPlaceStr() {
        return usedPlaceStr;
    }

    public void setUsedPlaceStr(String usedPlaceStr) {
        this.usedPlaceStr = usedPlaceStr;
    }

    public String getUsedPurpose() {
        return usedPurpose;
    }

    public void setUsedPurpose(String usedPurpose) {
        this.usedPurpose = usedPurpose;
    }

    public String getUsedPurposeStr() {
        return usedPurposeStr;
    }

    public void setUsedPurposeStr(String usedPurposeStr) {
        this.usedPurposeStr = usedPurposeStr;
    }

    public String getUsedPlace() {
        return usedPlace;
    }

    public void setUsedPlace(String usedPlace) {
        this.usedPlace = usedPlace;
    }

    public static class ApplicationItem {
        private String mattersId;
        private String mattersName;
        private Integer notarizationNumber;
        private Integer copyNumber;
        private Double notarialFee;
        private Long detailMattersId;
        private String bookNo;

        public String getMattersId() {
            return mattersId;
        }

        public void setMattersId(String mattersId) {
            this.mattersId = mattersId;
        }

        public String getMattersName() {
            return mattersName;
        }

        public void setMattersName(String mattersName) {
            this.mattersName = mattersName;
        }

        public Integer getNotarizationNumber() {
            return notarizationNumber;
        }

        public void setNotarizationNumber(Integer notarizationNumber) {
            this.notarizationNumber = notarizationNumber;
        }

        public Integer getCopyNumber() {
            return copyNumber;
        }

        public void setCopyNumber(Integer copyNumber) {
            this.copyNumber = copyNumber;
        }

        public Double getNotarialFee() {
            return notarialFee;
        }

        public void setNotarialFee(Double notarialFee) {
            this.notarialFee = notarialFee;
        }

        public Long getDetailMattersId() {
            return detailMattersId;
        }

        public void setDetailMattersId(Long detailMattersId) {
            this.detailMattersId = detailMattersId;
        }

        public String getBookNo() {
            return bookNo;
        }

        public void setBookNo(String bookNo) {
            this.bookNo = bookNo;
        }
    }

    public static class ApplicantListDTO {
        private String applicantId;//	当事人ID
        private String applicantName;//姓名
        private Integer applicantType;//1-申请人,2-代理人，3-单位
        private String contactNum;//联系电话
        private String legalPerson;//法定代表人
        private String principal;//被代理人
        private String credentialNum;//证件号码
        private Integer credentialType;//证件类型

        private String credentialTypeStr;//证件类型string

        public String getCredentialTypeStr() {
            return credentialTypeStr;
        }

        public void setCredentialTypeStr(String credentialTypeStr) {
            this.credentialTypeStr = credentialTypeStr;
        }

        public String getCredentialNum() {
            return credentialNum;
        }

        public void setCredentialNum(String credentialNum) {
            this.credentialNum = credentialNum;
        }



        public String getApplicantId() {
            return applicantId;
        }

        public void setApplicantId(String applicantId) {
            this.applicantId = applicantId;
        }

        public String getApplicantName() {
            return applicantName;
        }

        public void setApplicantName(String applicantName) {
            this.applicantName = applicantName;
        }

        public Integer getApplicantType() {
            return applicantType;
        }

        public void setApplicantType(Integer applicantType) {
            this.applicantType = applicantType;
        }

        public String getContactNum() {
            return contactNum;
        }

        public void setContactNum(String contactNum) {
            this.contactNum = contactNum;
        }

        public String getLegalPerson() {
            return legalPerson;
        }

        public void setLegalPerson(String legalPerson) {
            this.legalPerson = legalPerson;
        }

        public String getPrincipal() {
            return principal;
        }

        public void setPrincipal(String principal) {
            this.principal = principal;
        }

        public Integer getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(Integer credentialType) {
            this.credentialType = credentialType;
        }
    }
}
