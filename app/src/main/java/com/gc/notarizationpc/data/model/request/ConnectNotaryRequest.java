package com.gc.notarizationpc.data.model.request;

import java.io.Serializable;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.request
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/4/1
 */
public class ConnectNotaryRequest implements Serializable {

    private String mechanismId;//机构ID
    private String mobile;//当前用户手机号
    private String officeId;//公证员ID
    private String recordId;//记录ID
    private Integer type;//1-咨询，2-视频公证
    private String userId;//当前用户ID
    private String userName;//当前用户姓名
    private String macAddress;

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getMechanismId() {
        return mechanismId;
    }

    public void setMechanismId(String mechanismId) {
        this.mechanismId = mechanismId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOfficeId() {
        return officeId;
    }

    public void setOfficeId(String officeId) {
        this.officeId = officeId;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
