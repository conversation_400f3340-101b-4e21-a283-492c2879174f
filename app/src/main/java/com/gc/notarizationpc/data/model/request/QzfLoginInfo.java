package com.gc.notarizationpc.data.model.request;

import java.io.Serializable;
import java.util.List;

public class QzfLoginInfo implements Serializable {


    /**
     * role : [{"roleId":"1c83121a-2312-4e22-b025-7cbaab00f<PERSON>","notaryList":[{"notaryId":"fec878ca-e407-4822-b0c1-48cff045924a","notaryName":"测试公证处"}],"roleName":"取证人员","institutionType":"1"},{"roleId":"3d214a23-0947-4a24-9ca7-b6cbab3e2c6e","notaryList":[{"notaryId":"fec878ca-e407-4822-b0c1-48cff045924a","notaryName":"测试公证处"}],"roleName":"公证处管理员","institutionType":"1"},{"mechanismName":"测试取证机构","roleId":"c57af767-0d0f-4900-8548-7941cab26927","notaryList":[{"notaryId":"3e36d57b-76f5-42bc-96c7-29c8cfc58287","notaryName":"公证处二"},{"notaryId":"fec878ca-e407-4822-b0c1-48cff045924a","notaryName":"测试公证处"}],"roleName":"取证人员二","institutionType":"5","mechanismId":"6d07b716-4734-4cdd-9624-d59a2d3b97ac"},{"roleId":"1afc6ecd-46ae-47ae-ac40-62c9f167741d","notaryList":[{"notaryId":"3d8ee769-cc38-43cc-95bc-d065b219104e","notaryName":"阿公证处一"}],"roleName":"阿公证管理员一","institutionType":"1"},{"mechanismName":"阿取证机构一","roleId":"3dd59696-8e33-4f73-9b67-e8c94f30ca15","notaryList":[{"notaryId":"3d8ee769-cc38-43cc-95bc-d065b219104e","notaryName":"阿公证处一"}],"roleName":"阿取证管理员一","institutionType":"5","mechanismId":"4efcf395-49df-46e8-88b8-f4e3930f1340"}]
     * token : e31a0a00-73f5-4fcc-b606-15b5f5143da3
     */

    private List<RoleDTO> role;
    private String token;

    public List<RoleDTO> getRole() {
        return role;
    }

    public void setRole(List<RoleDTO> role) {
        this.role = role;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public static class RoleDTO {
        /**
         * roleId : 1c83121a-2312-4e22-b025-7cbaab00fbba
         * notaryList : [{"notaryId":"fec878ca-e407-4822-b0c1-48cff045924a","notaryName":"测试公证处"}]
         * roleName : 取证人员
         * institutionType : 1
         * mechanismName : 测试取证机构
         * mechanismId : 6d07b716-4734-4cdd-9624-d59a2d3b97ac
         */

        private String roleId;
        private List<NotaryListDTO> notaryList;
        private String roleName;
        private String roleType;
        private String institutionType;
        private String mechanismName;
        private String mechanismId;

        public String getRoleType() {
            return roleType;
        }

        public void setRoleType(String roleType) {
            this.roleType = roleType;
        }

        public String getRoleId() {
            return roleId;
        }

        public void setRoleId(String roleId) {
            this.roleId = roleId;
        }

        public List<NotaryListDTO> getNotaryList() {
            return notaryList;
        }

        public void setNotaryList(List<NotaryListDTO> notaryList) {
            this.notaryList = notaryList;
        }

        public String getRoleName() {
            return roleName;
        }

        public void setRoleName(String roleName) {
            this.roleName = roleName;
        }

        public String getInstitutionType() {
            return institutionType;
        }

        public void setInstitutionType(String institutionType) {
            this.institutionType = institutionType;
        }

        public String getMechanismName() {
            return mechanismName;
        }

        public void setMechanismName(String mechanismName) {
            this.mechanismName = mechanismName;
        }

        public String getMechanismId() {
            return mechanismId;
        }

        public void setMechanismId(String mechanismId) {
            this.mechanismId = mechanismId;
        }

        public static class NotaryListDTO {
            /**
             * notaryId : fec878ca-e407-4822-b0c1-48cff045924a
             * notaryName : 测试公证处
             */

            private String notaryId;
            private String notaryName;

            public String getNotaryId() {
                return notaryId;
            }

            public void setNotaryId(String notaryId) {
                this.notaryId = notaryId;
            }

            public String getNotaryName() {
                return notaryName;
            }

            public void setNotaryName(String notaryName) {
                this.notaryName = notaryName;
            }
        }
    }
}
