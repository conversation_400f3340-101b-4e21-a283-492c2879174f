package com.gc.notarizationpc.data.model.response;

import java.util.List;
import java.util.Objects;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: xuh<PERSON>feng
 * @CreateDate: 2024/6/5
 */
public class ListRequiredMaterialResponse {

    private List<MaterialTypeVoListDTO> materialTypeVoList;
    private PostInfoDTO postInfo;

    public List<MaterialTypeVoListDTO> getMaterialTypeVoList() {
        return materialTypeVoList;
    }

    public void setMaterialTypeVoList(List<MaterialTypeVoListDTO> materialTypeVoList) {
        this.materialTypeVoList = materialTypeVoList;
    }

    public PostInfoDTO getPostInfo() {
        return postInfo;
    }

    public void setPostInfo(PostInfoDTO postInfo) {
        this.postInfo = postInfo;
    }

    public static class MaterialVoListDTO {
        private String fileId;
        private String fileUrl;
        private boolean isShowDel;
        private String materialTypeId;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            MaterialVoListDTO that = (MaterialVoListDTO) o;
            return Objects.equals(fileId, that.fileId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(fileId);
        }

        public String getMaterialTypeId() {
            return materialTypeId;
        }

        public void setMaterialTypeId(String materialTypeId) {
            this.materialTypeId = materialTypeId;
        }

        public boolean isShowDel() {
            return isShowDel;
        }

        public void setShowDel(boolean showDel) {
            isShowDel = showDel;
        }

        public String getFileId() {
            return fileId;
        }

        public void setFileId(String fileId) {
            this.fileId = fileId;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }
    }

    public static class PostInfoDTO {
        private String busiTime;
        private String consigneeName;
        private String phoneNum;
        private String postAddress;
        private Integer receiveWay;//领取方式 1-自取，2-邮寄(到付)
        private String notarialName;
        private String notarialAddress;

        public String getNotarialName() {
            return notarialName;
        }

        public void setNotarialName(String notarialName) {
            this.notarialName = notarialName;
        }

        public String getNotarialAddress() {
            return notarialAddress;
        }

        public void setNotarialAddress(String notarialAddress) {
            this.notarialAddress = notarialAddress;
        }

        public String getBusiTime() {
            return busiTime;
        }

        public void setBusiTime(String busiTime) {
            this.busiTime = busiTime;
        }

        public String getConsigneeName() {
            return consigneeName;
        }

        public void setConsigneeName(String consigneeName) {
            this.consigneeName = consigneeName;
        }

        public String getPhoneNum() {
            return phoneNum;
        }

        public void setPhoneNum(String phoneNum) {
            this.phoneNum = phoneNum;
        }

        public String getPostAddress() {
            return postAddress;
        }

        public void setPostAddress(String postAddress) {
            this.postAddress = postAddress;
        }

        public Integer getReceiveWay() {
            return receiveWay;
        }

        public void setReceiveWay(Integer receiveWay) {
            this.receiveWay = receiveWay;
        }
    }

    public static class MaterialTypeVoListDTO {
        private String materialTypeCode;
        private String materialTypeId;
        private String materialTypeName;
        private List<MaterialVoListDTO> materialVoList;

        public String getMaterialTypeCode() {
            return materialTypeCode;
        }

        public void setMaterialTypeCode(String materialTypeCode) {
            this.materialTypeCode = materialTypeCode;
        }

        public String getMaterialTypeId() {
            return materialTypeId;
        }

        public void setMaterialTypeId(String materialTypeId) {
            this.materialTypeId = materialTypeId;
        }

        public String getMaterialTypeName() {
            return materialTypeName;
        }

        public void setMaterialTypeName(String materialTypeName) {
            this.materialTypeName = materialTypeName;
        }

        public List<MaterialVoListDTO> getMaterialVoList() {
            return materialVoList;
        }

        public void setMaterialVoList(List<MaterialVoListDTO> materialVoList) {
            this.materialVoList = materialVoList;
        }
    }
}
