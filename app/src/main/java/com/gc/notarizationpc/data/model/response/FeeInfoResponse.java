package com.gc.notarizationpc.data.model.response;

import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2024/4/9
 */
public class FeeInfoResponse implements Serializable {

    private Integer hadSetBilling;
    private List<MattersFeeListDTO> mattersFeeList;
    private List<OtherFeeListDTO> otherFeeList;
    private Integer otherTotalFee;
    private Integer reductionFee;
    private Integer totalFee;
    //收费变更后，调整为以下字段
    private double amountReceivable;//应收金额

    private String billingItemName;//项目

    private String mattersName;//事项名称

    public double getAmountReceivable() {
        return amountReceivable;
    }

    public void setAmountReceivable(double amountReceivable) {
        this.amountReceivable = amountReceivable;
    }

    public String getBillingItemName() {
        return billingItemName;
    }

    public void setBillingItemName(String billingItemName) {
        this.billingItemName = billingItemName;
    }

    public String getMattersName() {
        return mattersName;
    }

    public void setMattersName(String mattersName) {
        this.mattersName = mattersName;
    }

    public Integer getHadSetBilling() {
        return hadSetBilling;
    }

    public void setHadSetBilling(Integer hadSetBilling) {
        this.hadSetBilling = hadSetBilling;
    }

    public List<MattersFeeListDTO> getMattersFeeList() {
        return mattersFeeList;
    }

    public void setMattersFeeList(List<MattersFeeListDTO> mattersFeeList) {
        this.mattersFeeList = mattersFeeList;
    }

    public List<OtherFeeListDTO> getOtherFeeList() {
        return otherFeeList;
    }

    public void setOtherFeeList(List<OtherFeeListDTO> otherFeeList) {
        this.otherFeeList = otherFeeList;
    }

    public Integer getOtherTotalFee() {
        return otherTotalFee;
    }

    public void setOtherTotalFee(Integer otherTotalFee) {
        this.otherTotalFee = otherTotalFee;
    }

    public Integer getReductionFee() {
        return reductionFee;
    }

    public void setReductionFee(Integer reductionFee) {
        this.reductionFee = reductionFee;
    }

    public Integer getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Integer totalFee) {
        this.totalFee = totalFee;
    }

    public static class MattersFeeListDTO {
        private Integer copyFee;
        private String mattersId;
        private String mattersName;
        private Integer notarizationFee;
        private Integer num;

        public Integer getCopyFee() {
            return copyFee;
        }

        public void setCopyFee(Integer copyFee) {
            this.copyFee = copyFee;
        }

        public String getMattersId() {
            return mattersId;
        }

        public void setMattersId(String mattersId) {
            this.mattersId = mattersId;
        }

        public String getMattersName() {
            return mattersName;
        }

        public void setMattersName(String mattersName) {
            this.mattersName = mattersName;
        }

        public Integer getNotarizationFee() {
            return notarizationFee;
        }

        public void setNotarizationFee(Integer notarizationFee) {
            this.notarizationFee = notarizationFee;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }
    }

    public static class OtherFeeListDTO {
        private Integer billingItem;
        private String billingItemStr;
        private Integer totalCost;

        public String getBillingItemStr() {
            return billingItemStr;
        }

        public void setBillingItemStr(String billingItemStr) {
            this.billingItemStr = billingItemStr;
        }

        public Integer getBillingItem() {
            return billingItem;
        }

        public void setBillingItem(Integer billingItem) {
            this.billingItem = billingItem;
        }

        public Integer getTotalCost() {
            return totalCost;
        }

        public void setTotalCost(Integer totalCost) {
            this.totalCost = totalCost;
        }
    }
}
