package com.gc.notarizationpc.data.model.response;


import java.util.List;

public class SelfServiceOrderDetailModel {

    public String getApplicationItem() {
        return applicationItem;
    }

    public void setApplicationItem(String applicationItem) {
        this.applicationItem = applicationItem;
    }

    public String getApplicationItemStr() {
        return applicationItemStr;
    }

    public void setApplicationItemStr(String applicationItemStr) {
        this.applicationItemStr = applicationItemStr;
    }

    public String getCreatedOrgan() {
        return createdOrgan;
    }

    public void setCreatedOrgan(String createdOrgan) {
        this.createdOrgan = createdOrgan;
    }

    public String getCreatedOrganName() {
        return createdOrganName;
    }

    public void setCreatedOrganName(String createdOrganName) {
        this.createdOrganName = createdOrganName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public List<DocTypeListDTO> getDocTypeList() {
        return docTypeList;
    }

    public void setDocTypeList(List<DocTypeListDTO> docTypeList) {
        this.docTypeList = docTypeList;
    }

    public List<MaterialVoListDTO> getMaterialVoList() {
        return materialVoList;
    }

    public void setMaterialVoList(List<MaterialVoListDTO> materialVoList) {
        this.materialVoList = materialVoList;
    }

    public String getOfficerId() {
        return officerId;
    }

    public void setOfficerId(String officerId) {
        this.officerId = officerId;
    }

    public String getOfficerName() {
        return officerName;
    }

    public void setOfficerName(String officerName) {
        this.officerName = officerName;
    }

    public PaymentListVODTO getPaymentListVO() {
        return paymentListVO;
    }

    public void setPaymentListVO(PaymentListVODTO paymentListVO) {
        this.paymentListVO = paymentListVO;
    }

    public String getPostAddress() {
        return postAddress;
    }

    public void setPostAddress(String postAddress) {
        this.postAddress = postAddress;
    }

    public Integer getProcessState() {
        return processState;
    }

    public void setProcessState(Integer processState) {
        this.processState = processState;
    }

    public Integer getReceiveWay() {
        return receiveWay;
    }

    public void setReceiveWay(Integer receiveWay) {
        this.receiveWay = receiveWay;
    }

    public List<RecordApplicantAgentVosDTO> getRecordApplicantAgentVos() {
        return recordApplicantAgentVos;
    }

    public void setRecordApplicantAgentVos(List<RecordApplicantAgentVosDTO> recordApplicantAgentVos) {
        this.recordApplicantAgentVos = recordApplicantAgentVos;
    }

    public List<RecordApplicantVosDTO> getRecordApplicantVos() {
        return recordApplicantVos;
    }

    public void setRecordApplicantVos(List<RecordApplicantVosDTO> recordApplicantVos) {
        this.recordApplicantVos = recordApplicantVos;
    }

    public List<RecordCorporatonApplicantVosDTO> getRecordCorporatonApplicantVos() {
        return recordCorporatonApplicantVos;
    }

    public void setRecordCorporatonApplicantVos(List<RecordCorporatonApplicantVosDTO> recordCorporatonApplicantVos) {
        this.recordCorporatonApplicantVos = recordCorporatonApplicantVos;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTranslateLanguage() {
        return translateLanguage;
    }

    public void setTranslateLanguage(String translateLanguage) {
        this.translateLanguage = translateLanguage;
    }

    public String getTranslateLanguageStr() {
        return translateLanguageStr;
    }

    public void setTranslateLanguageStr(String translateLanguageStr) {
        this.translateLanguageStr = translateLanguageStr;
    }

    public String getUsedPlace() {
        return usedPlace;
    }

    public void setUsedPlace(String usedPlace) {
        this.usedPlace = usedPlace;
    }

    public String getUsedPlaceStr() {
        return usedPlaceStr;
    }

    public void setUsedPlaceStr(String usedPlaceStr) {
        this.usedPlaceStr = usedPlaceStr;
    }

    public String getUsedPurpose() {
        return usedPurpose;
    }

    public void setUsedPurpose(String usedPurpose) {
        this.usedPurpose = usedPurpose;
    }

    public String getUsedPurposeStr() {
        return usedPurposeStr;
    }

    public void setUsedPurposeStr(String usedPurposeStr) {
        this.usedPurposeStr = usedPurposeStr;
    }

    private String applicationItem;
    private String applicationItemStr;
    private String createdOrgan;
    private String createdOrganName;
    private String createdTime;
    private List<DocTypeListDTO> docTypeList;
    private List<MaterialVoListDTO> materialVoList;
    private String officerId;
    private String officerName;
    private PaymentListVODTO paymentListVO;
    private String postAddress;
    private Integer processState;
    private Integer receiveWay;
    private List<RecordApplicantAgentVosDTO> recordApplicantAgentVos;
    private List<RecordApplicantVosDTO> recordApplicantVos;
    private List<RecordCorporatonApplicantVosDTO> recordCorporatonApplicantVos;
    private String recordId;
    private Integer status;
    private String translateLanguage;
    private String translateLanguageStr;
    private String usedPlace;
    private String usedPlaceStr;
    private String usedPurpose;
    private String usedPurposeStr;

    public static class PaymentListVODTO {
        public Integer getHadSetBilling() {
            return hadSetBilling;
        }

        public void setHadSetBilling(Integer hadSetBilling) {
            this.hadSetBilling = hadSetBilling;
        }

        public List<MattersFeeListDTO> getMattersFeeList() {
            return mattersFeeList;
        }

        public void setMattersFeeList(List<MattersFeeListDTO> mattersFeeList) {
            this.mattersFeeList = mattersFeeList;
        }

        public List<OtherFeeListDTO> getOtherFeeList() {
            return otherFeeList;
        }

        public void setOtherFeeList(List<OtherFeeListDTO> otherFeeList) {
            this.otherFeeList = otherFeeList;
        }

        public Integer getOtherTotalFee() {
            return otherTotalFee;
        }

        public void setOtherTotalFee(Integer otherTotalFee) {
            this.otherTotalFee = otherTotalFee;
        }

        public Integer getReductionFee() {
            return reductionFee;
        }

        public void setReductionFee(Integer reductionFee) {
            this.reductionFee = reductionFee;
        }

        public Integer getTotalFee() {
            return totalFee;
        }

        public void setTotalFee(Integer totalFee) {
            this.totalFee = totalFee;
        }

        private Integer hadSetBilling;
        private List<MattersFeeListDTO> mattersFeeList;
        private List<OtherFeeListDTO> otherFeeList;
        private Integer otherTotalFee;
        private Integer reductionFee;
        private Integer totalFee;

        public static class MattersFeeListDTO {
            public Integer getCopyFee() {
                return copyFee;
            }

            public void setCopyFee(Integer copyFee) {
                this.copyFee = copyFee;
            }

            public String getMattersId() {
                return mattersId;
            }

            public void setMattersId(String mattersId) {
                this.mattersId = mattersId;
            }

            public String getMattersName() {
                return mattersName;
            }

            public void setMattersName(String mattersName) {
                this.mattersName = mattersName;
            }

            public Integer getNotarizationFee() {
                return notarizationFee;
            }

            public void setNotarizationFee(Integer notarizationFee) {
                this.notarizationFee = notarizationFee;
            }

            public Integer getNum() {
                return num;
            }

            public void setNum(Integer num) {
                this.num = num;
            }

            private Integer copyFee;
            private String mattersId;
            private String mattersName;
            private Integer notarizationFee;
            private Integer num;
        }

        public static class OtherFeeListDTO {
            public Integer getBillingItem() {
                return billingItem;
            }

            public void setBillingItem(Integer billingItem) {
                this.billingItem = billingItem;
            }

            public Integer getTotalCost() {
                return totalCost;
            }

            public void setTotalCost(Integer totalCost) {
                this.totalCost = totalCost;
            }

            private Integer billingItem;
            private Integer totalCost;
        }
    }

    public static class DocTypeListDTO {
        public List<DocumentVOListDTO> getDocumentVOList() {
            return documentVOList;
        }

        public void setDocumentVOList(List<DocumentVOListDTO> documentVOList) {
            this.documentVOList = documentVOList;
        }

        public String getTypeName() {
            return typeName;
        }

        public void setTypeName(String typeName) {
            this.typeName = typeName;
        }

        private List<DocumentVOListDTO> documentVOList;
        private String typeName;


        public static class DocumentVOListDTO {
            public Integer getCanSign() {
                return canSign;
            }

            public void setCanSign(Integer canSign) {
                this.canSign = canSign;
            }

            public String getDocumentName() {
                return documentName;
            }

            public void setDocumentName(String documentName) {
                this.documentName = documentName;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public Integer getInitSign() {
                return initSign;
            }

            public void setInitSign(Integer initSign) {
                this.initSign = initSign;
            }

            public Integer getMinReadTime() {
                return minReadTime;
            }

            public void setMinReadTime(Integer minReadTime) {
                this.minReadTime = minReadTime;
            }

            public Integer getPersonSignStatus() {
                return personSignStatus;
            }

            public void setPersonSignStatus(Integer personSignStatus) {
                this.personSignStatus = personSignStatus;
            }

            public String getSignDocumentFile() {
                return signDocumentFile;
            }

            public void setSignDocumentFile(String signDocumentFile) {
                this.signDocumentFile = signDocumentFile;
            }

            public List<String> getSignDocumentFileUrl() {
                return signDocumentFileUrl;
            }

            public void setSignDocumentFileUrl(List<String> signDocumentFileUrl) {
                this.signDocumentFileUrl = signDocumentFileUrl;
            }

            public Integer getSignStatus() {
                return signStatus;
            }

            public void setSignStatus(Integer signStatus) {
                this.signStatus = signStatus;
            }

            public String getUnsignDocumentFile() {
                return unsignDocumentFile;
            }

            public void setUnsignDocumentFile(String unsignDocumentFile) {
                this.unsignDocumentFile = unsignDocumentFile;
            }

            public List<String> getUnsignDocumentFileUrl() {
                return unsignDocumentFileUrl;
            }

            public void setUnsignDocumentFileUrl(List<String> unsignDocumentFileUrl) {
                this.unsignDocumentFileUrl = unsignDocumentFileUrl;
            }

            private Integer canSign;
            private String documentName;
            private String id;
            private Integer initSign;
            private Integer minReadTime;
            private Integer personSignStatus;
            private String signDocumentFile;
            private List<String> signDocumentFileUrl;
            private Integer signStatus;
            private String unsignDocumentFile;
            private List<String> unsignDocumentFileUrl;
        }
    }

    public static class MaterialVoListDTO {
        public String getAnnexId() {
            return annexId;
        }

        public void setAnnexId(String annexId) {
            this.annexId = annexId;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public String getMaterialId() {
            return materialId;
        }

        public void setMaterialId(String materialId) {
            this.materialId = materialId;
        }

        public String getMaterialTypeName() {
            return materialTypeName;
        }

        public void setMaterialTypeName(String materialTypeName) {
            this.materialTypeName = materialTypeName;
        }

        public String getTypeDesc() {
            return typeDesc;
        }

        public void setTypeDesc(String typeDesc) {
            this.typeDesc = typeDesc;
        }

        private String annexId;
        private String fileUrl;
        private String materialId;
        private String materialTypeName;
        private String typeDesc;
    }


    public static class RecordApplicantAgentVosDTO {
        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getAgentName() {
            return agentName;
        }

        public void setAgentName(String agentName) {
            this.agentName = agentName;
        }

        public String getBirthday() {
            return birthday;
        }

        public void setBirthday(String birthday) {
            this.birthday = birthday;
        }

        public String getContactNum() {
            return contactNum;
        }

        public void setContactNum(String contactNum) {
            this.contactNum = contactNum;
        }

        public String getCredentialNum() {
            return credentialNum;
        }

        public void setCredentialNum(String credentialNum) {
            this.credentialNum = credentialNum;
        }

        public String getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(String credentialType) {
            this.credentialType = credentialType;
        }

        public String getCredentialTypeStr() {
            return credentialTypeStr;
        }

        public void setCredentialTypeStr(String credentialTypeStr) {
            this.credentialTypeStr = credentialTypeStr;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        private String address;
        private String agentName;
        private String birthday;
        private String contactNum;
        private String credentialNum;
        private String credentialType;
        private String credentialTypeStr;
        private Integer gender;
        private String id;
        private String recordId;
    }

    public static class RecordApplicantVosDTO {
        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getApplicantName() {
            return applicantName;
        }

        public void setApplicantName(String applicantName) {
            this.applicantName = applicantName;
        }

        public String getBirthday() {
            return birthday;
        }

        public void setBirthday(String birthday) {
            this.birthday = birthday;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getContactNum() {
            return contactNum;
        }

        public void setContactNum(String contactNum) {
            this.contactNum = contactNum;
        }

        public String getCredentialNum() {
            return credentialNum;
        }

        public void setCredentialNum(String credentialNum) {
            this.credentialNum = credentialNum;
        }

        public Integer getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(Integer credentialType) {
            this.credentialType = credentialType;
        }

        public String getCredentialTypeStr() {
            return credentialTypeStr;
        }

        public void setCredentialTypeStr(String credentialTypeStr) {
            this.credentialTypeStr = credentialTypeStr;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getNationality() {
            return nationality;
        }

        public void setNationality(String nationality) {
            this.nationality = nationality;
        }

        public String getRace() {
            return race;
        }

        public void setRace(String race) {
            this.race = race;
        }

        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        private String address;
        private String applicantName;
        private String birthday;
        private String city;
        private String contactNum;
        private String credentialNum;
        private Integer credentialType;
        private String credentialTypeStr;
        private Integer gender;
        private String id;
        private String nationality;
        private String race;
        private String recordId;
    }

    public static class RecordCorporatonApplicantVosDTO {
        public String getContactNum() {
            return contactNum;
        }

        public void setContactNum(String contactNum) {
            this.contactNum = contactNum;
        }

        public String getCorporationAddress() {
            return corporationAddress;
        }

        public void setCorporationAddress(String corporationAddress) {
            this.corporationAddress = corporationAddress;
        }

        public String getCorporationCredentialNum() {
            return corporationCredentialNum;
        }

        public void setCorporationCredentialNum(String corporationCredentialNum) {
            this.corporationCredentialNum = corporationCredentialNum;
        }

        public String getCorporationCredentialType() {
            return corporationCredentialType;
        }

        public void setCorporationCredentialType(String corporationCredentialType) {
            this.corporationCredentialType = corporationCredentialType;
        }

        public String getCorporationCredentialTypeStr() {
            return corporationCredentialTypeStr;
        }

        public void setCorporationCredentialTypeStr(String corporationCredentialTypeStr) {
            this.corporationCredentialTypeStr = corporationCredentialTypeStr;
        }

        public String getCorporationName() {
            return corporationName;
        }

        public void setCorporationName(String corporationName) {
            this.corporationName = corporationName;
        }

        public Integer getCorporationType() {
            return corporationType;
        }

        public void setCorporationType(Integer corporationType) {
            this.corporationType = corporationType;
        }

        public String getCredentialNum() {
            return credentialNum;
        }

        public void setCredentialNum(String credentialNum) {
            this.credentialNum = credentialNum;
        }

        public Integer getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(Integer credentialType) {
            this.credentialType = credentialType;
        }

        public String getCredentialTypeStr() {
            return credentialTypeStr;
        }

        public void setCredentialTypeStr(String credentialTypeStr) {
            this.credentialTypeStr = credentialTypeStr;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getLegalRepresentative() {
            return legalRepresentative;
        }

        public void setLegalRepresentative(String legalRepresentative) {
            this.legalRepresentative = legalRepresentative;
        }

        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        private String contactNum;
        private String corporationAddress;
        private String corporationCredentialNum;
        private String corporationCredentialType;
        private String corporationCredentialTypeStr;
        private String corporationName;
        private Integer corporationType;
        private String credentialNum;
        private Integer credentialType;
        private String credentialTypeStr;
        private String id;
        private String legalRepresentative;
        private String recordId;
    }
}
