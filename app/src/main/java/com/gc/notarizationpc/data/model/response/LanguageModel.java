package com.gc.notarizationpc.data.model.response;

import com.google.gson.annotations.SerializedName;

/**
 *  语言数据模型
 */

public class LanguageModel {

    /**
     * id
     */
    @SerializedName("id")
    private String id;
    /**
     * dictId
     */
    @SerializedName("dictId")
    private String dictId;
    /**
     * label
     */
    @SerializedName("label")
    private String label;
    /**
     * dictType
     */
    @SerializedName("dictType")
    private String dictType;
    /**
     * description
     */
    @SerializedName("description")
    private Object description;
    /**
     * sortOrder
     */
    @SerializedName("sortOrder")
    private Integer sortOrder;
    /**
     * createBy
     */
    @SerializedName("createBy")
    private String createBy;
    /**
     * updateBy
     */
    @SerializedName("updateBy")
    private String updateBy;
    /**
     * createTime
     */
    @SerializedName("createTime")
    private String createTime;
    /**
     * updateTime
     */
    @SerializedName("updateTime")
    private String updateTime;
    /**
     * remarks
     */
    @SerializedName("remarks")
    private String remarks;
    /**
     * delFlag
     */
    @SerializedName("delFlag")
    private String delFlag;
    /**
     * mechanismId
     */
    @SerializedName("mechanismId")
    private String mechanismId;
    /**
     * dictKind
     */
    @SerializedName("dictKind")
    private Object dictKind;
    /**
     * value
     */
    @SerializedName("value")
    private String value;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDictId() {
        return dictId;
    }

    public void setDictId(String dictId) {
        this.dictId = dictId;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getDictType() {
        return dictType;
    }

    public void setDictType(String dictType) {
        this.dictType = dictType;
    }

    public Object getDescription() {
        return description;
    }

    public void setDescription(Object description) {
        this.description = description;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getMechanismId() {
        return mechanismId;
    }

    public void setMechanismId(String mechanismId) {
        this.mechanismId = mechanismId;
    }

    public Object getDictKind() {
        return dictKind;
    }

    public void setDictKind(Object dictKind) {
        this.dictKind = dictKind;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
