package com.gc.notarizationpc.data.model.response;

import android.text.TextUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 上传材料 所需材料
 *
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/4/16
 */


public class MaterialListResponse implements Serializable {

    public String getMaterialTypeName() {
        return materialTypeName;
    }

    public void setMaterialTypeName(String materialTypeName) {
        this.materialTypeName = materialTypeName;
    }

    public List<MaterialVoListDTO> getMaterialVoList() {
        return materialVoList;
    }

    public void setMaterialVoList(List<MaterialVoListDTO> materialVoList) {
        this.materialVoList = materialVoList;
    }

    private String materialTypeName;
    private List<MaterialVoListDTO> materialVoList;

    public static class MaterialVoListDTO {
        public String getMaterialId() {
            return materialId;
        }

        public void setMaterialId(String materialId) {
            this.materialId = materialId;
        }

        public String getMaterialTypeId() {
            return materialTypeId;
        }

        public void setMaterialTypeId(String materialTypeId) {
            this.materialTypeId = materialTypeId;
        }

        public Integer getTypeSource() {
            return typeSource;
        }

        public void setTypeSource(Integer typeSource) {
            this.typeSource = typeSource;
        }

        public String getTypeSourceStr() {
            return typeSourceStr;
        }

        public void setTypeSourceStr(String typeSourceStr) {
            this.typeSourceStr = typeSourceStr;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getStatusStr() {
            return statusStr;
        }

        public void setStatusStr(String statusStr) {
            this.statusStr = statusStr;
        }

        public String getMaterialTypeName() {
            return materialTypeName;
        }

        public void setMaterialTypeName(String materialTypeName) {
            this.materialTypeName = materialTypeName;
        }

        public String getTypeDesc() {
            return typeDesc;
        }

        public void setTypeDesc(String typeDesc) {
            this.typeDesc = typeDesc;
        }

        public String getFileUrl() {
            return !TextUtils.isEmpty(fileUrl) ? fileUrl : filePath;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public String getAnnexId() {
            return annexId;
        }

        public void setAnnexId(String annexId) {
            this.annexId = annexId;
        }

        private String materialId;
        private String materialTypeId;
        private Integer typeSource;
        private String typeSourceStr;
        private Integer status;
        private String statusStr;
        private String materialTypeName;
        private String typeDesc;
        private String fileUrl;
        private String annexId;
        private String type = "PICTURE"; //CREATE PICTURE
        private String suffixName;//文件后缀

        //以下三个是视频连线需要用到的
        private String fileId;
        private String fileName;
        private String filePath;

        public String getSuffixName() {
            return suffixName;
        }

        public void setSuffixName(String suffixName) {
            this.suffixName = suffixName;
        }

        public String getFileId() {
            return fileId;
        }

        public void setFileId(String fileId) {
            this.fileId = fileId;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public MaterialVoListDTO(String type, String fileUrl) {
            this.fileUrl = fileUrl;
            this.type = type;
        }

        public String getType() {
            return TextUtils.isEmpty(type) ? "PICTURE" : type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }
}
