package com.gc.notarizationpc.data.model.request;

/**
 * @ProjectName:
 * @Package: com.gc.shenggx.data.model
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2023/5/30
 */
public class LoginOnRequest {
    private String loginName;
    private String password;
    private int source;
    private int type;

    public LoginOnRequest(String loginName, String password, int source, int type) {
        this.loginName = loginName;
        this.password = password;
        this.source = source;
        this.type = type;
    }

    @Override
    public String toString() {
        return "LoginOnRequest{" +
                "loginName='" + loginName + '\'' +
                ", password='" + password + '\'' +
                ", source=" + source +
                ", type=" + type +
                '}';
    }
}
