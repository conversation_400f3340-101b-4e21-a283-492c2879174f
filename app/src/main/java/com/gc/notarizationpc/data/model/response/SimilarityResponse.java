package com.gc.notarizationpc.data.model.response;

import java.io.Serializable;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/5/24
 */
public class SimilarityResponse implements Serializable {

    private String annexId;
    private String annexPath;

    public String getAnnexId() {
        return annexId;
    }

    public void setAnnexId(String annexId) {
        this.annexId = annexId;
    }

    public String getAnnexPath() {
        return annexPath;
    }

    public void setAnnexPath(String annexPath) {
        this.annexPath = annexPath;
    }
}
