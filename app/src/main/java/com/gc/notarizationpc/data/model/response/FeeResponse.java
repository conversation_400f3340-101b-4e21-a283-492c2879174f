package com.gc.notarizationpc.data.model.response;

import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: xuh<PERSON>feng
 * @CreateDate: 2024/3/20
 */
public class FeeResponse implements Serializable {

    private Integer hadSetBilling;
    private List<MattersFeeListDTO> mattersFeeList;
    private List<OtherFeeListDTO> otherFeeList;
    private Integer otherTotalFee;
    private Integer reductionFee;
    private Integer totalFee;

    public Integer getHadSetBilling() {
        return hadSetBilling;
    }

    public void setHadSetBilling(Integer hadSetBilling) {
        this.hadSetBilling = hadSetBilling;
    }

    public List<MattersFeeListDTO> getMattersFeeList() {
        return mattersFeeList;
    }

    public void setMattersFeeList(List<MattersFeeListDTO> mattersFeeList) {
        this.mattersFeeList = mattersFeeList;
    }

    public List<OtherFeeListDTO> getOtherFeeList() {
        return otherFeeList;
    }

    public void setOtherFeeList(List<OtherFeeListDTO> otherFeeList) {
        this.otherFeeList = otherFeeList;
    }

    public Integer getOtherTotalFee() {
        return otherTotalFee;
    }

    public void setOtherTotalFee(Integer otherTotalFee) {
        this.otherTotalFee = otherTotalFee;
    }

    public Integer getReductionFee() {
        return reductionFee;
    }

    public void setReductionFee(Integer reductionFee) {
        this.reductionFee = reductionFee;
    }

    public Integer getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Integer totalFee) {
        this.totalFee = totalFee;
    }

    public static class MattersFeeListDTO {
        private Integer copyFee;
        private String mattersId;
        private String mattersName;
        private Integer notarizationFee;
        private Integer num;

        public Integer getCopyFee() {
            return copyFee;
        }

        public void setCopyFee(Integer copyFee) {
            this.copyFee = copyFee;
        }

        public String getMattersId() {
            return mattersId;
        }

        public void setMattersId(String mattersId) {
            this.mattersId = mattersId;
        }

        public String getMattersName() {
            return mattersName;
        }

        public void setMattersName(String mattersName) {
            this.mattersName = mattersName;
        }

        public Integer getNotarizationFee() {
            return notarizationFee;
        }

        public void setNotarizationFee(Integer notarizationFee) {
            this.notarizationFee = notarizationFee;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }
    }

    public static class OtherFeeListDTO {
        private Integer billingItem;
        private Integer totalCost;

        public Integer getBillingItem() {
            return billingItem;
        }

        public void setBillingItem(Integer billingItem) {
            this.billingItem = billingItem;
        }

        public Integer getTotalCost() {
            return totalCost;
        }

        public void setTotalCost(Integer totalCost) {
            this.totalCost = totalCost;
        }
    }
}
