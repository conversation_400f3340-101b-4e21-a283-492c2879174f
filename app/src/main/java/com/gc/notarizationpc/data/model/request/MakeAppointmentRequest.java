package com.gc.notarizationpc.data.model.request;

public class MakeAppointmentRequest {
    /**
     * applicationItem	公证事项		false
     * string
     * bookOrgan	预约公证处		false
     * string
     * endTime	预约办理结束时间		false
     * string(date-time)
     * phoneNum	手机号码		false
     * string
     * predetermineSource	预约来源		false
     * integer(int32)
     * remark	备注		false
     * string
     * startTime	预约办理开始时间		false
     * string(date-time)
     * sysUserId	用户ID		false
     * string
     * translateLanguage	译文		false
     * string
     * usedPlace	使用地		false
     * string
     * verificationCode	验证码		false
     * integer(int32)
     */


    public String getApplicationItem() {
        return applicationItem;
    }

    public void setApplicationItem(String applicationItem) {
        this.applicationItem = applicationItem;
    }

    public String getBookOrgan() {
        return bookOrgan;
    }

    public void setBookOrgan(String bookOrgan) {
        this.bookOrgan = bookOrgan;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public Integer getPredetermineSource() {
        return predetermineSource;
    }

    public void setPredetermineSource(Integer predetermineSource) {
        this.predetermineSource = predetermineSource;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(String sysUserId) {
        this.sysUserId = sysUserId;
    }

    public String getTranslateLanguage() {
        return translateLanguage;
    }

    public void setTranslateLanguage(String translateLanguage) {
        this.translateLanguage = translateLanguage;
    }

    public String getUsedPlace() {
        return usedPlace;
    }

    public void setUsedPlace(String usedPlace) {
        this.usedPlace = usedPlace;
    }

    public Integer getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(Integer verificationCode) {
        this.verificationCode = verificationCode;
    }

    private String applicationItem;
    private String bookOrgan;
    private String endTime;
    private String phoneNum;
    private Integer predetermineSource;
    private String remark;
    private String startTime;
    private String sysUserId;
    private String translateLanguage;
    private String usedPlace;
    private Integer verificationCode;

}
