package com.gc.notarizationpc.data.model.response;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;

import org.w3c.dom.Text;

public class NotaryAffairResponseModel {

    /**
     * mattersName
     */
    @SerializedName("mattersName")
    private String mattersName;
    /**
     * mattersId
     */
    @SerializedName("mattersId")
    private String mattersId;
    /**
     * notarialFee
     */
    @SerializedName("notarialFee")
    private Integer notarialFee;
    /**
     * copyFee
     */
    @SerializedName("copyFee")
    private Integer copyFee;
    /**
     * detailMattersId
     */
    @SerializedName("detailMattersId")
    private Integer detailMattersId;

    private String newNotarial;
    private String hadProtectedContent;
    private String protectedContent;
    private String wrongRelatedMatters;

    public String getNewNotarial() {
        return TextUtils.isEmpty(newNotarial) ? "" : newNotarial;
    }

    public void setNewNotarial(String newNotarial) {
        this.newNotarial = newNotarial;
    }

    public String getHadProtectedContent() {
        return TextUtils.isEmpty(hadProtectedContent) ? "" : hadProtectedContent;
    }

    public void setHadProtectedContent(String hadProtectedContent) {
        this.hadProtectedContent = hadProtectedContent;
    }

    public String getProtectedContent() {
        return TextUtils.isEmpty(protectedContent) ? "" : protectedContent;
    }

    public void setProtectedContent(String protectedContent) {
        this.protectedContent = protectedContent;
    }

    public String getWrongRelatedMatters() {
        return TextUtils.isEmpty(wrongRelatedMatters) ? "" : wrongRelatedMatters;
    }

    public void setWrongRelatedMatters(String wrongRelatedMatters) {
        this.wrongRelatedMatters = wrongRelatedMatters;
    }

    public String getMattersName() {
        return mattersName;
    }

    public void setMattersName(String mattersName) {
        this.mattersName = mattersName;
    }

    public String getMattersId() {
        return mattersId;
    }

    public void setMattersId(String mattersId) {
        this.mattersId = mattersId;
    }

    public Integer getNotarialFee() {
        return notarialFee;
    }

    public void setNotarialFee(Integer notarialFee) {
        this.notarialFee = notarialFee;
    }

    public Integer getCopyFee() {
        return copyFee;
    }

    public void setCopyFee(Integer copyFee) {
        this.copyFee = copyFee;
    }

    public Integer getDetailMattersId() {
        return detailMattersId;
    }

    public void setDetailMattersId(Integer detailMattersId) {
        this.detailMattersId = detailMattersId;
    }
}
