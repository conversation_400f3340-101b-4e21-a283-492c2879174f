package com.gc.notarizationpc.data.model.request;

import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.request
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2024/6/5
 */
public class UpdateSelfServiceMaterialRequest implements Serializable {

    private String consigneeName;
    private String createdOrgan;
    private List<MaterialInfoListDTO> materialInfoList;
    private String phoneNum;
    private String postAddress;
    private Integer receiveWay;
    private String selfId;
    private String sysUserId;

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getCreatedOrgan() {
        return createdOrgan;
    }

    public void setCreatedOrgan(String createdOrgan) {
        this.createdOrgan = createdOrgan;
    }

    public List<MaterialInfoListDTO> getMaterialInfoList() {
        return materialInfoList;
    }

    public void setMaterialInfoList(List<MaterialInfoListDTO> materialInfoList) {
        this.materialInfoList = materialInfoList;
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public String getPostAddress() {
        return postAddress;
    }

    public void setPostAddress(String postAddress) {
        this.postAddress = postAddress;
    }

    public Integer getReceiveWay() {
        return receiveWay;
    }

    public void setReceiveWay(Integer receiveWay) {
        this.receiveWay = receiveWay;
    }

    public String getSelfId() {
        return selfId;
    }

    public void setSelfId(String selfId) {
        this.selfId = selfId;
    }

    public String getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(String sysUserId) {
        this.sysUserId = sysUserId;
    }

    public static class MaterialInfoListDTO {
        private String fileId;
        private String materialTypeId;

        public String getFileId() {
            return fileId;
        }

        public void setFileId(String fileId) {
            this.fileId = fileId;
        }

        public String getMaterialTypeId() {
            return materialTypeId;
        }

        public void setMaterialTypeId(String materialTypeId) {
            this.materialTypeId = materialTypeId;
        }
    }
}
