package com.gc.notarizationpc.data.model.response;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/4/28
 */
public class UpgradeResponse {

    private String apkPackage;
    private String apkPackageUrl;
    private Integer forceUpdates;
    private String renewExplain;//更新说明
    private String thisTimeVersion;//本次更新版本号

    public String getApkPackage() {
        return apkPackage;
    }

    public void setApkPackage(String apkPackage) {
        this.apkPackage = apkPackage;
    }

    public String getApkPackageUrl() {
        return apkPackageUrl;
    }

    public void setApkPackageUrl(String apkPackageUrl) {
        this.apkPackageUrl = apkPackageUrl;
    }

    public Integer getForceUpdates() {
        return forceUpdates;
    }

    public void setForceUpdates(Integer forceUpdates) {
        this.forceUpdates = forceUpdates;
    }

    public String getRenewExplain() {
        return renewExplain;
    }

    public void setRenewExplain(String renewExplain) {
        this.renewExplain = renewExplain;
    }

    public String getThisTimeVersion() {
        return thisTimeVersion;
    }

    public void setThisTimeVersion(String thisTimeVersion) {
        this.thisTimeVersion = thisTimeVersion;
    }
}
