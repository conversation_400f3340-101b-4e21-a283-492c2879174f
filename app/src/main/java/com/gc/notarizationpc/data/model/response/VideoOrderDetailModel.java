package com.gc.notarizationpc.data.model.response;


import java.util.List;


public class VideoOrderDetailModel {
    private String roomId;

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getApplicationItem() {
        return applicationItem;
    }

    public void setApplicationItem(String applicationItem) {
        this.applicationItem = applicationItem;
    }

    public List<CaseApplicantAgentVosDTO> getCaseApplicantAgentVos() {
        return caseApplicantAgentVos;
    }

    public void setCaseApplicantAgentVos(List<CaseApplicantAgentVosDTO> caseApplicantAgentVos) {
        this.caseApplicantAgentVos = caseApplicantAgentVos;
    }

    public List<CaseApplicantVosDTO> getCaseApplicantVos() {
        return caseApplicantVos;
    }

    public void setCaseApplicantVos(List<CaseApplicantVosDTO> caseApplicantVos) {
        this.caseApplicantVos = caseApplicantVos;
    }

    public List<CaseCorporatonApplicantVosDTO> getCaseCorporatonApplicantVos() {
        return caseCorporatonApplicantVos;
    }

    public void setCaseCorporatonApplicantVos(List<CaseCorporatonApplicantVosDTO> caseCorporatonApplicantVos) {
        this.caseCorporatonApplicantVos = caseCorporatonApplicantVos;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getCreatedOrgan() {
        return createdOrgan;
    }

    public void setCreatedOrgan(String createdOrgan) {
        this.createdOrgan = createdOrgan;
    }

    public String getCreatedOrganName() {
        return createdOrganName;
    }

    public void setCreatedOrganName(String createdOrganName) {
        this.createdOrganName = createdOrganName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public List<DocTypeListDTO> getDocTypeList() {
        return docTypeList;
    }

    public void setDocTypeList(List<DocTypeListDTO> docTypeList) {
        this.docTypeList = docTypeList;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<MaterialVoListDTO> getMaterialVoList() {
        return materialVoList;
    }

    public void setMaterialVoList(List<MaterialVoListDTO> materialVoList) {
        this.materialVoList = materialVoList;
    }

    public PaymentListVODTO getPaymentListVO() {
        return paymentListVO;
    }

    public void setPaymentListVO(PaymentListVODTO paymentListVO) {
        this.paymentListVO = paymentListVO;
    }

    public String getPostAddress() {
        return postAddress;
    }

    public void setPostAddress(String postAddress) {
        this.postAddress = postAddress;
    }

    public Integer getReceiveWay() {
        return receiveWay;
    }

    public void setReceiveWay(Integer receiveWay) {
        this.receiveWay = receiveWay;
    }

    public String getTranslateLanguage() {
        return translateLanguage;
    }

    public void setTranslateLanguage(String translateLanguage) {
        this.translateLanguage = translateLanguage;
    }

    public String getUsedPlace() {
        return usedPlace;
    }

    public void setUsedPlace(String usedPlace) {
        this.usedPlace = usedPlace;
    }

    public String getUsedPurpose() {
        return usedPurpose;
    }

    public void setUsedPurpose(String usedPurpose) {
        this.usedPurpose = usedPurpose;
    }

    private String applicationItem;
    private List<CaseApplicantAgentVosDTO> caseApplicantAgentVos;
    private List<CaseApplicantVosDTO> caseApplicantVos;
    private List<CaseCorporatonApplicantVosDTO> caseCorporatonApplicantVos;
    private String caseNo;
    private String createdOrgan;
    private String createdOrganName;
    private String createdTime;
    private List<DocTypeListDTO> docTypeList;
    private String id;
    private List<MaterialVoListDTO> materialVoList;
    private PaymentListVODTO paymentListVO;
    private String postAddress;
    private Integer receiveWay;
    private String translateLanguage;
    private String usedPlace;
    private String usedPurpose;

    private String usedPurposeStr;

    public String getOfficerId() {
        return officerId;
    }

    public void setOfficerId(String officerId) {
        this.officerId = officerId;
    }

    public String getOfficerName() {
        return officerName;
    }

    public void setOfficerName(String officerName) {
        this.officerName = officerName;
    }

    // 办理公证员ID
    private String officerId;
    // 办理公证员名称
    private String officerName;


    public String getUsedPurposeStr() {
        return usedPurposeStr;
    }

    public void setUsedPurposeStr(String usedPurposeStr) {
        this.usedPurposeStr = usedPurposeStr;
    }

    public String getUsedPlaceStr() {
        return usedPlaceStr;
    }

    public void setUsedPlaceStr(String usedPlaceStr) {
        this.usedPlaceStr = usedPlaceStr;
    }

    public String getTranslateLanguageStr() {
        return translateLanguageStr;
    }

    public void setTranslateLanguageStr(String translateLanguageStr) {
        this.translateLanguageStr = translateLanguageStr;
    }

    public String getApplicationItemStr() {
        return applicationItemStr;
    }

    public void setApplicationItemStr(String applicationItemStr) {
        this.applicationItemStr = applicationItemStr;
    }

    private String usedPlaceStr;

    private String translateLanguageStr;

    private String applicationItemStr;

    public static class PaymentListVODTO {
        public Integer getHadSetBilling() {
            return hadSetBilling;
        }

        public void setHadSetBilling(Integer hadSetBilling) {
            this.hadSetBilling = hadSetBilling;
        }

        public List<MattersFeeListDTO> getMattersFeeList() {
            return mattersFeeList;
        }

        public void setMattersFeeList(List<MattersFeeListDTO> mattersFeeList) {
            this.mattersFeeList = mattersFeeList;
        }

        public List<OtherFeeListDTO> getOtherFeeList() {
            return otherFeeList;
        }

        public void setOtherFeeList(List<OtherFeeListDTO> otherFeeList) {
            this.otherFeeList = otherFeeList;
        }

        public Integer getOtherTotalFee() {
            return otherTotalFee;
        }

        public void setOtherTotalFee(Integer otherTotalFee) {
            this.otherTotalFee = otherTotalFee;
        }

        public Integer getReductionFee() {
            return reductionFee;
        }

        public void setReductionFee(Integer reductionFee) {
            this.reductionFee = reductionFee;
        }

        public Integer getTotalFee() {
            return totalFee;
        }

        public void setTotalFee(Integer totalFee) {
            this.totalFee = totalFee;
        }

        private Integer hadSetBilling;
        private List<MattersFeeListDTO> mattersFeeList;
        private List<OtherFeeListDTO> otherFeeList;
        private Integer otherTotalFee;
        private Integer reductionFee;
        private Integer totalFee;


        public static class MattersFeeListDTO {
            public Integer getCopyFee() {
                return copyFee;
            }

            public void setCopyFee(Integer copyFee) {
                this.copyFee = copyFee;
            }

            public String getMattersId() {
                return mattersId;
            }

            public void setMattersId(String mattersId) {
                this.mattersId = mattersId;
            }

            public String getMattersName() {
                return mattersName;
            }

            public void setMattersName(String mattersName) {
                this.mattersName = mattersName;
            }

            public Integer getNotarizationFee() {
                return notarizationFee;
            }

            public void setNotarizationFee(Integer notarizationFee) {
                this.notarizationFee = notarizationFee;
            }

            public Integer getNum() {
                return num;
            }

            public void setNum(Integer num) {
                this.num = num;
            }

            private Integer copyFee;
            private String mattersId;
            private String mattersName;
            private Integer notarizationFee;
            private Integer num;
        }

        public static class OtherFeeListDTO {
            public Integer getBillingItem() {
                return billingItem;
            }

            public void setBillingItem(Integer billingItem) {
                this.billingItem = billingItem;
            }

            public Integer getTotalCost() {
                return totalCost;
            }

            public void setTotalCost(Integer totalCost) {
                this.totalCost = totalCost;
            }

            private Integer billingItem;
            private Integer totalCost;
        }
    }


    public static class CaseApplicantAgentVosDTO {
        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getAgentName() {
            return agentName;
        }

        public void setAgentName(String agentName) {
            this.agentName = agentName;
        }

        public String getBirthday() {
            return birthday;
        }

        public void setBirthday(String birthday) {
            this.birthday = birthday;
        }

        public List<CaseApplicantVoListDTO> getCaseApplicantVoList() {
            return caseApplicantVoList;
        }

        public void setCaseApplicantVoList(List<CaseApplicantVoListDTO> caseApplicantVoList) {
            this.caseApplicantVoList = caseApplicantVoList;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getContactNum() {
            return contactNum;
        }

        public void setContactNum(String contactNum) {
            this.contactNum = contactNum;
        }

        public String getCredentialNum() {
            return credentialNum;
        }

        public void setCredentialNum(String credentialNum) {
            this.credentialNum = credentialNum;
        }

        public String getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(String credentialType) {
            this.credentialType = credentialType;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getNationality() {
            return nationality;
        }

        public void setNationality(String nationality) {
            this.nationality = nationality;
        }

        public String getRace() {
            return race;
        }

        public void setRace(String race) {
            this.race = race;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        private String address;
        private String agentName;
        private String birthday;
        private List<CaseApplicantVoListDTO> caseApplicantVoList;
        private String city;
        private String contactNum;
        private String credentialNum;
        private String credentialType;

        public String getCredentialTypeStr() {
            return credentialTypeStr;
        }

        public void setCredentialTypeStr(String credentialTypeStr) {
            this.credentialTypeStr = credentialTypeStr;
        }

        private String credentialTypeStr;
        private String email;
        private Integer gender;
        private String id;
        private String nationality;
        private String race;
        private String remark;


        public static class CaseApplicantVoListDTO {
            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            private String id;
            private String name;
        }
    }


    public static class CaseApplicantVosDTO {
        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getApplicantName() {
            return applicantName;
        }

        public void setApplicantName(String applicantName) {
            this.applicantName = applicantName;
        }

        public Integer getApplicantType() {
            return applicantType;
        }

        public void setApplicantType(Integer applicantType) {
            this.applicantType = applicantType;
        }

        public String getBirthday() {
            return birthday;
        }

        public void setBirthday(String birthday) {
            this.birthday = birthday;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getContactNum() {
            return contactNum;
        }

        public void setContactNum(String contactNum) {
            this.contactNum = contactNum;
        }

        public String getCredentialNum() {
            return credentialNum;
        }

        public void setCredentialNum(String credentialNum) {
            this.credentialNum = credentialNum;
        }

        public String getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(String credentialType) {
            this.credentialType = credentialType;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getNationality() {
            return nationality;
        }

        public void setNationality(String nationality) {
            this.nationality = nationality;
        }

        public String getRace() {
            return race;
        }

        public void setRace(String race) {
            this.race = race;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        private String address;
        private String applicantName;
        private Integer applicantType;
        private String birthday;
        private String city;
        private String contactNum;
        private String credentialNum;
        private String credentialType;
        private String email;
        private Integer gender;
        private String id;
        private String nationality;
        private String race;
        private String remark;

        public String getCredentialTypeStr() {
            return credentialTypeStr;
        }

        public void setCredentialTypeStr(String credentialTypeStr) {
            this.credentialTypeStr = credentialTypeStr;
        }

        private String credentialTypeStr;

    }


    public static class CaseCorporatonApplicantVosDTO {
        public String getArea() {
            return area;
        }

        public void setArea(String area) {
            this.area = area;
        }

        public String getContactNum() {
            return contactNum;
        }

        public void setContactNum(String contactNum) {
            this.contactNum = contactNum;
        }

        public String getCorporationAddress() {
            return corporationAddress;
        }

        public void setCorporationAddress(String corporationAddress) {
            this.corporationAddress = corporationAddress;
        }

        public String getCorporationCredentialNum() {
            return corporationCredentialNum;
        }

        public void setCorporationCredentialNum(String corporationCredentialNum) {
            this.corporationCredentialNum = corporationCredentialNum;
        }

        public String getCorporationCredentialType() {
            return corporationCredentialType;
        }

        public void setCorporationCredentialType(String corporationCredentialType) {
            this.corporationCredentialType = corporationCredentialType;
        }

        public String getCorporationName() {
            return corporationName;
        }

        public void setCorporationName(String corporationName) {
            this.corporationName = corporationName;
        }

        public Integer getCorporationType() {
            return corporationType;
        }

        public void setCorporationType(Integer corporationType) {
            this.corporationType = corporationType;
        }

        public String getCredentialNum() {
            return credentialNum;
        }

        public void setCredentialNum(String credentialNum) {
            this.credentialNum = credentialNum;
        }

        public String getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(String credentialType) {
            this.credentialType = credentialType;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getLegalRepresentative() {
            return legalRepresentative;
        }

        public void setLegalRepresentative(String legalRepresentative) {
            this.legalRepresentative = legalRepresentative;
        }

        public String getPhoneNum() {
            return phoneNum;
        }

        public void setPhoneNum(String phoneNum) {
            this.phoneNum = phoneNum;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getSealNum() {
            return sealNum;
        }

        public void setSealNum(String sealNum) {
            this.sealNum = sealNum;
        }

        public String getTelautogram() {
            return telautogram;
        }

        public void setTelautogram(String telautogram) {
            this.telautogram = telautogram;
        }

        private String area;
        private String contactNum;
        private String corporationAddress;
        private String corporationCredentialNum;
        private String corporationCredentialType;
        private String corporationName;
        private Integer corporationType;
        private String credentialNum;
        private String credentialType;
        private String email;
        private String id;
        private String legalRepresentative;
        private String phoneNum;
        private String remark;
        private String sealNum;
        private String telautogram;

        public String getCorporationCredentialTypeStr() {
            return corporationCredentialTypeStr;
        }

        public void setCorporationCredentialTypeStr(String corporationCredentialTypeStr) {
            this.corporationCredentialTypeStr = corporationCredentialTypeStr;
        }

        public String getCredentialTypeStr() {
            return credentialTypeStr;
        }

        public void setCredentialTypeStr(String credentialTypeStr) {
            this.credentialTypeStr = credentialTypeStr;
        }

        private String corporationCredentialTypeStr;

        private String credentialTypeStr;
    }


    public static class DocTypeListDTO {
        public List<DocumentVOListDTO> getDocumentVOList() {
            return documentVOList;
        }

        public void setDocumentVOList(List<DocumentVOListDTO> documentVOList) {
            this.documentVOList = documentVOList;
        }

        public String getTypeName() {
            return typeName;
        }

        public void setTypeName(String typeName) {
            this.typeName = typeName;
        }

        private List<DocumentVOListDTO> documentVOList;
        private String typeName;


        public static class DocumentVOListDTO {
            public Integer getCanSign() {
                return canSign;
            }

            public void setCanSign(Integer canSign) {
                this.canSign = canSign;
            }

            public String getDocumentName() {
                return documentName;
            }

            public void setDocumentName(String documentName) {
                this.documentName = documentName;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public Integer getInitSign() {
                return initSign;
            }

            public void setInitSign(Integer initSign) {
                this.initSign = initSign;
            }

            public Integer getMinReadTime() {
                return minReadTime;
            }

            public void setMinReadTime(Integer minReadTime) {
                this.minReadTime = minReadTime;
            }

            public Integer getPersonSignStatus() {
                return personSignStatus;
            }

            public void setPersonSignStatus(Integer personSignStatus) {
                this.personSignStatus = personSignStatus;
            }

            public String getSignDocumentFile() {
                return signDocumentFile;
            }

            public void setSignDocumentFile(String signDocumentFile) {
                this.signDocumentFile = signDocumentFile;
            }

            public List<String> getSignDocumentFileUrl() {
                return (List<String>) signDocumentFileUrl;
            }

            public void setSignDocumentFileUrl(List<String> signDocumentFileUrl) {
                this.signDocumentFileUrl = signDocumentFileUrl;
            }

            public Integer getSignStatus() {
                return signStatus;
            }

            public void setSignStatus(Integer signStatus) {
                this.signStatus = signStatus;
            }

            public String getUnsignDocumentFile() {
                return unsignDocumentFile;
            }

            public void setUnsignDocumentFile(String unsignDocumentFile) {
                this.unsignDocumentFile = unsignDocumentFile;
            }

            public List<String> getUnsignDocumentFileUrl() {
                return (List<String>) unsignDocumentFileUrl;
            }

            public void setUnsignDocumentFileUrl(List<String> unsignDocumentFileUrl) {
                this.unsignDocumentFileUrl = unsignDocumentFileUrl;
            }

            private Integer canSign;
            private String documentName;
            private String id;
            private Integer initSign;
            private Integer minReadTime;
            private Integer personSignStatus;
            private String signDocumentFile;
            private List<?> signDocumentFileUrl;
            private Integer signStatus;
            private String unsignDocumentFile;
            private List<?> unsignDocumentFileUrl;
        }
    }


    public static class MaterialVoListDTO {
        private String annexId;
        private String fileUrl;
        private String materialId;
        private String materialTypeName;
        private String typeDesc;

        public String getAnnexId() {
            return annexId;
        }

        public void setAnnexId(String annexId) {
            this.annexId = annexId;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public String getMaterialId() {
            return materialId;
        }

        public void setMaterialId(String materialId) {
            this.materialId = materialId;
        }

        public String getMaterialTypeName() {
            return materialTypeName;
        }

        public void setMaterialTypeName(String materialTypeName) {
            this.materialTypeName = materialTypeName;
        }

        public String getTypeDesc() {
            return typeDesc;
        }

        public void setTypeDesc(String typeDesc) {
            this.typeDesc = typeDesc;
        }
    }
}
