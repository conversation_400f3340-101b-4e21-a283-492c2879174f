package com.gc.notarizationpc.data;


import com.gc.notarizationpc.data.model.request.AddSelfServiceRequest;
import com.gc.notarizationpc.data.model.request.CancelConnectOfficeRequest;
import com.gc.notarizationpc.data.model.request.ConnectNotaryRequest;
import com.gc.notarizationpc.data.model.request.LineOfficeRequest;
import com.gc.notarizationpc.data.model.request.LoginOnRequest;
import com.gc.notarizationpc.data.model.request.MacAddressRequest;
import com.gc.notarizationpc.data.model.request.MakeAppointmentRequest;
import com.gc.notarizationpc.data.model.request.OfficeListRequest;
import com.gc.notarizationpc.data.model.request.SignDocRequest;
import com.gc.notarizationpc.data.model.request.StartApplyRequest;
import com.gc.notarizationpc.data.model.request.UpdateSelfServiceMaterialRequest;
import com.gc.notarizationpc.data.model.request.VideoAddRequest;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.network.BindObservable;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.network.NoHeaderRetrofitUtils;
import me.goldze.mvvmhabit.network.RetrofitUtils;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.http.Part;

/**
 * ****************************************************************
 * 文件描述 : 实例化请求方法 <br>
 * ****************************************************************
 */
public class RequestUtil {

    public static void LoginOn(LoginOnRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .LoginOn(request), observer);
    }

    public static void uploadSingleFile(MultipartBody.Part file, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .uploadSingleFile(file), observer);
    }

    public static void getAllOfficerListAPI(OfficeListRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getAllOfficerListAPI(request), observer);
    }

    public static void lineOffice(LineOfficeRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .lineOffice(request), observer);
    }

    public static void loginByMac(String grantType, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                NoHeaderRetrofitUtils.getInstance().generateReq(ApiService.class)
                        .loginByMac(grantType), observer);
    }

    public static void getHomeModule(String macAdress, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getHomeModule(macAdress), observer);
    }

    /**
     * 获取公证处区域列表
     */
    public static void getNotaryAreaList(MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getNotaryAreaList(), observer);
    }

    /**
     * 获取语言列表
     */
    public static void getNotaryLanguageList(MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getNotaryLanguageList(), observer);
    }

    /**
     * 预约办证
     */
    public static void addNotaryMatters(MakeAppointmentRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .addNotaryMatters(request), observer);
    }

    /**
     * 获取公证处列表
     */
    public static void getMachineByMac(HashMap<String, String> macAddress, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getMachineByMac(macAddress), observer);
    }

    /**
     * 获取短信验证码
     */
    public static void getMessageCode(HashMap<String, String> mobile, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getMessageCode(mobile), observer);
    }

    /**
     * 获取公证事项
     */
    public static void getNotaryMatters(MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getNotaryMatters(), observer);
    }

    /**
     * 获取公证事项
     */
    public static void recordPreProcessListMatters(String mechanismId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .recordPreProcessListMatters(mechanismId), observer);
    }

    /**
     * 获取案件详情
     */
    public static void getCaseDetail(String id, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getCaseDetail(id), observer);
    }

    /**
     * 案件订单列表
     */
    public static void getOrderList(Map<String, Object> dataSource, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getOrderList(dataSource), observer);
    }

    /**
     * 自助订单列表
     */
    public static void getSelfOrderList(Map<String, Object> dataSource, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getSelfOrderList(dataSource), observer);
    }

    /**
     * 预约列表
     */
    public static void getAppointmentList(Map<String, Object> dataSource, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getAppointmentList(dataSource), observer);
    }

    /**
     * 视频受理室订单新增
     */
    public static void addVideoOrder(VideoAddRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .addVideoOrder(request), observer);
    }

    /**
     * 视频连线订单新增
     */
    public static void addVideoLineOrder(VideoAddRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .addVideoLineOrder(request), observer);
    }

    /**
     * 文件上传
     */
    public static void uploadFile(MultipartBody.Part file, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .uploadFile(file), observer);
    }

    /**
     * 开始申办
     */
    public static void addVideoOrder(StartApplyRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .startApply(request), observer);
    }

    /**
     * 根据mac地址查询关联的所属区域
     */
    public static void findIntegratedMachineBindArea(MacAddressRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .findIntegratedMachineBindArea(request), observer);
    }

    /**
     * 指定连线公证人员
     */
    public static void lineNotary(ConnectNotaryRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .lineNotary(request), observer);
    }

    /**
     * 视频咨询指定连线公证人员
     */
    public static void consultLineNotary(ConnectNotaryRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .consultLineNotary(request), observer);
    }

    /**
     * 根据公证人员ID查询公证人员状态
     */
    public static void refreshOfficeStateByOfficeId(String notaryId, String officeId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .refreshOfficeStateByOfficeId(notaryId, officeId), observer);
    }

    /**
     * 取消连线公证员
     */
    public static void cancelConnectOffice(CancelConnectOfficeRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .cancelConnectOffice(request), observer);
    }

    /**
     * 匹配公证员
     */
    public static void matchNotary(ConnectNotaryRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .matchNotary(request), observer);
    }

    /**
     * 取消匹配公证员
     */
    public static void cancelNotaryMatch(CancelConnectOfficeRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .cancelNotaryMatch(request), observer);
    }

    /**
     * 申请信息
     */
    public static void getCaseInfo(String caseInfoId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getCaseInfo(caseInfoId), observer);
    }

    /**
     * 缴费信息
     */
    public static void getPaymentList(String caseInfoId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getPaymentList(caseInfoId), observer);
    }

    /**
     * 查阅文书
     */
    public static void listApplicantsAndDocuments(String caseInfoId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .listApplicantsAndDocuments(caseInfoId), observer);
    }

    /**
     * 公证文书签名(批量)
     */
    public static void signMultiDocument(SignDocRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .signMultiDocument(request), observer);
    }

    /**
     * 查询所需材料
     */
    public static void listMaterialByCaseInfoId(String caseInfoId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .listMaterialByCaseInfoId(caseInfoId), observer);
    }


    /**
     * 视频订单详情
     */
    public static void getVideoOrderDetail(String orderId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getVideoOrderDetail(orderId), observer);
    }

    /**
     * 公证用途全局字典
     */
    public static void getNotaryUse(String type, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getNotaryUse(type), observer);
    }

    /**
     * 终止案件
     */
    public static void terminateCase(Map<String, String> data, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .terminateCase(data), observer);
    }

    /**
     * 上传材料
     */
    public static void uploadMaterial(RequestBody entryptStr, @Part MultipartBody.Part file, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .uploadMaterial(entryptStr, file), observer);
    }

    /**
     * 用户清空重传
     */
    public static void clearMaterialAndReUploadByUser(Map<String, Object> param, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .clearMaterialAndReUploadByUser(param), observer);
    }

    /**
     * 获得设备配置管理
     */
    public static void getCfg(Map<String, String> param, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getCfg(param), observer);
    }

    /**
     * 视频咨询视频连线成功之后更新咨询记录状态
     */
    public static void justState(Map<String, String> param, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .justState(param), observer);
    }

    /**
     * 获取用户信息
     */
    public static void getUserInformation(Map<String, Object> param, MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).getUserInformation(param), observer);
    }

    /**
     * 验证手机号收到的短信验证码
     */
    public static void verifyPhoneCode(Map<String, Object> data, MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).validateCode(data), observer);
    }

    /**
     * 视频咨询查询上传材料
     */
    public static void listOtherMaterial(String recordId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .listOtherMaterial(recordId), observer);
    }

    /**
     * 视频咨询删除材料
     */
    public static void delOtherMaterial(String recordId, String fileId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .delOtherMaterial(recordId, fileId), observer);
    }

    /**
     * 视频咨询上传材料
     */
    public static void uploadOtherMaterialBindRecord(RequestBody recordId, @Part MultipartBody.Part file, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .uploadOtherMaterialBindRecord(recordId, file), observer);
    }

    /**
     * app 升级
     */
    public static void upgradeApp(Map<String, String> param, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .upgradeApp(param), observer);
    }

    /**
     * 定位报告
     */
    public static void getLocationReport(Map<String, Object> data, MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).locationReport(data), observer);
    }

    /**
     * 人脸对比相似度，生成pdf
     */
    public static void universalSimilarity(RequestBody username, @Part MultipartBody.Part file, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .universalSimilarity(username, file), observer);
    }


    /**
     * 人脸对比相似度，生成pdf（新）
     */
    public static void faceDetectToPDF(Map<String, Object> data, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .faceDetectToPDF(data), observer);
    }

    /**
     * 根据用途获取事项 useType 出国留学--1->学习 定居移民--2->定居 探亲旅游--3->探亲 商务劳务--5-->劳务 境外其他--4-->其他
     */
    public static void getMattersByUseType(String useType, String mechanismId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getMattersByUseType(useType, mechanismId), observer);
    }

    /**
     * 获取公证处列表
     */
    public static void getListNotaryByRegion(OfficeListRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).getListNotaryByRegion(request), observer);
    }

    /**
     * 获取个人证件类型
     */
    public static void getTypePerson(MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getTypePerson(), observer);
    }

    /**
     * 获取企业证件类型
     */
    public static void getEnterpriseCertificateType(MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getEnterpriseCertificateType(), observer);
    }

    /**
     * 新增自助办理记录
     */
    public static void addSelfService(AddSelfServiceRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .addSelfService(request), observer);
    }

    /**
     * 新增预受理办理记录
     */
    public static void addPreProcess(AddSelfServiceRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .addPreProcess(request), observer);
    }

    /**
     * 小一体机新增用户
     */
    public static void userAdd(Map<String, Object> dataSource, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .userAdd(dataSource), observer);
    }

    /**
     * 自助办证-查询所需材料
     */
    public static void listRequiredMaterial(String mechanismId, String recordId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .listRequiredMaterial(mechanismId, recordId), observer);
    }

    /**
     * 案件预受理-小一体机-查询所需材料
     */
    public static void listRequiredRecordPreProcessMaterial(String mechanismId, String recordId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .listRequiredRecordPreProcessMaterial(mechanismId, recordId), observer);
    }

    /**
     * 自助办证-更新所需材料
     */
    public static void updateSelfServiceMaterial(UpdateSelfServiceMaterialRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .updateSelfServiceMaterial(request), observer);
    }

    /**
     * 案件预受理-小一体机-更新材料
     */
    public static void updateRecordPreProcessMaterial(UpdateSelfServiceMaterialRequest request, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .updateRecordPreProcessMaterial(request), observer);
    }

    /**
     * 自助办证-获取文书
     */
    public static void listDocument(Map<String, Object> dataSource, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .listDocument(dataSource), observer);
    }

    /**
     * 案件预受理-小一体机-获取文书
     */
    public static void recordPreListDocument(Map<String, Object> dataSource, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .recordPreListDocument(dataSource), observer);
    }

    /**
     * 自助办证-文书签字
     */
    public static void signDocument(Map<String, Object> dataSource, MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).signDocument(dataSource), observer);
    }

    /**
     * 自助办证-删除自助订单
     */

    public static void selfDelete(String orderId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).selfDelete(orderId), observer);
    }

    /**
     * 自助办证-获取自助订单详情
     */
    public static void getSelfServiceOrderDetail(String orderId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).selfServiceOrderDetail(orderId), observer);
    }

    /**
     * 自助办证-终止申办
     */
    public static void endApply(String id, MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).endApply(id), observer);
    }

    /**
     * 获取配置的公证事项
     */
    public static void getNotaryItemByUser(MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).getNotaryItemByUser(), observer);
    }

    /**
     * 案件预受理-小一体机-完成提交
     */
    public static void recordPreProcessSubmit(String recordId, MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).recordPreProcessSubmit(recordId), observer);
    }

    /**
     * 获取sm2公钥
     *
     * @param observer
     */
    public static void getPublicKey(MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                NoHeaderRetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getPublicKey(), observer);
    }

    /**
     * 视频办证-缴费信息
     */
    public static void payMentRecords(Map<String, Object> dataSource, MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).payMentRecords(dataSource), observer);
    }


    /**
     * 获取配置的公证用途列表
     */
    public static void getPurPoseList(MyObserver observer) {
        BindObservable.getInstance().bindObservable(RetrofitUtils.getInstance().generateReq(ApiService.class).getPurPoseList(), observer);
    }

    /**
     * 自助办证获取事项
     */
    public static void getSelfMatters(MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                RetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getSelfMatters(), observer);
    }

    /**
     * 获取图形验证码
     */
    public static void getGraphCode(Map<String, Object> data, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                NoHeaderRetrofitUtils.getInstance().generateReq(ApiService.class)
                        .getGraphCode(data), observer);
    }

    /**
     * 校验图形验证码
     */
    public static void checkGraphCode(String captchaType, String pointJson, String token, MyObserver observer) {
        BindObservable.getInstance().bindObservable(
                NoHeaderRetrofitUtils.getInstance().generateReq(ApiService.class)
                        .checkCode(captchaType, pointJson, token), observer);
    }
}


