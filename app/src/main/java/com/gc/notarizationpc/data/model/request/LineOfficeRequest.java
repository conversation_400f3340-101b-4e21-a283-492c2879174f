package com.gc.notarizationpc.data.model.request;

import java.util.HashMap;
import java.util.Objects;

public class LineOfficeRequest {

    /**
     * mechanismId
     */
    private final String mechanismId;
    /**
     * mobile
     */
    private final String mobile;
    /**
     * officeId
     */
    private final String officeId;
    /**
     * recordId
     */
    private final String recordId;
    /**
     * type
     */
    private final Integer type;
    /**
     * userId
     */
    private final String userId;
    /**
     * userName
     */
    private final String userName;

    public LineOfficeRequest(HashMap<String,Object> map) {
        this.mechanismId = map.get("mechanismId") != null ? Objects.requireNonNull(map.get("mechanismId")).toString() : "";
        this.mobile = map.get("mobile") != null ? Objects.requireNonNull(map.get("mobile")).toString() : "";
        this.officeId = map.get("officeId") != null ? Objects.requireNonNull(map.get("officeId")).toString() : "";
        this.type = map.get("type") != null ? Integer.parseInt(Objects.requireNonNull(map.get("type")).toString()) : 0;
        this.userId = map.get("userId") != null ? Objects.requireNonNull(map.get("userId")).toString() : "";
        this.userName = map.get("userName") != null ? Objects.requireNonNull(map.get("userName")).toString() : "";
        this.recordId = map.get("recordId") != null ? Objects.requireNonNull(map.get("recordId")).toString() : "";
    }

    public HashMap<String, Object> getMap() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("mechanismId", mechanismId);
        map.put("mobile", mobile);
        map.put("officeId", officeId);
        map.put("recordId", recordId);
        map.put("type", type);
        map.put("userId", userId);
        map.put("userName", userName);
        return map;
    }
}
