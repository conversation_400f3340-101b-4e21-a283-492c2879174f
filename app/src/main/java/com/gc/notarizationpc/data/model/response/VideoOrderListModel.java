package com.gc.notarizationpc.data.model.response;

import java.io.Serializable;
import java.util.List;


public class VideoOrderListModel implements Serializable {

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public List<DataListDTO> getDataList() {
        return dataList;
    }

    public void setDataList(List<DataListDTO> dataList) {
        this.dataList = dataList;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    private Integer currentPage;
    private List<DataListDTO> dataList;
    private Integer pageSize;
    private Integer total;

    /**
     * applicantName	当事人姓名	string
     * createdTime	案件创建时间	string
     * evaluate		boolean
     * id	案件主键	string
     * item	公证事项	string
     * mechanismManageId	公证处ID	string
     * mechanismManageName	公证处名称	string
     * notaryId	公证员ID	string
     * notaryName	公证员名称	string
     * status	案件状态 5-受理中 6-待审批 7-待发证 8-待归档 9-已归档 10-待阅读文书 11-待补充材料 12-待支付 91-已终止	integer
     */

    public static class DataListDTO implements  Serializable{
        public String roomId;

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public String getApplicantName() {
            return applicantName;
        }

        public void setApplicantName(String applicantName) {
            this.applicantName = applicantName;
        }

        public String getCreatedTime() {
            return createdTime;
        }

        public void setCreatedTime(String createdTime) {
            this.createdTime = createdTime;
        }

        public Boolean getEvaluate() {
            return evaluate;
        }

        public void setEvaluate(Boolean evaluate) {
            this.evaluate = evaluate;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getItem() {
            return item;
        }

        public void setItem(String item) {
            this.item = item;
        }

        public String getMechanismManageId() {
            return mechanismManageId;
        }

        public void setMechanismManageId(String mechanismManageId) {
            this.mechanismManageId = mechanismManageId;
        }

        public String getMechanismManageName() {
            return mechanismManageName;
        }

        public void setMechanismManageName(String mechanismManageName) {
            this.mechanismManageName = mechanismManageName;
        }

        public String getNotaryId() {
            return notaryId;
        }

        public void setNotaryId(String notaryId) {
            this.notaryId = notaryId;
        }

        public String getNotaryName() {
            return notaryName;
        }

        public void setNotaryName(String notaryName) {
            this.notaryName = notaryName;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        private String applicantName;
        private String createdTime;
        private Boolean evaluate;
        private String id;
        private String item;
        private String mechanismManageId;
        private String mechanismManageName;
        private String notaryId;
        private String notaryName;
        private Integer status;
        private String statusStr;

        public String getStatusStr() {
            return statusStr;
        }

        public void setStatusStr(String statusStr) {
            this.statusStr = statusStr;
        }
    }
}
