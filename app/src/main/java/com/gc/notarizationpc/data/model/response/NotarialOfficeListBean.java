package com.gc.notarizationpc.data.model.response;

import java.util.List;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model
 * @Description:
 * @Author: xuh<PERSON>feng
 * @CreateDate: 2024/1/31
 */
public class NotarialOfficeListBean {

    private String address;
    private String busiTime;
    private GroupVoDTO groupVo;
    private String id;
    private String mechanismName;
    private boolean isChoose;

    public boolean isChoose() {
        return isChoose;
    }

    public void setChoose(boolean choose) {
        isChoose = choose;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBusiTime() {
        return busiTime;
    }

    public void setBusiTime(String busiTime) {
        this.busiTime = busiTime;
    }

    public GroupVoDTO getGroupVo() {
        return groupVo;
    }

    public void setGroupVo(GroupVoDTO groupVo) {
        this.groupVo = groupVo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMechanismName() {
        return mechanismName;
    }

    public void setMechanismName(String mechanismName) {
        this.mechanismName = mechanismName;
    }

    public static class GroupVoDTO {
        private List<OfficerListDTO> officerList;//公证员列表
        private List<OfficerListDTO> officerOnDutyList;//

        public List<OfficerListDTO> getOfficerList() {
            return officerList;
        }

        public void setOfficerList(List<OfficerListDTO> officerList) {
            this.officerList = officerList;
        }

        public List<OfficerListDTO> getOfficerOnDutyList() {
            return officerOnDutyList;
        }

        public void setOfficerOnDutyList(List<OfficerListDTO> officerOnDutyList) {
            this.officerOnDutyList = officerOnDutyList;
        }

        public static class OfficerListDTO {
            private String officeId;
            private String officeName;
            private Integer state;
            private boolean isChoose;
            private boolean isClickable;
            private String headImg;
            private int sourceFrom;

            private String phoneNumber;

            public String getPhoneNumber() {
                return phoneNumber;
            }

            public void setPhoneNumber(String phoneNumber) {
                this.phoneNumber = phoneNumber;
            }

            public int getSourceFrom() {
                return sourceFrom;
            }

            public void setSourceFrom(int sourceFrom) {
                this.sourceFrom = sourceFrom;
            }

            public String getHeadImg() {
                return headImg;
            }

            public void setHeadImg(String headImg) {
                this.headImg = headImg;
            }

            public boolean isClickable() {
                return isClickable;
            }

            public void setClickable(boolean clickable) {
                isClickable = clickable;
            }

            public boolean isChoose() {
                return isChoose;
            }

            public void setChoose(boolean choose) {
                isChoose = choose;
            }

            public String getOfficeId() {
                return officeId;
            }

            public void setOfficeId(String officeId) {
                this.officeId = officeId;
            }

            public String getOfficeName() {
                return officeName;
            }

            public void setOfficeName(String officeName) {
                this.officeName = officeName;
            }

            public Integer getState() {
                return state;
            }

            public void setState(Integer state) {
                this.state = state;
            }
        }

        public static class OfficerOnDutyListDTO {
            private String officeId;
            private String officeName;
            private Integer state;

            public String getOfficeId() {
                return officeId;
            }

            public void setOfficeId(String officeId) {
                this.officeId = officeId;
            }

            public String getOfficeName() {
                return officeName;
            }

            public void setOfficeName(String officeName) {
                this.officeName = officeName;
            }

            public Integer getState() {
                return state;
            }

            public void setState(Integer state) {
                this.state = state;
            }
        }
    }
}
