package com.gc.notarizationpc.data.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:公证处数据模型
 *
 * */

public class NotaryOrderModel{

    /**
     * id
     */
    @SerializedName("id")
    private String id;
    /**
     * createdBy
     */
    @SerializedName("createdBy")
    private String createdBy;
    /**
     * createdTime
     */
    @SerializedName("createdTime")
    private String createdTime;
    /**
     * updatedBy
     */
    @SerializedName("updatedBy")
    private Object updatedBy;
    /**
     * updatedTime
     */
    @SerializedName("updatedTime")
    private Object updatedTime;
    /**
     * deleteFlag
     */
    @SerializedName("deleteFlag")
    private Integer deleteFlag;
    /**
     * deviceType
     */
    @SerializedName("deviceType")
    private Integer deviceType;
    /**
     * macAddress
     */
    @SerializedName("macAddress")
    private String macAddress;
    /**
     * provinceCode
     */
    @SerializedName("provinceCode")
    private String provinceCode;
    /**
     * cityCode
     */
    @SerializedName("cityCode")
    private String cityCode;
    /**
     * areaCode
     */
    @SerializedName("areaCode")
    private String areaCode;
    /**
     * deploySiteType
     */
    @SerializedName("deploySiteType")
    private Integer deploySiteType;
    /**
     * deploySite
     */
    @SerializedName("deploySite")
    private String deploySite;
    /**
     * deploySiteName
     */
    @SerializedName("deploySiteName")
    private String deploySiteName;
    /**
     * configId
     */
    @SerializedName("configId")
    private String configId;
    /**
     * configName
     */
    @SerializedName("configName")
    private Object configName;
    /**
     * remark
     */
    @SerializedName("remark")
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public Object getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Object updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Object getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Object updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Integer getDeploySiteType() {
        return deploySiteType;
    }

    public void setDeploySiteType(Integer deploySiteType) {
        this.deploySiteType = deploySiteType;
    }

    public String getDeploySite() {
        return deploySite;
    }

    public void setDeploySite(String deploySite) {
        this.deploySite = deploySite;
    }

    public String getDeploySiteName() {
        return deploySiteName;
    }

    public void setDeploySiteName(String deploySiteName) {
        this.deploySiteName = deploySiteName == null ? "" : deploySiteName;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public Object getConfigName() {
        return configName;
    }

    public void setConfigName(Object configName) {
        this.configName = configName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
