package com.gc.notarizationpc.data.model.response;

import android.text.TextUtils;
import android.util.Log;

import org.w3c.dom.Text;

import java.util.List;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2024/5/28
 */
public class NotaryMatterItem {

    private String initial;
    private List<MattersInfosDTO> mattersInfos;

    public String getInitial() {
        return initial;
    }

    public void setInitial(String initial) {
        this.initial = initial;
    }

    public List<MattersInfosDTO> getMattersInfos() {
        return mattersInfos;
    }

    public void setMattersInfos(List<MattersInfosDTO> mattersInfos) {
        this.mattersInfos = mattersInfos;
    }

    public static class MattersInfosDTO {
        private String conditionDesc;
        private Integer copyFee;
        private String firstUpperLetter;
        private String itemDesc;
        private String materialDesc;
        private String mattersCode;
        private String mattersId;
        private String mattersName;
        private Integer notarizationFee;
        private String parentMattersCode;
        private String roleDtl;
        private String notarizationNumber = "1";//份数
        private String newNotarial;
        private String hadProtectedContent;
        private String protectedContent;
        private String wrongRelatedMatters;

        public String getNotarizationNumber() {
            if (!TextUtils.isEmpty(notarizationNumber)) {
                Log.e("Test", "修改一下公证份数：" + String.valueOf(Integer.parseInt(notarizationNumber)));
                return String.valueOf(Integer.parseInt(notarizationNumber));
            }
            return "1";

        }

        public String getNewNotarial() {
            return TextUtils.isEmpty(newNotarial) ? "" : newNotarial;
        }

        public void setNewNotarial(String newNotarial) {
            this.newNotarial = newNotarial;
        }

        public String getHadProtectedContent() {
            return TextUtils.isEmpty(hadProtectedContent) ? "" : hadProtectedContent;
        }

        public void setHadProtectedContent(String hadProtectedContent) {
            this.hadProtectedContent = hadProtectedContent;
        }

        public String getProtectedContent() {
            return TextUtils.isEmpty(protectedContent) ? "" : protectedContent;
        }

        public void setProtectedContent(String protectedContent) {
            this.protectedContent = protectedContent;
        }

        public String getWrongRelatedMatters() {
            return TextUtils.isEmpty(wrongRelatedMatters) ? "" : wrongRelatedMatters;
        }

        public void setWrongRelatedMatters(String wrongRelatedMatters) {
            this.wrongRelatedMatters = wrongRelatedMatters;
        }

        public void setNotarizationNumber(String notarizationNumber) {
            this.notarizationNumber = notarizationNumber;
        }

        public String getConditionDesc() {
            return conditionDesc;
        }

        public void setConditionDesc(String conditionDesc) {
            this.conditionDesc = conditionDesc;
        }

        public Integer getCopyFee() {
            return copyFee == null ? 0 : copyFee;
        }

        public void setCopyFee(Integer copyFee) {
            this.copyFee = copyFee;
        }

        public String getFirstUpperLetter() {
            return firstUpperLetter;
        }

        public void setFirstUpperLetter(String firstUpperLetter) {
            this.firstUpperLetter = firstUpperLetter;
        }

        public String getItemDesc() {
            return itemDesc;
        }

        public void setItemDesc(String itemDesc) {
            this.itemDesc = itemDesc;
        }

        public String getMaterialDesc() {
            return materialDesc;
        }

        public void setMaterialDesc(String materialDesc) {
            this.materialDesc = materialDesc;
        }

        public String getMattersCode() {
            return mattersCode;
        }

        public void setMattersCode(String mattersCode) {
            this.mattersCode = mattersCode;
        }

        public String getMattersId() {
            return mattersId;
        }

        public void setMattersId(String mattersId) {
            this.mattersId = mattersId;
        }

        public String getMattersName() {
            return mattersName;
        }

        public void setMattersName(String mattersName) {
            this.mattersName = mattersName;
        }

        public Integer getNotarizationFee() {
            return notarizationFee == null ? 0 : notarizationFee;
        }

        public void setNotarizationFee(Integer notarizationFee) {
            this.notarizationFee = notarizationFee;
        }

        public String getParentMattersCode() {
            return parentMattersCode;
        }

        public void setParentMattersCode(String parentMattersCode) {
            this.parentMattersCode = parentMattersCode;
        }

        public String getRoleDtl() {
            return roleDtl;
        }

        public void setRoleDtl(String roleDtl) {
            this.roleDtl = roleDtl;
        }
    }
}
