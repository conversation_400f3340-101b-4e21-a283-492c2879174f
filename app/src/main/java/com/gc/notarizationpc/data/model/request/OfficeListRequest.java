package com.gc.notarizationpc.data.model.request;

import java.io.Serializable;

/**
 * @Author:We<PERSON><PERSON>
 * @Date:2024/1/31
 * @CreateTime: 10:00
 * @PackageName:com.gc.notarizationpc.data.model
 */

/**
 * city	市		true
 * string
 * macAddress	macAddress		false
 * string
 * mechanismName	公证处名称/公证员名称		false
 * string
 * province	省		true
 * string
 * searchType	搜索类型,1-初始化列表，2-搜索		false
 * integer(int32)
 */
public class OfficeListRequest implements Serializable {
    private String macAddress;
    private String city;
    private String mechanismName;
    private String province;
    private Integer searchType;//,1-初始化列表，2-搜索

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getMechanismName() {
        return mechanismName;
    }

    public void setMechanismName(String mechanismName) {
        this.mechanismName = mechanismName;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public Integer getSearchType() {
        return searchType;
    }

    public void setSearchType(Integer searchType) {
        this.searchType = searchType;
    }

}
