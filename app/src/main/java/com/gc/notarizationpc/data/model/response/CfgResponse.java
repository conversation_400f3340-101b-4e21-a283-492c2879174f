package com.gc.notarizationpc.data.model.response;

import java.util.List;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.data.model.response
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2024/4/23
 */
public class CfgResponse {

    private String cfgName;
    private String createdBy;
    private String createdTime;
    private Integer deleteFlag;
    private String firstPageFileId;
    private String firstPageFileUrl;
    private String firstPageTitle;
    private List<FunctionVoListDTO> functionVoList;
    private String id;
    private String remark;
    private String startPageFileId;
    private String startPageFileUrl;
    private Integer status;
    private Integer sysStatus;
    private String updatedBy;
    private String updatedTime;

    public String getCfgName() {
        return cfgName;
    }

    public void setCfgName(String cfgName) {
        this.cfgName = cfgName;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getFirstPageFileId() {
        return firstPageFileId;
    }

    public void setFirstPageFileId(String firstPageFileId) {
        this.firstPageFileId = firstPageFileId;
    }

    public String getFirstPageFileUrl() {
        return firstPageFileUrl;
    }

    public void setFirstPageFileUrl(String firstPageFileUrl) {
        this.firstPageFileUrl = firstPageFileUrl;
    }

    public String getFirstPageTitle() {
        return firstPageTitle;
    }

    public void setFirstPageTitle(String firstPageTitle) {
        this.firstPageTitle = firstPageTitle;
    }

    public List<FunctionVoListDTO> getFunctionVoList() {
        return functionVoList;
    }

    public void setFunctionVoList(List<FunctionVoListDTO> functionVoList) {
        this.functionVoList = functionVoList;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStartPageFileId() {
        return startPageFileId;
    }

    public void setStartPageFileId(String startPageFileId) {
        this.startPageFileId = startPageFileId;
    }

    public String getStartPageFileUrl() {
        return startPageFileUrl;
    }

    public void setStartPageFileUrl(String startPageFileUrl) {
        this.startPageFileUrl = startPageFileUrl;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(Integer sysStatus) {
        this.sysStatus = sysStatus;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public static class FunctionVoListDTO {
        private String baseId;
        private Integer cfgOrder;
        private String createdBy;
        private String createdTime;
        private Integer deleteFlag;
        private String funcName;
        private String id;
        private Integer linkType;
        private String linkUrl;
        private String updatedBy;
        private String updatedTime;
        private boolean choose;

        public boolean isChoose() {
            return choose;
        }

        public void setChoose(boolean choose) {
            this.choose = choose;
        }

        public String getBaseId() {
            return baseId;
        }

        public void setBaseId(String baseId) {
            this.baseId = baseId;
        }

        public Integer getCfgOrder() {
            return cfgOrder;
        }

        public void setCfgOrder(Integer cfgOrder) {
            this.cfgOrder = cfgOrder;
        }

        public String getCreatedBy() {
            return createdBy;
        }

        public void setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
        }

        public String getCreatedTime() {
            return createdTime;
        }

        public void setCreatedTime(String createdTime) {
            this.createdTime = createdTime;
        }

        public Integer getDeleteFlag() {
            return deleteFlag;
        }

        public void setDeleteFlag(Integer deleteFlag) {
            this.deleteFlag = deleteFlag;
        }

        public String getFuncName() {
            return funcName;
        }

        public void setFuncName(String funcName) {
            this.funcName = funcName;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Integer getLinkType() {
            return linkType;
        }

        public void setLinkType(Integer linkType) {
            this.linkType = linkType;
        }

        public String getLinkUrl() {
            return linkUrl;
        }

        public void setLinkUrl(String linkUrl) {
            this.linkUrl = linkUrl;
        }

        public String getUpdatedBy() {
            return updatedBy;
        }

        public void setUpdatedBy(String updatedBy) {
            this.updatedBy = updatedBy;
        }

        public String getUpdatedTime() {
            return updatedTime;
        }

        public void setUpdatedTime(String updatedTime) {
            this.updatedTime = updatedTime;
        }
    }
}
