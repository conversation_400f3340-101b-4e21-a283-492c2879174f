package com.gc.notarizationpc.broadcast;

import static com.gc.notarizationpc.common.AppConfig.toastTime;
import static com.gc.notarizationpc.utils.CommonUtil.getSystemVoice;
import static com.gc.notarizationpc.utils.CommonUtil.setSystemVoice;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.util.Log;

import com.example.framwork.baseapp.AppManager;
import com.gc.notarizationpc.ui.MainActivity;
import com.gc.notarizationpc.ui.VideoNotarizationActivity;

import es.dmoral.toasty.Toasty;

public class BootBroadcastReceiver extends BroadcastReceiver {
    static final String ACTION = "android.intent.action.BOOT_COMPLETED";
    static final String ACTIONSound = "android.media.VOLUME_CHANGED_ACTION";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent.getAction().equals(ACTION)) {

            PackageManager packageManager = context.getPackageManager();
            if (null == packageManager) {
                return;
            }
            final Intent intent1 = packageManager.getLaunchIntentForPackage(context.getPackageName());
            if (intent1 != null) {
                intent1.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                context.startActivity(intent1);
            }
        } else if (intent.getAction().equals(ACTIONSound)) {
            if (AppManager.getAppManager().currentActivity() instanceof VideoNotarizationActivity && getSystemVoice(context) > 7) {
                if ((System.currentTimeMillis() - toastTime) > 5000) {
                    toastTime = System.currentTimeMillis();
                    Toasty.warning(context, "为保证通话正常，音量已调低").show();
                }
                setSystemVoice(context);
            }
        }
    }
}