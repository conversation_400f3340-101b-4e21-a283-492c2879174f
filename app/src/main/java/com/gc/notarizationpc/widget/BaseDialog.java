package com.gc.notarizationpc.widget;

import android.app.Dialog;
import android.content.Context;

import androidx.annotation.NonNull;

import com.gc.notarizationpc.ui.inquirycertification.InquiryOrderListHomeActivity;
import com.gc.notarizationpc.ui.inquirycertification.OrderDetailActivity;
import com.gc.notarizationpc.ui.video.CounselingRoomActivity;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.widget
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/5/17
 */
public class BaseDialog extends Dialog {
    private Context mContext;

    public BaseDialog(@NonNull Context context) {
        super(context);
        this.mContext = context;
    }


    public void showProgress() {
        if (mContext instanceof CounselingRoomActivity) {
            ((CounselingRoomActivity) mContext).showProgress();
        } else if (mContext instanceof OrderDetailActivity) {
            ((OrderDetailActivity) mContext).showProgress();
        } else if (mContext instanceof InquiryOrderListHomeActivity) {
            ((InquiryOrderListHomeActivity) mContext).showProgress();
        }

    }

    public void hideProgress() {
        if (mContext instanceof CounselingRoomActivity) {
            ((CounselingRoomActivity) mContext).hideProgress();
        } else if (mContext instanceof OrderDetailActivity) {
            ((OrderDetailActivity) mContext).hideProgress();
        } else if (mContext instanceof InquiryOrderListHomeActivity) {
            ((InquiryOrderListHomeActivity) mContext).hideProgress();
        }
    }
}
