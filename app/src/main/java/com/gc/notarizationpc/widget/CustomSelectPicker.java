package com.gc.notarizationpc.widget;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.gc.notarizationpc.R;

import java.util.ArrayList;

public class CustomSelectPicker implements View.OnClickListener, PickerView.OnSelectListener, DialogInterface.OnCancelListener {

    private Context mContext;
    private Callback mCallback;
    private boolean mCanDialogShow;

    private Dialog mPickerDialog;
    private PickerView pickerSelectItems;

    private String mSelectData;
    private int mPostion;

    @Override
    public void onCancel(DialogInterface dialog) {
        if (mCallback != null) {
            mCallback.outCancel();
        }
    }

    public interface Callback {
        void onSelectItems(String values , int index);

        void outCancel();//点击屏幕消失回调
    }

    public CustomSelectPicker(Context context, Callback callback) {
        mContext = context;
        mCallback = callback;
        initView();
        mCanDialogShow = true;
    }

    private void initView() {
        mPickerDialog = new Dialog(mContext, R.style.date_picker_dialog);
        mPickerDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        mPickerDialog.setContentView(R.layout.dialog_select_picker);

        Window window = mPickerDialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.gravity = Gravity.BOTTOM;
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            window.setAttributes(lp);
        }

        mPickerDialog.findViewById(R.id.tv_cancel).setOnClickListener(this);
        mPickerDialog.findViewById(R.id.tv_confirm).setOnClickListener(this);
        mPickerDialog.setOnCancelListener(this);

        pickerSelectItems = (PickerView) mPickerDialog.findViewById(R.id.picker_select_item);
        pickerSelectItems.setOnSelectListener(this);
        pickerSelectItems.setCanScrollLoop(false);

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tv_cancel:
                mCallback.outCancel();
                break;

            case R.id.tv_confirm:
                if (mCallback != null) {
                    mCallback.onSelectItems(mSelectData,mPostion);
                }
                break;
        }

        if (mPickerDialog != null && mPickerDialog.isShowing()) {
            mPickerDialog.dismiss();
        }
    }

    @Override
    public void onSelect(View view, String selected , int postion) {
        if (view == null || TextUtils.isEmpty(selected)) return;
        switch (view.getId()) {
            case R.id.picker_select_item:
                mSelectData = selected;
                mPostion = postion;
                break;
        }
    }

    public void show(ArrayList<String> list) {
        if (list.isEmpty() || !canShow()) return;
        pickerSelectItems.setDataList(list);
        pickerSelectItems.setSelected(0);
        mSelectData = list.get(0);
        mPostion = 0;

        mPickerDialog.show();
    }

    private boolean canShow() {
        return mCanDialogShow && mPickerDialog != null;
    }

    /**
     * 设置是否允许点击屏幕或物理返回键关闭
     */
    public CustomSelectPicker setCancelable(boolean cancelable) {
        if (!canShow()) return this;
        mPickerDialog.setCancelable(cancelable);
        return this;
    }

    /**
     * 销毁弹窗
     */
    public void onDestroy() {
        if (mPickerDialog != null) {
            mPickerDialog.dismiss();
            mPickerDialog = null;
            pickerSelectItems.onDestroy();
        }
    }

}
