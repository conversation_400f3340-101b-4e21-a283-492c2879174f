package com.gc.notarizationpc.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.Priority;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.gc.notarizationpc.R;
import com.gc.notarizationpc.utils.ImageUtil;
import com.gc.notarizationpc.utils.ZoomImageView;
import com.luck.picture.lib.tools.ScreenUtils;

public class ImageAlertDialog  extends Dialog {

    private Context mContext;
    private ZoomImageView imageViewUpload;
    private TextView closeDialog;
    private String imagePath;


    public ImageAlertDialog(Context context) {
        super(context, R.style.SelectedMoreDialogStyles);
        mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.image_alert_dialog);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();
    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        imageViewUpload = (ZoomImageView) findViewById(R.id.uploadPathImage);
        closeDialog = (TextView) findViewById(R.id.closeDialog);
        //设置取消按钮被点击后，向外界提供监听
        closeDialog.setOnClickListener(v -> {
            if (onClickBottomListener!= null) {
                onClickBottomListener.onCancelClick();
            }
            dismiss();
        });

    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
        SimpleTarget target = new SimpleTarget<Bitmap>() {
            @Override
            public void onResourceReady(@NonNull Bitmap bitmap, @Nullable Transition<? super Bitmap> transition) {
                //拿到img后处理一下在给ivOne
                int totalWidth = ScreenUtils.getScreenWidth(mContext);   // 屏幕宽度
                int bmWidth = bitmap.getWidth();
                int bmHeight = bitmap.getHeight();
                if (bmHeight >= bmWidth) {   //竖图
                    int bmWidthNew = totalWidth * 1;
                    int bmHeightNew = bmHeight * bmWidthNew / bmWidth;
                    Bitmap bitmapNew = ImageUtil.scale(bitmap, bmWidthNew, bmHeightNew);
                    imageViewUpload.setImageBitmap(bitmapNew);
                } else {   //横图
                    int bmWidthNew = totalWidth * 2 / 3;
                    int bmHeightNew = bmHeight * bmWidthNew / bmWidth;
                    Bitmap bitmapNew = ImageUtil.scale(bitmap, bmWidthNew, bmHeightNew);
                    imageViewUpload.setImageBitmap(bitmapNew);
                }
            }
        };

        Glide.with(mContext)
                .asBitmap()
                .apply(getRequestOptions())
                .load(this.imagePath)
                .into(target);

    }

    private RequestOptions getRequestOptions() {
        return new RequestOptions()
                // 填充方式
                .centerCrop()
                //优先级
                .priority(Priority.HIGH)
                //缓存策略
                .diskCacheStrategy(DiskCacheStrategy.ALL);
    }

    @Override
    public void show() {
        super.show();
    }

    /**
     * 设置确定取消按钮的回调
     */
    private OnClickBottomListener onClickBottomListener;

    public ImageAlertDialog setOnClickBottomListener(OnClickBottomListener onClickBottomListener) {
        this.onClickBottomListener = onClickBottomListener;
        return this;
    }

    public interface OnClickBottomListener{
        /**
         * 点击取消按钮事件
         */
        public void onCancelClick();

    }
}
