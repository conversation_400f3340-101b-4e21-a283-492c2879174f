package com.gc.notarizationpc.widget;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.widget
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/1/17
 */

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.gc.mininotarization.R;


public class DomesticEconomyStepBar extends RelativeLayout {

    private TextView tv_first_num;
    private TextView tv_first;
    private TextView tv_second_num;
    private TextView tv_second;
    private TextView tv_three_num;
    private TextView tv_three;


    public TextView getTv_first_num() {
        return tv_first_num;
    }

    public void setTv_first_num(TextView tv_first_num) {
        this.tv_first_num = tv_first_num;
    }

    public TextView getTv_first() {
        return tv_first;
    }

    public void setTv_first(TextView tv_first) {
        this.tv_first = tv_first;
    }

    public TextView getTv_second_num() {
        return tv_second_num;
    }

    public void setTv_second_num(TextView tv_second_num) {
        this.tv_second_num = tv_second_num;
    }

    public TextView getTv_second() {
        return tv_second;
    }

    public void setTv_second(TextView tv_second) {
        this.tv_second = tv_second;
    }

    public TextView getTv_three_num() {
        return tv_three_num;
    }

    public void setTv_three_num(TextView tv_three_num) {
        this.tv_three_num = tv_three_num;
    }

    public TextView getTv_three() {
        return tv_three;
    }

    public void setTv_three(TextView tv_three) {
        this.tv_three = tv_three;
    }


    public DomesticEconomyStepBar(Context context) {
        super(context);
    }

    /**
     * 注意这个构造方法是Context context, AttributeSet attrs两个参数
     *
     * @param context
     * @param attrs
     */
    public DomesticEconomyStepBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.domestic_economy_step_bar, this);//获取布局xml
        //初始化LinearLayout控件
        tv_first_num = (TextView) findViewById(R.id.tv_first_num);
        tv_first = (TextView) findViewById(R.id.tv_first);
        tv_second_num = (TextView) findViewById(R.id.tv_second_num);
        tv_second= (TextView) findViewById(R.id.tv_second);
        tv_three_num = (TextView) findViewById(R.id.tv_three_num);
        tv_three = (TextView) findViewById(R.id.tv_three);
    }
}



