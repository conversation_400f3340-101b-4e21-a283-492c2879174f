package com.gc.notarizationpc.widget.editspinner;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.DigitsKeyListener;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.view.inputmethod.EditorInfo;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.StringRes;
import androidx.appcompat.widget.AppCompatEditText;

import com.example.framwork.utils.ToastUtils;
import com.gc.notarizationpc.R;
import com.gc.notarizationpc.utils.MyOnclickClickListener;

import java.util.ArrayList;
import java.util.List;


/**
 * 带下拉支持模糊搜索的
 */
public class AppCompatEditSpinner extends RelativeLayout implements TextWatcher, TextView.OnEditorActionListener {

    private AppCompatEditText editText;
    private ImageView mRightIv;
    private View mRightImageTopView;

    private boolean rightImageDropShowAllItem;

    private boolean selectItemClick;
    private Animation mAnimation;
    private Animation mResetAnimation;

    private int maxLength;
    private int inputType;
    private String digits;

    //下拉选的背景
    private Drawable spinnerBackground;
    //匹配文本的颜色
    private String matchTextColor;
    //匹配文本时是否忽略字母大小写
    private boolean matchIgnoreCase;
    //待选项的文本颜色
    private ColorStateList spinnerItemTextColor;
    //待选项的文本大小，单位：SP
    private float spinnerItemTextSize;

    private View anchorView;
    private Context mContext;
    SearchCustomSelectPicker searchCustomSelectPicker;


    public AppCompatEditSpinner(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView(attrs);
        initAnimation();
    }


    public void setItemData(List<String> data) {
        if (data != null && data.size() > 0) {
            mSpinnerData = data;
            mCacheData = new ArrayList<>(mSpinnerData);
            indexs = new int[mSpinnerData.size()];
        }
        searchCustomSelectPicker = new SearchCustomSelectPicker(mContext, new SearchCustomSelectPicker.SearchCallback() {
            @Override
            public void onSelectItems(String values, int index) {
                selectItemClick = true;
                Log.i("Test", "onSelectItems$index---$values");
                editText.setText(values);
//        this.requestFocus();
                editText.requestFocus();
                editText.setSelection(getText().length());
                mRightImageTopView.setClickable(false);

                selectItemClick = false;
                if (callback != null) {
                    callback.selectItem(values, index);
                }
            }
        });
//        searchCustomSelectPicker.setCancelable(false);

    }

    private SelectItemCallback callback;

    public SelectItemCallback getCallback() {
        return callback;
    }

    public void setCallback(SelectItemCallback callback) {
        this.callback = callback;
    }

    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        if (actionId == EditorInfo.IME_ACTION_DONE) {
            if (mCacheData != null && mCacheData.size() == 0) {
                editText.setText("");
            } else {
                showFilterData(editText.getText().toString());
            }
            return true;
        }
        return false;
    }

    public interface SelectItemCallback {
        void selectItem(String values, int index);
    }

    /**
     * 设置文本
     */
    public void setText(@StringRes int text) {
        setText(getContext().getString(text));
    }

    /**
     * 设置文本
     */
    public void setText(String text) {
        editText.setText(text);
    }

    /**
     * 设置Hint文本
     */
    public void setHint(@StringRes int hint) {
        setHint(getContext().getString(hint));
    }

    /**
     * 设置Hint文本
     */
    public void setHint(String hint) {
        editText.setHint(hint);
    }

    /**
     * 设置Hint的文本颜色
     */
    public void setHintTextColor(@ColorInt int color) {
        editText.setHintTextColor(color);
    }

    /**
     * 设置背景
     */
    public void setEditBackgroundResource(@DrawableRes int resource) {
        editText.setBackgroundResource(resource);
    }

    /**
     * 设置输入文字字体大小，单位：sp
     */
    public void setEditTextSize(int size) {
        editText.setTextSize(TypedValue.COMPLEX_UNIT_SP, size);
    }

    /**
     * 设置输入文字颜色
     */
    public void setEditTextColor(@ColorInt int color) {
        editText.setTextColor(color);
    }

    /**
     * 设置输入限制最大行数
     */
    public void setEditMaxLines(int maxLines) {
        editText.setMaxLines(maxLines);
    }

    /**
     * 设置输入限制最大字符长度
     */
    public void setEditMaxLength(int maxLength) {
        this.maxLength = maxLength;
        setEditLimit();
    }

    /**
     * 设置输入限制类型
     * <p>
     * 例如：{@link  InputType#TYPE_CLASS_TEXT}
     */
    public void setEditInputType(int inputType) {
        this.inputType = inputType;
        setEditLimit();
    }

    /**
     * 设置输入限制字符
     */
    public void setEditDigits(@StringRes int digits) {
        setEditDigits(getContext().getString(digits));
    }

    /**
     * 设置输入限制字符
     */
    public void setEditDigits(String digits) {
        this.digits = digits;
        setEditLimit();
    }

    /**
     * 设置是否隐藏右侧下拉选图标
     */
    public void setRightImageGone(boolean rightImageGone) {
        mRightIv.setVisibility(rightImageGone ? GONE : VISIBLE);
    }

    /**
     * 右侧图片展开下拉选时是否显示全部数据，默认：点击时显示和当前输入匹配的数据
     */
    public void setRightImageDropShowAllItem(boolean rightImageDropShowAllItem) {
        this.rightImageDropShowAllItem = rightImageDropShowAllItem;
    }


    public void setMatchTextColor(String matchTextColor) {
        this.matchTextColor = matchTextColor;
    }


    public void setMatchIgnoreCase(boolean matchIgnoreCase) {
        this.matchIgnoreCase = matchIgnoreCase;
    }


    public void setSpinnerItemTextColor(@ColorInt int spinnerItemTextColor) {
        this.spinnerItemTextColor = ColorStateList.valueOf(spinnerItemTextColor);
    }

    public void setSpinnerItemTextSize(int spinnerItemTextSize) {
        this.spinnerItemTextSize = spinnerItemTextSize;
    }

    public View getAnchorView() {
        return anchorView;
    }

    public void setAnchorView(View anchorView) {
        this.anchorView = anchorView;
    }


    public void setSpinnerBackground(Drawable spinnerBackground) {
        this.spinnerBackground = spinnerBackground;
    }


    /**
     * 获取输入的文本
     */
    public String getText() {
        Editable editable = editText.getText();
        return null == editable ? "" : editable.toString();
    }

    /**
     * 设置下拉选图标
     */
    public void setRightImageDrawable(Drawable drawable) {
        mRightIv.setImageDrawable(drawable);
    }

    /**
     * 设置下拉选图标
     */
    public void setRightImageResource(@DrawableRes int res) {
        mRightIv.setImageResource(res);
    }

    /**
     * 获取输入控件EditText，可做特殊配置
     */
    public AppCompatEditText getEditText() {
        return editText;
    }

    /**
     * 获取右侧ImageView，可做特殊配置
     */
    public ImageView getRightImageView() {
        return mRightIv;
    }


    private void initAnimation() {
        mAnimation = new RotateAnimation(0, -90, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        mAnimation.setDuration(300);
        mAnimation.setFillAfter(true);

        mResetAnimation = new RotateAnimation(-90, 0, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        mResetAnimation.setDuration(300);
        mResetAnimation.setFillAfter(true);
    }


    private boolean hasInput(boolean containSpace) {
        if (containSpace) {
            return editText.getText().toString().length() > 0;
        } else {
            return editText.getText().toString().trim().length() > 0;
        }

    }

    private void initView(AttributeSet attrs) {
        LayoutInflater.from(getContext()).inflate(R.layout.app_compat_edit_spinner, this);
        editText = findViewById(R.id.spinner_edit_text);

        //监听输入框的内容变化
        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                //变化时候的通知
                //LogUtils.d(SearchFragment.this,"input text === > " + s.toString().trim());
                //如果长度不为0，那么显示删除按钮
                //否则隐藏删除按钮
                String input = s.toString();
                Drawable clear = getResources().getDrawable(R.drawable.base_ic_clear_edit);
                clear.setBounds(0, 0, 25, 25);//这里设置图片宽高，否则无法显示
                if (hasInput(false)) {
                    editText.setCompoundDrawables(null, null, clear, null);
                } else {
                    editText.setCompoundDrawables(null, null, null, null);
                }

                if (input.length() > 20) {
                    editText.setText("");
                    ToastUtils.getInstance(mContext).toastInfo("支持搜索的最大长度为" + input.length());
                }

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        editText.setOnTouchListener(new View.OnTouchListener() {

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                Drawable drawable = editText.getCompoundDrawables()[2];
                //如果右边没有图片，不再处理
                if (drawable == null) {
                    return false;
                }
                //如果不是按下事件，不再处理
                if (event.getAction() != MotionEvent.ACTION_UP) {
                    return false;
                }
                if (event.getX() > editText.getWidth()
                        - editText.getPaddingRight()
                        - drawable.getIntrinsicWidth()) {
                    editText.setText("");
                    //清空搜索内容
                    if (mCacheData != null && mCacheData.size() > 0) {
                        mCacheData.clear();
                    }
                }
                return false;
            }
        });
        //设置搜索事件监听
        editText.setOnEditorActionListener(this);

        mRightIv = findViewById(R.id.spinner_expand);
        mRightImageTopView = findViewById(R.id.spinner_expand_above);
        mRightImageTopView.setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                v.setClickable(false);
                return;
            }
        });
        mRightImageTopView.setClickable(false);

        findViewById(R.id.place).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                if (searchCustomSelectPicker != null && searchCustomSelectPicker.isShowing()) {
                    searchCustomSelectPicker.dismiss();
                } else {
                    showFilterData(rightImageDropShowAllItem ? null : getText());
                }
            }
        });
//        mRightIv.setOnClickListener(new MyOnclickClickListener() {
//            @Override
//            public void mOnClick(View v) {
//                if (searchCustomSelectPicker != null && searchCustomSelectPicker.isShowing()) {
//                    searchCustomSelectPicker.dismiss();
//                } else {
//                    showFilterData(rightImageDropShowAllItem ? null : getText());
//                }
//            }
//        });
//        mRightIv.setRotation(90);
        editText.addTextChangedListener(this);

        TypedArray tArray = getContext().obtainStyledAttributes(attrs,
                R.styleable.AppCompatEditSpinner);

        editText.setText(tArray.getString(R.styleable.AppCompatEditSpinner_text));

        editText.setHint(tArray.getString(R.styleable.AppCompatEditSpinner_hint));
        ColorStateList hintTextColor = tArray.getColorStateList(R.styleable.AppCompatEditSpinner_hintTextColor);
        if (null != hintTextColor) {
            editText.setHintTextColor(hintTextColor);
        }

        int bg = tArray.getResourceId(R.styleable.AppCompatEditSpinner_editBackground, 0);
        if (bg != 0) {
            editText.setBackgroundResource(bg);
        }

        float editTextSize = tArray.getDimension(R.styleable.AppCompatEditSpinner_editTextSize, 0);
        if (editTextSize != 0) {
            editText.setTextSize(TypedValue.COMPLEX_UNIT_PX, editTextSize);
        }

        ColorStateList editTextColor = tArray.getColorStateList(R.styleable.AppCompatEditSpinner_editTextColor);
        if (null != editTextColor) {
            editText.setTextColor(editTextColor);
        }

        int maxLines = tArray.getInt(R.styleable.AppCompatEditSpinner_editMaxLines, 0);
        if (maxLines > 0) {
            editText.setMaxLines(maxLines);
        }

        //edit 长度、输入类型、输入字符限制
        maxLength = tArray.getInt(R.styleable.AppCompatEditSpinner_editMaxLength, 0);
        inputType = tArray.getInt(R.styleable.AppCompatEditSpinner_editInputType, -1);
        digits = tArray.getString(R.styleable.AppCompatEditSpinner_editDigits);
        setEditLimit();

        int imageId = tArray.getResourceId(R.styleable.AppCompatEditSpinner_rightImage, 0);
        if (imageId != 0) {
            mRightIv.setImageResource(imageId);
        }

        boolean rightImageGone = tArray.getBoolean(R.styleable.AppCompatEditSpinner_rightImageGone, false);
        mRightIv.setVisibility(rightImageGone ? GONE : VISIBLE);
        //读取点击右侧图片时是否显示全部数据
        rightImageDropShowAllItem = tArray.getBoolean(R.styleable.AppCompatEditSpinner_rightImageDropShowAllItem, false);

        //读取匹配字符部分显示的颜色，由ARGB转为RGB表示
        CharSequence sequence = tArray.getText(R.styleable.AppCompatEditSpinner_spinnerItemMatchTextColor);
        if (!TextUtils.isEmpty(sequence)) {
            matchTextColor = String.format("#%s", sequence.toString().substring(3));
        }
        //读取匹配时是否忽略字母大小写
        matchIgnoreCase = tArray.getBoolean(R.styleable.AppCompatEditSpinner_spinnerItemMatchIgnoreCase, false);
        //读取待选项的文本颜色
        spinnerItemTextColor = tArray.getColorStateList(R.styleable.AppCompatEditSpinner_spinnerItemTextColor);
        //读取待选项的文本大小
        float itemTextSize = tArray.getDimensionPixelSize(R.styleable.AppCompatEditSpinner_spinnerItemTextSize, 0);
        if (itemTextSize != 0) {
            spinnerItemTextSize = pxToDp(itemTextSize);
        }
        //待选项的背景
        spinnerBackground = tArray.getDrawable(R.styleable.AppCompatEditSpinner_spinnerBackground);

        tArray.recycle();
    }

    private void setEditLimit() {
        if (maxLength > 0) {
            editText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxLength)});
        }
        if (inputType > -1) {
            editText.setInputType(inputType);
        }
        if (!TextUtils.isEmpty(digits)) {
            editText.setKeyListener(DigitsKeyListener.getInstance(digits));
        }
    }

    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public static int dp2px(float dpValue) {
        final float scale = Resources.getSystem().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }


    @Override
    public final void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public final void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public final void afterTextChanged(Editable s) {
        //选中待选项填入edit时，不触发此事件
        if (selectItemClick) return;

        String key = s.toString();
        editText.setSelection(key.length());
        if (!TextUtils.isEmpty(key)) {
            showFilterData(key);
        } else {
            if (searchCustomSelectPicker != null) {
                searchCustomSelectPicker.dismiss();
            }
        }
    }

    private List<String> mSpinnerData;
    private ArrayList<String> mCacheData;
    private int[] indexs;

    public boolean onFilter(String keyword) {
        if (mCacheData != null && mCacheData.size() > 0) {
            mCacheData.clear();
        }

        try {
            if (TextUtils.isEmpty(keyword)) {
                mCacheData.addAll(mSpinnerData);
                for (int i = 0; i < indexs.length; i++) {
                    indexs[i] = i;
                }
            } else {
                for (int i = 0; i < mSpinnerData.size(); i++) {
                    if (matchIgnoreCase) {
                        if (!mSpinnerData.get(i).toUpperCase().contains(keyword.toUpperCase())) {
                            continue;
                        }
                    } else {
                        if (!mSpinnerData.get(i).contains(keyword)) {
                            continue;
                        }
                    }

                    indexs[mCacheData.size()] = i;
                    if (TextUtils.isEmpty(matchTextColor)) {
                        mCacheData.add(mSpinnerData.get(i));
                    } else {
                        mCacheData.add(mSpinnerData.get(i));
                    }
                }
            }
        } catch (Exception e) {
            Log.e("Test", e.getMessage());
        }

        return mCacheData != null ? mCacheData.size() <= 0 : true;
    }

    private void showFilterData(String key) {
        if (searchCustomSelectPicker != null && searchCustomSelectPicker.isShowing()) {
            searchCustomSelectPicker.dismiss();
            return;
        }
        if (onFilter(key)) {
            if (searchCustomSelectPicker != null && searchCustomSelectPicker.isShowing()) {
                searchCustomSelectPicker.dismiss();
            }
        } else {
            searchCustomSelectPicker.show(mCacheData);
        }

    }

    private int pxToDp(float px) {
        float scale = getResources().getDisplayMetrics().density;
        return (int) (px / scale + 0.5f);
    }


}
