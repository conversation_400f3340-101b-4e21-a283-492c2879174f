package com.gc.notarizationpc.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.example.framwork.utils.ToastUtils;
import com.gc.notarizationpc.R;

public class PicCodeDialog extends Dialog {

    private Context mContext;
    private ImageView imageView;
    private ResultListener resultListener;
    private Bitmap mBitmap;

    public ResultListener getResultListener() {
        return resultListener;
    }

    public void setResultListener(ResultListener resultListener) {
        this.resultListener = resultListener;
    }

    public PicCodeDialog(Context context, Bitmap bitmap) {
        super(context);
        mContext = context;
        this.mBitmap = bitmap;
    }

    public void refreshBitmap(Bitmap bitmap) {
        if (bitmap != null) {
            imageView.setImageBitmap(bitmap);
        }
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_pic_code);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();

    }

    public interface ResultListener<T> {
        void result(T value);

        void resultTwo();
    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        ImageView ivClose = findViewById(R.id.iv_close);
        imageView = findViewById(R.id.iv_img);
        imageView.setImageBitmap(mBitmap);
        EditText edtPic = findViewById(R.id.edt_pic_code);
        TextView btnSign = findViewById(R.id.btn_sign);

        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });
        btnSign.setOnClickListener(v -> {
            if (TextUtils.isEmpty(edtPic.getText().toString())) {
                ToastUtils.getInstance(mContext).toastInfo("请先输入图形验证码");
            } else {
                dismiss();
                if (resultListener != null) {
                    resultListener.result(edtPic.getText().toString());
                }
            }

        });

        findViewById(R.id.iv_refresh).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (resultListener != null) {
                    resultListener.resultTwo();
                }
            }
        });
    }


    @Override
    public void show() {
        super.show();
    }


}
