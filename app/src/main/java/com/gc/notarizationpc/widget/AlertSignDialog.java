package com.gc.notarizationpc.widget;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.TextView;

import com.gc.notarizationpc.R;
import com.gc.notarizationpc.ui.NotarizationSelfActivity;

public class AlertSignDialog  extends Dialog {

    private WebView webView;//关闭按钮
    private Context mContext;
    private String urlString;

    public AlertSignDialog(Context context) {
        super(context, R.style.SelectedMoreDialogStyles);
        mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.sign_webview_dialog);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();
        initWebView();
    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        webView = (WebView) findViewById(R.id.wvWebView);
    }

    public void setUrlString(String urlString) {
        this.urlString = urlString;
    }

    public void reloadWebSign(){
        webView.reload();
    }

    @SuppressLint("JavascriptInterface")
    public void initWebView() {
        WebSettings mWebSettings = webView.getSettings();
        mWebSettings.setJavaScriptCanOpenWindowsAutomatically(true); //设置js可以直接打开窗口，如window.open()，默认为false
        mWebSettings.setJavaScriptEnabled(true);//是否允许JavaScript脚本运行，默认为false。设置true时，会提醒可能造成XSS漏洞
        mWebSettings.setSupportZoom(true);//是否可以缩放，默认true
        mWebSettings.setBuiltInZoomControls(true);//是否显示缩放按钮，默认false
        mWebSettings.setUseWideViewPort(true); //设置此属性，可任意比例缩放。大视图模式
        mWebSettings.setLoadWithOverviewMode(true);//和setUseWideViewPort(true)一起解决网页自适应问题
        mWebSettings.setAppCacheEnabled(true); //是否使用缓存
        mWebSettings.setDomStorageEnabled(true); //开启本地DOM存储
        mWebSettings.setLoadsImagesAutomatically(true); // 加载图片
        mWebSettings.setMediaPlaybackRequiresUserGesture(false);//播放音频，多媒体需要用户手动？设置为false为可自动播放
    }

    @Override
    public void show() {
        super.show();
        webView.loadUrl(this.urlString);
    }


}
