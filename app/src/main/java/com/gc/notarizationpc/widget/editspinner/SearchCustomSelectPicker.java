package com.gc.notarizationpc.widget.editspinner;

import android.app.Dialog;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.gc.notarizationpc.R;
import com.gc.notarizationpc.widget.PickerView;

import java.util.ArrayList;

public class SearchCustomSelectPicker implements View.OnClickListener, PickerView.OnSelectListener {

    private Context mContext;
    private SearchCallback mCallback;
    private boolean mCanDialogShow;

    private Dialog mPickerDialog;
    private PickerView pickerSelectItems;

    private String mSelectData;
    private int mPostion;

    public interface SearchCallback {
        void onSelectItems(String values , int index);
    }

    public SearchCustomSelectPicker(Context context, SearchCallback callback) {
        mContext = context;
        mCallback = callback;
        initView();
        mCanDialogShow = true;
    }

    private void initView() {
        mPickerDialog = new Dialog(mContext, R.style.date_picker_dialog);
        mPickerDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        mPickerDialog.setContentView(R.layout.dialog_select_picker);
        mPickerDialog.getWindow().setDimAmount(0.0f);
        Window window = mPickerDialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.gravity = Gravity.BOTTOM;
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            window.setAttributes(lp);
        }

        mPickerDialog.findViewById(R.id.tv_cancel).setOnClickListener(this);
        mPickerDialog.findViewById(R.id.tv_confirm).setOnClickListener(this);

        pickerSelectItems = (PickerView) mPickerDialog.findViewById(R.id.picker_select_item);
        pickerSelectItems.setOnSelectListener(this);
        pickerSelectItems.setCanScrollLoop(false);

    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_cancel) {
        } else if (id == R.id.tv_confirm) {
            if (mCallback != null) {
                mCallback.onSelectItems(mSelectData, mPostion);
            }
        }

        if (mPickerDialog != null && mPickerDialog.isShowing()) {
            mPickerDialog.dismiss();
        }
    }

    public void dismiss()
    {
        if (mPickerDialog != null && mPickerDialog.isShowing()) {
            mPickerDialog.dismiss();
        }
    }

    public boolean isShowing()
    {
        if (mPickerDialog != null ) {
            return mPickerDialog.isShowing();
        }
        return false;
    }

    @Override
    public void onSelect(View view, String selected , int postion) {
        if (view == null || TextUtils.isEmpty(selected)) return;
        if (view.getId() == R.id.picker_select_item) {
            mSelectData = selected;
            mPostion = postion;
        }
    }

    public void show(ArrayList<String> list) {
        if (list.isEmpty() || !canShow()) return;
        pickerSelectItems.setDataList(list);
        pickerSelectItems.setSelected(0);
        mSelectData = list.get(0);
        mPostion = 0;

        mPickerDialog.show();
    }

    private boolean canShow() {
        return mCanDialogShow && mPickerDialog != null;
    }

    /**
     * 设置是否允许点击屏幕或物理返回键关闭
     */
    public SearchCustomSelectPicker setCancelable(boolean cancelable) {
        if (!canShow()) return this;
        mPickerDialog.setCancelable(cancelable);
        return this;
    }

    /**
     * 销毁弹窗
     */
    public void onDestroy() {
        if (mPickerDialog != null) {
            mPickerDialog.dismiss();
            mPickerDialog = null;
            pickerSelectItems.onDestroy();
        }
    }

}
