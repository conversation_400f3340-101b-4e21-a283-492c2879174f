package com.gc.notarizationpc.model;

import java.io.Serializable;
import java.util.List;

public class NotrayObBean implements Serializable {

    /**
     * msg : 成功
     * code : 200
     * items : [{"classify":"2","unitGuid":"2eaa1749-2dcf-491b-9453-296d5864d033","children":[{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"af9ccbd5-743c-4bca-b140-721ca5d0de4d","price":200,"grade":"2","name":"资助出国留学协议公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"70554d92-d13d-441a-93cb-9e181d6df43b","price":200,"grade":"2","name":"未婚声明公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"7b913584-40aa-417d-90c3-6b045c9ded71","price":100,"grade":"2","name":"毕业证书公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"8d9a621a-0276-4878-8701-2b5ce397530d","price":200,"grade":"2","name":"离婚证公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"ace032ea-2c18-4f55-a3f1-cbcd4d0ca0bf","price":50,"grade":"2","name":"护照公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"eb232a41-67b6-4676-a5e0-00df3f8b8a7b","price":200,"grade":"2","name":"职业资格证书公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"bc9235ab-7cd0-4c12-8bb5-1a0ee6faee2f","price":200,"grade":"2","name":"居民户口簿公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"d3107a16-48c1-41d3-84be-f980980dfc7a","price":200,"grade":"2","name":"亲属关系公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"75b5f7d1-4b48-47be-ab9b-62fa50cdf0b3","price":200,"grade":"2","name":"成绩单公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"1ad5213e-ffdd-472c-b28c-d5395da999d6","price":200,"grade":"2","name":"结婚证公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"c1f54dc3-019e-4a93-af10-************","price":200,"grade":"2","name":"出生医学证明公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"ba3a0281-4465-4df6-a162-87c5157da951","price":200,"grade":"2","name":"完税证明"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"7677d8a6-22dd-41e1-ae00-c797aeb936ef","price":200,"grade":"2","name":"无犯罪记录公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"d3f35d94-5970-48ee-b4dc-0a826c3c502e","price":200,"grade":"2","name":"在学证明公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"6531bd04-f7e2-4714-9ebc-fddef0952d74","price":200,"grade":"2","name":"工作（收入、在职）证明公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"81fcf866-a478-4bd7-898a-c10bacf422cc","price":200,"grade":"2","name":"出生公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"9712cb9e-066c-4bb1-bb32-95bd06e60cce","price":200,"grade":"2","name":"房产证公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"90f8badd-b290-414a-9553-ae6cd7a6adb3","price":200,"grade":"2","name":"银行存款证明公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"cdf369dd-df74-4058-a060-69772be3865d","price":200,"grade":"2","name":"学历公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"9940057f-70d3-40f7-975e-f026c011fa00","price":200,"grade":"2","name":"学位证书公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"5c3012a0-0981-4620-9c29-e27fb3cae244","price":200,"grade":"2","name":"学位公证"}],"grade":"1","name":"出国留学"},{"classify":"2","unitGuid":"165ce246-eaf1-4d58-93c1-fcc323970916","children":[{"superior":"165ce246-eaf1-4d58-93c1-fcc323970916","unitGuid":"ca2f4592-ae74-4e50-a78d-eef7d60e522d","price":200,"grade":"2","name":"驾驶证公证"},{"superior":"165ce246-eaf1-4d58-93c1-fcc323970916","unitGuid":"5ac02415-5735-4e1f-a0a6-7608d7f9c079","price":200,"grade":"2","name":"职业资格证书公证"},{"superior":"165ce246-eaf1-4d58-93c1-fcc323970916","unitGuid":"5761b073-487c-4268-be4f-1baa8035778d","price":200,"grade":"2","name":"未婚声明公证"},{"superior":"165ce246-eaf1-4d58-93c1-fcc323970916","unitGuid":"b2cd5606-acb9-48ee-b605-ac8da1c8195c","price":200,"grade":"2","name":"工作（收入、在职）证明公证"},{"superior":"165ce246-eaf1-4d58-93c1-fcc323970916","unitGuid":"b2f37bf9-4a1c-4ba8-8c4d-82017d1ecbe1","price":200,"grade":"2","name":"出生公证"},{"superior":"165ce246-eaf1-4d58-93c1-fcc323970916","unitGuid":"2ca96a7c-a0ae-49b7-8d6a-db1c69065bbc","price":200,"grade":"2","name":"无犯罪记录公证"},{"superior":"165ce246-eaf1-4d58-93c1-fcc323970916","unitGuid":"5b6e58fa-a309-4c0d-a19f-610e014d800f","price":200,"grade":"2","name":"结婚证公证"},{"superior":"165ce246-eaf1-4d58-93c1-fcc323970916","unitGuid":"b4b94b0f-142c-4c6d-b44c-406d8fe80d61","price":200,"grade":"2","name":"居民户口簿公证"},{"superior":"165ce246-eaf1-4d58-93c1-fcc323970916","unitGuid":"5c32608c-7a81-4bc5-8df2-5c91d0fa9439","price":200,"grade":"2","name":"成绩单公证"},{"superior":"165ce246-eaf1-4d58-93c1-fcc323970916","unitGuid":"9b22a4a9-7463-4aa8-b44a-5e30a29a5dcb","price":200,"grade":"2","name":"离婚证公证"}],"grade":"1","name":"定居移民"},{"classify":"2","unitGuid":"edefd45a-f066-40be-93d7-e08df8e8cd61","children":[{"superior":"edefd45a-f066-40be-93d7-e08df8e8cd61","unitGuid":"c91a08e4-eae3-493f-af29-23a809efb9ca","price":200,"grade":"2","name":"派遣函公证"},{"superior":"edefd45a-f066-40be-93d7-e08df8e8cd61","unitGuid":"07ed432d-bfaf-49fd-aa09-8f2af3d07271","price":200,"grade":"2","name":"学位公证"},{"superior":"edefd45a-f066-40be-93d7-e08df8e8cd61","unitGuid":"bf5e085e-015f-4ef7-8d0b-cf605731c2e9","price":200,"grade":"2","name":"职业资格公证"},{"superior":"edefd45a-f066-40be-93d7-e08df8e8cd61","unitGuid":"78ea34a8-f6af-49c1-82f0-4bd913b19888","price":200,"grade":"2","name":"许可证公证"},{"superior":"edefd45a-f066-40be-93d7-e08df8e8cd61","unitGuid":"2c67d352-5e8e-4cea-9e30-73af71b0e23c","price":200,"grade":"2","name":"驾驶证公证"},{"superior":"edefd45a-f066-40be-93d7-e08df8e8cd61","unitGuid":"e1eb14fc-abcb-466b-9e5a-16932bd2f7fd","price":200,"grade":"2","name":"结婚证公证"},{"superior":"edefd45a-f066-40be-93d7-e08df8e8cd61","unitGuid":"a22f5c1c-ba24-45a3-8852-8aabf6d5d047","price":200,"grade":"2","name":"出生公证"},{"superior":"edefd45a-f066-40be-93d7-e08df8e8cd61","unitGuid":"9967bb89-552b-486c-9a5b-8017a6b12029","price":200,"grade":"2","name":"职务（职称）公证（专业技术职务）"}],"grade":"1","name":"境外其他用途"},{"classify":"2","unitGuid":"e9421c61-a7b5-40e4-a800-dae206d1e945","children":[{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"f5ce2db6-edfe-4426-be9f-082d0be9f8b3","price":200,"grade":"2","name":"收养登记证公证"},{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"1272fe93-7aa0-4d47-b9aa-70c28295179d","price":200,"grade":"2","name":"健康证公证"},{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"52b4b41c-b02c-4905-b3bd-************","price":200,"grade":"2","name":"机动车登记证书公证"},{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"2d9d588e-3a12-43e2-b558-841a486ef662","price":200,"grade":"2","name":"无犯罪记录公证"},{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"60278557-0dcc-4a53-bf8f-8a12c931bd08","price":200,"grade":"2","name":"已婚（再婚）"},{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"966107ee-c364-4f42-bf3a-bf8818930b8b","price":200,"grade":"2","name":"已婚（初婚）"},{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"8b11c014-cb37-4817-8485-598560f30a94","price":200,"grade":"2","name":"结婚证公证"},{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"1e133e2b-7792-4356-bd90-a90088918d4b","price":200,"grade":"2","name":"亲属关系公证"},{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"71aacce4-a5a2-402c-841b-dacbb65f4c61","price":200,"grade":"2","name":"工作（收入、在职）证明公证"},{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"af0027e3-af01-4b09-9eb4-0c53f593fbff","price":200,"grade":"2","name":"银行存款证明公证"},{"superior":"e9421c61-a7b5-40e4-a800-dae206d1e945","unitGuid":"e10829b2-a671-4c6c-8648-4af87f1f68d6","price":200,"grade":"2","name":"出行同意声明（委托）公证"}],"grade":"1","name":"探亲旅游"},{"classify":"2","unitGuid":"985d1bfd-73fc-424b-a08d-a81d77a7a906","children":[{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"fcdeea80-5e2e-4450-9fb5-26d740d0bcaa","price":200,"grade":"2","name":"派遣函公证"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"36ba62c4-b9e5-495f-98d6-9b95812d133b","price":200,"grade":"2","name":"驾驶证公证"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"2e3be9a5-97f9-4e07-ae7b-72c8afc87c92","price":200,"grade":"2","name":"法人的其他证明公证"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"d51f4761-fca5-43a4-86aa-e4fa10f4610d","price":200,"grade":"2","name":"用于知识产权事务的法人资格公证"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"db0a425a-dd9c-47fe-9aa8-4f9f6aa5f5dc","price":200,"grade":"2","name":"法人资格公证"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"63b0c6e3-a22c-4858-a3ed-c430e49b06be","price":200,"grade":"2","name":"经历公证（自然人经历）"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"b2d584f6-bf02-4a91-a60c-eaad8c494e02","price":200,"grade":"2","name":"组织机构代码证公证"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"2e4fde52-3c34-4182-a553-43f5646f3992","price":200,"grade":"2","name":"成绩单公证"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"95bc56ae-5bc3-4f68-9ade-cb22c9780b71","price":200,"grade":"2","name":"职务（职称）公证（职务）"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"2d11b490-939f-45c1-873f-bdef7dda4a37","price":200,"grade":"2","name":"职务（职称）公证（专业技术职务）"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"3b46f7b4-a9ed-4a32-a6c6-f391c739eb6f","price":200,"grade":"2","name":"非法人组织资格公证"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"77b6c85a-07c1-4ce5-bf25-a06ba89c6c74","price":200,"grade":"2","name":"经历公证（非法人组织经历）"},{"superior":"985d1bfd-73fc-424b-a08d-a81d77a7a906","unitGuid":"a4e45000-5a8f-4e28-a4b9-ce2167e7e50e","price":200,"grade":"2","name":"经历公证（自然人经历）"}],"grade":"1","name":"商务劳务"},{"classify":"1","unitGuid":"66bcb965-26bb-4d34-9065-64643f93f6a3","children":[{"superior":"66bcb965-26bb-4d34-9065-64643f93f6a3","unitGuid":"5f4c1ce0-0aa5-41f8-8f8d-a04de6cef390","price":0.01,"grade":"2","name":"测试阿"}],"grade":"1","name":"测试"}]
     */

    private String msg;
    private int code;
    private List<ItemsDTO> items;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public List<ItemsDTO> getItems() {
        return items;
    }

    public void setItems(List<ItemsDTO> items) {
        this.items = items;
    }

    public static class ItemsDTO {
        /**
         * classify : 2
         * unitGuid : 2eaa1749-2dcf-491b-9453-296d5864d033
         * children : [{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"af9ccbd5-743c-4bca-b140-721ca5d0de4d","price":200,"grade":"2","name":"资助出国留学协议公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"70554d92-d13d-441a-93cb-9e181d6df43b","price":200,"grade":"2","name":"未婚声明公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"7b913584-40aa-417d-90c3-6b045c9ded71","price":100,"grade":"2","name":"毕业证书公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"8d9a621a-0276-4878-8701-2b5ce397530d","price":200,"grade":"2","name":"离婚证公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"ace032ea-2c18-4f55-a3f1-cbcd4d0ca0bf","price":50,"grade":"2","name":"护照公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"eb232a41-67b6-4676-a5e0-00df3f8b8a7b","price":200,"grade":"2","name":"职业资格证书公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"bc9235ab-7cd0-4c12-8bb5-1a0ee6faee2f","price":200,"grade":"2","name":"居民户口簿公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"d3107a16-48c1-41d3-84be-f980980dfc7a","price":200,"grade":"2","name":"亲属关系公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"75b5f7d1-4b48-47be-ab9b-62fa50cdf0b3","price":200,"grade":"2","name":"成绩单公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"1ad5213e-ffdd-472c-b28c-d5395da999d6","price":200,"grade":"2","name":"结婚证公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"c1f54dc3-019e-4a93-af10-************","price":200,"grade":"2","name":"出生医学证明公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"ba3a0281-4465-4df6-a162-87c5157da951","price":200,"grade":"2","name":"完税证明"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"7677d8a6-22dd-41e1-ae00-c797aeb936ef","price":200,"grade":"2","name":"无犯罪记录公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"d3f35d94-5970-48ee-b4dc-0a826c3c502e","price":200,"grade":"2","name":"在学证明公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"6531bd04-f7e2-4714-9ebc-fddef0952d74","price":200,"grade":"2","name":"工作（收入、在职）证明公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"81fcf866-a478-4bd7-898a-c10bacf422cc","price":200,"grade":"2","name":"出生公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"9712cb9e-066c-4bb1-bb32-95bd06e60cce","price":200,"grade":"2","name":"房产证公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"90f8badd-b290-414a-9553-ae6cd7a6adb3","price":200,"grade":"2","name":"银行存款证明公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"cdf369dd-df74-4058-a060-69772be3865d","price":200,"grade":"2","name":"学历公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"9940057f-70d3-40f7-975e-f026c011fa00","price":200,"grade":"2","name":"学位证书公证"},{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"5c3012a0-0981-4620-9c29-e27fb3cae244","price":200,"grade":"2","name":"学位公证"}]
         * grade : 1
         * name : 出国留学
         */

        private String classify;
        private String unitGuid;
        private List<ChildrenDTO> children;
        private String grade;
        private String name;

        public String getClassify() {
            return classify;
        }

        public void setClassify(String classify) {
            this.classify = classify;
        }

        public String getUnitGuid() {
            return unitGuid;
        }

        public void setUnitGuid(String unitGuid) {
            this.unitGuid = unitGuid;
        }

        public List<ChildrenDTO> getChildren() {
            return children;
        }

        public void setChildren(List<ChildrenDTO> children) {
            this.children = children;
        }

        public String getGrade() {
            return grade;
        }

        public void setGrade(String grade) {
            this.grade = grade;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public static class ChildrenDTO {
            /**
             * superior : 2eaa1749-2dcf-491b-9453-296d5864d033
             * unitGuid : af9ccbd5-743c-4bca-b140-721ca5d0de4d
             * price : 200.0
             * grade : 2
             * name : 资助出国留学协议公证
             */

            private String superior;
            private String unitGuid;
            private double price;
            private String grade;
            private String name;

            public String getSuperior() {
                return superior;
            }

            public void setSuperior(String superior) {
                this.superior = superior;
            }

            public String getUnitGuid() {
                return unitGuid;
            }

            public void setUnitGuid(String unitGuid) {
                this.unitGuid = unitGuid;
            }

            public double getPrice() {
                return price;
            }

            public void setPrice(double price) {
                this.price = price;
            }

            public String getGrade() {
                return grade;
            }

            public void setGrade(String grade) {
                this.grade = grade;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }
        }
    }
}
