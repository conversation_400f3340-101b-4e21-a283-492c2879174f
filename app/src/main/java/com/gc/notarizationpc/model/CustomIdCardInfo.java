package com.gc.notarizationpc.model;

import java.io.Serializable;
import java.util.Arrays;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.model
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2023/11/14
 */
public class CustomIdCardInfo implements Serializable {
    private int cardType = 1;
    private int contentLength;
    private int photolength;
    private int fplength;
    private byte[] content;
    private byte[] photo;
    private byte[] fpdata;
    private String name;
    private String gender;//0女 1男
    private String nation;
    private String birthday;
    private String address;
    private String idCard;
    private String depart;
    private String validityTime;
    private String passNum;
    private int visaTimes;
    private boolean isDelMode;//是否是删除模式
    private String mobile;//电话
    private String relationShip;
    private String principal;
    private String orderId;
    private String purposeName;//申请人名称
    private int isDaiBan=0;//1是 0否

    public int getIsDaiBan() {
        return isDaiBan;
    }

    public void setIsDaiBan(int isDaiBan) {
        this.isDaiBan = isDaiBan;
    }

    public String getPurposeName() {
        return purposeName;
    }

    public void setPurposeName(String purposeName) {
        this.purposeName = purposeName;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getRelationShip() {
        return relationShip;
    }

    public void setRelationShip(String relationShip) {
        this.relationShip = relationShip;
    }

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public int getCardType() {
        return cardType;
    }

    public void setCardType(int cardType) {
        this.cardType = cardType;
    }

    public int getContentLength() {
        return contentLength;
    }

    public void setContentLength(int contentLength) {
        this.contentLength = contentLength;
    }

    public int getPhotolength() {
        return photolength;
    }

    public void setPhotolength(int photolength) {
        this.photolength = photolength;
    }

    public int getFplength() {
        return fplength;
    }

    public void setFplength(int fplength) {
        this.fplength = fplength;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public byte[] getPhoto() {
        return photo;
    }

    public void setPhoto(byte[] photo) {
        this.photo = photo;
    }

    public byte[] getFpdata() {
        return fpdata;
    }

    public void setFpdata(byte[] fpdata) {
        this.fpdata = fpdata;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }


    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDepart() {
        return depart;
    }

    public void setDepart(String depart) {
        this.depart = depart;
    }

    public String getValidityTime() {
        return validityTime;
    }

    public void setValidityTime(String validityTime) {
        this.validityTime = validityTime;
    }

    public String getPassNum() {
        return passNum;
    }

    public void setPassNum(String passNum) {
        this.passNum = passNum;
    }

    public int getVisaTimes() {
        return visaTimes;
    }

    public void setVisaTimes(int visaTimes) {
        this.visaTimes = visaTimes;
    }

    public boolean isDelMode() {
        return isDelMode;
    }

    public void setDelMode(boolean delMode) {
        isDelMode = delMode;
    }

    @Override
    public String toString() {
        return "CustomIdCardInfo{" +

                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", nation='" + nation + '\'' +
                ", birthday='" + birthday + '\'' +
                ", address='" + address + '\'' +
                ", idCard='" + idCard + '\'' +
                ", depart='" + depart + '\'' +
                ", validityTime='" + validityTime + '\'' +
                ", passNum='" + passNum + '\'' +
                ", visaTimes=" + visaTimes +
                ", isDelMode=" + isDelMode +
                ", mobile='" + mobile + '\'' +
                ", relationShip='" + relationShip + '\'' +
                ", principal='" + principal + '\'' +
                ", orderId='" + orderId + '\'' +
                ", purposeName='" + purposeName + '\'' +
                ", isDaiBan=" + isDaiBan +
                '}';
    }
}
