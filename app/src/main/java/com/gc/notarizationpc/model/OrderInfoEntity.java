package com.gc.notarizationpc.model;

public class OrderInfoEntity {
    public String unitGuid;      // "ecea38a8-1c5d-44a8-aac2-649ddda2f2e6",
    public String createDate;      // "2021-04-10 17:56:04",
    public String orderNo;      // "202104101756041311",
    public String userId;      // "9f1f1a52-7306-46f5-8350-9a62ced7d018",
    public String name;      // "张思炜",
    public String useArea;      // "中国",
    public String useLanguage;      // "中文（简体）",
    public String purposeName;      // "出国留学",
    public String notaryId;      // "a4cf81a8-d658-47d6-bc52-8e73d37527d9",
    public String notaryName;      // "测试公证处",
    public String greffierId;      // null,
    public String greffierName;      // null,
    public String notaryState;      // 10,
    public String notaryOrderLogs;      // null,
    public String lastDate;      // "2021-04-10 17:56:04",
    public String isDaiBan;      // 0,
    public String notaryForm;      // 1,
    public String description;      // "",
    public String fee;      // 223.0,
    public String supplementFee;      // null,
    public String takeUser;      // null,
    public String takeMobile;      // null,
    public String takeAddress;      // null,
    public String takeStyle;      // null,
    public String pdfUrl;      // null,
    public String signatureUrl;      // null,
    public String signName;      // null,
    public String deleteMark;      // 0,
    public String terminalType;      // 2,
    public String notaryItemNames;      // "出生公证/成绩单公证",
    public String notaryStateName;      // "未完成申办",
    public String enquire;      // null,
    public String scanFiles;      // null,
    public String userSaveVideo;      // null,
    public String userTakeVideo;      // null,
    public String notarySaveVideo;      // null,
    public String notaryTakeVideo;      // null,
    public String materialPdf;      // null,
    public String certificationAdd;      // null,
    public String statutoryPerson;      // null,
    public String companyName;      // null,
    public String companyAdd;      // null,
    public String statutoryMobile;      // null,
    public String wordId;      // null,
    public String lattice;      // null,
    public String materialName;      // null,
    public String notaryItemName;      // null,
    public String companyList;      // null,
    public String roomId;      // null,
    public String signatureUrlList;      // [],
    public String pdfUrlList;      // [],
    public String code;      // null,
    public String payType;      // null,
    public String pay;      // [],
    public String thirdPartyIdCard;      // null,
    public String thirdPartyMob;      // null,
    public String thirdPartyName;      // null,
    public String notaryNum;      // null,
    public String certificate;      // null,
    public String wordUrl;      // null,
    public String certificatePathList;      // [],
    public String secretKey;      // null,
    public String encDataFilePath;      // null,
    public String confirm;      // null,
    public String materialList;      // null,
    public String materUrlList;      // null,
    public String remarks;      // null,
    public String similarityPdf;      // null,
    public String macAddress;      // null,
    public String isIos;      // null,
    public String bankorder;      // null,
    public String num;      // 0,
    public String rtmpPath;      // null,
    public String rstRtmpPath;      // null
}
