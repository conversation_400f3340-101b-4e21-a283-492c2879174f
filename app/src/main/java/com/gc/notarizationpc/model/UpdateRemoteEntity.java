package com.gc.notarizationpc.model;

import java.io.Serializable;
import java.util.List;

public class UpdateRemoteEntity implements Serializable {


    /**
     * msg : 成功
     * item : [{"unitGuid":"69b99431-48b0-4b56-ad10-a1f85984a8e8","annexId":"752cb607-8968-4fa8-8aa6-98d1861b185a","bankOrderId":"a10a5c6e-9d44-41d5-9256-085961633c2d","createDate":"2021-05-10 15:51:33"}]
     * code : 200
     */

    private String msg;
    private List<ItemDTO> item;
    private int code;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<ItemDTO> getItem() {
        return item;
    }

    public void setItem(List<ItemDTO> item) {
        this.item = item;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static class ItemDTO implements Serializable{
        /**
         * unitGuid : 69b99431-48b0-4b56-ad10-a1f85984a8e8
         * annexId : 752cb607-8968-4fa8-8aa6-98d1861b185a
         * bankOrderId : a10a5c6e-9d44-41d5-9256-085961633c2d
         * createDate : 2021-05-10 15:51:33
         */

        private String unitGuid;
        private String annexId;
        private String bankOrderId;
        private String createDate;

        public String getUnitGuid() {
            return unitGuid;
        }

        public void setUnitGuid(String unitGuid) {
            this.unitGuid = unitGuid;
        }

        public String getAnnexId() {
            return annexId;
        }

        public void setAnnexId(String annexId) {
            this.annexId = annexId;
        }

        public String getBankOrderId() {
            return bankOrderId;
        }

        public void setBankOrderId(String bankOrderId) {
            this.bankOrderId = bankOrderId;
        }

        public String getCreateDate() {
            return createDate;
        }

        public void setCreateDate(String createDate) {
            this.createDate = createDate;
        }
    }
}
