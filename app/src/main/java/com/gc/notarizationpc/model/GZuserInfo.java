package com.gc.notarizationpc.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class GZuserInfo implements Serializable {

    boolean success;
    int code;
    String message;
    List<VedioInfo> data = new ArrayList<>();

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<VedioInfo> getData() {
        return data;
    }

    public void setData(List<VedioInfo> data) {
        this.data = data;
    }




    public class VedioInfo implements Serializable {
        String address = "";
        String idCard = "";
        Object otherField = "";
        String phoneNumber = "";
        int sort;
        int userType;

        public boolean isBankUser() {
            return bankUser;
        }

        public void setBankUser(boolean bankUser) {
            this.bankUser = bankUser;
        }

        boolean bankUser;
        String userName = "";

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getIdCard() {
            return idCard;
        }

        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }

        public Object getOtherField() {
            return otherField;
        }

        public void setOtherField(Object otherField) {
            this.otherField = otherField;
        }


        public String getPhoneNumber() {
            return phoneNumber;
        }

        public void setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
        }

        public int getSort() {
            return sort;
        }

        public void setSort(int sort) {
            this.sort = sort;
        }

        public int getUserType() {
            return userType;
        }

        public void setUserType(int userType) {
            this.userType = userType;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }
    }
}
