package com.gc.notarizationpc.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 建行支付，数币支付回调结果
 */
public class DataDTO implements Serializable {

    private String pyUrl;//普通支付返回的url
    private String bnkPyOrdrNo;
    private String cstmPyOrdrNo;
    private String payUrl;//数币支付返回的url
    private String linkOverTime;

    public String getLinkOverTime() {
        return linkOverTime;
    }

    public void setLinkOverTime(String linkOverTime) {
        this.linkOverTime = linkOverTime;
    }

    public String getPyUrl() {
        return pyUrl;
    }

    public void setPyUrl(String pyUrl) {
        this.pyUrl = pyUrl;
    }

    public String getBnkPyOrdrNo() {
        return bnkPyOrdrNo;
    }

    public void setBnkPyOrdrNo(String bnkPyOrdrNo) {
        this.bnkPyOrdrNo = bnkPyOrdrNo;
    }

    public String getCstmPyOrdrNo() {
        return cstmPyOrdrNo;
    }

    public void setCstmPyOrdrNo(String cstmPyOrdrNo) {
        this.cstmPyOrdrNo = cstmPyOrdrNo;
    }

    public String getPayUrl() {
        return payUrl;
    }

    public void setPayUrl(String payUrl) {
        this.payUrl = payUrl;
    }
}

