package com.gc.notarizationpc.model;

import java.io.Serializable;

public class NotaryEntity implements Serializable {

    public String unitGuid;       //"e40beb2a-c0f2-45af-b2c9-b4e14d875e80",
    public String notarialName;       //"测试公证处22",
    public String contactNumber;       //"13839123636",
    public String address;       //"熊猫软件园",
    public String leader;       //"测测二",
    public String notaryNumber;       //null,
    public String workTime;       //null,
    public String cityCode;       //null,
    public String enabledMark;       //null,
    public String deleteMark;       //0,
    public String createDate;       //"2020-10-20 10:15:48",
    public String description;       //null,
    public String watermark;       //"测试公证处二",
    public String institutionType;       //1,
    public String notarialId;       //null,
    public String organizationCode;       //"320100040202010013",
    public String creditCode;       //null,
    public String defaultPublicId;       //"0d49daa0-aaa9-4f18-8a02-0a1608289441"
    
}
