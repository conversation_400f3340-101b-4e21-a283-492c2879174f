package com.gc.notarizationpc.model;

import java.io.Serializable;
import java.util.Objects;

public class GzzxImageBean implements Serializable {
    public String annexFileList;//44cdcd0f-9f06-4741-b080-6e1504644792,
    public String imgPathList;//null

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GzzxImageBean that = (GzzxImageBean) o;
        return imgPathList.equals(that.imgPathList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(imgPathList);
    }
}
