package com.gc.notarizationpc.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class OrdersBean implements Serializable {

    private Integer code;



    private List<DataDTO> data =new ArrayList<>();
    private String message;
    private Boolean success;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }


    public List<DataDTO> getData() {
        return data;
    }

    public void setData(ArrayList<DataDTO> data) {
        this.data = data;
    }
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public static class DataDTO {
        private String acceptDate;
        private String businessType;
        private String createDate;
        private String idCard;
        private String loanPrice;
        private String orderNumber;
        private Integer orderStatus;
        private String unitGuid;
        private String userName;
        private String Abbreviation;
        private String blockBankBusinessInfoId = "";
        private boolean check;
        private String notaryName;

        public String getNotaryName() {
            return notaryName;
        }

        public void setNotaryName(String notaryName) {
            this.notaryName = notaryName;
        }

        public String getBlockBankBusinessInfoId() {
            return blockBankBusinessInfoId;
        }

        public void setBlockBankBusinessInfoId(String blockBankBusinessInfoId) {
            this.blockBankBusinessInfoId = blockBankBusinessInfoId;
        }

        public String getAbbreviation() {
            return Abbreviation;
        }

        public void setAbbreviation(String abbreviation) {
            Abbreviation = abbreviation;
        }

        public boolean isCheck() {
            return check;
        }

        public void setCheck(boolean check) {
            this.check = check;
        }

        public String getAcceptDate() {
            return acceptDate;
        }

        public void setAcceptDate(String acceptDate) {
            this.acceptDate = acceptDate;
        }

        public String getBusinessType() {
            return businessType;
        }

        public void setBusinessType(String businessType) {
            this.businessType = businessType;
        }

        public String getCreateDate() {
            return createDate;
        }

        public void setCreateDate(String createDate) {
            this.createDate = createDate;
        }

        public String getIdCard() {
            return idCard;
        }

        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }

        public String getLoanPrice() {
            return loanPrice;
        }

        public void setLoanPrice(String loanPrice) {
            this.loanPrice = loanPrice;
        }

        public String getOrderNumber() {
            return orderNumber;
        }

        public void setOrderNumber(String orderNumber) {
            this.orderNumber = orderNumber;
        }

        public Integer getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(Integer orderStatus) {
            this.orderStatus = orderStatus;
        }

        public String getUnitGuid() {
            return unitGuid;
        }

        public void setUnitGuid(String unitGuid) {
            this.unitGuid = unitGuid;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }
    }
}
