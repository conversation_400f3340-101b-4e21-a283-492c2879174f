package com.gc.notarizationpc.model;

import java.io.Serializable;

public class NotarizationInfo implements Serializable {
    public String unitGuid;// 1e692b98-5326-4efe-bc9b-b2ae0166c3b7,
    public String createDate;// 2021-01-22 16;//14;//47,
    public String orderNo;// 202101221614471901,
    public String userId;// 97d5a6f4-fef0-43de-9131-029553ad7cf6,
    public String name;// 叶平凡,
    public String useArea;// 中国大陆,
    public String useLanguage;// 中文,
    public String purposeName;// 南京,
    public String notaryId;// c281e156-26ad-4363-a1a0-03cf704ec56f,
    public String notaryName;// 测试公证处22,
    public int notaryState;// 10,
    public int isDaiBan;// 0,
    public int notaryForm;// 2,
    public int currentState = 3;//0在线 1忙碌 2离开 3离线
    public String description;// 迷你一体机发起公证,
}
