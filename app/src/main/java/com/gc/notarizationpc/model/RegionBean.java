package com.gc.notarizationpc.model;

import java.util.List;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.model
 * @Description:
 * @Author: xuh<PERSON>feng
 * @CreateDate: 2023/9/21
 */
public class RegionBean {

    private List<ProvinceBean> text;

    public List<ProvinceBean> getText() {
        return text;
    }

    public void setText(List<ProvinceBean> text) {
        this.text = text;
    }

    public static class ProvinceBean {
        private List<CityBean> children;
        private String text;

        public List<CityBean> getChildren() {
            return children;
        }

        public void setChildren(List<CityBean> children) {
            this.children = children;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public static class CityBean {
            private List<NortryDTO> children;
            private String text;

            public List<NortryDTO> getChildren() {
                return children;
            }

            public void setChildren(List<NortryDTO> children) {
                this.children = children;
            }

            public String getText() {
                return text;
            }

            public void setText(String text) {
                this.text = text;
            }

            public static class NortryDTO {
                private String text;
                private String unitGuid;
                private String watermark;

                public String getText() {
                    return text;
                }

                public void setText(String text) {
                    this.text = text;
                }

                public String getUnitGuid() {
                    return unitGuid;
                }

                public void setUnitGuid(String unitGuid) {
                    this.unitGuid = unitGuid;
                }

                public String getWatermark() {
                    return watermark;
                }

                public void setWatermark(String watermark) {
                    this.watermark = watermark;
                }
            }
        }
    }


//    @lombok.NoArgsConstructor
//    @lombok.Data
//    public static class TextDTO {
//        @com.fasterxml.jackson.annotation.JsonProperty("children")
//        private List<ChildrenDTO> children;
//        @com.fasterxml.jackson.annotation.JsonProperty("text")
//        private String text;
//
//        @lombok.NoArgsConstructor
//        @lombok.Data
//        public static class ChildrenDTO {
//            @com.fasterxml.jackson.annotation.JsonProperty("children")
//            private List<DataDTO.ProvinceBean.CityBean.NortryDTO> children;
//            @com.fasterxml.jackson.annotation.JsonProperty("text")
//            private String text;
//        }
//    }
}
