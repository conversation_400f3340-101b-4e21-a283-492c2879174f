package com.gc.notarizationpc.model;

import java.util.List;

public class OrdersNotaryEntity {

    public String costcompose;   // null,
    public String materials;   // null,
    public String orderSupplements;   // null,
    public List<NotaryItemsEntity> notaryItems;   // [
    public String cabinetLogs;   // null,
    public String thirdpartyusers;   // null,
    public List<OrdersLogsEntity> orderLogs;   // [
    public String videoLog;   // null,
    public UserPEntity user;   // {
    public List<ApplyUserEntity> applyuser;   // [
    public OrderInfoEntity order;   // {


}
