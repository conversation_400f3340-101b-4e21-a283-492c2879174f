package com.gc.notarizationpc.model

import com.alibaba.fastjson.annotation.JSONField
import java.io.Serializable

/**
 * Created by lenovo on 2018/4/8.
 */
class UserInfo : Serializable {
    var idCard: String? = null
    var name: String? = null
    var birthday: String? = null
    var gender: Int? = 1
    var nation: String? = null
    var mobile: String? = null
    var address: String? = null
    var registeAddress: String? = null
    var userId: String? = null

    constructor(idCard: String?, userName: String?, birthday: String?, gender: Int?, nation: String?, address: String?, registeAddress: String?, mobile: String?, userId: String?) {
        this.idCard = idCard
        this.name = userName
        this.birthday = birthday
        this.gender = gender
        this.nation = nation
        this.address = address
        this.registeAddress = registeAddress
        this.mobile = mobile
        this.userId = userId
    }

    constructor()
}