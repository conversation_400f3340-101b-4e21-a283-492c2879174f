package com.gc.notarizationpc

import com.alibaba.android.arouter.launcher.ARouter
import com.example.framwork.BaseApplication
import com.example.framwork.mvp.CustomRequest
import com.example.framwork.noHttp.NetworkConfig
import com.example.framwork.utils.CommonUtil
import com.gc.notarizationpc.common.AppConfig
import com.gc.notarizationpc.common.ResponseBean
import com.gc.notarizationpc.model.GlobalSettings
import com.gc.notarizationpc.model.UserInfo
import com.gc.notarizationpc.utils.CrashHandler
import com.gc.notarizationpc.utils.Log2FileUtils
import com.umeng.commonsdk.UMConfigure
import com.zzhoujay.richtext.RichText
import java.security.SecureRandom
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey


class AppApplication : BaseApplication() {
    var userInfo: UserInfo? = null
    var notaryId: String? = null
    var notaryPublicId: String? = null

    private var globalSettings: GlobalSettings? = null

    override fun onCreate() {
        super.onCreate()
        //富文本设置缓存目录
        RichText.initCacheDir(this)
        AppConfig.init(this, packageName)
        AppConfig.initServerSpServices()
        CustomRequest.setConfig(NetworkConfig.newBuilder().filterExecuteLinstener(null)
                .isEncryption(false).reponseClass(ResponseBean::class.java).build())
        //记录本地错误日志和捕获异常的
        Log2FileUtils.init(this)
        Log2FileUtils.setNeedWrite(true)
        CrashHandler.getInstance().init(this)
        UMConfigure.submitPolicyGrantResult(applicationContext, true)
        UMConfigure.preInit(this, "668b5454cac2a664de637e08", "省公协mini")
        UMConfigure.init(this, "668b5454cac2a664de637e08", "省公协mini", UMConfigure.DEVICE_TYPE_PHONE, "")
        if (CommonUtil.isDebug) {
            ARouter.openLog();     // Print log
            ARouter.openDebug();
        }
        ARouter.init(this);
//        val crashHandler = CrashHandler.getInstance(applicationContext)
//        crashHandler.init(applicationContext)

//        SentryAndroid.init(this) {
//            it.setDebug(true)
//            it.cacheDirPath = externalCacheDir?.absolutePath ?: cacheDir.absolutePath // 默认就是cacheDir，即data\data\包名\cache
//            it.environment = "test" // 环境标识，如生产环境、测试环境，随便自定义字符串
//            it.beforeSend = SentryOptions.BeforeSendCallback { event, hint ->
//                // BeforeSendCallback主要就是上传前的拦截器，比如设置debug数据不上报等，具体看需求
////                return@BeforeSendCallback if (event.level == SentryLevel.DEBUG) null else event
//                return@BeforeSendCallback  event
//            }
//        }

//        Cockroach.install { thread, throwable ->
//            run {
//                Handler(Looper.getMainLooper()).post(Runnable {
//                    try {
//                        Log.e("AndroidRuntime", "--->CockroachException:" + throwable.stackTraceToString())
//                        Toasty.error(applicationContext, "当前网络异常").show()
//                        if (thread == Looper.getMainLooper().thread) {
//                            Log.d("AndroidRuntime", "主线程")
//                            Sentry.captureMessage("主线程:" + throwable.stackTraceToString())
//                        } else {
//                            Log.d("AndroidRuntime", "子线程")
//                            Handler(getMainLooper()).post(Runnable { Sentry.captureMessage("子线程:" + throwable.stackTraceToString()) })
//                        }
//                    } catch (e: Throwable) {
//                        Log.e("AndroidRuntimeC", e.message)
//                    }
//                })
//            }
//        }


    }


    fun bytesToHex(bytes: ByteArray): String {
        val hexChars = CharArray(bytes.size * 2)
        for (j in bytes.indices) {
            val v = bytes[j].toInt() and 0xFF
            hexChars[j * 2] = "0123456789ABCDEF"[v ushr 4]
            hexChars[j * 2 + 1] = "0123456789ABCDEF"[v and 0x0F]
        }
        return String(hexChars)
    }


    fun getGlobalSettings(): GlobalSettings? {
        if (globalSettings == null) {
            globalSettings = GlobalSettings()
        }
        return globalSettings
    }
}

