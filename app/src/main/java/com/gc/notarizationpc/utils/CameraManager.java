package com.gc.notarizationpc.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.hardware.Camera;
import android.hardware.Camera.AutoFocusCallback;
import android.hardware.Camera.Parameters;
import android.hardware.Camera.PreviewCallback;
import android.hardware.Camera.Size;
import android.os.Handler;
import android.os.Message;
import android.os.Build.VERSION;
import android.util.Log;
import android.view.Display;
import android.view.SurfaceHolder;
import android.view.WindowManager;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * @author: wjw
 * @create：2020/7/13
 * @describe：
 */
public class CameraManager implements AutoFocusCallback {
    private final String TAG = "cc_camera";
    private int mWidth = 1400;
    private int mPreviewWidth = 1280;
    private int mPreviewHeigth = 720;
    private Handler mHandler;
    private Camera mCamera;
    private Context mContext;
    private boolean first = true;
    private AutoFocusCallback autoFocusCallback = new AutoFocusCallback() {
        public void onAutoFocus(boolean success, Camera camera) {
            Log.d(TAG, "---autoFoucs-------------->>" + success);
            if (success) {
            }

        }
    };
    private PreviewCallback previewCallback = new PreviewCallback() {
        public void onPreviewFrame(byte[] data, Camera camera) {
            if (!first) {
                Message msg = mHandler.obtainMessage();
                msg.what = 200;
                msg.obj = data;
                mHandler.sendMessage(msg);
            }
        }
    };

    public CameraManager(Context context, Handler handler) {
        this.mHandler = handler;
        this.mContext = context;
    }

    public void openCamera(SurfaceHolder holder) throws IOException {
        this.closeCamera();
        if (this.mCamera == null) {
            this.mCamera = Camera.open(0);
        }

        this.mCamera.setPreviewDisplay(holder);
        this.setWhiteBalanceAuto();
    }

    private void setPreviewOrientation(int angle) {
        if (this.mCamera != null) {
            Parameters parameters = this.mCamera.getParameters();
            if (Integer.parseInt(VERSION.SDK) > 7) {
                this.setDisplayOrientation(this.mCamera, angle);
            } else {
                if (this.mContext.getResources().getConfiguration().orientation == 1) {
                    parameters.set("orientation", "portrait");
                    parameters.set("rotation", 90);
                }

                if (this.mContext.getResources().getConfiguration().orientation == 2) {
                    parameters.set("orientation", "landscape");
                    parameters.set("rotation", 90);
                }
            }
        }

    }

    protected void setDisplayOrientation(Camera camera, int angle) {
        try {
            Method downPolymorphic = camera.getClass().getMethod("setDisplayOrientation", Integer.TYPE);
            if (downPolymorphic != null) {
                downPolymorphic.invoke(camera, angle);
            }
        } catch (Exception var5) {
        }

    }

    public boolean cameraOpened() {
        return this.mCamera != null;
    }

    public void openCamera() {
        if (this.mCamera == null) {
            this.mCamera = Camera.open(0);

            try {
                this.mCamera.setOneShotPreviewCallback(this.previewCallback);
            } catch (Exception var2) {

            }
        }

    }

    public void openCamera(int orientation) {
        if (this.mCamera == null) {
            this.mCamera = Camera.open(0);
            switch(orientation) {
                case 0:
                    this.setPreviewOrientation(90);
                case 1:
                default:
                    break;
                case 2:
                    this.setPreviewOrientation(270);
                    break;
                case 3:
                    this.setPreviewOrientation(180);
            }

            try {
                this.mCamera.setOneShotPreviewCallback(this.previewCallback);
            } catch (Exception var3) {
            }
        }

    }

    public void setPreviewDisplay(SurfaceHolder holder) {
        if (this.mCamera != null) {
            try {
                this.mCamera.setPreviewDisplay(holder);
            } catch (IOException var3) {
                var3.printStackTrace();
            }
        }

    }

    public void initDisplay() {
        if (this.mCamera != null) {
            try {
                this.mCamera.startPreview();
            } catch (Exception var2) {
            }
        }

    }

    public void pauseDisplay() {
        if (this.mCamera != null) {
            this.mCamera.stopPreview();
        }

    }

    public void closeCamera() {
        if (this.mCamera != null) {
            this.mCamera.stopPreview();
            this.mCamera.release();
            this.mCamera = null;
        }

    }

    public void setCameraFlashModel(String model) {
        if (this.mCamera != null) {
            Parameters parameters = this.mCamera.getParameters();
            parameters.setFlashMode(model);

            try {
                this.mCamera.setParameters(parameters);
            } catch (Exception var4) {
            }
        }

    }

    public boolean isSupportAutoFocus() {
        List<String> list = this.getSupportedFocusModes();
        if (list == null) {
            return false;
        } else {
            Iterator var3 = list.iterator();

            while(var3.hasNext()) {
                String string = (String)var3.next();
                if ("auto".equals(string)) {
                    this.setFocusMode("auto");
                    return true;
                }
            }

            return false;
        }
    }

    public void requestFoucs() {
        if (this.mCamera != null) {
            this.mCamera.autoFocus(this);
        }

    }

    public void setFocusMode(String mode) {
        if (this.mCamera != null) {
            try {
                Parameters parameters = this.mCamera.getParameters();
                parameters.setFocusMode(mode);
                this.mCamera.setParameters(parameters);
            } catch (Exception var3) {
            }
        }

    }

    public void setPreviewSize(int width, int height) {
        try {
            Parameters parameters = this.mCamera.getParameters();
            parameters.setPreviewSize(width, height);
            this.mCamera.setParameters(parameters);
        } catch (Exception var4) {
        }

    }

    public void setPicSize(Size optimalSize) {
        if (this.mCamera != null) {
            Parameters parameters = this.mCamera.getParameters();
            parameters.setPictureFormat(256);
            List<Size> pictureSize = parameters.getSupportedPictureSizes();
            if (optimalSize != null) {
                parameters.setPictureSize(optimalSize.width, optimalSize.height);
                parameters.setJpegQuality(100);
                this.mCamera.setParameters(parameters);
                Log.d(TAG, optimalSize.width + "<--w-optimalPicSize---h---->" + optimalSize.height);
                return;
            }

            int x = 0;
            int tempSize = 3000;

            for(int i = 0; i < pictureSize.size(); ++i) {
                if (((Size)pictureSize.get(i)).width >= this.mWidth && ((Size)pictureSize.get(i)).width <= tempSize) {
                    tempSize = ((Size)pictureSize.get(i)).width;
                    x = i;
                }
            }

            parameters.setPictureSize(((Size)pictureSize.get(x)).width, ((Size)pictureSize.get(x)).height);
            parameters.setJpegQuality(100);
            this.mCamera.setParameters(parameters);
            Log.d(TAG, ((Size)pictureSize.get(x)).width + "<--w-pictureSize---h---->" + ((Size)pictureSize.get(x)).height);
        }

    }

    private Size getPreviewSizes() {
        Size pSize = this.getPictureSizeForPreviewSize();
        if (pSize == null) {
            this.setPicSize((Size)null);
            Parameters var2 = this.mCamera.getParameters();
            List prviewSizes = var2.getSupportedPreviewSizes();
            if (prviewSizes != null) {
                @SuppressLint("WrongConstant") WindowManager windowManager = (WindowManager)this.mContext.getSystemService("window");
                Display display = windowManager.getDefaultDisplay();
                int w = display.getWidth();
                if (w > this.mPreviewWidth) {
                    this.mPreviewWidth = w;
                    return null;
                } else {
                    int x = 0;
                    int tempSize = 0;

                    for(int i = 0; i < prviewSizes.size(); ++i) {
                        if (((Size)prviewSizes.get(i)).width <= this.mPreviewWidth && ((Size)prviewSizes.get(i)).width >= tempSize) {
                            tempSize = ((Size)prviewSizes.get(i)).width;
                            x = i;
                        }
                    }

                    return (Size)prviewSizes.get(x);
                }
            } else {
                return null;
            }
        } else {
            return pSize;
        }
    }

    private Size getFitPreviewSize(float ps) {
        Parameters parameters = this.mCamera.getParameters();
        List<Size> prviewSizes = parameters.getSupportedPreviewSizes();
        int i;
        for(i = prviewSizes.size(); i >= 0; --i) {
            for(i = 0; i < i - 1; ++i) {
                if (((Size)prviewSizes.get(i)).width > ((Size)prviewSizes.get(i + 1)).width) {
                    Size tempSize = (Size)prviewSizes.get(i + 1);
                    prviewSizes.set(i + 1, (Size)prviewSizes.get(i));
                    prviewSizes.set(i, tempSize);
                }
            }
        }

        for(i = 0; i < prviewSizes.size(); ++i) {
            i = ((Size)prviewSizes.get(i)).width;
            int h = ((Size)prviewSizes.get(i)).height;
            float whp = (float)i * 1.0F / (float)h;
            if (i >= this.mPreviewWidth && h >= this.mPreviewHeigth && whp <= ps) {
                return (Size)prviewSizes.get(i);
            }
        }

        return null;
    }

    private Size getPictureSizeForPreviewSize() {
        Parameters parameters = this.mCamera.getParameters();
        List<Size> prviewSizes = parameters.getSupportedPreviewSizes();
        List<Size> pictureSize = parameters.getSupportedPictureSizes();
        List<Size> tempMaxSizes = new ArrayList();
        List<Size> tempMinSizes = new ArrayList();

        Size tempSize;
        int i;
        int j;
        for(i = pictureSize.size(); i >= 0; --i) {
            for(j = 0; j < i - 1; ++j) {
                if (((Size)pictureSize.get(j)).width > ((Size)pictureSize.get(j + 1)).width) {
                    tempSize = (Size)pictureSize.get(j + 1);
                    pictureSize.set(j + 1, (Size)pictureSize.get(j));
                    pictureSize.set(j, tempSize);
                }
            }
        }

        for(i = 0; i < pictureSize.size(); ++i) {
            if (((Size)pictureSize.get(i)).width >= this.mWidth) {
                tempMaxSizes.add((Size)pictureSize.get(i));
            } else {
                tempMinSizes.add((Size)pictureSize.get(i));
            }
        }

        for(i = prviewSizes.size(); i >= 0; --i) {
            for(j = 0; j < i - 1; ++j) {
                if (((Size)prviewSizes.get(j)).width > ((Size)prviewSizes.get(j + 1)).width) {
                    tempSize = (Size)prviewSizes.get(j + 1);
                    prviewSizes.set(j + 1, (Size)prviewSizes.get(j));
                    prviewSizes.set(j, tempSize);
                }
            }
        }

        for(i = prviewSizes.size() - 1; i >= 0; --i) {
            if (((Size)prviewSizes.get(i)).width <= this.mPreviewWidth) {
                for(j = 0; j < tempMaxSizes.size(); ++j) {
                    if ((double)((Size)prviewSizes.get(i)).width / (double)((Size)prviewSizes.get(i)).height == (double)((Size)tempMaxSizes.get(j)).width / (double)((Size)tempMaxSizes.get(j)).height) {
                        this.setPicSize((Size)tempMaxSizes.get(j));
                        return (Size)prviewSizes.get(i);
                    }
                }
            }
        }

        for(i = prviewSizes.size() - 1; i >= 0; --i) {
            if (((Size)prviewSizes.get(i)).width <= this.mPreviewWidth) {
                for(j = tempMinSizes.size() - 1; j >= 0; --j) {
                    if (((Size)prviewSizes.get(i)).width / ((Size)prviewSizes.get(i)).height == ((Size)tempMinSizes.get(j)).width / ((Size)tempMinSizes.get(j)).height) {
                        this.setPicSize((Size)tempMinSizes.get(j));
                        return (Size)prviewSizes.get(i);
                    }
                }
            }
        }

        return null;
    }

    public void setPreviewSize(float ps) {
        if (this.mCamera != null) {
        //    Size size = this.getFitPreviewSize(ps);
            Parameters parameters = this.mCamera.getParameters();
            if (true) {
                parameters.setPreviewSize(1280, 720);
            } else {
                @SuppressLint("WrongConstant") WindowManager windowManager = (WindowManager)this.mContext.getSystemService("window");
                Display display = windowManager.getDefaultDisplay();
                int w = display.getWidth();
                int h = display.getHeight();
                parameters.setPreviewSize(w, h);
            }

            try {
                this.mCamera.setParameters(parameters);
            } catch (Exception var8) {
            }

        }
    }

    public void onAutoFocus(boolean success, Camera camera) {
        Log.d(TAG, "--onAutoFocus------>>>" + success);
    }

    public void autoFocusAndPreviewCallback() {
        if (this.mCamera != null) {
            try {
                this.first = false;
                this.mCamera.setOneShotPreviewCallback(this.previewCallback);
            } catch (Exception var2) {
                var2.printStackTrace();
                this.mHandler.sendEmptyMessage(206);
            }
        }

    }

    public void autoFoucs() {
        try {
            this.mCamera.autoFocus(this.autoFocusCallback);
        } catch (Exception var2) {
        }

    }

    private List<String> getSupportedFocusModes() {
        List<String> list = null;
        if (this.mCamera != null) {
            Parameters parameters = this.mCamera.getParameters();
            list = parameters.getSupportedFocusModes();
            Iterator var4 = list.iterator();

            while(var4.hasNext()) {
                String string = (String)var4.next();
                Log.d("path", "------SupportedFocusModes----------->>" + string);
            }
        }

        return list;
    }

    private void setWhiteBalanceAuto() {
        if (this.mCamera != null) {
            Parameters parameters = this.mCamera.getParameters();
            parameters.setWhiteBalance("auto");

            try {
                this.mCamera.setParameters(parameters);
            } catch (Exception var3) {
                var3.printStackTrace();
            }
        }

    }

    public Rect getViewfinder(Rect finderRect) {
        @SuppressLint("WrongConstant") WindowManager windowManager = (WindowManager)this.mContext.getSystemService("window");
        Display display = windowManager.getDefaultDisplay();
        float w = (float)display.getWidth();
        float h = (float)display.getHeight();
        int width = this.mCamera.getParameters().getPreviewSize().width;
        int height = this.mCamera.getParameters().getPreviewSize().height;
        float xs = (float)width / w;
        float ys = (float)height / h;
        Rect rect = new Rect(finderRect);
        rect.left = (int)((float)finderRect.left * xs);
        rect.right = (int)((float)finderRect.right * xs);
        rect.top = (int)((float)finderRect.top * ys);
        rect.bottom = (int)((float)finderRect.bottom * ys);
        return rect;
    }

    public Rect getViewfinderP(Rect finderRect) {
        @SuppressLint("WrongConstant") WindowManager windowManager = (WindowManager)this.mContext.getSystemService("window");
        Display display = windowManager.getDefaultDisplay();
        float w = (float)display.getWidth();
        float h = (float)display.getHeight();
        int width = this.mCamera.getParameters().getPreviewSize().width;
        int height = this.mCamera.getParameters().getPreviewSize().height;
        float ys = (float)height / w;
        float xs = (float)width / h;
        Rect rect = new Rect(finderRect);
        rect.left = (int)((float)finderRect.left * ys);
        rect.right = (int)((float)finderRect.right * ys);
        rect.top = (int)((float)finderRect.top * xs);
        rect.bottom = (int)((float)finderRect.bottom * xs);
        return rect;
    }

    public Rect getRectByPhoto(Rect finderRect) {
        Rect previewRect = this.getViewfinder(finderRect);
        float w = (float)this.mCamera.getParameters().getPreviewSize().width;
        float h = (float)this.mCamera.getParameters().getPreviewSize().height;
        float width = (float)this.mCamera.getParameters().getPictureSize().width;
        float height = (float)this.mCamera.getParameters().getPictureSize().height;
        float xs = width / w;
        float ys = height / h;
        Rect rect = new Rect(previewRect);
        Log.d("ocr", rect.left + "<-----1--->" + rect.right + "<----->" + rect.top + "<------------>" + rect.bottom);
        rect.left = (int)((float)previewRect.left * xs);
        rect.right = (int)((float)previewRect.right * xs);
        rect.top = (int)((float)previewRect.top * ys);
        rect.bottom = (int)((float)previewRect.bottom * ys);
        Log.d("ocr", rect.left + "<-----2--->" + rect.right + "<----->" + rect.top + "<------------>" + rect.bottom);
        return rect;
    }

    public List<Size> getSupportedPreviewSizes() {
        return this.mCamera != null ? this.mCamera.getParameters().getSupportedPreviewSizes() : null;
    }

    public Size getPicSize() {
        return this.mCamera != null ? this.mCamera.getParameters().getPictureSize() : null;
    }

    public int getPreviewWidth() {
        return this.mCamera != null ? this.mCamera.getParameters().getPreviewSize().width : 0;
    }

    public int getPreviewHeight() {
        return this.mCamera != null ? this.mCamera.getParameters().getPreviewSize().height : 0;
    }

    public Size getOptimalPreviewSize(Activity currentActivity, List<Size> sizes, double targetRatio) {
        double ASPECT_TOLERANCE = 0.001D;
        if (sizes == null) {
            return null;
        } else {
            Size optimalSize = null;
            double minDiff = 1.7976931348623157E308D;
            Display display = currentActivity.getWindowManager().getDefaultDisplay();
            int targetHeight = Math.min(display.getHeight(), display.getWidth());
            if (targetHeight <= 0) {
                targetHeight = display.getHeight();
            }

            Iterator var13 = sizes.iterator();

            Size size;
            while(var13.hasNext()) {
                size = (Size)var13.next();
                double ratio = (double)size.width / (double)size.height;
                if (Math.abs(ratio - targetRatio) <= 0.001D && (double)Math.abs(size.height - targetHeight) < minDiff) {
                    optimalSize = size;
                    minDiff = (double)Math.abs(size.height - targetHeight);
                }
            }

            if (optimalSize == null) {
                Log.w(TAG, "No preview size match the aspect ratio");
                minDiff = 1.7976931348623157E308D;
                var13 = sizes.iterator();

                while(var13.hasNext()) {
                    size = (Size)var13.next();
                    if ((double)Math.abs(size.height - targetHeight) < minDiff) {
                        optimalSize = size;
                        minDiff = (double)Math.abs(size.height - targetHeight);
                    }
                }
            }

            return optimalSize;
        }
    }

    public Size getOptimalPictureSize(List<Size> sizes, double targetRatio, boolean isMax) {
        int tempMax = 100000000;
        int tempMin = 0;
        Size s = null;
        double ASPECT_TOLERANCE = 0.001D;
        if (sizes == null) {
            return null;
        } else {
            Iterator var11 = sizes.iterator();

            while(var11.hasNext()) {
                Size size = (Size)var11.next();
                Log.d(TAG, size.width + "<-------getOptimalPictureSize---------->" + isMax);
                double ratio = (double)size.width / (double)size.height;
                if (ratio == targetRatio) {
                    if (isMax) {
                        if (size.width * size.height < tempMax) {
                            tempMax = size.width * size.height;
                            s = size;
                        }
                    } else if (size.width * size.height > tempMin) {
                        tempMin = size.width * size.height;
                        s = size;
                    }
                }
            }

            return s;
        }
    }

    public boolean openFlashlight() {
        if (this.mCamera == null) {
            this.mCamera = Camera.open(0);
        }

        Parameters parameters = this.mCamera.getParameters();
        if (parameters.getSupportedFlashModes() == null) {
            return false;
        } else if (parameters.getFlashMode().equalsIgnoreCase("torch")) {
            return false;
        } else {
            parameters.setFlashMode("torch");

            try {
                this.mCamera.setParameters(parameters);
            } catch (Exception var3) {
            }

            return this.mCamera.getParameters().getFlashMode().equals("torch");
        }
    }

    public boolean closeFlashlight() {
        if (this.mCamera == null) {
            this.mCamera = Camera.open(0);
        }

        if (this.mCamera.getParameters().getFlashMode().equalsIgnoreCase("off")) {
            return false;
        } else {
            Parameters parameters = this.mCamera.getParameters();
            parameters.setFlashMode("off");

            try {
                this.mCamera.setParameters(parameters);
            } catch (Exception var3) {
                Log.e(TAG,var3.getMessage());
            }

            return this.mCamera.getParameters().getFlashMode().equalsIgnoreCase("off");
        }
    }
}
