package com.gc.notarizationpc.utils;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.text.TextUtils;
import android.util.Log;

import androidx.core.content.ContextCompat;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.gc.notarizationpc.ui.RemoteNotarizationActivity;
import com.gc.notarizationpc.ui.RemoteOneActivity;
import com.gc.notarizationpc.ui.RemoteTwoActivity;
import com.gc.notarizationpc.ui.SelectNotary;


public class GpsUtil {

    private static AMapLocationClient aMapLocationClient;
    private static AMapLocationClientOption clientOption;
    private static AMapLocationListener aMapLocationListener;

    /**
     * 第一步：检查应用是否拥有位置权限
     *
     * @return false代表没有该权限，ture代表有该权限
     */
    public static boolean hasGPSPermission(Context context) {
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
                || ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            return false;
        }

        return true;
    }

    /**
     * 第二步:  判断手机是否开启位置服务
     * 如果没有开启那么所有app将不能使用定位功能
     */
    public static boolean isLocationServiceEnable(Context context) {
        LocationManager locationManager = (LocationManager)
                context.getSystemService(Context.LOCATION_SERVICE);
        boolean gps = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
        boolean network = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
        if (gps || network) {
            return true;
        }
        return false;
    }

    public interface MapGPS {
        void success(AMapLocation aMapLocation);

        void failed();
    }

    public static void getlocation(Context context, MapGPS maps) {
        //初始化定位  用户合规检查
        AMapLocationClient.updatePrivacyShow(context, true, true);
        AMapLocationClient.updatePrivacyAgree(context, true);
        try {
            aMapLocationClient = new AMapLocationClient(context.getApplicationContext());
        } catch (Exception e) {
            e.printStackTrace();
            maps.failed();
        }
        aMapLocationListener = aMapLocation -> {
            try {
                if (aMapLocation != null) {
                    if (aMapLocation.getErrorCode() == 0 &&
                            Math.abs(aMapLocation.getLongitude()) > 0 &&
                            Math.abs(aMapLocation.getLatitude()) > 0) {
                        aMapLocationClient.stopLocation();
                        //获取成功
                        maps.success(aMapLocation);
                    } else {
                        maps.failed();
                    }

                } else {
                    maps.failed();
                }
            } catch (Exception e) {
                Log.e("MapGPS", e.getMessage());
                maps.failed();
            }
        };
        //设置监听回调
        aMapLocationClient.setLocationListener(aMapLocationListener);

        //初始化定位参数
        clientOption = new

                AMapLocationClientOption();
        clientOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Battery_Saving);
        clientOption.setNeedAddress(true);
        //获取一次定位结果：该方法默认为false
        clientOption.setOnceLocation(false);
        //设置是否强制刷新WIFI，默认为强制刷新
        clientOption.setWifiActiveScan(true);
        //设置是否允许模拟位置,默认为false，不允许模拟位置
        clientOption.setMockEnable(false);
        //设置定位间隔
        clientOption.setInterval(2000);
        aMapLocationClient.setLocationOption(clientOption);
        aMapLocationClient.startLocation();
    }

    public static void getlocation(Context context, MapGPS maps, boolean isOne) {
        //初始化定位  用户合规检查
        AMapLocationClient.updatePrivacyShow(context, true, true);
        AMapLocationClient.updatePrivacyAgree(context, true);
        try {
            aMapLocationClient = new AMapLocationClient(context.getApplicationContext());
        } catch (Exception e) {
            e.printStackTrace();
            maps.failed();
        }
        aMapLocationListener = aMapLocation -> {
            try {
                if (aMapLocation != null) {
                    if (aMapLocation.getErrorCode() == 0) {
                        aMapLocationClient.stopLocation();
                        //获取成功
                        maps.success(aMapLocation);
                    } else {
                        maps.failed();
                    }

                } else {
                    maps.failed();
                }
            } catch (Exception e) {
                Log.e("MapGPS", e.getMessage());
                maps.failed();
            }
        };
        //设置监听回调
        aMapLocationClient.setLocationListener(aMapLocationListener);

        //初始化定位参数
        clientOption = new

                AMapLocationClientOption();
        clientOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Battery_Saving);
        clientOption.setNeedAddress(true);
        //获取一次定位结果：该方法默认为false
        clientOption.setOnceLocation(isOne);
        //设置是否强制刷新WIFI，默认为强制刷新
        clientOption.setWifiActiveScan(true);
        //设置是否允许模拟位置,默认为false，不允许模拟位置
        clientOption.setMockEnable(false);
        //设置定位间隔
        clientOption.setInterval(2000);
        aMapLocationClient.setLocationOption(clientOption);
        aMapLocationClient.startLocation();
    }

    public static void getlocation(Context context) {

        //初始化定位  用户合规检查
        AMapLocationClient.updatePrivacyShow(context, true, true);
        AMapLocationClient.updatePrivacyAgree(context, true);
        try {
            aMapLocationClient = new AMapLocationClient(context.getApplicationContext());
        } catch (Exception e) {
            e.printStackTrace();
        }
        aMapLocationListener = aMapLocation -> {
            if (aMapLocation != null) {
                aMapLocationClient.stopLocation();
                if (aMapLocation.getErrorCode() == 0) {
                    //获取成功
                    double lng = aMapLocation.getLongitude();
                    double lat = aMapLocation.getLatitude();
                    String address = aMapLocation.getAddress();
                    Log.d("1111111", aMapLocation.toString());
                    if (context instanceof RemoteTwoActivity)
                        ((RemoteTwoActivity) context).push(lng, lat, address);
//                    if (context instanceof RemoteOneActivity)
//                        ((RemoteOneActivity) context).push(lng, lat, address);
                }

            }
            if (context instanceof RemoteTwoActivity)
                ((RemoteTwoActivity) context).push(0, 0, "");
        };
        //设置监听回调
        aMapLocationClient.setLocationListener(aMapLocationListener);

        //初始化定位参数
        clientOption = new AMapLocationClientOption();
        clientOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Battery_Saving);
        clientOption.setNeedAddress(true);
        //获取一次定位结果：该方法默认为false
        clientOption.setOnceLocation(true);
        //设置是否强制刷新WIFI，默认为强制刷新
        clientOption.setWifiActiveScan(true);
        //设置是否允许模拟位置,默认为false，不允许模拟位置
        clientOption.setMockEnable(false);
        //设置定位间隔
        clientOption.setInterval(2000);
        aMapLocationClient.setLocationOption(clientOption);
        aMapLocationClient.startLocation();
    }

}
