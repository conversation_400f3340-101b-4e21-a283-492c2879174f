package com.gc.notarizationpc.utils;

import android.content.Context;
import android.content.SharedPreferences;


public class SPMgr {

    private static SPMgr share = null;

    private SharedPreferences sharePre = null;

    private SharedPreferences.Editor editor = null;

    private SPMgr(Context context) {
        sharePre = context.getSharedPreferences("app_info", Context.MODE_PRIVATE);
        editor = sharePre.edit();
    }

    private SPMgr(Context context, String name) {
        sharePre = context.getSharedPreferences(name, Context.MODE_PRIVATE);
        editor = sharePre.edit();
    }

    public static SPMgr getInstance(Context context) {
        while (share == null) {
            synchronized (SPMgr.class) {
                if (share == null) {
                    share = new SPMgr(context);
                }
            }
        }
        return share;
    }

    public void putElement(String name, String value) {
        editor.putString(name, value);
        editor.commit();
    }

    public void putIntElement(String name, int value) {
        editor.putInt(name, value);
        editor.commit();
    }

    public int getIntElement(String name) {
        return sharePre.getInt(name, -1);
    }

    public void putBooleanElement(String name, boolean flag) {
        editor.putBoolean(name, flag);
        editor.commit();
    }

    public boolean getBooleanElement(String name) {
        return sharePre.getBoolean(name, false);
    }

    public void removeElement(String name) {
        editor.remove(name);
        editor.commit();
    }

    public String getElement(String name) {
        return sharePre.getString(name, "");
    }

    public String getElement(String name, String defaultName) {
        return sharePre.getString(name, defaultName);
    }

    public void clearSPMgr() {
        editor.clear();
        editor.commit();
    }
}
