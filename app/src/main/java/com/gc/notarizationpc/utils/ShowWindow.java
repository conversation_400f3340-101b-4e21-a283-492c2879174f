package com.gc.notarizationpc.utils;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.ui.MainActivity;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

public class ShowWindow {
    public static void checkMaintain(Context context) {
        new Thread() {
            @Override
            public void run() {
                String msg = "";
                try {
                    URL myFileUrl = null;
                    myFileUrl = new URL(AppConfig.checkMaintain);
                    HttpURLConnection conn;
                    conn = (HttpURLConnection) myFileUrl.openConnection();
                    conn.setDoInput(true);
                    conn.connect();
                    InputStream is = conn.getInputStream();
                    BufferedReader br = new BufferedReader(new InputStreamReader(is,
                            "UTF-8"));
                    StringBuffer sb = new StringBuffer();
                    String data = "";
                    while ((data = br.readLine()) != null) {
                        sb.append(data + "\n");
                    }
                    String result = sb.toString();
//                    Log.d("zpzp", result);
                    JSONObject jsonObject = JSON.parseObject(result);
                    int code = JSON.parseObject(jsonObject.get("country_state").toString()).getIntValue("code");
                    msg = JSON.parseObject(jsonObject.get("country_state").toString()).getString("message") + "";
                    Log.d("SystemErro", code + ""+msg);
                    if (code == 200001) {//显示灰色界面
                        if (context instanceof MainActivity)
                            ((MainActivity) context).setWindow();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    String finalMsg = msg;

                }
            }
        }.start();
    }
}
