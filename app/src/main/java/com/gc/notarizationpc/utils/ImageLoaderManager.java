package com.gc.notarizationpc.utils;

import android.graphics.Bitmap;
import android.text.TextUtils;
import android.widget.ImageView;

import androidx.core.graphics.drawable.RoundedBitmapDrawable;
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.BitmapImageViewTarget;

/**********************************************************
 * 文件名称：*.java
 * 创建时间：2015年8月26日 下午10:58:03
 * 文件描述：图处加载类，外界唯一调用类,直持为view,notifaication,appwidget加载图片
 * 修改历史：
 **********************************************************/
public class ImageLoaderManager {

    private ImageLoaderManager() {
    }

    public static ImageLoaderManager getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        private static ImageLoaderManager instance = new ImageLoaderManager();
    }


//    private RequestOptions initCommonRequestOption() {
//        RequestOptions options = new RequestOptions();
//        options.placeholder(R.mipmap.none)
//                .error(R.mipmap.none)
//                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
//                .skipMemoryCache(false)
//                .priority(Priority.NORMAL);
//        return options;
//    }

    public interface GifListener {
        void gifPlayComplete();
    }

    public void displayImage(final ImageView imageView, String url, RequestOptions requestOptions) {

        if (!TextUtils.isEmpty(url) && (url.startsWith("http://") || url.startsWith("https://"))) {
            Glide.with(imageView.getContext())
                    .asBitmap()
                    .load(url)
                    .apply(requestOptions)
                    .into(new BitmapImageViewTarget(imageView) {
                        @Override
                        protected void setResource(final Bitmap resource) {
                            RoundedBitmapDrawable circularBitmapDrawable =
                                    RoundedBitmapDrawableFactory.create(imageView.getResources(), resource);
                            circularBitmapDrawable.setCircular(false);
                            imageView.setImageDrawable(circularBitmapDrawable);
                        }
                    });
        } else {
            Glide.with(imageView.getContext())
                    .asBitmap()
                    .load(url)
                    .apply(requestOptions)
                    .into(new BitmapImageViewTarget(imageView) {
                        @Override
                        protected void setResource(final Bitmap resource) {
                            RoundedBitmapDrawable circularBitmapDrawable =
                                    RoundedBitmapDrawableFactory.create(imageView.getResources(), resource);
                            circularBitmapDrawable.setCircular(false);
                            imageView.setImageDrawable(circularBitmapDrawable);
                        }
                    });
        }

    }

    public void displayImage(final ImageView imageView, String url) {

        if (!TextUtils.isEmpty(url) && (url.startsWith("http://") || url.startsWith("https://"))) {
            Glide.with(imageView.getContext())
                    .asBitmap()
                    .load( url)
                    .into(new BitmapImageViewTarget(imageView) {
                        @Override
                        protected void setResource(final Bitmap resource) {
                            RoundedBitmapDrawable circularBitmapDrawable =
                                    RoundedBitmapDrawableFactory.create(imageView.getResources(), resource);
                            circularBitmapDrawable.setCircular(false);
                            imageView.setImageDrawable(circularBitmapDrawable);
                        }
                    });
        } else {
            Glide.with(imageView.getContext())
                    .asBitmap()
                    .load(url)
                    .into(new BitmapImageViewTarget(imageView) {
                        @Override
                        protected void setResource(final Bitmap resource) {
                            RoundedBitmapDrawable circularBitmapDrawable =
                                    RoundedBitmapDrawableFactory.create(imageView.getResources(), resource);
                            circularBitmapDrawable.setCircular(false);
                            imageView.setImageDrawable(circularBitmapDrawable);
                        }
                    });
        }

    }

}
