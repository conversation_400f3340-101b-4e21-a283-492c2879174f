package com.gc.notarizationpc.utils.helper;


import static com.gc.notarizationpc.utils.helper.render.opengl.OpenGlUtils.NO_TEXTURE;

import android.graphics.SurfaceTexture;
import android.opengl.EGLContext;
import android.opengl.GLES20;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.util.Pair;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.eloam.gaopaiyi.util.UVCCameraUtil;
import com.gc.notarizationpc.utils.helper.basic.TextureFrame;
import com.gc.notarizationpc.utils.helper.render.EglCore;
import com.gc.notarizationpc.utils.helper.render.opengl.GPUImageFilter;
import com.gc.notarizationpc.utils.helper.render.opengl.GPUImageFilterGroup;
import com.gc.notarizationpc.utils.helper.render.opengl.OesInputFilter;
import com.gc.notarizationpc.utils.helper.render.opengl.OpenGlUtils;
import com.gc.notarizationpc.utils.helper.render.opengl.Rotation;
import com.serenegiant.usb.IFrameCallback;
import com.serenegiant.usb.USBMonitor;
import com.tencent.custom.customcapture.structs.FrameBuffer;

import java.lang.ref.WeakReference;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;


public class CustomCameraCapture1 implements SurfaceTexture.OnFrameAvailableListener {

    private static final String TAG = "CustomCameraCapture1";

    private static final int WIDTH = 1920;
    private static final int HEIGHT = 1080;
    private static final int WHAT_START1 = 2;
    private static final int WHAT_UPDATE = 1;

    private SurfaceTexture mSurfaceTexture;
    private EglCore mEglCore;
    private FrameBuffer mFrameBuffer;
    private OesInputFilter mOesInputFilter;
    private GPUImageFilterGroup mGpuImageFilterGroup;

    private final FloatBuffer mGLCubeBuffer;
    private final FloatBuffer mGLTextureBuffer;
    private final float[] mTextureTransform = new float[16]; // OES纹理转换为2D纹理
    private int mSurfaceTextureId = NO_TEXTURE;
    private boolean mFrameUpdated;
    private VideoFrameReadListener mVideoFrameReadListener;
    private HandlerThread mRenderHandlerThread;
    private volatile RenderHandler mRenderHandler;
    private String pid;
    private String name;
    private USBMonitor.UsbControlBlock controlBlock;

    public interface VideoFrameReadListener {
        void onFrameAvailable(EGLContext eglContext, int textureId, int width, int height);
    }

    public CustomCameraCapture1(String pid, USBMonitor.UsbControlBlock controlBlock) {
        mFrameUpdated = false;

        Pair<float[], float[]> cubeAndTextureBuffer = OpenGlUtils.calcCubeAndTextureBuffer(ImageView.ScaleType.MATRIX, Rotation.NORMAL,
                false, WIDTH, HEIGHT, WIDTH, HEIGHT);

        mGLCubeBuffer = ByteBuffer.allocateDirect(OpenGlUtils.CUBE.length * 4).order(ByteOrder.nativeOrder()).asFloatBuffer();
        mGLCubeBuffer.put(cubeAndTextureBuffer.first);
        mGLTextureBuffer = ByteBuffer.allocateDirect(OpenGlUtils.TEXTURE.length * 4).order(ByteOrder.nativeOrder()).asFloatBuffer();
        mGLTextureBuffer.put(cubeAndTextureBuffer.second);
        this.pid = pid;
        this.controlBlock = controlBlock;
    }


    @Override
    public void onFrameAvailable(SurfaceTexture surfaceTexture) {
        //surfacetexture渲染完成一个数据帧
        mFrameUpdated = true;
        mRenderHandler.sendEmptyMessage(WHAT_UPDATE);
    }

    public void startInternal(final VideoFrameReadListener videoFrameReadListener, String name) {
        this.name = name;
        //视频上传数据监听器
        mVideoFrameReadListener = videoFrameReadListener;
        mRenderHandlerThread = new HandlerThread("RenderHandlerThread");
        mRenderHandlerThread.start();
        mRenderHandler = new RenderHandler(mRenderHandlerThread.getLooper(), this);
        //开始屏幕捕捉
        mRenderHandler.sendEmptyMessage(WHAT_START1);
    }

    public void stop() {
        if (mRenderHandlerThread != null) {
            mRenderHandlerThread.quit();
        }
        if (mGpuImageFilterGroup != null) {
            mGpuImageFilterGroup.destroy();
            mGpuImageFilterGroup = null;
        }

        if (mFrameBuffer != null) {
            mFrameBuffer.uninitialize();
            mFrameBuffer = null;
        }

        if (mSurfaceTextureId != NO_TEXTURE) {
            OpenGlUtils.deleteTexture(mSurfaceTextureId);
            mSurfaceTextureId = NO_TEXTURE;
        }


        if (mSurfaceTexture != null) {
            mSurfaceTexture.setOnFrameAvailableListener(null);
            mSurfaceTexture.release();
            mSurfaceTexture = null;
        }
        if (mRenderHandler != null && mRenderHandler.hasMessages(WHAT_START1))
            mRenderHandler.removeMessages(WHAT_START1);

        if (mEglCore != null) {
            mEglCore.unmakeCurrent();
            mEglCore.destroy();
            mEglCore = null;
        }
    }


    private void startInternal1() {
        //开始推屏对象初始化准备
        mEglCore = new EglCore(WIDTH, HEIGHT);
        mEglCore.makeCurrent();

        mSurfaceTextureId = OpenGlUtils.generateTextureOES();
        mSurfaceTexture = new SurfaceTexture(mSurfaceTextureId);
        mFrameBuffer = new FrameBuffer(1920, 2800);
        mFrameBuffer.initialize();

        mGpuImageFilterGroup = new GPUImageFilterGroup();
        mOesInputFilter = new OesInputFilter();
        mGpuImageFilterGroup.addFilter(mOesInputFilter);
        mGpuImageFilterGroup.addFilter(new GPUImageFilter(true));
        mGpuImageFilterGroup.init();
        mGpuImageFilterGroup.onOutputSizeChanged(WIDTH, HEIGHT);

        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                try {
                    mSurfaceTextureId = OpenGlUtils.generateTextureOES();
                    mSurfaceTexture = new SurfaceTexture(mSurfaceTextureId);
                    mSurfaceTexture.setOnFrameAvailableListener(CustomCameraCapture1.this);
                    if (pid.equals(name)) { //打开usb摄像头
                        Log.i(TAG, "onSurfaceTextureAvailable openRGBCameraUSB");
                        UVCCameraUtil.INSTANCE.openRGBCameraUSB(mSurfaceTexture, controlBlock, new IFrameCallback() {
                            @Override
                            public void onFrame(ByteBuffer frame) {
                                Log.i(TAG, "onSurfaceTextureAvailable openRGBCameraUSB success");
                            }

                            @Override
                            public void onFail() {
                                Log.i(TAG, "onSurfaceTextureAvailable openRGBCameraUSB fail");
                            }
                        });
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR1)
    private void updateTexture() {
        synchronized (this) {
            if (mFrameUpdated) {
                mFrameUpdated = false;
            }
            try {
                if (mSurfaceTexture != null) {

                    mSurfaceTexture.updateTexImage();
                    mSurfaceTexture.getTransformMatrix(mTextureTransform);
                    mOesInputFilter.setTexutreTransform(mTextureTransform);
                    mGpuImageFilterGroup.draw(mSurfaceTextureId, mFrameBuffer.getFrameBufferId(), mGLCubeBuffer, mGLTextureBuffer);

                    GLES20.glFinish();

                    if (mVideoFrameReadListener != null) {
                        TextureFrame textureFrame = new TextureFrame();
                        textureFrame.eglContext = (EGLContext) mEglCore.getEglContext();
                        textureFrame.textureId = mFrameBuffer.getTextureId();
//                        textureFrame.width = 1600;
//                        textureFrame.height = 1200;
                        textureFrame.width = WIDTH;
                        textureFrame.height = HEIGHT;
//                        发送数据
                        mVideoFrameReadListener.onFrameAvailable(textureFrame.eglContext, textureFrame.textureId, textureFrame.width, textureFrame.height);
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "onFrameAvailable: " + e.getMessage(), e);
            }
        }
    }

    private static class RenderHandler extends Handler {

        private final WeakReference<CustomCameraCapture1> readerWeakReference;

        public RenderHandler(@NonNull Looper looper, CustomCameraCapture1 cameraVideoFrameReader) {
            super(looper);
            readerWeakReference = new WeakReference<>(cameraVideoFrameReader);
        }

        @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR1)
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            CustomCameraCapture1 cameraVideoFrameReader = readerWeakReference.get();
            if (cameraVideoFrameReader != null) {
                if (WHAT_START1 == msg.what) {
                    cameraVideoFrameReader.startInternal1();
                } else if (WHAT_UPDATE == msg.what) {
                    cameraVideoFrameReader.updateTexture();
                }
            }
        }
    }
}
