package com.gc.notarizationpc.utils;

/**
 * @ProjectName:
 * @Package: com.gc.devicex.utils
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2023/4/17
 */

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;


public class Log2FileUtils {
    private static Context mContext;
    /**
     * 用于保存日志的文件
     */
    private static File cFile;
    private static File eFile;
    /**
     * 日志类型
     */
    private static final int C_LOG_FILE = 0;
    private static final int E_LOG_FILE = 1;
    private static final int G_LOG_FILE = 2;
    private static final int T_LOG_FILE = 3;
    private static final int H_LOG_FILE = 4;
    /**
     * 用于区分每天的日志
     */
    private static Date todayDate;
    /**
     * 日志中的时间显示格式
     */
    private static SimpleDateFormat logSDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
    /**
     * 日志文件夹的显示格式
     */
    public static SimpleDateFormat LOG_FOLDER_SDF = new SimpleDateFormat("yyyy-MM-dd", Locale.CHINA);

    private static final String TAG = "Log2FileUtils";

    private static boolean isNeedWrite = false;//是否要写日志进入文件

    public static void setNeedWrite(boolean needWrite) {
        isNeedWrite = needWrite;
    }

    /**
     * 初始化日志库
     *
     * @param context
     */
    public static void init(Context context) {

        //获取文件路径
        mContext = context;
        initLogFile();
        deleteUselessDatas();
    }

    private static void initLogFile() {

        //重置时间 至指定年月日
        Calendar now = Calendar.getInstance();
        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH) + 1;
        int day = now.get(Calendar.DAY_OF_MONTH);

        todayDate = new Date(year, month, day);

        //创建文件
        cFile = getLogFile(C_LOG_FILE);
        eFile = getLogFile(E_LOG_FILE);
//        gFile = getLogFile(G_LOG_FILE);
//        tFile = getLogFile(T_LOG_FILE);
//        hFile = getLogFile(H_LOG_FILE);

    }

    /**
     * 删除2天前的无用数据
     */
    private static void deleteUselessDatas() {
        try {
            File file;
            if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
                file = new File(mContext.getExternalFilesDir("Log").getPath() + "/");
            } else {
                file = new File(mContext.getFilesDir().getPath() + "/Log/");
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat parseDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();
            //只保留一周的数据(删除一周前所有数据)
            Date date = new Date();
            calendar.setTime(date);//当前日期
            calendar.add(Calendar.DAY_OF_MONTH, -1);  //设置为前一天
            String lastweekTime = simpleDateFormat.format(calendar.getTime());
            //遍历文件夹
            if (file.isDirectory()) {
                File[] files = file.listFiles();
                for (File tempFile :
                        files) {
                    String fileName = tempFile.getName();
                    if (fileName.matches("\\d{4}-\\d{2}-\\d{2}")) {
                        Date fileNameByTime = parseDateFormat.parse(fileName);
                        if (null != fileNameByTime) {
                            if (Integer.parseInt(simpleDateFormat.format(fileNameByTime)) < Integer.parseInt(lastweekTime)) {
                                tempFile.delete();
                            }
                        }
                    }

                }
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

    public static void c(Object msg) {

        write(msg, C_LOG_FILE);
    }

    public static void e(Object tag, Object msg) {

        write(tag + "---" + msg, E_LOG_FILE);
    }

    public static void e(Object msg) {
        write(msg, E_LOG_FILE);
    }

    public static void g(Object msg) {

        write(msg, G_LOG_FILE);

    }

    public static void g(Object tag, Object msg) {

        write(tag + "---" + msg, G_LOG_FILE);
    }

    public static void t(Object msg) {

        write(msg, T_LOG_FILE);
    }

    public static void h(Object msg) {

        write(msg, H_LOG_FILE);
    }

    public static void t(Object tag, Object msg) {
        write(tag + "---" + msg, T_LOG_FILE);
    }

    /**
     * 写入日志文件的数据
     *
     * @param str     需要写入的数据
     * @param logType 日志类型
     */
    public static void write(final Object str, final int logType) {
        if (isNeedWrite) {
            Calendar now = Calendar.getInstance();
            int year = now.get(Calendar.YEAR);
            int month = now.get(Calendar.MONTH) + 1;
            int day = now.get(Calendar.DAY_OF_MONTH);
            Date date = new Date(year, month, day);

//        if (date.after(todayDate)) {

            initLogFile();
//        }

            final String logStr = getFunctionInfo() + " - " + str + "";

            //封装线程池 防止大数据写入造成卡顿
            ThreadManager.getThreadPool().execute(new Runnable() {
                @Override
                public void run() {
                    //同步，防止写错
                    synchronized (Log2FileUtils.class) {

                        File logFile;

                        switch (logType) {

                            case C_LOG_FILE:

                                logFile = cFile;
                                break;
                            case E_LOG_FILE:

                                logFile = eFile;
                                break;
                            default:

                                logFile = cFile;
                                break;
                        }

                        BufferedWriter bw = null;
                        try {
                            bw = new BufferedWriter(new FileWriter(logFile, true));
                            bw.write(logStr);
                            bw.write("\r\n");

                        } catch (Exception e) {

                            Log.e(TAG, "日志写入文件时出错" + e.toString());
                        } finally {

                            if (bw != null) {

                                try {
                                    bw.flush();
                                    bw.close();
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                }
            });
        }
    }

    public static void writeException(String tag, Exception e) {
        StackTraceElement[] stackTrace = e.getStackTrace();
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(e.getMessage());
        for (int i = 0; i < stackTrace.length; i++) {
            stringBuffer.append("file:" + stackTrace[i].getFileName() + " class:"
                    + stackTrace[i].getClassName() + " method:"
                    + stackTrace[i].getMethodName() + " line:"
                    + stackTrace[i].getLineNumber() + "\n");
        }
        Log2FileUtils.e(tag + stringBuffer);
    }

    /**
     * 获取APP日志文件
     *
     * @return APP日志文件
     */
    private static File getLogFile(int logType) {

        File file;
        // 判断是否有SD卡或者外部存储器
        String logFolderFormat = LOG_FOLDER_SDF.format(new Date());
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            // 有SD卡则使用SD - PS:没SD卡但是有外部存储器，会使用外部存储器
            // SD\Android\data\包名\files\Log\date\log.txt
            file = new File(mContext.getExternalFilesDir("Log").getPath() + "/" + logFolderFormat + "/");
//            File rootFile = new File(Constant.projDir);
//            if (rootFile.exists()) {
//                Log.e(TAG, "rootFile 存在");
//            } else {
//                Log.e(TAG, "rootFile 不存在");
//            }
//            file = new File(Constant.projDir + "/Log/" + logFolderFormat + "/");
        } else {
            // 没有SD卡或者外部存储器，使用内部存储器
            // \data\data\包名\files\Log\date\log.txt
            file = new File(mContext.getFilesDir().getPath() + "/Log/" + logFolderFormat + "/");
        }
        // 若目录不存在则创建目录
        if (!file.exists()) {
            file.mkdirs();
        }
        Log.e(TAG, "file.path ==" + file.getPath() + "\n" + "是否是目录" + file.isDirectory());
        File logFile;

        switch (logType) {
            case C_LOG_FILE:
                logFile = new File(file.getPath() + "/c_log.txt");
                break;
            case E_LOG_FILE:
                logFile = new File(file.getPath() + "/e_log.txt");
                break;
            case G_LOG_FILE:
                logFile = new File(file.getPath() + "/g_log.txt");
                break;
            case T_LOG_FILE:
                logFile = new File(file.getPath() + "/t_log.txt");
                break;
            case H_LOG_FILE:
                logFile = new File(file.getPath() + "/h_log.txt");
                break;
            default:
                logFile = new File(file.getPath() + "/c_log.txt");
                break;
        }

        if (!logFile.exists()) {
            try {
                logFile.createNewFile();
            } catch (Exception e) {
                Log.e(TAG, "创建日志写入文件失败" + e.toString());
            }
        }
        return logFile;
    }

    /**
     * 获取当前函数的信息,格式化日志信息
     *
     * @return 当前函数的信息
     */
    private static String getFunctionInfo() {
        StackTraceElement[] sts = Thread.currentThread().getStackTrace();
        if (sts == null) {
            return null;
        }

        for (StackTraceElement st : sts) {

            if (st.isNativeMethod()) {
                continue;
            }
            if (st.getClassName().equals(Thread.class.getName())) {
                continue;
            }
            if (st.getClassName().equals(Log2FileUtils.class.getName())) {
                continue;
            }

            return "[" + logSDF.format(new Date()) + " " + st.getClassName() + " " + st
                    .getMethodName() + " Line:" + st.getLineNumber() + "]";
        }
        return "[获取失败]";
    }

    public static void saveErrorMessage(Exception exception) {
        StackTraceElement[] stackTrace = exception.getStackTrace();
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(exception.toString());
        for (int i = 0; i < stackTrace.length; i++) {
            stringBuffer.append("file:" + stackTrace[i].getFileName() + " class:"
                    + stackTrace[i].getClassName() + " method:"
                    + stackTrace[i].getMethodName() + " line:"
                    + stackTrace[i].getLineNumber() + "\n");
        }
        Log2FileUtils.e("7-555" + stringBuffer);
    }

    public static void saveErrorMessage(Throwable exception) {
        StackTraceElement[] stackTrace = exception.getStackTrace();
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(exception.toString());
        for (int i = 0; i < stackTrace.length; i++) {
            stringBuffer.append("file:" + stackTrace[i].getFileName() + " class:"
                    + stackTrace[i].getClassName() + " method:"
                    + stackTrace[i].getMethodName() + " line:"
                    + stackTrace[i].getLineNumber() + "\n");
        }
        Log2FileUtils.e("7-555" + stringBuffer);
    }
}

