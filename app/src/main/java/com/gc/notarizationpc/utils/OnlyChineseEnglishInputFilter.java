package com.gc.notarizationpc.utils;

import android.text.InputFilter;
import android.text.Spanned;

import java.util.regex.Pattern;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.utils
 * @Description: 只允许输入中英文
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2023/11/15
 */
public class OnlyChineseEnglishInputFilter implements InputFilter {
    private static final String PATTERN = "^[a-zA-Z\\u4E00-\\u9FA5\\s]+$";
    private static final Pattern pattern = Pattern.compile(PATTERN);

    @Override
    public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
        if (!pattern.matcher(source).matches()) {
            return "";
        }
        return null;
    }
}
