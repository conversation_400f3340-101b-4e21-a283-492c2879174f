package com.gc.notarizationpc.utils;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.media.AudioManager;
import android.os.Handler;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.view.inputmethod.InputMethodSubtype;
import android.widget.TextView;

import com.amap.api.maps.CoordinateConverter;
import com.amap.api.maps.model.LatLng;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtil {

    /**
     * 正则表达式:验证身份证号
     */
    public static final String REGEX_IDCARD = "(\\d{14}[0-9a-zA-Z])|(\\d{17}[0-9a-zA-Z])";

    public static long time_o = 0L;
    public static long time_t = 0L;

    //百度坐标转高德（传入经度、纬度）
    public static String bd_decrypt(String bd_lng, String bd_lat) {//有偏差
//        if (!TextUtils.isEmpty(bd_lng) && !TextUtils.isEmpty(bd_lat)) {
//            try {
//                float x = (float) (Float.parseFloat(bd_lng) - 0.0065);
//                float y = (float) (Float.parseFloat(bd_lat) - 0.006);
//                float z = (float) (Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * Math.PI * 3000.0 / 180.0));
//                float theta = (float) (Math.atan2(y, x) - 0.000003 * Math.cos(x * Math.PI * 3000.0 / 180.0));
//                float gg_lng = (float) (z * Math.cos(theta));
//                float gg_lat = (float) (z * Math.sin(theta));
//                Log.d("zpzp", gg_lng + "," + gg_lat);
//                return gg_lng + "," + gg_lat;
//            } catch (Exception e) {
//                Log.e("zpzp", e.getMessage());
//            }
//        }
//        return "";
        return bd_lng + "," + bd_lat;
    }

    /**
     * 校验手机号
     *
     * @param mobile
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isIdCard(String mobile) {
        return Pattern.matches(REGEX_IDCARD, mobile);
    }

    //百度坐标转高德（传入经度、纬度）
    public static ArrayList<Double> bd_decrypt(Double bd_lng, Double bd_lat, Context context) {
        LatLng sourceLatLng = new LatLng(bd_lat, bd_lng);
        CoordinateConverter converter = new CoordinateConverter(context);
        converter.from(CoordinateConverter.CoordType.BAIDU);
        converter.coord(sourceLatLng);
        LatLng desLatLng = converter.convert();
        Log.d("高德坐标", desLatLng.latitude + "  " + desLatLng.longitude);
        ArrayList<Double> locatin = new ArrayList<>();
        locatin.add(desLatLng.longitude);
        locatin.add(desLatLng.latitude);
        return locatin;
    }

    public static String getFromAssets(Context context, String fileName) {
        String result = "";
        try {
            InputStream in = context.getResources().getAssets().open(fileName);
            int size = in.available();
            int len = -1;
            byte[] bytes = new byte[size];
            in.read(bytes);
            in.close();
            result = new String(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static String getcode(Context context, String city) {
        String text = getFromAssets(context, "code.txt");
        int pos = text.replace(" ", "").trim().indexOf(city);
        String content = text.replace(" ", "").trim().substring(pos - 9, pos - 3);
        Log.d("zpzp", "code == " + content);
        return subcode(content);
    }

    public static String subcode(String adcode) {

        if (!TextUtils.isEmpty(adcode) && adcode.length() > 4)
            adcode = adcode.substring(0, 4) + "00";
        else
            adcode = "320000";
        return adcode;
    }

    /**
     * @param view  控件
     * @param color 颜色
     * @param text  文本內容
     */
    public static void setTextColor(TextView view, String color, String text) {
        String tt = "请输入\"" + text + "\"";
        SpannableString spannableString = new SpannableString(tt);

        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor(color)), 4, 5, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);

        view.setText(spannableString);
    }

    public static void disabledView(final View v) {
        v.setClickable(false);// 延迟1秒，恢复点击事件
        new Handler().postDelayed(new Runnable() {

            @Override
            public void run() {
                v.setClickable(true);
            }
        }, 2000);
    }

    /**
     * 设置系统音量
     *
     * @param context
     * @param lv      音量值
     * <AUTHOR>
     */
    public static void setSystemVoice(Context context, int lv) {
        AudioManager am = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        Log.d("zpzp", "音量最大值 ==" + am.getStreamMaxVolume(AudioManager.STREAM_MUSIC));
        am.setStreamVolume(AudioManager.STREAM_MUSIC, lv, AudioManager.FLAG_PLAY_SOUND);
    }

    /**
     * 设置系统音量
     *
     * @param context
     * <AUTHOR>
     */
    public static void setSystemVoice(Context context) {
        AudioManager am = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        Log.d("zpzp", "音量最大值 ==" + am.getStreamMaxVolume(AudioManager.STREAM_MUSIC));
        // TODO: 2022/10/20 测试暂时注释
        am.setStreamVolume(AudioManager.STREAM_MUSIC, 1 + am.getStreamMaxVolume(AudioManager.STREAM_MUSIC) / 2, AudioManager.FLAG_PLAY_SOUND);
//        am.setStreamVolume(AudioManager.STREAM_MUSIC, 3, AudioManager.FLAG_PLAY_SOUND);
    }

    /**
     * 获取当前系统音量
     *
     * @param context
     * <AUTHOR>
     */
    public static int getSystemVoice(Context context) {
        AudioManager am = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        int current = am.getStreamVolume(AudioManager.STREAM_MUSIC);
        Log.d("zpzp", "当前音量 ==" + current);
        return current;
    }

    public static void restart(Context context) {
        Intent intent = context.getPackageManager()
                .getLaunchIntentForPackage(context.getPackageName());
        PendingIntent restartIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_ONE_SHOT);
        AlarmManager mgr = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 1000, restartIntent); // 1秒钟后重启应用
        android.os.Process.killProcess(android.os.Process.myPid());
    }

    public static String getTypeWriting(Context context) {
        InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        InputMethodSubtype ims = imm.getCurrentInputMethodSubtype();
        String localeString = ims.getLocale();
        Locale locale = new Locale(localeString);//zh_cn   en_us
        String currentLanguage = locale.getDisplayLanguage();
        Log.d("zpzp", currentLanguage);
        return currentLanguage;
    }

    public static boolean compareListWithStr(ArrayList<String> lists,  String str1) {
        for (String tmp : lists) {
            if (!TextUtils.isEmpty(tmp) && !TextUtils.isEmpty(str1) && compareTwoStr(tmp, str1)) {
                return false;
            }
        }
        return true;
    }


    public static boolean  compareTwoStr(String str1, String str2) {
        Pattern pattern = Pattern.compile("[\\u4e00-\\u9fa5]+");
        Matcher matcher1 = pattern.matcher(str1);
        Matcher matcher2 = pattern.matcher(str2);
        String text1 = "";
        String text2 = "";
        while (matcher1.find()) {
            text1 += matcher1.group();
        }
        while (matcher2.find()) {
            text2 += matcher2.group();
        }
        if (text1.equals(text2)) {
            // 汉字相同
            return true;
        } else {
            // 汉字不同
            return false;
        }
    }

    /**
     * dp转px
     *
     * @param dpValue dp值
     * @return px值
     */
    public static int dp2px(Context context,final float dpValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }

}
