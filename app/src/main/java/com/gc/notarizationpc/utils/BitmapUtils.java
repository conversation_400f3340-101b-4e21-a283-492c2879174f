package com.gc.notarizationpc.utils;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.Log;

import java.util.ArrayList;

public class BitmapUtils {
    private final static String TAG = "BitmapUtils";

    /**
     * 将bitmap集合上下拼接,纵向(多个)
     *#4088F0
     * @param bitmaps
     * @return
     */
    public static Bitmap combineBitmapsV(ArrayList<Bitmap> bitmaps) {
        int width = bitmaps.get(0).getWidth();
        int height = bitmaps.get(0).getHeight();
        for (int i = 1; i < bitmaps.size(); i++) {
            if (width < bitmaps.get(i).getWidth()) {
                width = bitmaps.get(i).getWidth();
            }
            height = height + bitmaps.get(i).getHeight();
        }
        Bitmap result = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(result);
        canvas.drawColor(Color.TRANSPARENT);//无背景色
        Paint paint = new Paint();
        paint.setDither(true);
        canvas.drawBitmap(bitmaps.get(0), 0, 0, paint);
        int h = 0;
        for (int j = 1; j < bitmaps.size(); j++) {
            h = bitmaps.get(j).getHeight() + h;
            canvas.drawBitmap(bitmaps.get(j), 0, h, paint);
        }
        return result;
    }

    /**
     * 将bitmap集合上下拼接,横向(多个)
     *
     * #4088F0
     * @param bitmaps
     * @return
     */
    public static Bitmap combineBitmapsH(ArrayList<Bitmap> bitmaps) {
        int width = bitmaps.get(0).getWidth();
        int height = bitmaps.get(0).getHeight();
        Log.d(TAG, " drawMultiH w_" + 0 + " = " + bitmaps.get(0).getWidth() + "," + bitmaps.get(0).getHeight());
        Log.d(TAG, " drawMultiH totalW = " + width);
        for (int i = 1; i < bitmaps.size(); i++) {
            if (height < bitmaps.get(i).getHeight()) {
                height = bitmaps.get(i).getHeight();
            }
            width = width + bitmaps.get(i).getWidth();
            Log.d(TAG, " drawMultiH w_" + i + " = " + bitmaps.get(i).getWidth());
            Log.d(TAG, " drawMultiH totalW = " + width);
        }
        Log.d(TAG, " drawMultiH totalW=---------------------------------------------------------");
        Bitmap result = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(result);
        canvas.drawColor(Color.TRANSPARENT);//无背景色
        Paint paint = new Paint();
        paint.setDither(true);
        canvas.drawBitmap(bitmaps.get(0), 0, 0, paint);
        int w = 0;
        for (int j = 1; j < bitmaps.size(); j++) {
            w = bitmaps.get(j).getWidth() + w;
            canvas.drawBitmap(bitmaps.get(j), w, 0, paint);
        }
        return result;
    }
}
