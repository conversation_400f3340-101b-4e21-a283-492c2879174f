package com.gc.notarizationpc.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.example.framwork.baseapp.AppManager;
import com.example.framwork.utils.DensityUtil;
import com.example.framwork.utils.StatusBarUtil;
import com.example.framwork.utils.ToastUtils;
import com.gc.notarizationpc.R;
import com.gc.notarizationpc.model.RegionBean;
import com.gc.notarizationpc.select.CityList;
import com.gc.notarizationpc.select.OnWheelChangedListener;
import com.gc.notarizationpc.select.WheelView;
import com.gc.notarizationpc.select.adapters.ArrayWheelAdapter;
import com.gc.notarizationpc.ui.MainActivity;
import com.gc.notarizationpc.ui.MobileActivity;
import com.gc.notarizationpc.ui.NotarizationConfirmActivity;
import com.gc.notarizationpc.ui.NotarizationSelfActivity;
import com.gc.notarizationpc.ui.RemoteDetailsActivity;
import com.gc.notarizationpc.ui.SelectNotary;
import com.gc.notarizationpc.ui.presenter.MainPresenter;
import com.zkteco.android.biometric.core.utils.ToolUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import es.dmoral.toasty.Toasty;

public class PopwindowUtil implements OnWheelChangedListener {
    private TextView btnCancel;
    private TextView btnConfirm;
    private WheelView mProvince; // 省滚轮
    private WheelView mCity;// 市滚轮
    // 存放省->市(全部) 的键值关系的Map
    private Map<String, String[]> mCitisDatasMap = new HashMap<String, String[]>();
    private String[] mProvinceDatas; // 存放省字符串数组
    private String mCurrentCityName; // 当前选择的市
    private String mCurrentProviceName;// 当前选择的省
    private PopupWindow popup;
    private View menuCity;
    private Context context;
    private Activity mactivity;
    private RegionBean regionBean = null;

    private static Dialog dialog;
    private static ImageView imageView;

    /**
     * 弹出 图形验证码
     */
    public static void showPicCode(Context mContext, Bitmap codePic, ResultListener resultListener) {
        if (dialog != null && dialog.isShowing()) {
            imageView.setImageBitmap(codePic);
        } else {
            AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
            LayoutInflater inflater = LayoutInflater.from(mContext);
            View view = inflater.inflate(R.layout.dialog_pic_code, null);
            ImageView ivClose = view.findViewById(R.id.iv_close);
            imageView = view.findViewById(R.id.iv_img);
            EditText edtPic = view.findViewById(R.id.edt_pic_code);
            TextView btnSign = view.findViewById(R.id.btn_sign);
            dialog = builder.create();
            imageView.setImageBitmap(codePic);

            dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
            dialog.setOnShowListener(new DialogInterface.OnShowListener() {
                @Override
                public void onShow(DialogInterface dialog) {
                    InputMethodManager imm = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
                    imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY);
                }
            });
            dialog.show();

            dialog.setContentView(view);
            Window window = dialog.getWindow();
            //设置透明才能将 圆角 显示
            window.getDecorView().setBackgroundColor(Color.TRANSPARENT);
            window.getDecorView().setPadding(0, 0, 0, 0);
            window.setLayout(
                    DensityUtil.getInstance().dip2px(mContext, 550F),
                    DensityUtil.getInstance().dip2px(mContext, 350F));
            window.setGravity(Gravity.CENTER);
            dialog.setContentView(view);
            dialog.setCanceledOnTouchOutside(false);

            ivClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    dialog.dismiss();
                }
            });
            btnSign.setOnClickListener(v -> {
                if (TextUtils.isEmpty(edtPic.getText().toString())) {
                    ToastUtils.getInstance(mContext).toastInfo("请先输入图形验证码");
                } else {
                    dialog.dismiss();
                    if (resultListener != null) {
                        resultListener.result(edtPic.getText().toString());
                    }
                }

            });

            view.findViewById(R.id.iv_refresh).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (resultListener != null) {
                        resultListener.resultTwo();
                    }
                }
            });
        }
    }

    public interface ResultListener<T> {
        void result(T value);

        void resultTwo();
    }



    public void showpop(Context context, View v, Activity mactivity) {
        this.context = context;
        if (mactivity != null) {
            this.mactivity = mactivity;
        }

        initDatas();// 初始化省、市数据
        /**
         * popmenu
         */
        menuCity = LayoutInflater.from(context).inflate(R.layout.city, null);
        popup = new PopupWindow(menuCity, 320,
                400, true);
//        popup.setFocusable(true);
        popup.setOutsideTouchable(false);
//        menuCity.requestFocus();
        // popup.setAnimationStyle(R.style.popwin_anim_style);
        // 实例化一个ColorDrawable颜色为半透明
        ColorDrawable dw = new ColorDrawable(0xb0000000);
        // 设置SelectPicPopupWindow弹出窗体的背景
        popup.setBackgroundDrawable(dw);
        // menu菜单获得焦点 如果没有获得焦点menu菜单中的控件事件无法响应
        popup.setFocusable(true);
        popup.update();
        //界面弹出
        popup.showAtLocation(v, Gravity.CENTER,
                0, 0);
        initCityView();//初始化布局
        menuCity.setFocusable(true);
        menuCity.requestFocus();
        menuCity.bringToFront();
        popup.setOnDismissListener(() -> {
            backgroundAlpha(1f);
        });
        backgroundAlpha(0.6f);
    }


    /**
     * 可以从网络、本地等获取数据。 建议是本地获取，因为选择时城市变化快，他的下一级是根据上一级来变换的，数据不好获取。
     * 当然除非服务器可以一次性获取所有数据，或者使用其他方式解决。
     */
    private void initDatas() {
        try {

            if (mProvinceDatas == null) {
                CityList cityList = new CityList();
                mProvinceDatas = cityList.getProviceData();
                JSONObject city = cityList.getCityDate();
                for (int i = 0; i < mProvinceDatas.length; i++) {
                    String pName = mProvinceDatas[i];// 每个省的json对象
                    String[] mCitiesDatas = city.getString(pName).split("\\+");
                    mCitisDatasMap.put(pName, mCitiesDatas);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    protected void initCityView() {
        mProvince = (WheelView) menuCity.findViewById(R.id.id_province);
        mCity = (WheelView) menuCity.findViewById(R.id.id_city);
        btnCancel = (TextView) menuCity.findViewById(R.id.cancel);
        btnConfirm = (TextView) menuCity.findViewById(R.id.confirm);

        mProvince.setViewAdapter(new ArrayWheelAdapter<String>(
                ToolUtils.getApplicationContext(), mProvinceDatas));
        //设置默认的选中的
        mProvince.setCurrentItem(2);
        // 添加change事件
        mProvince.addChangingListener(this);
        // 添加change事件
        mCity.addChangingListener(this);

        //显示的条目
        mProvince.setVisibleItems(7);
        mCity.setVisibleItems(7);
        //一开始是需要根据当前选择的省来初始化市的
        updateCities();
        btnCancel.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {//取消按钮
                if (popup != null) {
                    popup.dismiss();
                    if (context instanceof MainActivity) {
                        Log.d("zpzp", "cancel pop");
                        ((MainActivity) context).show_dialog();
                    }
                }

            }
        });
        btnConfirm.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {//确定按钮
                String selectNameDetail = mCurrentProviceName + mCurrentCityName;
                if ("北京市上海市重庆市天津市".contains(mCurrentProviceName)) {
                    selectNameDetail = mCurrentProviceName;
                }
                if (context instanceof MainActivity) {
                    Log.d("zpzp", "current activity is main");
                    ((MainActivity) context).change_location_text(mCurrentCityName, mCurrentProviceName);
                }
                if (context instanceof MobileActivity) {
                    Log.d("zpzp", "current activity is Mobile");
                    ((MobileActivity) context).change_location_text(mCurrentCityName);
                }
                if (context instanceof RemoteDetailsActivity) {
                    Log.d("zpzp", "current activity is RemoteDetails");
                    ((RemoteDetailsActivity) context).change_location_text(mCurrentCityName);
                }
                if (context instanceof NotarizationConfirmActivity) {
                    Log.d("zpzp", "current activity is NotarizationConfirm");
                    ((NotarizationConfirmActivity) context).change_location_text(mCurrentCityName);
                }
                if (context instanceof NotarizationSelfActivity) {
                    Log.d("zpzp", "current activity is NotarizationConfirm");
                    ((NotarizationSelfActivity) context).change_location(mCurrentCityName);
                }
                if (context instanceof SelectNotary) {
                    Log.d("zpzp", "current activity is NotarizationConfirm");
                    ((SelectNotary) context).change_location(mCurrentCityName);
                }
                if (popup != null) {
                    popup.dismiss();
                }

            }
        });

    }

    private void updateCities() {
        int pCurrent = mProvince.getCurrentItem(); //获取当前省选择的位置
        mCurrentProviceName = mProvinceDatas[pCurrent];//根据位置获取省的名字
        String[] cities = mCitisDatasMap.get(mCurrentProviceName);//根据省来获取当前省的市
        if (mCitisDatasMap != null && cities != null) {
            mCurrentCityName = mCitisDatasMap.get(mCurrentProviceName)[0];
        }
        if (cities == null) {
            cities = new String[]{""};
        }
        mCity.setViewAdapter(new ArrayWheelAdapter<String>(context, cities));
        mCity.setCurrentItem(0);

    }

    @Override
    public void onChanged(WheelView wheel, int oldValue, int newValue) {
        if (wheel == mProvince) {
            updateCities();
        } else if (wheel == mCity) {
            mCurrentCityName = mCitisDatasMap.get(mCurrentProviceName)[newValue];

        }
    }

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha 外部透明度
     */
    public void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = mactivity.getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        mactivity.getWindow().setAttributes(lp);
    }

    /**
     * @param context
     * @param v
     */
    public void showpopNewNotaryListw(Context context, View v, Activity activity) {
        this.context = context;
        if (activity != null) {
            this.mactivity = activity;
        }

        initDatas(v);// 初始化省、市数据
//        /**
//         * popmenu
//         */
//        menuCity = LayoutInflater.from(context).inflate(R.layout.city, null);
//        popup = new PopupWindow(menuCity, 320,
//                400, true);
////        popup.setFocusable(true);
//        popup.setOutsideTouchable(false);
////        menuCity.requestFocus();
//        // popup.setAnimationStyle(R.style.popwin_anim_style);
//        // 实例化一个ColorDrawable颜色为半透明
//        ColorDrawable dw = new ColorDrawable(0xb0000000);
//        // 设置SelectPicPopupWindow弹出窗体的背景
//        popup.setBackgroundDrawable(dw);
//        // menu菜单获得焦点 如果没有获得焦点menu菜单中的控件事件无法响应
//        popup.setFocusable(true);
//        popup.update();
//        //界面弹出
//        popup.showAtLocation(v, Gravity.CENTER,
//                0, 0);
//        initCityView();//初始化布局
//        menuCity.setFocusable(true);
//        menuCity.requestFocus();
//        menuCity.bringToFront();
//        popup.setOnDismissListener(() -> {
//            backgroundAlpha(1f);
//        });
//        backgroundAlpha(0.6f);
    }


    /**
     * 可以从网络、本地等获取数据。 建议是本地获取，因为选择时城市变化快，他的下一级是根据上一级来变换的，数据不好获取。
     * 当然除非服务器可以一次性获取所有数据，或者使用其他方式解决。
     */
    private void initDatas(View v) {
        new MainPresenter(context).getCity(new MainPresenter.CityView() {
            @Override
            public void getCitySuccess(RegionBean bean) {
                regionBean = bean;
                if (mProvinceDatas == null) {
                    mProvinceDatas = new String[bean.getText().size()];
                    for (int i = 0; i < bean.getText().size(); i++) {
                        mProvinceDatas[i] = bean.getText().get(i).getText();
                        if (bean.getText().get(i).getChildren().size() == 0) {
                            mCitisDatasMap.put(mProvinceDatas[i], new String[]{mProvinceDatas[i]});
                            continue;
                        }
                        String[] mCitiesDatas = new String[bean.getText().get(i).getChildren().size()];
                        for (int j = 0; j < bean.getText().get(i).getChildren().size(); j++) {
                            mCitiesDatas[j] = bean.getText().get(i).getChildren().get(j).getText();
                        }
                        mCitisDatasMap.put(mProvinceDatas[i], mCitiesDatas);
                    }
                    show(v);//初始化布局

                }
            }

            @Override
            public void getCityFailed(String msg) {
                Toasty.error(AppManager.getAppManager().currentActivity(), "网络异常，请检查网络并重试。").show();
                if (popup != null && popup.isShowing())
                    popup.dismiss();
            }
        });

    }

    public void show(View v) {
        /**
         * popmenu
         */
        menuCity = LayoutInflater.from(context).inflate(R.layout.city, null);
        popup = new PopupWindow(menuCity, 320,
                400, true);
//        popup.setFocusable(true);
        popup.setOutsideTouchable(false);
//        menuCity.requestFocus();
        // popup.setAnimationStyle(R.style.popwin_anim_style);
        // 实例化一个ColorDrawable颜色为半透明
        ColorDrawable dw = new ColorDrawable(0xb0000000);
        // 设置SelectPicPopupWindow弹出窗体的背景
        popup.setBackgroundDrawable(dw);
        // menu菜单获得焦点 如果没有获得焦点menu菜单中的控件事件无法响应
        popup.setFocusable(true);
        popup.update();
        //界面弹出
        popup.showAtLocation(v, Gravity.CENTER,
                0, 0);
        initCityView();//初始化布局
        menuCity.setFocusable(true);
        menuCity.requestFocus();
        menuCity.bringToFront();
        popup.setOnDismissListener(() -> {
            backgroundAlpha(1f);
        });
        backgroundAlpha(0.6f);
    }
}
