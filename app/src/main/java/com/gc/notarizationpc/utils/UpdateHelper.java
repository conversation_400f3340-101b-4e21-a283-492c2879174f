package com.gc.notarizationpc.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import com.alibaba.fastjson.JSONArray;
import com.example.framwork.baseapp.AppManager;
import com.example.framwork.mvp.BasePresenter;
import com.example.framwork.noHttp.Bean.BaseResponseBean;
import com.example.framwork.noHttp.OnInterfaceRespListener;
import com.example.framwork.utils.DLog;
import com.example.framwork.utils.NetUtil;
import com.gc.notarizationpc.R;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.common.BaseRequestInfo;
import com.gc.notarizationpc.common.ResponseBean;
import com.gc.notarizationpc.model.UpdateSet;
import com.gc.notarizationpc.ui.MainActivity;
import com.umeng.analytics.MobclickAgent;

import java.io.File;
import java.io.IOException;

import es.dmoral.toasty.Toasty;

public class UpdateHelper extends BasePresenter {
    private Context mContext;


    private TextView tv_desc;
    private TextView tv_confirm;
    private TextView tv_cancel;
    private String TAG = "register";

    private ProgressDialog mProgressDialog;

    private UpdateSet us;
    private String code;

    public UpdateHelper(Context mContext) {
        super(mContext);
        this.mContext = mContext;

    }

    public void checkUpdate() {
//        showProgress(true,"检查更新");
//        DLog.d("更新检测：1");
        new Thread(new Runnable() {
            @Override
            public void run() {
//                if (!NetUtil.ping())
//                    return;
                AppManager.getAppManager().currentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.USER + "cgz/appedition/selectNewBy");
                        requestInfo.put("editionType", 19);//省公协19  石城版本23
                        postNoToast("检查更新", new OnInterfaceRespListener<BaseResponseBean>() {
                            @Override
                            public void requestSuccess(BaseResponseBean bean) {
                                if (bean == null) {
                                    return;
                                }
                                ResponseBean responseBean = (ResponseBean) bean;
                                if (responseBean.getData() != null) {
                                    try {
                                        us = responseBean.parseObject(UpdateSet.class);
                                        code = getVersionName();
                                        if (compareVersion(us.versionCode, code) > 0) {
                                            showDialogUpdate();
                                        }
                                    } catch (Exception ex) {
                                        Toasty.error(mContext, "自动更新：" + ex.getMessage());
                                    }

                                }
                            }

                            @Override
                            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                                super.requestFailed(errorCode, bean, exception, error);
                                Log.d(TAG, "checkUpdate erro " + errorCode + error);
                            }
                        });
                    }
                });
            }
        }).start();

    }


    public void checkUpdate(IUpgradeListener listener) {
//        showProgress(true,"检查更新");
//        DLog.d("更新检测：1");
        new Thread(new Runnable() {
            @Override
            public void run() {
//                if (!NetUtil.ping())
//                    return;
                AppManager.getAppManager().currentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.USER + "cgz/appedition/selectNewBy");
                        requestInfo.put("editionType", 19);//省公协19  石城版本23
                        postNoToast("检查更新", new OnInterfaceRespListener<BaseResponseBean>() {
                            @Override
                            public void requestSuccess(BaseResponseBean bean) {
                                if (bean == null) {
                                    return;
                                }
                                ResponseBean responseBean = (ResponseBean) bean;
                                if (responseBean.getData() != null) {
                                    try {
                                        us = responseBean.parseObject(UpdateSet.class);
                                        code = getVersionName();
                                        if (compareVersion(us.versionCode, code) > 0) {
                                            showDialogUpdate();
                                            listener.cancel();
                                        } else if (listener != null) {
                                            listener.checkUpgrade();
                                        }
                                    } catch (Exception ex) {
                                        listener.cancel();
                                        Toasty.error(mContext, "自动更新：" + ex.getMessage());
                                    }

                                } else {
                                    listener.cancel();
                                }
                            }

                            @Override
                            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                                super.requestFailed(errorCode, bean, exception, error);
                                Log.d(TAG, "checkUpdate erro " + errorCode + error);
                                listener.cancel();
                            }
                        });
                    }
                });
            }
        }).start();

    }

    private int compareVersion(String bd, String flw) {
        String[] bdbb = bd.split("\\.", -1);
        String[] flwbb = flw.split("\\.", -1);
        int num = Math.min(bdbb.length, flwbb.length);//取出较小的一位数
        for (int i = 0; i < num; i++) {
            int a1 = "".equals(bdbb[i]) ? 0 : Integer.parseInt(bdbb[i]);
            int a2 = "".equals(flwbb[i]) ? 0 : Integer.parseInt(flwbb[i]);
            if (a1 != a2) {
                return a1 - a2;
            }
        }
        return bdbb.length - flwbb.length;
    }

    AlertDialog alertDialog = null;

    public void showDialogUpdate() {
        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        builder.setCancelable(false); //开启强制更新，无法关闭
        builder.setTitle("版本升级").
                // 设置提示框的图标
//                        setIcon(R.mipmap.ic_launcher).
                // 设置要显示的信息
                        setMessage(us.desc).
                // 设置确定按钮
                        setPositiveButton("更新", (dialog, which) -> {
                    if (alertDialog != null && alertDialog.isShowing()) {
                        alertDialog.dismiss();
                        downloadApk();
                    }
                });
        // 生产对话框
        alertDialog = builder.create();
        // 显示对话框
        alertDialog.show();
        initProgress();
    }

    /**
     * 获取当前使用的软件包的版本号
     */
    public int getVersionCode() {
        try {
            //获取packagemanager的实例
            PackageManager packageManager = mContext.getPackageManager();
            //getPackageName()是你当前类的包名，0代表是获取版本信息
            PackageInfo packInfo = packageManager.getPackageInfo(mContext.getPackageName(), 0);
            Log.e("TAG", "版本号" + packInfo.versionCode);  //更新软件用的是版本号
            return packInfo.versionCode;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 1;
    }

    public String getVersionName() {
        try {
            //获取packagemanager的实例
            PackageManager packageManager = mContext.getPackageManager();
            //getPackageName()是你当前类的包名，0代表是获取版本信息
            PackageInfo packInfo = packageManager.getPackageInfo(mContext.getPackageName(), 0);
            Log.e("TAG", "版本号" + packInfo.versionCode);  //更新软件用的是版本号
            return packInfo.versionName;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "1.0.0";
    }


    private void initProgress() {
        mProgressDialog = new ProgressDialog(mContext);
        mProgressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        mProgressDialog.setProgressDrawable(mContext.getDrawable(R.drawable.common_progressdialog_progressbar_background));
        mProgressDialog.setCancelable(false);
    }

    private String getRootDirPath(Context context) {
        if (Environment.MEDIA_MOUNTED == Environment.getExternalStorageState()) {
            File file = ContextCompat.getExternalFilesDirs(context.getApplicationContext(), null)[0];
            return file.getAbsolutePath();
        } else {
            return context.getApplicationContext().getFilesDir().getAbsolutePath();
        }
    }

    private String isExistDir(String saveDir) throws IOException, IOException {
        File downloadFile = new File(saveDir);
        if (!downloadFile.mkdirs()) {
            downloadFile.createNewFile();
        }
        String savePath = downloadFile.getAbsolutePath();
        return savePath;
    }


    /**
     * 下载`
     */
    private void downloadApk() {
//        UpdateSet updateSet = us;
        if (TextUtils.isEmpty(us.filePath)) {
            Toasty.error(mContext, "未获取到更新下载地址");
            return;
        }
        String Path = mContext.getFilesDir() + "";
        try {
            Path = isExistDir(Path);
        } catch (Exception ex) {
            Toasty.error(mContext, "路径保存出错");
            return;
        }
        final String filePath = Path + "/" + System.currentTimeMillis() + ".apk";
        if (mProgressDialog != null) {
            mProgressDialog.show();

        }
        DLog.d("测试更新：filePath：", filePath);
        //开始下载：
        HttpManager.getInstance().download(us.filePath, filePath, new HttpManager.OnDownloadListener() {
            @Override
            public void onDownloadSuccess(String path) {
                mProgressDialog.dismiss();
                Log.i(TAG, "onDownloadSuccess:" + path);
                if (!TextUtils.isEmpty(path)) {
                    installApk(path);
                }
            }

            @Override
            public void onDownloading(int progress) {
                mProgressDialog.setProgress(progress);
                mProgressDialog.setSecondaryProgress(progress);
                Log.i(TAG, "onDownloading:" + progress);
                /*
                if(Utils.isRunOnUIThread()){
                    Log.i(TAG,"运行在主线程");
                }else{
                    Log.i(TAG,"不是运行在主线程");
                }

                 */
            }

            @Override
            public void onDownloadFailed(Exception e) {
                mProgressDialog.dismiss();
                Log.i(TAG, "onDownloadFailed:" + e.getMessage());
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        Looper.prepare();
                        Log.d(TAG, "Countdown start");
                        ((MainActivity) mContext).toastWarning("升级失败请检查网络");
                        Looper.loop();//增加部分
                    }
                }).start();

            }
        });
    }


    public interface OnUpdateAppListener {
        void OnUpdate(boolean isUpdate);
    }

    /**
     * 安装Apk
     */
    public void installApk(String filePath) {
        try {
            File apkFile = new File(filePath);
            Uri data = null;
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                data = FileProvider.getUriForFile(mContext, mContext.getPackageName() + ".fileprovider", apkFile);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            } else {
                data = Uri.fromFile(apkFile);
            }
            DLog.d("测试：" + filePath);
            intent.setDataAndType(data, "application/vnd.android.package-archive");
            ((Activity) mContext).startActivity(intent);
        } catch (Exception ex) {
//            Toasty.error(mContext, ex.getMessage());
            DLog.e("测试:错误：" + ex.getMessage());
        }
    }

}
