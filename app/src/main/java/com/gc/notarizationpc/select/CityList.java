package com.gc.notarizationpc.select;

import org.json.JSONException;
import org.json.JSONObject;


public class CityList {
	private String[] provice = new String[] { "北京市", "上海市", "广东省", "天津市",
			"重庆市", "浙江省", "福建省", "江苏省", "安徽省", "湖南省", "湖北省", "山东省", "四川省",
			"贵州省", "海南省", "河北省", "河南省", "黑龙江省", "吉林省", "江西省", "辽宁省", "青海省",
			"山西省", "陕西省", "云南省", "甘肃省", "广西自治区", "宁夏自治区", "西藏自治区", "新疆自治区",
			"内蒙古自治区" };
	private JSONObject citJson;

	public CityList() {
		try {
			citJson = setCity();
		} catch (JSONException e) {
			e.printStackTrace();;
		}
	}

	public String[] getProviceData() {
		return provice;
	}

	public JSONObject getCityDate() {
		return citJson;

	}

	private JSONObject setCity() throws JSONException {
		JSONObject jo = new JSONObject();
		jo.put("安徽省",
				"合肥市+安庆市+蚌埠市+巢湖市+池州市+滁州市+阜阳市+淮北市+淮南市+黄山市+界首市+六安市+马鞍山市+明光市+宁国市+宿州市+天长市+桐城市+铜陵市+芜湖市+宣城市+亳州市");
		jo.put("北京市", "北京市+");
		jo.put("福建省",
				"福州市+厦门市+泉州市+漳州市+龙岩市+长乐市+福安市+福鼎市+福清市+建阳市+建瓯市+晋江市+龙海市+南安市+南平市+宁德市+莆田市+三明市+邵武市+石狮市+武夷山市+永安市+漳平市");
		jo.put("甘肃省",
				"兰州市+白银市+定西市+敦煌市+合作市+嘉峪关市+金昌市+酒泉市+临夏市+陇南市+平凉市+庆阳市+天水市+武威市+玉门市+张掖市");
		jo.put("广东省",
				"广州市+潮州市+从化市+东莞市+恩平市+佛山市+高要市+高州市+河源市+鹤山市+化州市+惠州市+江门市+揭阳市+开平市+乐昌市+雷州市+连州市+廉江市+陆丰市+罗定市+茂名市+梅州市+南雄市+普宁市+清远市+汕头市+汕尾市+韶关市+深圳市+四会市+台山市+吴川市+信宜市+兴宁市+阳春市+阳江市+英德市+云浮市+增城市+湛江市+肇庆市+中山市+珠海市");
		jo.put("广西自治区",
				"桂林市+柳州市+南宁市+玉林市+百色市+北海市+北流市+崇左市+东兴市+防城港市+桂平市+贵港市+合山市+河池市+贺州市+来宾市+凭祥市+钦州市+梧州市+宜州市+岑溪市");
		jo.put("贵州省", "贵阳市+遵义市+铜仁市+安顺市+毕节市+赤水市+都匀市+福泉市+凯里市+六盘水市+清镇市+仁怀市+兴义市");
		jo.put("海南省", "海口市+三亚市+万宁市+琼海市+文昌市+五指山市+东方市+儋州市");
		jo.put("河北省",
				"安国市+霸州市+保定市+泊头市+沧州市+承德市+定州市+高碑店市+邯郸市+河间市+衡水市+黄骅市+冀州市+晋州市+廊坊市+鹿泉市+南宫市+迁安市+秦皇岛市+任丘市+三河市+沙河市+深州市+石家庄市+唐山市+武安市+辛集市+新乐市+邢台市+张家口市+遵化市+藁城市+涿州市");
		jo.put("河南省",
				"安阳市+长葛市+登封市+邓州市+巩义市+鹤壁市+辉县市+济源市+焦作市+开封市+林州市+灵宝市+洛阳市+孟州市+南阳市+平顶山市+沁阳市+汝州市+三门峡市+商丘市+卫辉市+舞钢市+项城市+新密市+新乡市+新郑市+信阳市+许昌市+义马市+永城市+禹州市+郑州市+周口市+驻马店市+偃师市+荥阳市+漯河市+濮阳市");
		jo.put("黑龙江省",
				"安达市+北安市+大庆市+大兴安岭地区+富锦市+哈尔滨市+海林市+海伦市+鹤岗市+黑河市+虎林市+鸡西市+佳木斯市+密山市+牡丹江市+穆棱市+宁安市+七台河市+齐齐哈尔市+尚志市+双城市+双鸭山市+绥芬河市+绥化市+铁力市+同江市+五常市+五大连池市+伊春市+肇东市+讷河市");
		jo.put("湖北省",
				"安陆市+赤壁市+大冶市+丹江口市+当阳市+鄂州市+恩施市+广水市+汉川市+洪湖市+黄冈市+黄石市+荆门市+荆州市+老河口市+利川市+麻城市+潜江市+十堰市+石首市+松滋市+随州市+天门市+武汉市+武穴市+仙桃市+咸宁市+襄樊市+孝感市+宜昌市+宜城市+宜都市+应城市+枣阳市+枝江市+钟祥市");
		jo.put("湖南省",
				"常德市+常宁市+长沙市+郴州市+衡阳市+洪江市+怀化市+吉首市+津市市+冷水江市+涟源市+临湘市+娄底市+韶山市+邵阳市+武冈市+湘潭市+湘乡市+益阳市+永州市+岳阳市+张家界市+株洲市+资兴市+沅江市+汨罗市+浏阳市+耒阳市+醴陵市");
		jo.put("吉林省",
				"白城市+白山市+长春市+大安市+德惠市+敦化市+公主岭市+和龙市+吉林市+集安市+九台市+辽源市+临江市+龙井市+梅河口市+磐石市+舒兰市+双辽市+四平市+松原市+通化市+图们市+延吉市+榆树市+洮南市+珲春市+桦甸市+蛟河市");
		jo.put("江苏省",
				"常熟市+常州市+大丰市+丹阳市+东台市+高邮市+海门市+淮安市+姜堰市+江都市+江阴市+金坛市+靖江市+句容市+昆山市+连云港市+南京市+南通市+启东市+如皋市+苏州市+宿迁市+泰兴市+泰州市+太仓市+通州市+无锡市+吴江市+新沂市+兴化市+徐州市+盐城市+扬中市+扬州市+仪征市+宜兴市+张家港市+镇江市+邳州市+溧阳市");
		jo.put("江西省",
				"德兴市+丰城市+抚州市+赣州市+高安市+贵溪市+吉安市+井冈山市+景德镇市+九江市+乐平市+南昌市+南康市+萍乡市+瑞昌市+瑞金市+上饶市+新余市+宜春市+鹰潭市+樟树市");
		jo.put("辽宁省",
				"鞍山市+北票市+北镇市+本溪市+朝阳市+大连市+大石桥市+丹东市+灯塔市+调兵山市+东港市+凤城市+抚顺市+阜新市+盖州市+海城市+葫芦岛市+锦州市+开原市+辽阳市+凌海市+凌源市+盘锦市+普兰店市+沈阳市+铁岭市+瓦房店市+新民市+兴城市+营口市+庄河市");
		jo.put("内蒙古自治区",
				"阿尔山市+巴彦淖尔市+包头市+赤峰市+额尔古纳市+鄂尔多斯市+二连浩特市+丰镇市+根河市+呼和浩特市+呼伦贝尔市+霍林郭勒市+满洲里市+通辽市+乌海市+乌兰察布市+乌兰浩特市+锡林浩特市+牙克石市+扎兰屯市");
		jo.put("宁夏自治区", "固原市+青铜峡市+石嘴山市+吴忠市+银川市+中卫市");
		jo.put("青海省",
				"果洛自治州+海北自治州+海东地区+海南自治州+海西自治州+黄南自治州+西宁市+玉树自治州");
		jo.put("山东省",
				"安丘市+滨州市+昌邑市+德州市+东营市+肥城市+高密市+海阳市+荷泽市+即墨市+济南市+济宁市+胶南市+胶州市+莱芜市+莱西市+莱阳市+莱州市+乐陵市+聊城市+临清市+临沂市+龙口市+蓬莱市+平度市+栖霞市+青岛市+青州市+曲阜市+日照市+荣成市+乳山市+寿光市+泰安市+威海市+潍坊市+文登市+新泰市+烟台市+禹城市+枣庄市+章丘市+招远市+诸城市+淄博市+邹城市+兖州市+滕州市");
		jo.put("山西省",
				"长治市+大同市+汾阳市+高平市+古交市+河津市+侯马市+霍州市+介休市+晋城市+晋中市+临汾市+潞城市+吕梁市+朔州市+太原市+孝义市+忻州市+阳泉市+永济市+原平市+运城市");
		jo.put("陕西省", "安康市+宝鸡市+韩城市+汉中市+华阴市+商洛市+铜川市+渭南市+西安市+咸阳市+兴平市+延安市+榆林市");
		jo.put("上海市", "上海市");
		jo.put("四川省",
				"巴中市+成都市+崇州市+达州市+德阳市+都江堰市+峨眉山市+广安市+广汉市+广元市+华蓥市+简阳市+江油市+乐山市+眉山市+绵阳市+绵竹市+南充市+内江市+攀枝花市+彭州市+什邡市+遂宁市+万源市+西昌市+雅安市+宜宾市+资阳市+自贡市+邛崃市+阆中市+泸州市");
		jo.put("天津市", "天津市");
		jo.put("西藏自治区", "阿里地区+昌都地区+拉萨市+林芝地区+那曲地区+尼玛县双湖特别区+日喀则市+山南地区");
		jo.put("新疆自治区",
				"阿克苏地区+阿拉尔市+阿勒泰地区+巴音郭楞自治州+博尔塔拉自治州+昌吉自治州+哈密地区+和田地区+喀什地区+克拉玛依市+克孜勒苏柯尔克孜+石河子市+塔城地区+图木舒克市+吐鲁番地区+乌鲁木齐市+五家渠市+伊犁哈萨克自治州");
		jo.put("云南省",
				"安宁市+保山市+楚雄市+大理市+个旧市+景洪市+开远市+昆明市+丽江市+临沧市+潞西市+普洱市+曲靖市+瑞丽市+香格里拉+宣威市+玉溪市+昭通市");
		jo.put("浙江省",
				"慈溪市+东阳市+奉化市+富阳市+海宁市+杭州市+湖州市+嘉兴市+建德市+江山市+金华市+兰溪市+乐清市+丽水市+临安市+临海市+龙泉市+宁波市+平湖市+瑞安市+上虞市+绍兴市+台州市+桐乡市+温岭市+温州市+义乌市+永康市+余姚市+舟山市+诸暨市+嵊州市+衢州市");
		jo.put("重庆市", "重庆市");
		return jo;

	}

}
