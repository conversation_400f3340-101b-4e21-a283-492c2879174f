package com.gc.notarizationpc.bean;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.ArrayList;
import java.util.List;

public class SerachBean {
    @JSONField(alternateNames = {"firstChar"})
    private String key = "";
    @JSONField(alternateNames = {"cityList"})
    private List<Bean> bean = new ArrayList<>();

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<Bean> getBean() {
        return bean;
    }

    public void setBean(List<Bean> bean) {
        this.bean = bean;
    }

    public static class Bean {
        String name = "";
        @JSONField(alternateNames = {"code"})
        String id = "";
        String address;
        String timeLot;
        private int notarizationFee;
        private int copyFee;
        private String newNotarial;
        private String hadProtectedContent;
        private String protectedContent;
        private String wrongRelatedMatters;

        public String getNewNotarial() {
            return newNotarial;
        }

        public void setNewNotarial(String newNotarial) {
            this.newNotarial = newNotarial;
        }

        public String getHadProtectedContent() {
            return hadProtectedContent;
        }

        public void setHadProtectedContent(String hadProtectedContent) {
            this.hadProtectedContent = hadProtectedContent;
        }

        public String getProtectedContent() {
            return protectedContent;
        }

        public void setProtectedContent(String protectedContent) {
            this.protectedContent = protectedContent;
        }

        public String getWrongRelatedMatters() {
            return wrongRelatedMatters;
        }

        public void setWrongRelatedMatters(String wrongRelatedMatters) {
            this.wrongRelatedMatters = wrongRelatedMatters;
        }

        public int getNotarizationFee() {
            return notarizationFee;
        }

        public void setNotarizationFee(int notarizationFee) {
            this.notarizationFee = notarizationFee;
        }

        public int getCopyFee() {
            return copyFee;
        }

        public void setCopyFee(int copyFee) {
            this.copyFee = copyFee;
        }

        public Boolean getSelected() {
            return isSelected;
        }

        public void setSelected(Boolean selected) {
            isSelected = selected;
        }

        Boolean isSelected = false;

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getTimeLot() {
            return timeLot;
        }

        public void setTimeLot(String timeLot) {
            this.timeLot = timeLot;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }
}
