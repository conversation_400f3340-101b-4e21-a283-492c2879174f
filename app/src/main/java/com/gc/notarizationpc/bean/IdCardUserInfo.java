package com.gc.notarizationpc.bean;

import java.io.Serializable;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.bean
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2024/3/30
 */
public class IdCardUserInfo implements Serializable {
    private String idCard;
    private String name;
    private String birthday;
    private Integer gender;
    private String nation;
    private String mobile;
    private String address;
    private String registeAddress;
    private String userId;
    private String sysUserId;

    public String getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(String sysUserId) {
        this.sysUserId = sysUserId;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public Integer getGender() {
        return gender== null ? 0 : gender ;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRegisteAddress() {
        return registeAddress;
    }

    public void setRegisteAddress(String registeAddress) {
        this.registeAddress = registeAddress;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
