package com.gc.notarizationpc.bean;

import android.graphics.drawable.Drawable;

import java.util.List;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.bean
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/1/11
 */
public class MenuInfo {
    private String id;
    private String key;
    private String name;
    private Drawable drawImg;
    private boolean isChoose;
    private List<MenuInfo> child;

    public List<MenuInfo> getChild() {
        return child;
    }

    public void setChild(List<MenuInfo> child) {
        this.child = child;
    }

    public MenuInfo(String name, Drawable drawImg) {
        this.name = name;
        this.drawImg = drawImg;
    }

    public MenuInfo() {

    }

    public MenuInfo(String key, String name) {
        this.key = key;
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public boolean isChoose() {
        return isChoose;
    }

    public void setChoose(boolean choose) {
        isChoose = choose;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Drawable getDrawImg() {
        return drawImg;
    }

    public void setDrawImg(Drawable drawImg) {
        this.drawImg = drawImg;
    }
}
