package com.gc.notarizationpc.bean;

import com.gc.mininotarization.R;

import me.goldze.mvvmhabit.utils.Utils;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.bean
 * @Description:
 * @Author: xuh<PERSON><PERSON>
 * @CreateDate: 2024/2/1
 */
public enum WorkstatusEnum {
    DUTYONLINE(1, "值班在线"),
    ONLINE(2, "在线"),
    BUSY(3, "忙碌"),
    DUTYONLINE_BUSY(4, "值班在线忙碌"),
    OFFLINE(5, "离线"),

    ADD_VIDEO_ORDER_FAIL(110,Utils.getContext().getString(R.string.createNotaryOrderFail)),
    ENTER_VIDEO_ROOM(111,Utils.getContext().getString(R.string.enter_video_room)),
    CANCEL_MATCH(112,Utils.getContext().getString(R.string.cancel_match)),
    MATCH_NOTRAY_SUCCESS(113,Utils.getContext().getString(R.string.match_notary_success)),
    APPLICATION_INFO_FAIL(114,Utils.getContext().getString(R.string.application_info_fail)),
    FEE_INFO_FAIL(115,Utils.getContext().getString(R.string.fee_info_fail)),
    DOC_INFO_FAIL(116,Utils.getContext().getString(R.string.failedToLookDocument)),
    UPLOAD_SIGN_FAIL(117,Utils.getContext().getString(R.string.upload_sign_fail)),
    SIGN_DOC_FAIL(118,Utils.getContext().getString(R.string.faileSignDocument)),
    LIST_MATERIAL_FAIL(119,Utils.getContext().getString(R.string.list_material_fail)),
    CANCEL_MATCH_FAIL(120,Utils.getContext().getString(R.string.cancel_match_fail)),
    JUST_STATE_FAIL(121,Utils.getContext().getString(R.string.just_state_fail)),
    PHONT_HINT(122, Utils.getContext().getString(R.string.pleaseInputCorrectPhoneNumber)),
    VERIFY_CODE_HINT(123,Utils.getContext().getString(R.string.verifycode_hint)),
    READ_RULE_HINT(124,Utils.getContext().getString(R.string.read_rule_hint)),
    NOT_GET_IDCARD(125,Utils.getContext().getString(R.string.not_get_idcard)),
    GET_VERIFY_CODE_FAIL(126,Utils.getContext().getString(R.string.getMessageCodeFail)),
    SECOND_SEND_HINT(127,Utils.getContext().getString(R.string.second_send_hint)),
    PLEASE_UPLOAD_HINT(128,Utils.getContext().getString(R.string.theMaterialToBeUploadedIsNotFound)),
    GET_LOCATION_FAIL(129,Utils.getContext().getString(R.string.getUserLocationInformationFail));

    public Integer code;
    public String msg;

    WorkstatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
