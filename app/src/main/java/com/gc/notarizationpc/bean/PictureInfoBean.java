package com.gc.notarizationpc.bean;

public class PictureInfoBean {

    private String type;

    private String picturePath;

    private String name;

    private String id;
    private String fileId;
    private String fileName;
    private String filePath;


    public PictureInfoBean(String type, String picturePath, String materialId) {
        this.type = type;
        this.picturePath = picturePath;
        this.id = materialId;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public PictureInfoBean(String type, String picturePath) {
        this.type = type;
        this.picturePath = picturePath;
    }

//    public PictureInfoBean(String type, String picturePath, String name) {
//        this.type = type;
//        this.picturePath = picturePath;
//        this.name = name;
//    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPicturePath() {
        return picturePath;
    }

    public void setPicturePath(String picturePath) {
        this.picturePath = picturePath;
    }
}
