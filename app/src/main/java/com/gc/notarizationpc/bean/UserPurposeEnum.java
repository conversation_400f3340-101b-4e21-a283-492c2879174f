package com.gc.notarizationpc.bean;

/**
 * 证书用途
 * @ProjectName:
 * @Package: com.gc.notarizationpc.bean
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/2/1
 */
public enum UserPurposeEnum {
    //本系统中：01-探亲,02-定居,03-学习,04-继承,05-就业,06-结婚,07-探病,08-奔丧,09-扶养亲属,10-领取养老金,11-对外贸易,12-诉讼（索赔,13-提供劳务,14-申请知识产,15-招标投标,16-签订合同,17-减免税,18-考察访问,99-其他
    DUTYONLINE(1, "值班在线"),
    ONLINE(2, "在线"),
    BUSY(3, "忙碌"),
    DUTYONLINE_BUSY(4, "值班在线忙碌"),
    OFFLINE(5, "离线"),

    ADD_VIDEO_ORDER_FAIL(110,"创建视频订单失败"),
    ENTER_VIDEO_ROOM(111,"匹配成功准备进入受理室");

    public Integer code;
    public String msg;

    UserPurposeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
