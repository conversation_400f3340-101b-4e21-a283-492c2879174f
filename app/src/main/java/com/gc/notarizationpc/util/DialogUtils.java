package com.gc.notarizationpc.util;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.Window;
import android.view.WindowManager;

import com.gc.mininotarization.R;


/**
 * Dialog对话框生成工具类
 *
 * <AUTHOR>
 */
public class DialogUtils {
    /**
     * 内部类实现单例模式
     * 延迟加载，减少内存开销
     *
     * <AUTHOR>
     */
    private static class SingletonHolder {
        private static DialogUtils instance = new DialogUtils();
    }

    /**
     * 私有的构造函数
     */
    private DialogUtils() {

    }

    public static DialogUtils getInstance() {
        return SingletonHolder.instance;
    }

    /**
     * 中间Dialog
     *
     * @param context
     * @param iscan   是否允许外部取消
     * @param id      layout id
     * @return
     */
    public Dialog getCenterDialog(Context context, boolean iscan, int id) {
        Dialog dialog = new Dialog(context, R.style.Dialog_Fullscreen);
        dialog.setContentView(id);

        Window dialogWindow = dialog.getWindow();
//        dialogWindow.setType(WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG);
        //dialogWindow.setWindowAnimations(R.style.mystyle);
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = context.getResources().getDisplayMetrics().widthPixels;
        lp.alpha = 1.0f;
        dialogWindow.setAttributes(lp);
        dialogWindow.setGravity(Gravity.CENTER);
        dialog.setCanceledOnTouchOutside(iscan);
        if (!iscan) {
            dialog.setOnKeyListener(keylistener);
        }
        return dialog;
    }

    private final DialogInterface.OnKeyListener keylistener = new DialogInterface.OnKeyListener() {
        public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
            if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
                return true;
            } else {
                return false;
            }
        }
    };
}
