package com.gc.notarizationpc.util.helper;


import static com.gc.notarizationpc.util.helper.render.opengl.OpenGlUtils.NO_TEXTURE;

import android.graphics.SurfaceTexture;
import android.hardware.Camera;
import android.opengl.EGLContext;
import android.opengl.GLES20;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.util.Pair;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.eloam.gaopaiyi.util.UVCCameraUtil;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.util.helper.render.EglCore;
import com.gc.notarizationpc.util.helper.render.opengl.OpenGlUtils;
import com.gc.notarizationpc.util.helper.render.opengl.Rotation;
import com.serenegiant.usb.IFrameCallback;
import com.serenegiant.usb.USBMonitor;
import com.tencent.custom.customcapture.opengl.GPUImageFilter;
import com.tencent.custom.customcapture.opengl.GPUImageFilterGroup;
import com.tencent.custom.customcapture.opengl.OesInputFilter;
import com.tencent.custom.customcapture.structs.FrameBuffer;
import com.tencent.custom.customcapture.structs.TextureFrame;

import java.lang.ref.WeakReference;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;


public class CustomCameraCapture implements SurfaceTexture.OnFrameAvailableListener {

    private static final String TAG = "CameraVideoFrameReader";

    private static final int WIDTH = 480;
    private static final int HEIGHT = 640;
    private static final int WHAT_START = 0;
    private static final int WHAT_UPDATE = 1;
    public static final int VIDEO_FPS = 15;

    private Camera mCamera;
    private SurfaceTexture mSurfaceTexture;
    private EglCore mEglCore;
    private FrameBuffer mFrameBuffer;
    private OesInputFilter mOesInputFilter;
    private GPUImageFilterGroup mGpuImageFilterGroup;

    private final FloatBuffer mGLCubeBuffer;
    private final FloatBuffer mGLTextureBuffer;
    private final float[] mTextureTransform = new float[16]; // OES纹理转换为2D纹理
    private int mSurfaceTextureId = NO_TEXTURE;
    private boolean mFrameUpdated;
    private VideoFrameReadListener mVideoFrameReadListener;
    private HandlerThread mRenderHandlerThread;
    private volatile RenderHandler mRenderHandler;
    private String pid;
    private USBMonitor.UsbControlBlock controlBlock;

    public interface VideoFrameReadListener {
        void onFrameAvailable(EGLContext eglContext, int textureId, int width, int height);
    }

    public CustomCameraCapture(String pid, USBMonitor.UsbControlBlock controlBlock) {
        mFrameUpdated = false;

        Pair<float[], float[]> cubeAndTextureBuffer = OpenGlUtils.calcCubeAndTextureBuffer(ImageView.ScaleType.MATRIX, Rotation.NORMAL, false, WIDTH, HEIGHT, WIDTH, HEIGHT);

        mGLCubeBuffer = ByteBuffer.allocateDirect(OpenGlUtils.CUBE.length * 4).order(ByteOrder.nativeOrder()).asFloatBuffer();
        mGLCubeBuffer.put(cubeAndTextureBuffer.first);
        mGLTextureBuffer = ByteBuffer.allocateDirect(OpenGlUtils.TEXTURE.length * 4).order(ByteOrder.nativeOrder()).asFloatBuffer();
        mGLTextureBuffer.put(cubeAndTextureBuffer.second);
        this.pid = pid;
        this.controlBlock = controlBlock;
    }


    @Override
    public void onFrameAvailable(SurfaceTexture surfaceTexture) {
        //surfacetexture渲染完成一个数据帧
        mFrameUpdated = true;
        mRenderHandler.sendEmptyMessage(WHAT_UPDATE);
    }

    public void startInternal(final VideoFrameReadListener videoFrameReadListener) {
        //视频上传数据监听器
        mVideoFrameReadListener = videoFrameReadListener;
        mRenderHandlerThread = new HandlerThread("RenderHandlerThread");
        mRenderHandlerThread.start();
        mRenderHandler = new RenderHandler(mRenderHandlerThread.getLooper(), this);
        //开始屏幕捕捉
        mRenderHandler.sendEmptyMessage(WHAT_START);
    }

    public void stop() {
        if (mRenderHandlerThread != null) {
            mRenderHandlerThread.quit();
        }

        if (mCamera != null) {
            mCamera.stopPreview();
        }
        if (mGpuImageFilterGroup != null) {
            mGpuImageFilterGroup.destroy();
            mGpuImageFilterGroup = null;
        }

        if (mFrameBuffer != null) {
            mFrameBuffer.uninitialize();
            mFrameBuffer = null;
        }

        if (mSurfaceTextureId != NO_TEXTURE) {
            OpenGlUtils.deleteTexture(mSurfaceTextureId);
            mSurfaceTextureId = NO_TEXTURE;
        }

        if (mSurfaceTexture != null) {
            mSurfaceTexture.release();
            mSurfaceTexture = null;
        }

        if (mEglCore != null) {
            mEglCore.unmakeCurrent();
            mEglCore.destroy();
            mEglCore = null;
        }
    }

    private void startInternal() {
        //开始推屏对象初始化准备
        mEglCore = new EglCore(WIDTH, HEIGHT);
        mEglCore.makeCurrent();

        mSurfaceTextureId = OpenGlUtils.generateTextureOES();
        mSurfaceTexture = new SurfaceTexture(mSurfaceTextureId);

        mFrameBuffer = new FrameBuffer(WIDTH, HEIGHT);
        mFrameBuffer.initialize();

        mGpuImageFilterGroup = new GPUImageFilterGroup();
        mOesInputFilter = new OesInputFilter();
        mGpuImageFilterGroup.addFilter(mOesInputFilter);
        mGpuImageFilterGroup.addFilter(new GPUImageFilter(true));
        mGpuImageFilterGroup.init();
        mGpuImageFilterGroup.onOutputSizeChanged(WIDTH, HEIGHT);

        new Handler(Looper.getMainLooper()).post(() -> {
            try {
                mSurfaceTextureId = OpenGlUtils.generateTextureOES();
                mSurfaceTexture = new SurfaceTexture(mSurfaceTextureId);
                mSurfaceTexture.setOnFrameAvailableListener(CustomCameraCapture.this);
                if (pid.equals(AppConfig.RGBPID)) { //打开彩色摄像头
                    Log.i(TAG, "onSurfaceTextureAvailable openRGBCamera");
                    UVCCameraUtil.INSTANCE.openRGBCamera(mSurfaceTexture, controlBlock, new IFrameCallback() {
                        @Override
                        public void onFrame(ByteBuffer frame) {
                            Log.i(TAG, "onSurfaceTextureAvailable openRGBCamera success");
                        }

                        @Override
                        public void onFail() {
                            Log.i(TAG, "onSurfaceTextureAvailable openRGBCamera fail");
                        }
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR1)
    private void updateTexture() {
        synchronized (this) {
            if (mFrameUpdated) {
                mFrameUpdated = false;
            }
            try {
                if (mSurfaceTexture != null) {

                    mSurfaceTexture.updateTexImage();
                    mSurfaceTexture.getTransformMatrix(mTextureTransform);
                    mOesInputFilter.setTexutreTransform(mTextureTransform);
                    mGpuImageFilterGroup.draw(mSurfaceTextureId, mFrameBuffer.getFrameBufferId(), mGLCubeBuffer, mGLTextureBuffer);

                    GLES20.glFinish();

                    if (mVideoFrameReadListener != null) {
                        TextureFrame textureFrame = new TextureFrame();
                        textureFrame.eglContext = (EGLContext) mEglCore.getEglContext();
                        textureFrame.textureId = mFrameBuffer.getTextureId();
                        textureFrame.width = HEIGHT;
                        textureFrame.height = WIDTH;
//                        发送数据
                        mVideoFrameReadListener.onFrameAvailable(textureFrame.eglContext, textureFrame.textureId, textureFrame.width, textureFrame.height);
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "onFrameAvailable: " + e.getMessage(), e);
            }
        }
    }

    private static class RenderHandler extends Handler {

        private final WeakReference<CustomCameraCapture> readerWeakReference;

        public RenderHandler(@NonNull Looper looper, CustomCameraCapture cameraVideoFrameReader) {
            super(looper);
            readerWeakReference = new WeakReference<>(cameraVideoFrameReader);
        }

        @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR1)
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            CustomCameraCapture cameraVideoFrameReader = readerWeakReference.get();
            if (cameraVideoFrameReader != null) {
                if (WHAT_START == msg.what) {
                    cameraVideoFrameReader.startInternal();
                } else if (WHAT_UPDATE == msg.what) {
                    cameraVideoFrameReader.updateTexture();
                }
            }
        }
    }
}
