package com.gc.notarizationpc.util;

import android.util.Log;
import android.view.View;

public abstract class MyO<PERSON><PERSON><PERSON><PERSON>Listener implements View.OnClickListener {
    public static final int MIN_CLICK_DELAY_TIME = 1500;
    public static final String TAG = MyOnclickClickListener.class.getSimpleName();
    private long lastClickTime = 0;
    @Override
    public void onClick(View v) {
        if (System.currentTimeMillis() - lastClickTime >= MIN_CLICK_DELAY_TIME) {
            Log.i(TAG, "not double Click");
            lastClickTime = System.currentTimeMillis();
            mOnClick(v);
        } else {
            Log.i(TAG, "double Click");
        }
    }
    public abstract void mOnClick(View v);
}
