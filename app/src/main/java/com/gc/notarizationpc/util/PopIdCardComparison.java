package com.gc.notarizationpc.util;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;

import com.example.scarx.idcardreader.utils.IdCardRenderUtils;
import com.example.scarx.idcardreader.utils.imp.MyCallBack;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.zkteco.android.biometric.core.device.ParameterHelper;
import com.zkteco.android.biometric.core.device.TransportType;
import com.zkteco.android.biometric.core.utils.ToolUtils;
import com.zkteco.android.biometric.module.idcard.IDCardReader;
import com.zkteco.android.biometric.module.idcard.IDCardReaderFactory;
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo;

import java.util.HashMap;
import java.util.concurrent.CountDownLatch;

import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.StatusBarUtil;

/**
 * 身份证识别弹出框
 */
public class PopIdCardComparison {
    private String TAG = "Test";

    private IdCardRenderUtils idCardReaderUtils = new IdCardRenderUtils();
    private SoundPool soundPool = new SoundPool(100, AudioManager.STREAM_MUSIC, 0);
    private boolean isActivity = true;
    private boolean isNoCard = true;
    private IDCardInfo idcardinfo = null;
    private Bitmap idCardBmp = null;
    private Integer readSuccess = null;
    private Integer readTips = null;
    private Integer validateSuccess = null;
    private Integer validateFail = null;
    private Integer retry = null;
    private Integer faceCamera = null;
    private AlertDialog dialog;

    public PopIdCardComparison(Context context) {
        loadSoundRes(context);
    }

    public interface ConfirmClickInterface {
        void onConfirm();

        void onCancel();
    }

    /**
     * @param context
     * @return
     */
    public void showIdCardComparison(final Context context, IdCardNotarizationiListener idCardNotarizationiListener) {
        //获取自定义布局文件pop.xml的视图
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.dialog_idcard_comparise, null);
        ImageView ivClose = view.findViewById(R.id.iv_close);
        dialog = builder.create();

        dialog.show();

        // 开启身份证识别
        if (idCardReaderUtils.isbStoped()) {
            Log.e("Test", "idCardReaderUtils.setbStoped(false)");
            idCardReaderUtils.setbStoped(false);
        } else {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    startIdCardRead(context, idCardNotarizationiListener);
                }
            }, 3500);
        }
        Window window = dialog.getWindow();
        //设置透明才能将 圆角 显示
        window.getDecorView().setBackgroundColor(context.getResources().getColor(com.hjq.bar.R.color.bar_transparent));
        window.getDecorView().setPadding(0, 0, 0, 0);
        window.setLayout(
                StatusBarUtil.dip2px(context, 600F),
                StatusBarUtil.dip2px(context, 500F));
        window.setGravity(Gravity.CENTER);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (dialog.isShowing()) {
                    dialog.dismiss();
                }
            }
        });


        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialogInterface) {
                try {
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            idCardReaderUtils.setbStoped(true);
                            soundPool.release();
                        }
                    }, 200);

                } catch (Exception e) {
                    Log.e(TAG, e.getMessage());
                }

            }
        });

        dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialogInterface, int i, KeyEvent keyEvent) {
                if (i == keyEvent.KEYCODE_BACK) {
                    dialog.setCancelable(false);
                    return true;
                }
                return false;
            }
        });

        isActivity = true;

    }

    private void startIdCardRead(Context context, IdCardNotarizationiListener idCardNotarizationiListener) {
        try {
            Log.i(TAG, "startIdCardRead");
//        LogHelper.setLevel(8); // 身份证打印等级
            HashMap<String, Object> ideograms = new HashMap<>();
            ideograms.put(ParameterHelper.PARAM_KEY_VID, 1024);
            ideograms.put(ParameterHelper.PARAM_KEY_PID, 50010);
            IDCardReader idCardReader = IDCardReaderFactory.createIDCardReader(
                    ToolUtils.getApplicationContext(),
                    TransportType.USB,
                    ideograms
            );
            idCardReaderUtils.readerIdCard(idCardReader, new CountDownLatch(1), new MyCallBack() {
                @Override
                public void onSuccess(IDCardInfo idCardInfo) {
                    Log.i(TAG, "readerIdCard success 读卡成功" + (null == idcardinfo));
                    if (isNoCard && null == idcardinfo) {
                        idcardinfo = idCardInfo;
                        Log.e(TAG, "播放身份识别成功的声音");
                        playSound(1);
                        isNoCard = false;
                        IdCardUserInfo customIdCardInfo = new IdCardUserInfo();
                        customIdCardInfo.setAddress(idcardinfo.getAddress());
                        customIdCardInfo.setBirthday(idcardinfo.getBirth());
                        customIdCardInfo.setIdCard(idcardinfo.getId());
                        customIdCardInfo.setName(idcardinfo.getName());
                        customIdCardInfo.setNation(idcardinfo.getNation());
                        if ("男".equals(idcardinfo.getSex())) {
                            customIdCardInfo.setGender(1);
                        } else {
                            customIdCardInfo.setGender(2);
                        }

                        idCardNotarizationiListener.getResult(customIdCardInfo);
                        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (dialog.isShowing()) {
                                    dialog.dismiss();
                                }
                            }
                        }, 400);
                    }
                }

                @Override
                public void onFail(String error) {
                    Log.e(TAG, "身份证读卡器出现异常,请检查设备");
                    if (idCardNotarizationiListener != null) {
                        idCardNotarizationiListener.getError(error);
                    }
                    Log.i(TAG, "readerIdCard fail");
                }

                @Override
                public void onRequestDevicePermission() {
                }

                @Override
                public void onNoCards() {
                    Log.i(TAG, "onNoCards");
                    isNoCard = true;
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void loadSoundRes(Context mActivity) {
        Log.i(TAG, "loadSoundRes");
        new Handler(Looper.getMainLooper()).post(() -> {
            if (SPUtils.getInstance().getString("changeLanguage", "zh").equals("bo")) {
                //咚
                readSuccess = soundPool.load(mActivity, R.raw.readcard_success_z, 1);
                //请将身份证放置于阅读器上
                readTips = soundPool.load(mActivity, R.raw.idcardread_z, 1);
                //请正对摄像头
                faceCamera = soundPool.load(mActivity, R.raw.face_camera_z, 1);
                //请重试
                retry = soundPool.load(mActivity, R.raw.retry_z, 1);
                //比对成功
                validateSuccess = soundPool.load(mActivity, R.raw.validate_success_z, 1);
                //比对失败
                validateFail = soundPool.load(mActivity, R.raw.validate_fail_z, 1);
            } else {
                //咚
                readSuccess = soundPool.load(mActivity, R.raw.readcard_success, 1);
                //请将身份证放置于阅读器上
                readTips = soundPool.load(mActivity, R.raw.idcardread, 1);
                //请正对摄像头
                faceCamera = soundPool.load(mActivity, R.raw.face_camera, 1);
                //请重试
                retry = soundPool.load(mActivity, R.raw.retry, 1);
                //比对成功
                validateSuccess = soundPool.load(mActivity, R.raw.validate_success, 1);
                //比对失败
                validateFail = soundPool.load(mActivity, R.raw.validate_fail, 1);
            }
        });
    }

    /**
     * 1: 读卡成功   2：请将身份证放置到阅读器上  3：请正对摄像头   4：比对失败 5：比对成功  6：请重试
     */
    public void playSound(int index) {
        Log.i(TAG, "playSound index" + index);
        switch (index) {
            case 1:
                soundPool.play(readSuccess, 1f, 1f, 1, 0, 1f);
                break;
            case 2:
                soundPool.play(readTips, 1f, 1f, 1, 0, 1f);
                break;
            case 3:
                soundPool.play(faceCamera, 1f, 1f, 1, 0, 1f);
                break;
            case 4:
                soundPool.play(validateFail, 1f, 1f, 1, 0, 1f);
                break;
            case 5:
                soundPool.play(validateSuccess, 1f, 1f, 1, 0, 1f);
                break;
            case 6:
                soundPool.play(retry, 1f, 1f, 1, 0, 1f);
                break;
        }
    }


    public interface IdCardNotarizationiListener {
        void getResult(IdCardUserInfo idCardInfo);

        void getError(String error);

    }


}
