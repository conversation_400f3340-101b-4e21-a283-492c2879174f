package com.gc.notarizationpc.util;


import android.content.res.Configuration;
import android.content.res.Resources;
import android.util.DisplayMetrics;

import java.util.Locale;

import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;

public class ChangeLanguageClass {

    /**
     * 修改App内文字语言
     *
     * @param langCode 语言code码
     */
  public static  final void setLocale(String langCode) {
      Resources resources = Utils.getContext().getResources();
      Configuration configuration = resources.getConfiguration();
      DisplayMetrics displayMetrics = resources.getDisplayMetrics();
      Locale locale = null;
      if(langCode.equals("en")){
          locale = Locale.ENGLISH;
      }else if(langCode.equals("zh")) {
          locale = Locale.SIMPLIFIED_CHINESE;
      } else if (langCode.equals("bo")) {
          Locale tempLocale = new Locale("bo");
          Locale.setDefault(tempLocale);
         locale = tempLocale;
      } else {
            locale = Locale.SIMPLIFIED_CHINESE;
      }
      configuration.setLocale(locale);
      SPUtils.getInstance().put("changeLanguage", langCode.isEmpty()?"zh":langCode);
      resources.updateConfiguration(configuration, displayMetrics);
    }
}
