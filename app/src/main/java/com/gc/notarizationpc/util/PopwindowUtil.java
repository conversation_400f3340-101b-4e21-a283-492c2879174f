package com.gc.notarizationpc.util;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.CountDownTimer;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.SerachBean;
import com.gc.notarizationpc.data.model.response.BindingNotaryOfficeModel;
import com.gc.notarizationpc.myview.SearchDialog;
import com.gc.notarizationpc.ui.adapter.NotaryOfficeListAdapter;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import es.dmoral.toasty.Toasty;
import me.goldze.mvvmhabit.utils.ConvertUtils;
import me.goldze.mvvmhabit.utils.StatusBarUtil;
import me.goldze.mvvmhabit.utils.Utils;
import me.goldze.mvvmhabit.widget.DrawView;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

public class PopwindowUtil {
    private static final String TAG = PopwindowUtil.class.getSimpleName();

    private static DrawView drawView = null;
    private static AlertDialog matchDialog;
    private static AlertDialog videoConnectingDialog;

    /**
     * 弹出签名框
     */
    public static void showSignature(final Context context, final SignatureListener signatureListener) {
        //获取自定义布局文件pop.xml的视图
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.dialog_signature, null);
        TextView tvClear = view.findViewById(R.id.tv_clear);
        ImageView mClose = view.findViewById(R.id.iv_close);
        TextView tvConfirm = view.findViewById(R.id.tv_confirm);
        LinearLayout mainLayout = view.findViewById(R.id.main_linlayout);
        LinearLayout signatureView = view.findViewById(R.id.signatureView);
        final AlertDialog dialog = builder.create();

        //TODO XHF
        String name = "徐海凤";
        if (mainLayout != null && mainLayout.getWidth() == 0) {
            ViewTreeObserver vto2 = mainLayout.getViewTreeObserver();
            vto2.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    Log.d(TAG, "画布初始化完成");
                    mainLayout.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    if (!TextUtils.isEmpty(name)) {
                        setDrawView(context, name.length(), mainLayout);
                    }
                }
            });
        } else {
            if (drawView != null) {
                assert mainLayout != null;
                mainLayout.removeAllViews();
            }
            assert mainLayout != null;
            setDrawView(context, name.length(), mainLayout);
        }

        signatureView.setVisibility(View.VISIBLE);
        dialog.show();
        Window window = dialog.getWindow();
        assert window != null : "showSignature: dialog get window but window is null";
        window.getDecorView().setBackgroundColor(context.getResources().getColor(R.color.white));
        window.getDecorView().setPadding(0, 0, 0, 0);
        window.setLayout(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        window.setGravity(Gravity.CENTER);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @SuppressLint("CheckResult")
            @Override
            public void onClick(View v) {
                if (drawView != null) {
                    Bitmap bit = drawView.getPaintBitmap(name.length());
                    if (bit != null) {
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        bit.compress(Bitmap.CompressFormat.JPEG, 100, baos);
                        byte[] imageBytes = baos.toByteArray();
                        RequestBody requestFile = RequestBody.create(MediaType.parse("image/jpeg"), imageBytes);
                        MultipartBody.Part body = MultipartBody.Part.createFormData("image", "image.jpg", requestFile);
                        if (signatureListener != null) {
                            signatureListener.result(body);
                        }
                    } else {
                        Log.e(TAG, "请先签名");
                        Toasty.warning(context, Utils.getContext().getString(R.string.pleaseSignAtFirst));
                    }
                } else {
                    Log.e(TAG, "签名失败");
                    Toasty.error(context, Utils.getContext().getString(R.string.failedToSign));
                }
            }
        });

        tvClear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (drawView != null) {
                    drawView.clear();
                }

            }
        });

        mClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
    }

    public static void dismissMatchDialog() {
        if (matchDialog != null && matchDialog.isShowing()) {
            matchDialog.dismiss();
        }
    }

    public static void dismissVideoConnectingDialog() {
        if (videoConnectingDialog != null && videoConnectingDialog.isShowing()) {
            videoConnectingDialog.dismiss();
        }
    }

    static CountDownTimer timer;


    /**
     * 视频连线等待中
     */
    public static void showConnectionVideoAlert(final Context context, final ButtonClickListener signatureListener) {
        //获取自定义布局文件pop.xml的视图
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.connection_video_alert, null);
        ((TextView) view.findViewById(R.id.connectionTimeOut)).setText(R.string.connecting_video_hint);
        TextView clockTextView = view.findViewById(R.id.clock_text_id);
        Button closeTransactionBtn = view.findViewById(R.id.close_transaction);
//        Button makeAppointmentBtn = view.findViewById(R.id.make_appointment);
        videoConnectingDialog = builder.create();
        timer = new CountDownTimer(20000, 1000) {
            @SuppressLint("SetTextI18n")
            @Override
            public void onTick(long millisUntilFinished) {
                if (millisUntilFinished / 1000 < 10)
                    clockTextView.setText("00:0" + millisUntilFinished / 1000 + "s");
                else
                    clockTextView.setText("00:" + millisUntilFinished / 1000 + "s");
            }

            @Override
            public void onFinish() {
                signatureListener.cancel();
            }
        };
        timer.start();
        videoConnectingDialog.show();
        Window window = videoConnectingDialog.getWindow();
        window.getDecorView().setBackgroundColor(context.getResources().getColor(android.R.color.white));
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));//设置Dialog背景透明
        window.setLayout(
                ConvertUtils.dp2px(568),
                ConvertUtils.dp2px(450));
        window.setGravity(Gravity.CENTER);
        videoConnectingDialog.setContentView(view);
        videoConnectingDialog.setCanceledOnTouchOutside(false);

//        makeAppointmentBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                dialog.dismiss();
//                signatureListener.decide();
//
//            }
//        });

        closeTransactionBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                signatureListener.decide();
                videoConnectingDialog.dismiss();

                if (timer != null) {
                    timer.cancel();
                    timer = null;
                }
            }
        });

        videoConnectingDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (timer != null) {
                    timer.cancel();
                    timer = null;
                }
            }
        });
    }


    /**
     * 输入手机号弹框
     */
    public static void showInputIphoneNumberAlert(final Context context, ButtonClickListenerWithBackValue buttonClickListener, ResultListener resultListener) {
        //获取自定义布局文件pop.xml的视图
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.dialog_input_phone, null);
        EditText phoneTextView = view.findViewById(R.id.dialog_input_phone_edit_text);
        EditText verifyCodeTextView = view.findViewById(R.id.dialog_input_phone_verifycode_edit_text);
        Button cancelBtn = view.findViewById(R.id.dialog_input_phone_cancelButton);
        Button confirmBtn = view.findViewById(R.id.dialog_input_phone_confirmButton);
        Button verifyCodeBtn = view.findViewById(R.id.dialog_input_phone_verifyButton);
        TextView agreeTextView = view.findViewById(R.id.dialog_input_phone_agreement_text);
        CheckBox agreeCheckBox = view.findViewById(R.id.dialog_input_phone_checkBox);
        final AlertDialog dialog = builder.create();
        dialog.show();
        Window window = dialog.getWindow();
        assert window != null : "showConnectionVideoAlert: dialog get window but window is null";
        window.getDecorView().setBackgroundColor(context.getResources().getColor(android.R.color.white));
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));//设置Dialog背景透明
        window.setLayout(
                ConvertUtils.dp2px(568),
                ConvertUtils.dp2px(450));
        window.setGravity(Gravity.CENTER);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        phoneTextView.setFocusable(true);
        phoneTextView.setFocusableInTouchMode(true);
        phoneTextView.requestFocus();
        verifyCodeTextView.setFocusable(true);
        verifyCodeTextView.setFocusableInTouchMode(true);
        verifyCodeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                if (!RegexUtils.isMobileExact(phoneTextView.getText().toString())) {
//                    ToastUtils.showLong(Utils.getContext().getString(R.string.pleaseInputCorrectPhoneNumber));
//                    return;
//                } else {
                verifyCodeBtn.setEnabled(false);
                verifyCodeBtn.setText("60s");
                verifyCodeBtn.setBackground(context.getResources().getDrawable(R.drawable.shape_corner5_e8e8e8));
                CountDownTimer timer = new CountDownTimer(60000, 1000) {
                    @SuppressLint("SetTextI18n")
                    @Override
                    public void onTick(long millisUntilFinished) {
                        verifyCodeBtn.setText(millisUntilFinished / 1000 + "s");
                    }

                    @Override
                    public void onFinish() {
                        verifyCodeBtn.setEnabled(true);
                        verifyCodeBtn.setText(context.getResources().getString(R.string.getVerfyCode));
                        verifyCodeBtn.setBackground(context.getResources().getDrawable(R.drawable.shape_corner5_3c6af4));
                    }
                };
                timer.start();
                resultListener.result(phoneTextView.getText().toString());
//                }
                //获取验证码
            }
        });
        agreeTextView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //查看协议
                buttonClickListener.otherEvent(agreeCheckBox.isChecked());
            }
        });
        agreeCheckBox.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (agreeCheckBox.isChecked()) {
                    agreeCheckBox.setBackground(context.getResources().getDrawable(R.mipmap.cb_press));
                } else {
                    agreeCheckBox.setBackground(context.getResources().getDrawable(R.mipmap.cb_normal));
                }
            }
        });
        cancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                buttonClickListener.cancel();
            }
        });
        confirmBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                Map<String, String> map = new HashMap<>();
                map.put("phone", phoneTextView.getText() == null ? "" : phoneTextView.getText().toString());
                map.put("verifyCode", verifyCodeTextView.getText() == null ? "" : verifyCodeTextView.getText().toString());
                map.put("agree", agreeCheckBox.isChecked() ? "1" : "0");
                buttonClickListener.decide(map);
            }
        });
    }


    /**
     * 输入身份信息弹框
     */
    public static void showInputIdCardInformationAlert(final Context context, ButtonClickListenerWithBackValue buttonClickListener) {
        //获取自定义布局文件pop.xml的视图
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.dialog_input_idcard_information, null);
        TextView idCardNumberTextView = view.findViewById(R.id.dialog_input_id_card_information_id_card);
        EditText idCardText = view.findViewById(R.id.dialog_input_id_card_information_id_card_edit_text);
        CheckBox checkBox = view.findViewById(R.id.dialog_input_id_card_checkBox);
        TextView onLineRules = view.findViewById(R.id.dialog_input_id_card_agreement_text);
        Button cancelBtn = view.findViewById(R.id.dialog_input_id_card_information_back);
        Button confirmBtn = view.findViewById(R.id.dialog_input_id_card_information_confirm);
        final AlertDialog dialog = builder.create();
        dialog.show();
        Window window = dialog.getWindow();
        assert window != null : "showConnectionVideoAlert: dialog get window but window is null";
        window.getDecorView().setBackgroundColor(context.getResources().getColor(android.R.color.white));
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));//设置Dialog背景透明
        window.setLayout(
                ConvertUtils.dp2px(568),
                ConvertUtils.dp2px(450));
        window.setGravity(Gravity.CENTER);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        idCardText.setFocusable(true);
        idCardText.setFocusableInTouchMode(true);

        onLineRules.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                buttonClickListener.otherEvent(checkBox.isChecked());
            }
        });
        checkBox.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (checkBox.isChecked()) {
                    checkBox.setBackground(context.getResources().getDrawable(R.mipmap.cb_press));
                } else {
                    checkBox.setBackground(context.getResources().getDrawable(R.mipmap.cb_normal));
                }

            }
        });
        cancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                buttonClickListener.cancel();
            }
        });
        confirmBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                Map<String, String> map = new HashMap<>();
                map.put("idCard", idCardText.getText() == null ? "" : idCardText.getText().toString());
                map.put("agree", checkBox.isChecked() ? "1" : "0");
                buttonClickListener.decide(map);
            }
        });
    }

    /**
     * 读取身份证弹框
     */
    public static void showReadIdCardInformationAlert(final Context context) {
        //获取自定义布局文件pop.xml的视图
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.dialog_scan_idcard, null);
        final AlertDialog dialog = builder.create();
        dialog.show();
        Window window = dialog.getWindow();
        assert window != null : "showConnectionVideoAlert: dialog get window but window is null";
        window.getDecorView().setBackgroundColor(context.getResources().getColor(android.R.color.white));
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));//设置Dialog背景透明
        window.setLayout(
                ConvertUtils.dp2px(568),
                ConvertUtils.dp2px(450));
        window.setGravity(Gravity.CENTER);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
    }

    private static void setDrawView(Context context, Integer length, LinearLayout main_linlayout) {
        int with = main_linlayout.getWidth() * length;
//        int with = 600 * length;
        if (length != 0) {
            Double dou = null;
            dou = 1.5 + length - 1 * 0.5;
            with = (int) (dou * main_linlayout.getWidth()) + 1;
            drawView = new DrawView(context, with, main_linlayout.getHeight());//main_linlayout.getHeight()
            main_linlayout.addView(drawView);
            drawView.requestFocus();
        }

    }

    /**
     * @param mContext
     * @param dialog
     */
    private static void DialogHeightAndWidthPixels(Context mContext, AlertDialog dialog) {
        Window window = dialog.getWindow();
        assert window != null;
        window.getDecorView().setBackgroundColor(mContext.getResources().getColor(R.color.white));
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        window.setLayout(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        window.setGravity(Gravity.CENTER);
        dialog.setCanceledOnTouchOutside(false);
    }

    /**
     * 弹出选择公证处
     *
     * @param mContext
     * @param list
     */
    public static void showNotaryOffice(final Context mContext, final List<BindingNotaryOfficeModel> list) {
        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(R.layout.dialog_select_notary_office, null);
        EditText edtSearch = view.findViewById(R.id.edt_search);
        TextView tvSearch = view.findViewById(R.id.tv_search);
        TextView tvCancel = view.findViewById(R.id.tv_cancel);
        TextView tvConfirm = view.findViewById(R.id.tv_confirm);
        ImageView ivClose = view.findViewById(R.id.iv_close);
        RecyclerView rvNotarialOffice = view.findViewById(R.id.list_notarial_office);
        rvNotarialOffice.setLayoutManager(new GridLayoutManager(mContext, 3));

        final AlertDialog dialog = builder.create();

        dialog.show();
        dialog.setContentView(view);
        Window window = dialog.getWindow();
        window.getDecorView().setBackgroundColor(mContext.getResources().getColor(R.color.white));
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        window.setLayout(
                window.getContext().getResources().getDisplayMetrics().widthPixels - 200,
                window.getContext().getResources().getDisplayMetrics().heightPixels - 120);
        window.setGravity(Gravity.CENTER);
        dialog.setCanceledOnTouchOutside(false);

        if (null != list && list.size() > 0) {
            NotaryOfficeListAdapter notaryOfficeListAdapter = new NotaryOfficeListAdapter(mContext, R.layout.item_list_notary_office, 0);
            rvNotarialOffice.setAdapter(notaryOfficeListAdapter);
            notaryOfficeListAdapter.refreshAdapter(list);

            notaryOfficeListAdapter.setOnClick(new NotaryOfficeListAdapter.Onclick() {
                @Override
                public void OnClicklistener(int position, int flag) {
                    //点击单选


                }
            });

        }

        //确认
        tvConfirm.setOnClickListener(v -> {

        });

        //搜索
        tvSearch.setOnClickListener(v -> {
        });

        //关闭
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            }
        });

        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            }
        });

    }


    /**
     * 弹出 企业签章
     */
    public static void showCompanySign(Activity mContext, String url, ResultListener resultListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(R.layout.dialog_company_signature, null);
        ImageView ivClose = view.findViewById(R.id.iv_close);
        ImageView imageView = view.findViewById(R.id.iv_img);
        Glide.with(imageView.getContext())
                .load(url)
                .placeholder(R.mipmap.erroimage).error(R.mipmap.erroimage)
                .into(imageView);
        Button btnSign = view.findViewById(R.id.btn_sign);
        final AlertDialog dialog = builder.create();

        dialog.show();
        dialog.setContentView(view);
        Window window = dialog.getWindow();
        //设置透明才能将 圆角 显示
        window.getDecorView().setBackgroundColor(mContext.getResources().getColor(com.hjq.bar.R.color.bar_transparent));
        window.getDecorView().setPadding(0, 0, 0, 0);
        window.setLayout(
                StatusBarUtil.dip2px(mContext, 550F),
                StatusBarUtil.dip2px(mContext, 450F));
        window.setGravity(Gravity.CENTER);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);

        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dialog.dismiss();
            }
        });
        btnSign.setOnClickListener(v -> {
            dialog.dismiss();
            if (resultListener != null) {
                resultListener.result("");
            }
        });


    }

    private static void settingDialog(Context context, AlertDialog dialog) {
        Window window = dialog.getWindow();
        window.getDecorView().setBackgroundColor(context.getResources().getColor(R.color.white));
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        window.setLayout(
                window.getContext().getResources().getDisplayMetrics().widthPixels - 100,
                window.getContext().getResources().getDisplayMetrics().heightPixels - 120);
        window.setGravity(Gravity.CENTER);
        dialog.setCanceledOnTouchOutside(false);
    }

    /**
     * 视频连线退出提示弹框
     */
    public static void videoExitAlert(Activity mContext, ResultListener resultListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(R.layout.dialog_video_exit_alert, null);
        TextView decide = view.findViewById(R.id.dialog_video_exit_alert_decide);
        TextView cancel = view.findViewById(R.id.dialog_video_exit_alert_cancel);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        decide.setOnClickListener(v -> {

            if (resultListener != null) {
                resultListener.result("");
            }
            dialog.dismiss();
        });
        cancel.setOnClickListener(v -> dialog.dismiss());
        dialog.show();
        dialog.setContentView(view);
        Window window = dialog.getWindow();
        window.getDecorView().setBackgroundColor(mContext.getResources().getColor(R.color.white));
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        window.setLayout(
                window.getContext().getResources().getDisplayMetrics().widthPixels - 100,
                window.getContext().getResources().getDisplayMetrics().heightPixels - 120);
        window.setGravity(Gravity.CENTER);

    }

    /**
     * 公证事项，地区等公共弹框
     */
    public static void showGroup(List<SerachBean> serachBeans, Activity mContext, int type, ResultListener resultListener) {
        //展示搜索dialog
        SearchDialog dialog = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            //type 表示可选个数
            dialog = new SearchDialog(mContext, serachBeans, true, "", type);
        }
        dialog.setInterface(new SearchDialog.SearchInterface() {
            @Override
            public void GetChoosedMsg(List<SerachBean.Bean> result) {
                if (resultListener != null) {
                    resultListener.result(result);
                }
            }
        });
//        dialog.setInterface(o -> {
//            String text = "";
//            for (int i = 0; i < o.size(); i++) {
//                if (i == o.size() - 1) {
//                    text += o.get(i);
//                } else
//                    text += o.get(i) + ",";
//            }
//            Toasty.info(mContext, "当前选择的是" + text, 3000).show();
//
//        });
        dialog.show();


    }

    /**
     * 预约成功等弹框
     *
     * @param mContext
     */
    public static void showReservationSuccess(final Context mContext, ButtonClickListener buttonClickListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(R.layout.dialog_reservation_success, null);
        TextView tvConfirm = view.findViewById(R.id.tv_confirm);

        final AlertDialog dialog = builder.create();
        if (dialog != null && !dialog.isShowing()) {
            dialog.show();
            dialog.setContentView(view);
            DialogHeightAndWidthPixels(mContext, dialog);
        }

        //确认
        tvConfirm.setOnClickListener(v -> {
            dialog.dismiss();
            if (buttonClickListener != null) {
                buttonClickListener.decide();
            }
        });

    }

    /**
     * 预约申办说明
     *
     * @param mContext
     */
    public static void showShenbanHintDialog(final Context mContext) {
        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(R.layout.dialog_announcement, null);
        TextView tvConfirm = view.findViewById(R.id.btn_ok);

        final AlertDialog dialog = builder.create();
        if (dialog != null && !dialog.isShowing()) {
            dialog.show();
            dialog.setContentView(view);
            DialogHeightAndWidthPixels(mContext, dialog);
        }

        //确认
        tvConfirm.setOnClickListener(v -> {
            dialog.dismiss();
        });
    }


    /**
     * 材料上传弹窗
     */

    public static void applyMaterialAlert(Dialog dialog, Context context) {
        Window dialogWindow = dialog.getWindow();
        dialogWindow.getDecorView().setBackgroundColor(context.getResources().getColor(android.R.color.white));
        dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
        // dialogWindow.setWindowAnimations(R.style.mystyle);
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = ConvertUtils.dp2px(470f);
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        lp.alpha = 1.0f;
        dialogWindow.setAttributes(lp);
        dialogWindow.setGravity(Gravity.RIGHT);
        dialog.setCanceledOnTouchOutside(false);
    }

    /**
     * 文书预览弹框
     */

    public static void readDocumentAlert(Dialog lawDialog, Context context) {
        Window dialogWindow = lawDialog.getWindow();
        dialogWindow.getDecorView().setBackgroundColor(context.getResources().getColor(android.R.color.white));
        dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
        // dialogWindow.setWindowAnimations(R.style.mystyle);
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        lp.alpha = 1.0f;
        dialogWindow.setAttributes(lp);
        dialogWindow.setGravity(Gravity.RIGHT);
        lawDialog.setCanceledOnTouchOutside(false);
    }

    /**
     * 签字的弹窗
     */
    public static void showSignAlert(Dialog signDialog, Context context) {
        if (!signDialog.isShowing()) {
            signDialog.show();
            Window dialogWindow = signDialog.getWindow();
            dialogWindow.getDecorView().setBackgroundColor(context.getResources().getColor(android.R.color.white));
            dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
            // dialogWindow.setWindowAnimations(R.style.mystyle);
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = ConvertUtils.dp2px(1000f);
            lp.height = ConvertUtils.dp2px(700f);
            lp.alpha = 1.0f;
            dialogWindow.setAttributes(lp);
            dialogWindow.setGravity(Gravity.CENTER);
            signDialog.setCanceledOnTouchOutside(false);
        }
    }

    /**
     * 获取键盘弹框
     */
    public static void showKeyBoard(EditText editText) {
        if (editText != null) {
            //设置可获得焦点
            editText.setFocusable(true);
            editText.setFocusableInTouchMode(true);
            //请求获得焦点
            editText.requestFocus();
            //调用系统输入法
            InputMethodManager inputManager = (InputMethodManager) editText.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            inputManager.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
        }
    }

    /**
     * 设置字体颜色
     *
     * @param inputString
     * @param start
     * @param end
     * @param firstColor
     * @param secondColor
     * @return
     */
    public static SpannableStringBuilder setSpanLineString(String inputString, int start, int end, String firstColor, String secondColor) {

        SpannableStringBuilder builder = new SpannableStringBuilder(inputString);

        ForegroundColorSpan redSpan = new ForegroundColorSpan(Color.parseColor(firstColor));
        builder.setSpan(redSpan, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        ForegroundColorSpan blueSpan = new ForegroundColorSpan(Color.parseColor(secondColor));
        builder.setSpan(blueSpan, end, inputString.length() - end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        return builder;
    }

    /**
     * Android 常规的弹框
     */
    public static void showNormalDialog(Context context, String title, String message, String positiveText, String negativeText, ButtonClickListener buttonClickListener) {
        if (title == null) {
            title = "提示";
        }
        if (message == null) {
            message = "这是一个提示对话框";
        }
        if (positiveText == null) {
            positiveText = "确定";
        }
        if (negativeText == null) {
            negativeText = "取消";
        }

        AlertDialog dialog = new AlertDialog.Builder(context)
                .setTitle(title)//设置标题
                .setMessage(message)//设置要显示的内容
                .setNegativeButton(negativeText, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        dialogInterface.dismiss();//销毁对话框
                        buttonClickListener.cancel();
                        ;
                    }
                })
                .setPositiveButton(positiveText, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();//销毁对话框
                        buttonClickListener.decide();
                    }
                }).create();//create（）方法创建对话框
        dialog.show();//显示对话框

    }

    public interface SignatureListener {
        void result(MultipartBody.Part body);

        void ondissmiss();
    }

    public interface ButtonClickListener {
        void cancel();


        void decide();
    }

    public interface ButtonClickListenerWithBackValue<T> {
        void cancel();

        void decide(T value);

        void otherEvent(T value);
    }

    public interface ResultListener<T> {
        void result(T value);
    }

    public interface ResultSecondListener<T> {
        void result(T value);

        void secondResult(T value);
    }

    public interface ResultThirdListener<T> {
        void result(T value);

        void secondResult(T value);

        void thirdResult(T value);
    }


}
