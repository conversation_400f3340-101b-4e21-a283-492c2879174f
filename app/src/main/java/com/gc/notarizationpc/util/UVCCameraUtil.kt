package com.eloam.gaopaiyi.util

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.*
import android.hardware.usb.UsbDevice
import android.text.TextUtils
import android.util.Log
import android.view.Surface
import android.view.TextureView
import com.gc.mininotarization.R
import com.gc.notarizationpc.common.AppConfig
import com.serenegiant.usb.DeviceFilter
import com.serenegiant.usb.IFrameCallback
import com.serenegiant.usb.USBMonitor
import com.serenegiant.usb.UVCCamera
import me.goldze.mvvmhabit.utils.SPUtils
import java.io.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * @author: wjw
 * @create：2020/7/8
 * @describe：
 */
@SuppressLint("StaticFieldLeak")
object UVCCameraUtil {
    private var TAG = "UVCCameraUtil"

    // 双目彩色摄像头
    private val mRGBSync = Any()
    private var mRGBCamera: UVCCamera? = null
    private var mRGBPreview: Surface? = null

    // 双目红外摄像头
    private val mIRSync = Any()
    private var mIRCamera: UVCCamera? = null
    private var mIRPreview: Surface? = null

    // 高拍仪摄像头
    private val mHighSync = Any()
    private var mHighCamera: UVCCamera? = null
    private var mHighPreview: Surface? = null

    // 外接usb摄像头
    private val mUsbSync = Any()
    private var mUsbCamera: UVCCamera? = null
    private var mUsbPreview: Surface? = null

    private var context: Context? = null

    interface OnMyDevConnectListener {
        fun onConnectDev(device: UsbDevice, ctrlBlock: USBMonitor.UsbControlBlock)
    }

    fun initUSBMonitor(context: Context, onMyDevConnectListener: OnMyDevConnectListener): USBMonitor {
        Log.i(TAG, "initUSBMonitor")
        this.context = context
        var usbMonitor = USBMonitor(context, object : USBMonitor.OnDeviceConnectListener {
            override fun onConnect(device: UsbDevice, ctrlBlock: USBMonitor.UsbControlBlock, createNew: Boolean) {
                Log.i(TAG, "initUSBMonitor onConnect")
                onMyDevConnectListener.onConnectDev(device, ctrlBlock)

            }

            override fun onCancel(device: UsbDevice?) {
                Log.i(TAG, "initUSBMonitor onCancel")
            }

            override fun onAttach(device: UsbDevice?) {
                Log.i(TAG, "initUSBMonitor onAttach")
            }

            override fun onDisconnect(device: UsbDevice?, ctrlBlock: USBMonitor.UsbControlBlock?) {
                Log.i(TAG, "initUSBMonitor onDisconnect")
            }

            override fun onDettach(device: UsbDevice?) {
                Log.i(TAG, "initUSBMonitor onDettach")
            }

        })
        return usbMonitor
    }

    fun initUSBMonitorForPid(context: Context) {
        Log.i(TAG, "initUSBMonitorForPid")
        this.context = context
        var usbMonitor = USBMonitor(context, object : USBMonitor.OnDeviceConnectListener {
            override fun onConnect(device: UsbDevice, ctrlBlock: USBMonitor.UsbControlBlock, createNew: Boolean) {
            }

            override fun onCancel(device: UsbDevice?) {
            }

            override fun onAttach(device: UsbDevice?) {
            }

            override fun onDisconnect(device: UsbDevice?, ctrlBlock: USBMonitor.UsbControlBlock?) {
            }

            override fun onDettach(device: UsbDevice?) {
            }

        })
        AppConfig.USBPNAME = ""
        val devList = getUsbDeviceList(context, usbMonitor)
        var name = SPUtils.getInstance().getString(AppConfig.LOCAL_USB_NAME, "")
        for (usbDevice in devList) {
            val productid = String.format("%x", usbDevice?.productId)
            Log.e("Test", "找到了: $productid")
            if (productid == "c013" || productid == "202") {
                AppConfig.RGBPID = productid
            } else if (productid == "1054") {
                AppConfig.HIGHPID = productid
            } else if (!TextUtils.isEmpty(name) && name.equals(usbDevice?.productName))
                AppConfig.USBPNAME = name

        }
    }

    fun checkUsbDevice(context: Context, name: String): Boolean {
        Log.i(TAG, "checkUsbDevice")
        var result: Boolean? = false
        this.context = context
        var usbMonitor = USBMonitor(context, object : USBMonitor.OnDeviceConnectListener {
            override fun onConnect(device: UsbDevice, ctrlBlock: USBMonitor.UsbControlBlock, createNew: Boolean) {
            }

            override fun onCancel(device: UsbDevice?) {
            }

            override fun onAttach(device: UsbDevice?) {
            }

            override fun onDisconnect(device: UsbDevice?, ctrlBlock: USBMonitor.UsbControlBlock?) {
            }

            override fun onDettach(device: UsbDevice?) {
            }

        })
        val devList = getUsbDeviceList(context, usbMonitor)
        for (usbDevice in devList) {
            Log.e("zzkong", "找到了:" + usbDevice?.productName)
            if (name == usbDevice?.productName)
                result = true

        }
        return result!!
    }

    fun requestPermission(context: Context, pid: String, delay: Long, userMonitor: USBMonitor) {
        Log.i(TAG, "requestPermission")
        val devList = getUsbDeviceList(context, userMonitor)
        var device: UsbDevice? = null
        if (devList == null || devList.size < 0) {
            return
        }
        for (usbDevice in devList) {
            val productid = String.format("%x", usbDevice?.productId)
            Log.e("Test", "找到了: " + productid)
            if (productid == pid) {
                device = usbDevice
            }
        }
        if (device == null) return
        Thread(Runnable { // wait for camera created
            try {
                Thread.sleep(delay)
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
            Log.e("Test", "打开PID：" + String.format("%x", device?.productId))
            userMonitor.requestPermission(device!!)
        }).start()
    }

    fun requestFingerPermission(context: Context, pid: String, delay: Long, userMonitor: USBMonitor): Boolean {
        val devList = getDeviceList(context, userMonitor)
        var device: UsbDevice? = null
        if (devList == null || !devList.hasNext()) {
            return false
        }
        for (usbDevice in devList) {
            val productid = String.format("%x", usbDevice?.productId)
            //  Log.e("zzkong", "找到了: " + productid);
            if (productid == pid) {
                device = usbDevice
            }
        }
        if (device == null) {
            return false
        }
        Thread(Runnable { // wait for camera created
            try {
                Thread.sleep(delay)
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
            Log.e("zzkong", "打开PID：" + String.format("%x", device?.productId))
            userMonitor.requestPermission(device)

        }).start()
        return true
    }


    /**
     * 打开双目彩色摄像头
     */
    fun openRGBCamera(textureView: TextureView, ctrlBlock: USBMonitor.UsbControlBlock, mIFrameCallbackOne: IFrameCallback?) {
        Log.i(TAG, "openRGBCamera")
        synchronized(mRGBSync) {
            if (mRGBCamera == null)
                mRGBCamera = UVCCamera()
            mRGBCamera?.open(ctrlBlock)
            mRGBCamera?.setFrameCallback(mIFrameCallbackOne, 4)
            if (mRGBPreview != null) {
                mRGBPreview?.release()
                mRGBPreview = null
            }
            try {
                mRGBCamera?.setPreviewSize(UVCCamera.DEFAULT_PREVIEW_WIDTH, UVCCamera.DEFAULT_PREVIEW_HEIGHT, UVCCamera.FRAME_FORMAT_MJPEG)
            } catch (e: IllegalArgumentException) {
                try {
                    // fallback to YUV mode
                    mRGBCamera?.setPreviewSize(UVCCamera.DEFAULT_PREVIEW_WIDTH, UVCCamera.DEFAULT_PREVIEW_HEIGHT, UVCCamera.DEFAULT_PREVIEW_MODE)
                } catch (e1: IllegalArgumentException) {
                    mRGBCamera?.destroy()
                    return
                }
            }

            val st = textureView.surfaceTexture
            if (st != null) {
                mRGBPreview = Surface(st)
                mRGBCamera?.setPreviewDisplay(mRGBPreview)
                mRGBCamera?.startPreview()
                Log.e("zzkong", "打开彩色摄像头成功: ")
            } else {
                Log.e("zzkong", "open 彩色 camera failed ing")
            }

        }
    }

    /**
     * 打开双目彩色摄像头
     */
    fun openRGBCamera(textureView: SurfaceTexture, ctrlBlock: USBMonitor.UsbControlBlock, mIFrameCallbackOne: IFrameCallback?) {
        Log.i(TAG, "openRGBCamera")
        synchronized(mRGBSync) {
            mRGBCamera = UVCCamera()
            mRGBCamera?.open(ctrlBlock)
            mRGBCamera?.setFrameCallback(mIFrameCallbackOne, 4)

            mRGBPreview?.release()
            mRGBPreview = null

            try {
                mRGBCamera?.setPreviewSize(UVCCamera.DEFAULT_PREVIEW_WIDTH, UVCCamera.DEFAULT_PREVIEW_HEIGHT, UVCCamera.FRAME_FORMAT_MJPEG)
            } catch (e: IllegalArgumentException) {
                try {
                    // fallback to YUV mode
                    mRGBCamera?.setPreviewSize(UVCCamera.DEFAULT_PREVIEW_WIDTH, UVCCamera.DEFAULT_PREVIEW_HEIGHT, UVCCamera.DEFAULT_PREVIEW_MODE)
                } catch (e1: IllegalArgumentException) {
                    mRGBCamera?.destroy()
                    return
                }
            }

            if (textureView != null) {
                mRGBPreview = Surface(textureView)
                mRGBCamera?.setPreviewDisplay(mRGBPreview)
                mRGBCamera?.startPreview()
                Log.e("zzkong", "打开彩色摄像头成功: ")
            } else {
                Log.e("zzkong", "open 彩色 camera failed ing")
            }

        }
    }

    /**
     * 打开usb外接摄像头
     */
    fun openRGBCameraUSB(textureView: SurfaceTexture, ctrlBlock: USBMonitor.UsbControlBlock, mIFrameCallbackOne: IFrameCallback?) {
        Log.i(TAG, "openRGBCamera")
        synchronized(mUsbSync) {
            mUsbCamera = UVCCamera()
            mUsbCamera?.open(ctrlBlock)
            mUsbCamera?.setFrameCallback(mIFrameCallbackOne, 4)

            mUsbPreview?.release()
            mUsbPreview = null

            try {
                mUsbCamera?.setPreviewSize(1920, 1080, UVCCamera.FRAME_FORMAT_MJPEG)
            } catch (e: IllegalArgumentException) {
                try {
                    // fallback to YUV mode
                    mUsbCamera?.setPreviewSize(1920, 1080, UVCCamera.DEFAULT_PREVIEW_MODE)
                } catch (e1: IllegalArgumentException) {
                    mUsbCamera?.destroy()
                    return
                }
            }

            if (textureView != null) {
                mUsbPreview = Surface(textureView)
                mUsbCamera?.setPreviewDisplay(mUsbPreview)
                mUsbCamera?.startPreview()
                Log.e("zzkong", "打开usb外接摄像头成功: ")
            } else {
                Log.e("zzkong", "open usb camera failed ing")
            }

        }
    }

    /**
     * usb外接摄像头
     */
    fun requestPermissionUSB(context: Context, delay: Long, userMonitor: USBMonitor) {
        Log.i(TAG, "requestPermissionUSB")
        val devList = getUsbDeviceList(context, userMonitor)
        var device: UsbDevice? = null
        if (devList == null || devList.size < 0) {
            return
        }
        for (usbDevice in devList) {
            val productName = usbDevice?.productName
            Log.e("zzkong", "找到了: " + productName + "  " + usbDevice?.productId);
            if ((productName + "").equals(SPUtils.getInstance().getString(AppConfig.LOCAL_USB_NAME, ""))) {
                device = usbDevice
                break
            }
        }
        if (device == null) return
        Thread {
            try {
                Thread.sleep(delay)
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
            Log.e("zzkong", "打开PID：" + String.format("%x", device?.productId))
            userMonitor.requestPermission(device!!)
        }.start()
    }

    /**
     * 打开双目红外摄像头
     */
    fun openIRCamera(textureView: SurfaceTexture, ctrlBlock: USBMonitor.UsbControlBlock, mIFrameCallbackOne: IFrameCallback) {
        synchronized(mIRSync) {
            mIRCamera = UVCCamera()
            mIRCamera?.open(ctrlBlock)
            mIRCamera?.setFrameCallback(mIFrameCallbackOne, 4)

            mIRPreview?.release()
            mIRPreview = null

            try {
                mIRCamera?.setPreviewSize(UVCCamera.DEFAULT_PREVIEW_WIDTH, UVCCamera.DEFAULT_PREVIEW_HEIGHT, UVCCamera.FRAME_FORMAT_MJPEG)
            } catch (e: IllegalArgumentException) {
                try {
                    // fallback to YUV mode
                    mIRCamera?.setPreviewSize(UVCCamera.DEFAULT_PREVIEW_WIDTH, UVCCamera.DEFAULT_PREVIEW_HEIGHT, UVCCamera.DEFAULT_PREVIEW_MODE)
                } catch (e1: IllegalArgumentException) {
                    mIRCamera?.destroy()
                    return
                }
            }

            if (textureView != null) {
                mIRPreview = Surface(textureView)
                mIRCamera?.setPreviewDisplay(mIRPreview)
                mIRCamera?.startPreview()
                Log.e("zzkong", "打开红外摄像头成功: ")
            } else {
                Log.e("zzkong", "open 红外 camera failed, textureView 没有准备好")
            }
        }
    }

    /**
     * 打开高拍仪摄像头
     */
    fun openHighCamera(textureView: TextureView, ctrlBlock: USBMonitor.UsbControlBlock, mIFrameCallbackOne: IFrameCallback) {
        Log.i(TAG, "openHighCamera")
        synchronized(mHighSync) {
            mHighCamera = UVCCamera()
            mHighCamera?.open(ctrlBlock)
            mHighCamera?.setFrameCallback(mIFrameCallbackOne, 4)

            //  Log.e("zzkong", ": " + mHighCamera?.supportedSize);

            mHighPreview?.release()
            mHighPreview = null

            try {
                mHighCamera?.setPreviewSize(1280, 960, UVCCamera.FRAME_FORMAT_MJPEG)
            } catch (e: IllegalArgumentException) {
                try {
                    mHighCamera?.setPreviewSize(1280, 960, UVCCamera.DEFAULT_PREVIEW_MODE)
                } catch (e1: IllegalArgumentException) {
                    mHighCamera?.destroy()
                    return
                }
            }

            val st = textureView.surfaceTexture
            if (st != null) {
                mHighPreview = Surface(st)
                mHighCamera?.setPreviewDisplay(mHighPreview)
                mHighCamera?.startPreview()
                Log.e("zzkong", "打开高拍仪摄像头成功: ")
            } else {
                mIFrameCallbackOne.onFail()
                Log.e("zzkong", "open 高拍仪 camera failed, textureView 没有准备好")
            }
        }
    }

    /**
     * 关闭双目彩色摄像头
     */
    fun releaseRGBCamera() {
        try {
            Log.i(TAG, "releaseRGBCamera")
            if (mRGBCamera != null) {
                mRGBCamera?.stopPreview()
                mRGBCamera?.destroy()
                mRGBCamera = null
            }
            if (mRGBPreview != null) {
                mRGBPreview?.release()
                mRGBPreview = null
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 关闭双目红外摄像头
     */
    fun releaseIRCamera() {
        try {
            Log.i(TAG, "releaseIRCamera")
            if (mIRCamera != null) {
                mIRCamera?.stopPreview()
                mIRCamera?.destroy()
                mIRCamera = null
            }
            if (mIRPreview != null) {
                mIRPreview?.release()
                mIRPreview = null
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 关闭高拍仪摄像头
     */
    fun releaseHighCamera() {
        try {
            Log.e(TAG, "releaseHighCamera: ");
            if (mHighCamera != null) {
                mHighCamera?.stopPreview()
                mHighCamera?.destroy()
                mHighCamera = null
            }
            Log.e(TAG, "closecloseclose: ")
            if (mHighPreview != null) {
                mHighPreview?.release()
                mHighPreview = null
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 关闭本地usb摄像头
     */
    fun releaseUsbCamera() {
        try {
            Log.e(TAG, "releaseUsbCamera: ");
            if (mUsbCamera != null) {
                mUsbCamera?.stopPreview()

                mUsbCamera?.destroy()
                mUsbCamera = null
            }
            Log.e(TAG, "closecloseclose: ")
            if (mUsbPreview != null) {
                mUsbPreview?.release()
                mUsbPreview = null
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun getUsbDeviceList(context: Context, mUSBMonitor: USBMonitor): List<UsbDevice?> {
        Log.i(TAG, "getUsbDeviceList")
        val deviceFilters = DeviceFilter.getDeviceFilters(context, R.xml.device_filter)
        return mUSBMonitor.getDeviceList(deviceFilters[0])
    }

    fun getDeviceList(context: Context, mUSBMonitor: USBMonitor): MutableIterator<UsbDevice>? {
        Log.i(TAG, "getDeviceList")
        return mUSBMonitor.getDevices()
    }

    fun saveYuv2PNGOrJPG(path: String, data: ByteArray, type: Int): String {
        Log.i(TAG, "saveYuv2PNGOrJPG")
        val yuvImage = YuvImage(data, ImageFormat.NV21, 1280, 960, null)
        var file: File? = null
        val bos = ByteArrayOutputStream(data.size)
        val result = yuvImage.compressToJpeg(Rect(0, 0, 1280, 960), 100, bos)
        if (result) {
            val buffer = bos.toByteArray()
            val files = File(path)
            files.mkdir()

            if (type == 1) {
                file = File(path + getTimeBySystem("yyyy.MM.dd-HH:mm:ss") + ".jpg")
            } else {
                file = File(path + getTimeBySystem("yyyy.MM.dd-HH:mm:ss") + ".png")
            }
            var fos: FileOutputStream? = null
            try {
                fos = FileOutputStream(file)
                // fixing bm is null bug instead of using BitmapFactory.decodeByteArray
                fos.write(buffer)
                fos.close()
                return file.path
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        try {
            bos.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return ""
    }


    private fun getTimeBySystem(dateFormat: String): String? {
        val simpleDateFormat = SimpleDateFormat(dateFormat) // yyyy.MM.dd-HH:mm:ss
        //获取当前时间
        val date = Date(System.currentTimeMillis())
        return simpleDateFormat.format(date)
    }
}