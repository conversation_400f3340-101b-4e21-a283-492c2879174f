package com.gc.notarizationpc.util;

import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Build;
import android.os.Looper;
import android.os.Process;
import android.util.Log;

import com.gc.notarizationpc.ui.HomeActivity;
import com.umeng.analytics.MobclickAgent;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.Thread.UncaughtExceptionHandler;
import java.util.HashMap;
import java.util.Map;

import es.dmoral.toasty.Toasty;

/**
 * UncaughtException处理类,当程序发生Uncaught异常的时候,有该类来接管程序,并记录发送错误报告.
 *
 * <AUTHOR>
 */
public class CrashHandler implements UncaughtExceptionHandler {

    public static final String TAG = "CrashHandler";
    /**
     * CrashHandler 实例
     */
    private static CrashHandler sInstance = new CrashHandler();

    /**
     * 程序的 Context 对象
     */
    private Context mContext;

    /**
     * 系统默认的 UncaughtException 处理类
     */
    private UncaughtExceptionHandler mDefaultHandler;


    /**
     * 用来存储设备信息和异常信息
     */
    private Map<String, String> info = new HashMap<>();

    /**
     * 保证只有一个 CrashHandler 实例
     */
    private CrashHandler() {
        super();
    }

    /**
     * 获取 CrashHandler 实例 ,单例模式
     */
    public static CrashHandler getInstance() {

        return sInstance;
    }

    /**
     * 初始化
     *
     * @param context 上下文环境
     */
    public void init(Context context) {

        mContext = context;

        // 获取系统默认的 UncaughtException 处理器
        mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler();

        // 设置该 CrashHandler 为程序的默认处理器
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    /**
     * 当 UncaughtException 发生时会转入该函数来处理
     */
    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        if (!handleException(ex) && mDefaultHandler != null) {
            // 如果用户没有处理则让系统默认的异常处理器来处理
            mDefaultHandler.uncaughtException(thread, ex);
        } else {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Log.e("Test", "app发生错误:" + e.getMessage());
//                Log2FileUtils.e(TAG, "app报错重启" + e);
                MobclickAgent.reportError(mContext, "app报错重启" + ex);
                e.printStackTrace();
            } finally {
//                //退出程序
                Process.killProcess(Process.myPid());
                System.exit(1);
            }
        }
    }

    /**
     * 自定义错误处理，收集错误信息，发送错误报告等操作均在此完成
     *
     * @param ex 异常信息
     * @return true：如果处理了该异常信息；否则返回 false
     */
    private boolean handleException(final Throwable ex) {
        if (ex == null) {
            return false;
        }
        // 使用 Toast 来显示异常信息
        new Thread() {
            @Override
            public void run() {
                Looper.prepare();
                NotificationManager notificationManager = (NotificationManager) mContext
                        .getSystemService(mContext.NOTIFICATION_SERVICE);
                notificationManager.cancelAll();
                if (ex instanceof OutOfMemoryError) {
                    Toasty.error(mContext, "系统可用内存过低，即将重启App").show();
                } else {
                    Toasty.error(mContext, "发生未知错误，即将重启App").show();
                }
                Looper.loop();
            }
        }.start();

        // 收集设备参数信息
        collectDeviceInfo(mContext);
        // 保存日志文件
        saveCrashInfo2File(ex);
        //crash后重启app
        Intent intent = new Intent(mContext, HomeActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP |
                Intent.FLAG_ACTIVITY_NEW_TASK);
        mContext.startActivity(intent);
        return true;
    }

    /**
     * 收集设备参数信息
     *
     * @param ctx
     */
    public void collectDeviceInfo(Context ctx) {
        try {
            PackageManager pm = ctx.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(ctx.getPackageName(), PackageManager.GET_ACTIVITIES);

            if (pi != null) {
                String versionName = pi.versionName == null ? "null" : pi.versionName;
                String versionCode = pi.versionCode + "";
                info.put("versionName", versionName);
                info.put("versionCode", versionCode);

                //手机系统信息
                info.put("OSVersion", Build.VERSION.RELEASE);
                info.put("SDKVersion", Build.VERSION.SDK_INT + "");

                //手机制造商
                info.put("phoneVendor", Build.MANUFACTURER);
                //手机型号
                info.put("phoneModel", Build.MODEL);
//                Log2FileUtils.e("设备参数信息：\n" + "versionName ==" + versionName + "--------versionCode ==" + versionCode + "\n" + "手机系统信息:" + "\n" +
//                        "OSVersion" + Build.VERSION.RELEASE + "--------SDKVersion" + Build.VERSION.SDK_INT + "\n手机制造商:" + "phoneModel ==" + Build.MANUFACTURER + "\n手机型号:" + "手机型号: " + Build.MODEL);
            }
        } catch (NameNotFoundException e) {
            MobclickAgent.reportError(mContext, "收集设备参数信息时出错" + e);
        }
    }

    /**
     * 保存错误信息到文件中
     *
     * @param ex 异常信息
     * @return 返回文件名称, 便于将文件传送到服务器
     */
    private void saveCrashInfo2File(Throwable ex) {

        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, String> entry : info.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            sb.append(key + "===" + value + "\n");
        }
        //打印到控制台
        Writer writer = new StringWriter();
        PrintWriter printWriter = new PrintWriter(writer);
        ex.printStackTrace(printWriter);
        Throwable cause = ex.getCause();
        while (cause != null) {
            cause.printStackTrace(printWriter);
            cause = cause.getCause();
        }
        printWriter.close();

        String result = writer.toString();
        sb.append(result);
        try {
            //写入到e的日志文件中
            MobclickAgent.reportError(mContext, sb.toString());
//            Log2FileUtils.e(TAG, "app报错重启" + sb.toString());
            Log.e("Test", sb.toString());
        } catch (Exception e) {
            Log.e(TAG, "写入文件时出错", e);
            MobclickAgent.reportError(mContext, e);
            e.printStackTrace();
        }
    }
}