package com.gc.notarizationpc.util;

import android.content.ClipboardManager;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.media.AudioManager;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.drawable.RoundedBitmapDrawable;
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory;

import com.bigkoo.pickerview.builder.OptionsPickerBuilder;
import com.bigkoo.pickerview.listener.OnOptionsSelectListener;
import com.bigkoo.pickerview.view.OptionsPickerView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.BitmapImageViewTarget;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.YearMonthDayBean;
import com.gc.notarizationpc.common.AppConfig;
import com.github.promeg.pinyinhelper.Pinyin;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Random;

import me.goldze.mvvmhabit.utils.GlideRoundedCornersTransform;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;


public class CommonUtil {

    /**
     * @param V 设置下划线
     */
    private final static int kSystemRootStateUnknow = -1;
    private final static int kSystemRootStateDisable = 0;
    private final static int kSystemRootStateEnable = 1;
    private static int systemRootState = kSystemRootStateUnknow;

    public final static int RecordTypeConsult = 1;//记录类型 1:咨询，2:公证
    public final static int RecordTypeVideo = 2;
    public final static int APPLY_PERSON = 1;//1-申请人,2-代理人，3-单位
    public final static int APPLY_COMPANY = 2;
    public final static int APPLY_AGENT = 3;

    public final static int SOURCE_FROM_SPBZ = 2;//视频办证
    public final static int SOURCE_FROM_SPLX = 1;//视频咨询
    public final static int SOURCE_FROM_ORDER = 3;//列表或者详情

    public final static int SOURCE_FROM_YYDJ = 4;

    //出国留学--1->学习 定居移民--2->定居 探亲旅游--3->探亲 商务劳务--5-->劳务 境外其他--4-->其他
    public final static String GO_ABROAD = "1";
    public final static String DINGJU = "2";
    public final static String TANQING = "3";
    public final static String OTHER = "4";
    public final static String LAOWU = "5";

    public final static String DOMESTIC_ECONOMY = "6";

    public final static String PHONE_PREFIX = "+86-";

    // 文件类型
    public final static String FILE_TYPE_ZIP = "ZIP";

    public final static String FILE_TYPE_DOC = "DOC";

    public final static String FILE_TYPE_7Z = "7Z";

    public final static String FILE_TYPE_ACC = "ACC";

    public final static String FILE_TYPE_AVI = "AVI";

    public final static String FILE_TYPE_CSV = "CSV";

    public final static String FILE_TYPE_DOCX = "DOCX";

    public final static String FILE_TYPE_GIF = "GIF";

    public final static String FILE_TYPE_JPG = "JPG";

    public final static String FILE_TYPE_JEPG = "jpeg";

    public final static String FILE_TYPE_MOV = "MOV";

    public final static String FILE_TYPE_MP3 = "MP3";

    public final static String FILE_TYPE_MP4 = "MP4";

    public final static String FILE_TYPE_ODS = "ODS";

    public final static String FILE_TYPE_OFD = "OFD";

    public final static String FILE_TYPE_PDF = "PDF";

    public final static String FILE_TYPE_PNG = "PNG";

    public final static String FILE_TYPE_PPT = "PPT";

    public final static String FILE_TYPE_PPTX = "PPTX";

    public final static String FILE_TYPE_RAR = "RAR";

    public final static String FILE_TYPE_WAV = "WAV";

    public final static String FILE_TYPE_XLSX = "XLSX";

    //支持文件格式：png、jpg、jpeg，docx、doc、xlsx、xls、pdf、ofd、MP4、mov、MP3、avi；一体机端展示不了的格式，直接提示：暂不支持在线预览该格式文件
    public static boolean fileCanNotPreview(String suffixName) {
        if (TextUtils.isEmpty(suffixName)) {
            return true;
        }
        if (suffixName.contains(FILE_TYPE_PNG.toLowerCase(Locale.ROOT)) || suffixName.contains(FILE_TYPE_JPG.toLowerCase(Locale.ROOT)) ||
                suffixName.contains(FILE_TYPE_JEPG.toLowerCase(Locale.ROOT))) {
            return false;
        }
        return true;
    }

    public static void showFileType(Context context, ImageView imageView, int drawable) {
        //查看大图
        RequestOptions options = new RequestOptions();
        //圆角
        options = options.transform(new
                GlideRoundedCornersTransform(context, 6f, GlideRoundedCornersTransform.CornerType.ALL));
//            if (!TextUtils.isEmpty(itemData.getName())) {
        try {
            Glide.with(context)
                    .load(context.getDrawable(drawable))
                    .apply(options).placeholder(R.mipmap.erroimage)
                    .into(imageView);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e("Test", e.getMessage());
        }

    }

    public static String getVersionName(Context context) {
        try {
            //获取packagemanager的实例
            PackageManager packageManager = context.getPackageManager();
            //getPackageName()是你当前类的包名，0代表是获取版本信息
            PackageInfo packInfo = packageManager.getPackageInfo(context.getPackageName(), 0);
            Log.e("TAG", "版本号" + packInfo.versionCode);  //更新软件用的是版本号
            return packInfo.versionName;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "1.0.0";
    }

    public static int compareVersion(String bd, String flw) {
        String[] bdbb = bd.split("\\.", -1);
        String[] flwbb = flw.split("\\.", -1);
        int num = Math.min(bdbb.length, flwbb.length);//取出较小的一位数
        for (int i = 0; i < num; i++) {
            int a1 = "".equals(bdbb[i]) ? 0 : Integer.parseInt(bdbb[i]);
            int a2 = "".equals(flwbb[i]) ? 0 : Integer.parseInt(flwbb[i]);
            if (a1 != a2) {
                return a1 - a2;
            }
        }
        return bdbb.length - flwbb.length;
    }

    public static int getDrawableByFileType(String suffixName) {
        if (suffixName.contains(FILE_TYPE_ZIP.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_zip;
        } else if (suffixName.contains(FILE_TYPE_DOC.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_doc;
        } else if (suffixName.contains(FILE_TYPE_7Z.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_7z;
        } else if (suffixName.contains(FILE_TYPE_ACC.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_acc;
        } else if (suffixName.contains(FILE_TYPE_AVI.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_avi;
        } else if (suffixName.contains(FILE_TYPE_CSV.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_csv;
        } else if (suffixName.contains(FILE_TYPE_DOCX.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_docx;
        } else if (suffixName.contains(FILE_TYPE_GIF.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_gif;
        } else if (suffixName.contains(FILE_TYPE_MOV.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_mov;
        } else if (suffixName.contains(FILE_TYPE_MP3.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_mp3;
        } else if (suffixName.contains(FILE_TYPE_MP4.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_mp4;
        } else if (suffixName.contains(FILE_TYPE_ODS.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_ods;
        } else if (suffixName.contains(FILE_TYPE_PDF.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_pdf;
        } else if (suffixName.contains(FILE_TYPE_PPT.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_ppt;
        } else if (suffixName.contains(FILE_TYPE_OFD.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_ofd;
        } else if (suffixName.contains(FILE_TYPE_PPTX.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_pptx;
        } else if (suffixName.contains(FILE_TYPE_RAR.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_rar;
        } else if (suffixName.contains(FILE_TYPE_WAV.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_wav;
        } else if (suffixName.contains(FILE_TYPE_XLSX.toLowerCase(Locale.ROOT))) {
            return R.mipmap.icon_xlsx;
        }
        return R.mipmap.erroimage;
    }

    public static void setFlags(TextView V) {
        V.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        V.getPaint().setAntiAlias(true);//抗锯齿
    }

    public static Bitmap getBitmap(Context context, int resId) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        TypedValue value = new TypedValue();
        context.getResources().openRawResource(resId, value);
        options.inTargetDensity = value.density;
        options.inScaled = false; // Do not scale
        return BitmapFactory.decodeResource(context.getResources(), resId, options);
    }

    /**
     * 设置系统音量
     *
     * @param context
     * <AUTHOR>
     */
    public static void setSystemVoice(Context context) {
        AudioManager am = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        Log.d("Test", "音量最大值 ==" + am.getStreamMaxVolume(AudioManager.STREAM_MUSIC));
        // TODO: 2022/10/20 测试暂时注释
        am.setStreamVolume(AudioManager.STREAM_MUSIC, 1 + am.getStreamMaxVolume(AudioManager.STREAM_MUSIC) / 2, AudioManager.FLAG_PLAY_SOUND);
//        am.setStreamVolume(AudioManager.STREAM_MUSIC, 3, AudioManager.FLAG_PLAY_SOUND);
    }

    public static void loadRoundeImg(ImageView imageView, String url) {
        Glide.with(imageView.getContext())
                .asBitmap()
                .load(url)
                .into(new BitmapImageViewTarget(imageView) {
                    @Override
                    protected void setResource(final Bitmap resource) {
                        RoundedBitmapDrawable circularBitmapDrawable =
                                RoundedBitmapDrawableFactory.create(imageView.getResources(), resource);
                        circularBitmapDrawable.setCircular(false);
                        imageView.setImageDrawable(circularBitmapDrawable);
                    }
                });
    }

    public static File bitmapToFile(Bitmap bitmap) {
        File file = new File(AppConfig.APP_PATH_ROOT + System.currentTimeMillis() + ".png");
        try {
            file.createNewFile();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.PNG, 0, bos);
            byte[] bitmapData = bos.toByteArray();
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(bitmapData);
            fos.flush();
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }

    /**
     * 获取版本号
     *
     * @return 当前应用的版本号
     */
    public static String getVersion(Context context) {
        try {
            PackageManager manager = context.getPackageManager();
            PackageInfo info = manager.getPackageInfo(context.getPackageName(), 0);
            String version = info.versionName;
            return version;
        } catch (Exception e) {
            e.printStackTrace();
            return "0";
        }
    }

    public static void setDrawableLeft(Context context, TextView textView, int res) {
        Drawable search = context.getResources().getDrawable(res);
        search.setBounds(0, 0, search.getMinimumWidth(), search.getMinimumHeight());//这里设置图片宽高
        textView.setCompoundDrawables(search, null, null, null);
    }

    public static void displayImage(final ImageView imageView, String url) {
        RequestOptions options = new RequestOptions();
        //圆角
        options = options.transform(new
                GlideRoundedCornersTransform(Utils.getContext(), 6f, GlideRoundedCornersTransform.CornerType.ALL));
        if (!TextUtils.isEmpty(url) && (url.startsWith("http://") || url.startsWith("https://"))) {

            Glide.with(Utils.getContext())
                    .asBitmap()
                    .load(url)
                    .apply(options).error(R.mipmap.erroimage).placeholder(R.mipmap.erroimage)
                    .into(new BitmapImageViewTarget(imageView) {
                        @Override
                        protected void setResource(final Bitmap resource) {
                            RoundedBitmapDrawable circularBitmapDrawable =
                                    RoundedBitmapDrawableFactory.create(imageView.getResources(), resource);
                            circularBitmapDrawable.setCircular(false);
                            imageView.setImageDrawable(circularBitmapDrawable);
                        }
                    });
        } else {
            Glide.with(Utils.getContext())
                    .asBitmap()
                    .load(url)
                    .apply(options)
                    .into(new BitmapImageViewTarget(imageView) {
                        @Override
                        protected void setResource(final Bitmap resource) {
                            RoundedBitmapDrawable circularBitmapDrawable =
                                    RoundedBitmapDrawableFactory.create(imageView.getResources(), resource);
                            circularBitmapDrawable.setCircular(false);
                            imageView.setImageDrawable(circularBitmapDrawable);
                        }
                    });
        }

    }

    /**
     * 隐藏软键盘
     */
    public static void hideSoftInput(InputMethodManager mInputMethodManager, View view) {
        mInputMethodManager
                .hideSoftInputFromWindow(view.getWindowToken(), 0);
    }


    /**
     * 显示软键盘
     */
    public static void showSoftInput(InputMethodManager mInputMethodManager, View view) {
        mInputMethodManager
                .showSoftInput(view, 0);
    }


    private static long lastClickTime = 0;

    //取1秒内的第一次点击进行响应。
    public synchronized static boolean isFastClick() {
        long time = System.currentTimeMillis();
        if (time - lastClickTime < 1000) {
            return true;
        }
        lastClickTime = time;
        return false;
    }

    public static void setDrawableBottom(Context context, TextView textView, Integer res) {
        if (res != null) {
            Drawable search = context.getResources().getDrawable(res);
            textView.setCompoundDrawablePadding(10);
            search.setBounds(0, 0, search.getMinimumWidth(), search.getMinimumHeight());//这里设置图片宽高
        }

    }

    /**
     * 将字符串转为Date
     *
     * @param strDate 时间字符串
     * @param pattern 时间格式
     * @return Date
     */
    public static String dateStringToString(String strDate, String pattern) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy年MM月dd日", new Locale("CHINESE", "CHINA"));
        try {
            Date date = df.parse(strDate);
            if (date != null) {
                SimpleDateFormat df1 = new SimpleDateFormat(pattern);
                return df1.format(date);
            }
        } catch (ParseException e) {
            Log.e("tag", e.getMessage());
        }
        return null;
    }

    /**
     * 复制文本到剪切板
     *
     * @param text 文本
     */
    public static void copy(AppCompatActivity aty, String text) {

        ClipboardManager clipboard = (ClipboardManager) aty.getSystemService(Context
                .CLIPBOARD_SERVICE);
        android.content.ClipData clip = android.content.ClipData.newPlainText(null, text);
        clipboard.setPrimaryClip(clip);
    }

    /**
     * 获取人气显示 默认保留后四位
     *
     * @param popularity
     * @return
     */
    public static String popularity(int popularity) {
        if (popularity < 10000) {
            return String.valueOf(popularity);
        } else {
            double popularityD = (double) popularity / 10000;
            BigDecimal b = new BigDecimal(popularityD);
            double d = b.setScale(4, BigDecimal.ROUND_DOWN).doubleValue();
            return new StringBuilder().append(d).append("万").toString();
        }
    }

    /**
     * 保留两位小数 四舍五入
     *
     * @param popularity
     * @return
     */
    public static String popularityTwo(Double popularity) {
        BigDecimal b = new BigDecimal(popularity);
        double d = b.setScale(2, BigDecimal.ROUND_UP).doubleValue();
        return new StringBuilder().append(d).toString();
    }

    public static double popularityTwod(Double popularity) {
        BigDecimal b = new BigDecimal(popularity);
        double d = b.setScale(2, BigDecimal.ROUND_UP).doubleValue();
        return d;
    }

    /**
     * 保留两位小数 截取
     *
     * @param popularity
     * @return
     */
    public static String popularityTwoDown(Double popularity) {
        BigDecimal b = new BigDecimal(popularity);
        double d = b.setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
        return new StringBuilder().append(d).toString();
    }

    /**
     * 保留两位小数
     *
     * @return
     */
    public static String popularityTwoDou(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        DecimalFormat df = new DecimalFormat("0.00");
        String result = df.format(b1.multiply(b2).doubleValue());
        return result;
    }

    /**
     * 保留两位小数
     *
     * @return
     */
    public static Double popularityPrice(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.multiply(b2).doubleValue();
    }

    //  f=  "0.00"
    public static String formatDouble(String f, Double d) {
        DecimalFormat df = new DecimalFormat(f);
        return df.format(d);
    }

    /**
     * 获取数据显示 保留小数点后一位
     *
     * @return
     */
    public static String popularityOne(String sPopularity) {
        float popularity = 0;
        try {
            popularity = Float.valueOf(sPopularity);
            if (popularity < 10000) {
                return sPopularity;
            } else if (popularity > 10000 && popularity < 100000000) {
                double popularityD = (double) popularity / 10000;
                BigDecimal b = new BigDecimal(popularityD);
                double d = b.setScale(1, BigDecimal.ROUND_DOWN).doubleValue();
                return new StringBuilder().append(d).append("万").toString();
            } else {
                double popularityD = (double) popularity / 100000000;
                BigDecimal b = new BigDecimal(popularityD);
                double d = b.setScale(1, BigDecimal.ROUND_DOWN).doubleValue();
                return new StringBuilder().append(d).append("亿").toString();
            }
        } catch (NumberFormatException e) {
            return "0";
        }
    }

    /**
     * 判断str1中包含str2的个数
     *
     * @param str1
     * @param str2
     * @return counter
     */
    public static int countStr(String str1, char str2) {
        int count = 0;
        for (int i = 0; i < str1.length(); i++) {
            if (str1.charAt(i) == str2) {
                count++;
            }
        }
        return count;
    }


    /*计算最大页数*/
    public static int getMaxPage(int count, int perpage) {
        if (perpage == 0) {
            return 1;
        } else {
            return count % perpage == 0 ? count / perpage : count / perpage + 1;
        }
    }

    public static String getUUId(Context context) {
        String identity = (String) SPUtils.getInstance().readObject(context, "identity");
        if (identity == null) {
            identity = java.util.UUID.randomUUID().toString().replace("-", "_");
            SPUtils.getInstance().saveObject(context, "identity", identity);
        }
        return identity;
    }

    /**
     * 判断是否root  无弹框
     *
     * @return
     */
    public static boolean isRootSystem() {
        if (systemRootState == kSystemRootStateEnable) {
            return true;
        } else if (systemRootState == kSystemRootStateDisable) {
            return false;
        }
        File f = null;
        final String kSuSearchPaths[] = {"/system/bin/", "/system/xbin/", "/system/sbin/", "/sbin/", "/vendor/bin/"};
        try {
            for (int i = 0; i < kSuSearchPaths.length; i++) {
                f = new File(kSuSearchPaths[i] + "su");
                if (f != null && f.exists()) {
                    systemRootState = kSystemRootStateEnable;
                    return true;
                }
            }
        } catch (Exception e) {
        }
        systemRootState = kSystemRootStateDisable;
        return false;
    }

    /**
     * String字符串为空时，展示""
     *
     * @param str
     * @return
     */
    public static String dealEmptyDataEmpty(String str) {

        return TextUtils.isEmpty(str) ? "" : str;
    }

    /**
     * 根据中文获取首字母
     *
     * @param chinese
     * @return
     */
    public static String getFirstLetter(String chinese) {
        if (chinese == null || chinese.isEmpty()) {
            return "#";
        }
        char firstChar = chinese.charAt(0);
        if (Pinyin.isChinese(firstChar)) {
            return String.valueOf(Pinyin.toPinyin(firstChar).charAt(0)).toUpperCase();
        }
        return "#";
    }

    public static String convertUserPurpose(int code) {
        ////本系统中：01-探亲,02-定居,03-学习,04-继承,05-就业,06-结婚,07-探病,08-奔丧,09-扶养亲属,10-领取养老金,11-对外贸易,12-诉讼（索赔,13-提供劳务,14-申请知识产,15-招标投标,16-签订合同,17-减免税,18-考察访问,99-其他
        String purpose = "其他";
        switch (code) {
            case 1:
                purpose = "探亲";
                break;
            case 2:
                purpose = "定居";
                break;
            case 3:
                purpose = "学习";
                break;
            case 4:
                purpose = "继承";
                break;
            case 5:
                purpose = "就业";
                break;
            case 6:
                purpose = "结婚";
                break;
            case 7:
                purpose = "探病";
                break;
            case 8:
                purpose = "奔丧";
                break;
            case 9:
                purpose = "扶养亲属";
                break;
            case 10:
                purpose = "领取养老金";
                break;
            case 11:
                purpose = "对外贸易";
                break;
            case 12:
                purpose = "诉讼";
                break;
            case 13:
                purpose = "提供劳务";
                break;
            case 14:
                purpose = "招标投标";
                break;
            case 15:
                purpose = "签订合同";
                break;
            default:
                purpose = "其他";
                break;

        }
        return purpose;
    }


    public static void disabledView(final View v) {
        v.setClickable(false);// 延迟1秒，恢复点击事件
        new Handler().postDelayed(new Runnable() {

            @Override
            public void run() {
                v.setClickable(true);
            }
        }, 2000);
    }


    public static String getBirthDateFromIDCard(String idCardNumber) {
        if (idCardNumber == null || idCardNumber.isEmpty()) {
            return "";
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String birthDateStr = "";

        if (idCardNumber.length() == 18) {
            birthDateStr = idCardNumber.substring(6, 14);
        } else if (idCardNumber.length() == 15) {
            birthDateStr = "19" + idCardNumber.substring(6, 12);
        }

        try {
            birthDateStr = format.format(dateFormat.parse(birthDateStr));
            return birthDateStr;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }


    /**
     * 根据身份证号获取性别
     */
    public static String getGenderFromIDCard(String idCardNumber) {
        String gender = "";

        if (idCardNumber.length() == 18) {
            // 获取身份证号的倒数第二位
            int genderDigit = Integer.parseInt(idCardNumber.substring(idCardNumber.length() - 2, idCardNumber.length() - 1));
            // 判断奇偶性
            gender = (genderDigit % 2 == 0) ? "0" : "1";//0女性，1男性
        } else if (idCardNumber.length() == 15) {
            try {
                // 获取身份证号的最后一位
                int genderDigit = Integer.parseInt(idCardNumber.substring(idCardNumber.length() - 1));
                // 判断奇偶性
                gender = (genderDigit % 2 == 0) ? "0" : "1";
            } catch (Exception exc) {
                exc.printStackTrace();
            }
        }

        return gender;
    }
    /**
     * 获取五位随机的不重复的数字
     *
     * @return
     */
    public static int randomNum() {
        int[] array = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
        Random rand = new Random();
        for (int i = 10; i > 1; i--) {
            int index = rand.nextInt(i);
            int tmp = array[index];
            array[index] = array[i - 1];
            array[i - 1] = tmp;
        }
        int result = 0;
        for (int i = 0; i < 5; i++) {
            result = result * 10 + array[i];
        }
        if (String.valueOf(result).length() == 5) {
            return result;
        } else {
            return randomNum();
        }
    }

    /**
     *
     */

    private static ArrayList<YearMonthDayBean> options1Items = new ArrayList<>();

    private static ArrayList<ArrayList<String>> options2Items = new ArrayList<>();

    private static OptionsPickerView pvOptions;


    private static String[] timeList;

    private static String[] timeAllList;

    public static void showTimePicker(Context context, PopwindowUtil.ResultSecondListener resultListener) {
        dealTime();
//        chooseTime(0,0,resultListener);
        pvOptions = new OptionsPickerBuilder(context, new OnOptionsSelectListener() {
            @Override
            public void onOptionsSelect(int options1, int options2, int options3, View v) {
                Log.e("onOptionsSelect", "options1:" + options1 + "options2:" + options2);
                chooseTime(options1, options2, resultListener);
            }
        })
                .setTitleText("")
                .setContentTextSize(20)//设置滚轮文字大小
                .setDividerColor(Utils.getContext().getResources().getColor(me.goldze.mvvmhabit.R.color.color_A5A5A5))//设置分割线的颜色
                .setSelectOptions(0, 0)//默认选中项
                .setBgColor(Color.WHITE)
                .setTitleBgColor(Color.WHITE)
                .setTitleColor(Color.WHITE)
                .setCancelColor(Color.GRAY)
                .setSubmitColor(Utils.getContext().getResources().getColor(me.goldze.mvvmhabit.R.color.blue))
                .setTextColorCenter(Utils.getContext().getResources().getColor(me.goldze.mvvmhabit.R.color.color3333))
                .isRestoreItem(true)//切换时是否还原，设置默认选中第一项。
                .isCenterLabel(false) //是否只显示中间选中项的label文字，false则每项item全部都带有label。
                .setLabels("", "", "")
                .setOutSideColor(0x00000000) //设置外部遮罩颜色
                .build();

        pvOptions.setPicker(options1Items, options2Items);//二级选择器
        pvOptions.show(); //弹出条件选择器
    }

    private static void dealTime() {
//        cal.set(2024,12,29,10,0,0);
        String year = getDateStr(0).get(0);
        String month = getDateStr(0).get(1);
        String day = getDateStr(0).get(2);
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        options1Items.clear();
        options2Items.clear();
        timeList = new String[]{"09:00 - 09:30", "09:30 - 10:00", "10:00 - 10:30", "10:30 - 11:00", "11:00 - 11:30", "11:30 - 12:00", "12:00 - 12:30", "12:30 - 13:00", "13:00 - 13:30", "13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00", "17:00 - 17:30"};
        timeAllList = new String[]{"09:00 - 09:30", "09:30 - 10:00", "10:00 - 10:30", "10:30 - 11:00", "11:00 - 11:30", "11:30 - 12:00", "12:00 - 12:30", "12:30 - 13:00", "13:00 - 13:30", "13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00", "17:00 - 17:30"};
        if (hour == 9) {
            if (minute < 30) {
                timeList = new String[]{"09:30 - 10:00", "10:00 - 10:30", "10:30 - 11:00", "11:00 - 11:30", "11:30 - 12:00", "12:00 - 12:30", "12:30 - 13:00", "13:00 - 13:30", "13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00"};

            } else {
                timeList = new String[]{"10:00 - 10:30", "10:30 - 11:00", "11:00 - 11:30", "11:30 - 12:00", "12:00 - 12:30", "12:30 - 13:00", "13:00 - 13:30", "13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00"};

            }
        } else if (hour == 10) {
            if (minute < 30) {
                timeList = new String[]{"10:30 - 11:00", "11:00 - 11:30", "11:30 - 12:00", "12:00 - 12:30", "12:30 - 13:00", "13:00 - 13:30", "13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00"};

            } else {
                timeList = new String[]{"11:00 - 11:30", "11:30 - 12:00", "12:00 - 12:30", "12:30 - 13:00", "13:00 - 13:30", "13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00"};

            }
        } else if (hour == 11) {
            if (minute < 30) {
                timeList = new String[]{"11:30 - 12:00", "12:00 - 12:30", "12:30 - 13:00", "13:00 - 13:30", "13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00"};

            } else {
                timeList = new String[]{"12:00 - 12:30", "12:30 - 13:00", "13:00 - 13:30", "13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00", "17:00 - 17:30"};

            }
        } else if (hour == 12) {
            if (minute < 30) {
                timeList = new String[]{"12:30 - 13:00", "13:00 - 13:30", "13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00", "17:00 - 17:30"};

            } else {
                timeList = new String[]{"13:00 - 13:30", "13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00", "17:00 - 17:30"};

            }
        } else if (hour == 13) {
            if (minute < 30) {
                timeList = new String[]{"13:30 - 14:00", "14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00", "17:00 - 17:30"};

            } else {
                timeList = new String[]{"14:00 - 14:30", "14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00", "17:00 - 17:30"};
            }
        } else if (hour == 14) {
            if (minute < 30) {
                timeList = new String[]{"14:30 - 15:00", "15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00", "17:00 - 17:30"};

            } else {
                timeList = new String[]{"15:00 - 15:30", "15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00", "17:00 - 17:30"};

            }
        } else if (hour == 15) {
            if (minute < 30) {
                timeList = new String[]{"15:30 - 16:00", "16:00 - 16:30", "16:30 - 17:00", "17:00 - 17:30"};

            } else {
                timeList = new String[]{"16:00 - 16:30", "16:30 - 17:00"};
            }
        } else if (hour == 16) {
            if (minute < 30) {
                timeList = new String[]{"16:30 - 17:00", "17:00 - 17:30"};
            } else {
                timeList = new String[]{"17:00 - 17:30"};
            }
        }
        if (hour < 17) {
            options1Items.add(new YearMonthDayBean(1, year + "年" + month + "月" + day + "日"));
            day = getDateStr(1).get(2);
            month = getDateStr(1).get(1);
            year = getDateStr(1).get(0);
            options1Items.add(new YearMonthDayBean(2, year + "年" + month + "月" + day + "日"));
            day = getDateStr(2).get(2);
            month = getDateStr(2).get(1);
            year = getDateStr(2).get(0);

            options1Items.add(new YearMonthDayBean(3, year + "年" + month + "月" + day + "日"));
            day = getDateStr(3).get(2);
            month = getDateStr(3).get(1);
            year = getDateStr(3).get(0);

            options1Items.add(new YearMonthDayBean(4, year + "年" + month + "月" + day + "日"));
            day = getDateStr(4).get(2);
            month = getDateStr(4).get(1);
            year = getDateStr(4).get(0);

            options1Items.add(new YearMonthDayBean(5, year + "年" + month + "月" + day + "日"));
            day = getDateStr(5).get(2);
            month = getDateStr(5).get(1);
            year = getDateStr(5).get(0);

            options1Items.add(new YearMonthDayBean(6, year + "年" + month + "月" + day + "日"));
            day = getDateStr(6).get(2);
            month = getDateStr(6).get(1);
            year = getDateStr(6).get(0);

            options1Items.add(new YearMonthDayBean(7, year + "年" + month + "月" + day + "日"));
        } else {
            day = getDateStr(1).get(2);
            month = getDateStr(1).get(1);
            year = getDateStr(1).get(0);

            options1Items.add(new YearMonthDayBean(1, year + "年" + month + "月" + day + "日"));
            day = getDateStr(2).get(2);
            month = getDateStr(2).get(1);
            year = getDateStr(2).get(0);

            options1Items.add(new YearMonthDayBean(2, year + "年" + month + "月" + day + "日"));
            day = getDateStr(3).get(2);
            month = getDateStr(3).get(1);
            year = getDateStr(3).get(0);

            options1Items.add(new YearMonthDayBean(3, year + "年" + month + "月" + day + "日"));
            day = getDateStr(4).get(2);
            month = getDateStr(4).get(1);
            year = getDateStr(4).get(0);

            options1Items.add(new YearMonthDayBean(4, year + "年" + month + "月" + day + "日"));
            day = getDateStr(5).get(2);
            month = getDateStr(5).get(1);
            year = getDateStr(5).get(0);

            options1Items.add(new YearMonthDayBean(5, year + "年" + month + "月" + day + "日"));
            day = getDateStr(6).get(2);
            month = getDateStr(6).get(1);
            year = getDateStr(6).get(0);

            options1Items.add(new YearMonthDayBean(6, year + "年" + month + "月" + day + "日"));
            day = getDateStr(7).get(2);
            month = getDateStr(7).get(1);
            year = getDateStr(7).get(0);

            options1Items.add(new YearMonthDayBean(7, year + "年" + month + "月" + day + "日"));
        }


        ArrayList<String> options2Items_01 = new ArrayList<>();
        options2Items_01.addAll(Arrays.asList(timeList));
        ArrayList<String> options2Items_02 = new ArrayList<>();
        options2Items_02.addAll(Arrays.asList(timeAllList));
        ArrayList<String> options2Items_03 = new ArrayList<>();
        options2Items_03.addAll(Arrays.asList(timeAllList));
        ArrayList<String> options2Items_04 = new ArrayList<>();
        options2Items_04.addAll(Arrays.asList(timeAllList));
        ArrayList<String> options2Items_05 = new ArrayList<>();
        options2Items_05.addAll(Arrays.asList(timeAllList));
        ArrayList<String> options2Items_06 = new ArrayList<>();
        options2Items_06.addAll(Arrays.asList(timeAllList));
        ArrayList<String> options2Items_07 = new ArrayList<>();
        options2Items_07.addAll(Arrays.asList(timeAllList));
        options2Items.add(options2Items_01);
        options2Items.add(options2Items_02);
        options2Items.add(options2Items_03);
        options2Items.add(options2Items_04);
        options2Items.add(options2Items_05);
        options2Items.add(options2Items_06);
        options2Items.add(options2Items_07);
    }

    private static void chooseTime(int index, int index1, PopwindowUtil.ResultSecondListener resultSecondListener) {
        YearMonthDayBean yearMonthDayBean = options1Items.get(index);
        String timeString = "";
        if (index == 0) {
            timeString = Arrays.asList(timeList).get(index1);
        } else {
            timeString = Arrays.asList(timeAllList).get(index1);
        }

        List tempTimeList = Arrays.asList(timeString.split(" - "));
        String newTimeString = yearMonthDayBean.getName().replace("年", "-").replace("月", "-").replace("日", " ");
        Log.e("onOptionsSelectChanged", newTimeString + timeString);
        if (tempTimeList != null && tempTimeList.size() > 1) {
            String startTime = convertData(newTimeString + tempTimeList.get(0) + ":00");
            String endTime = convertData(newTimeString + tempTimeList.get(1) + ":00");

            resultSecondListener.result(startTime);
            resultSecondListener.secondResult(endTime);
        }
    }

    public static String convertData(String dateStr) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-M-d HH:mm:ss");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Date date = null;
        try {
            date = inputFormat.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        String formattedDate = outputFormat.format(date);
        return formattedDate;
    }

    public static List<String> getDateStr(int Num) {
        List<String> tempList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
//        calendar.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
//        calendar.set(calendar.get(Calendar.YEAR),calendar.get(Calendar.MONTH)+1,calendar.get(Calendar.DAY_OF_MONTH),0,0,0);
        calendar.add(Calendar.DAY_OF_MONTH, Num);
        tempList.add(String.valueOf(calendar.get(Calendar.YEAR)));
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        month++;
        String monthString = "";
        String dayString = "";
        if (month < 9) {
            monthString = "0" + month;
        } else {
            monthString += month;
        }
        if (day < 9) {
            dayString = "0" + day;
        } else {
            dayString += day;
        }
        tempList.add(monthString);
        tempList.add(dayString);
        return tempList;
    }

}

