package com.gc.notarizationpc.util;

import android.text.TextUtils;
import android.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.util
 * @Description:
 * @Author: x<PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/29
 */
public class AESUtil {

    private static String ALGORITHMSTR = "AES/ECB/PKCS7Padding";
    private static String ALGORITHM = "AES";

    public static String encode(String content, String key) {
        if (TextUtils.isEmpty(content)) {
            return content;
        }
        if (key.isEmpty()) {
            return content;
        }
        try {
            byte[] result = encrypt(key, content); // Assuming encrypt is a method defined elsewhere
            String s = new String(Base64.encode(result, Base64.DEFAULT));
            if (s.contains("\n")) {
                return s.replace("\n", "");
            }
            return s;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public  static byte[] encrypt(String encryptKey, String content) throws Exception {
        // Create AES key
        SecretKeySpec secretKeySpec = new SecretKeySpec(encryptKey.getBytes(), ALGORITHM);
        // Create cipher
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        // Initialize cipher in encrypt mode
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        // Encrypt
        return cipher.doFinal(content.getBytes("UTF-8"));
    }
}
