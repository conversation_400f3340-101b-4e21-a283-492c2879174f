package com.gc.notarizationpc.common;

import static me.goldze.mvvmhabit.utils.ToastUtils.toastError;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.Gravity;
import android.view.Window;
import android.view.WindowManager;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.request.SignDocRequest;
import com.gc.notarizationpc.data.model.response.DocInfoResponse;
import com.gc.notarizationpc.data.model.response.MaterialListResponse;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.PopwindowUtil;

import java.io.File;
import java.util.List;

import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.Utils;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

public class CommonRequest {

    public static void getNotaryUse(String notaryId, PopwindowUtil.ResultListener resultListener) {
        RequestUtil.getNotaryUse(notaryId, new MyObserver() {
            @Override
            public void onSuccess(Object response) {
                resultListener.result(response);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.getDocumentsFail) : errorMsg);
            }
        });
    }

    //查询所需材料
    public static void listMaterialByCaseInfoId(String caseInfoId, PopwindowUtil.ResultSecondListener resultListener) {
        RequestUtil.listMaterialByCaseInfoId(caseInfoId, new MyObserver<List<MaterialListResponse>>() {
            @Override
            public void onSuccess(List<MaterialListResponse> result) {
                resultListener.result(result);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                resultListener.secondResult(errorMsg);
//                toastError(errorMsg == null ? "获取所需材料失败" : errorMsg);
            }
        });

    }

    // 阅读文书
    public static void readDocument(String mCaseInfoId, PopwindowUtil.ResultSecondListener<List<DocInfoResponse>> resultListener) {
        RequestUtil.listApplicantsAndDocuments(mCaseInfoId, new MyObserver<List<DocInfoResponse>>() {
            @Override
            public void onSuccess(List<DocInfoResponse> result) {
                resultListener.result(result);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.getDocumentsFail) : errorMsg);
                resultListener.secondResult(null);
            }
        });
    }

    // 签字 上传界面

    public static void uploadSignPic(Bitmap bitmap, PopwindowUtil.ResultSecondListener resultListener) {
        File file = CommonUtil.bitmapToFile(bitmap);
        RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);
        MultipartBody.Part body = MultipartBody.Part.createFormData("file", file.getName(), requestFile);
        RequestUtil.uploadFile(body, new MyObserver<UploadFileBean>() {
            @Override
            public void onSuccess(UploadFileBean result) {
                if (result != null) {
                    resultListener.result(result);
//                    uploadFileSingleLiveEvent.setValue(result);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
//                dismissDialog();
                resultListener.secondResult(errorMsg);
                Log.e("Test", "图片上传失败");
//                resultListener.otherEvent(WorkStatusEnum.UPLOAD_SIGN_FAIL.code);
//                getInfoErrorEvent.setValue(WorkstatusEnum.UPLOAD_SIGN_FAIL.code);
            }
        });
    }

    //公证文书签名(批量)
    public static void signMultiDocument(SignDocRequest request, PopwindowUtil.ResultSecondListener resultListener) {
        RequestUtil.signMultiDocument(request, new MyObserver<Object>() {
            @Override
            public void onSuccess(Object result) {
                //一个当事人签名针对的是当前所有的文书
                resultListener.result(result);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                resultListener.secondResult(errorMsg);
//                getInfoErrorEvent.setValue(WorkstatusEnum.SIGN_DOC_FAIL.code);
            }
        });

    }

    // windows 设置
    public static void settingDialog(Dialog dialog, Context context) {
        Window dialogWindow = dialog.getWindow();
        dialogWindow.getDecorView().setBackgroundColor(context.getResources().getColor(android.R.color.white));
        dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
        // dialogWindow.setWindowAnimations(R.style.mystyle);
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        lp.alpha = 1.0f;
        dialogWindow.setAttributes(lp);
        dialogWindow.setGravity(Gravity.RIGHT);
        dialog.setCanceledOnTouchOutside(false);

    }

}
