package com.gc.notarizationpc.common

import android.Manifest

/**
 * 类型管理
 *
 * @date 2017/8/26 上午10:41
 */
object FusionType {
    val STORAGE = arrayOf(
            Manifest.permission.CAMERA)

    interface SPKey {
        companion object {
            const val USER_INFO = "user_info"
            const val USER_ACCOUNT = "user_account"
            const val AD_INFO = "AD_INFO"
        }
    }

    interface EBKey {
        companion object {
            const val EB_LOGIN_SUCCESS = "EB_LOGIN_SUCCESS"

            //刷新个人中心
            const val EB_REFRESH_USER = "EB_REFRESH_USER"
            const val EB_LOGOUT_SUCCESS = "LOGOUT_SUCCESS"
        }
    }
}