package com.gc.notarizationpc.common

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import com.example.framwork.base.QuickActivity
import com.example.framwork.baseapp.AppManager
import com.gc.notarizationpc.AppApplication
import com.gc.notarizationpc.R
import com.gc.notarizationpc.model.UserInfo
import com.gc.notarizationpc.ui.*

abstract class BaseActivity : QuickActivity() {
    var userInfo: UserInfo? = null
    var notaryId: String? = null
    var mApplication: AppApplication? = null
    var notaryPublicId: String? = null
    override fun initPUserInfo() {
        mApplication = application as AppApplication
        userInfo = mApplication?.userInfo
        notaryId = mApplication?.notaryId
        notaryPublicId = mApplication?.notaryPublicId
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    protected fun readyGo(clazz: Class<*>) {
        val intent = Intent(this, clazz)
        startActivity(intent)
    }

    protected fun readyGo(intent: Intent) {
        startActivity(intent)
    }

    override fun setStatusBarTrans(): Boolean {
        return true
    }

    protected fun showTwoDialog(tips: String?, title: String?, leftTxt: String?, rightTxt: String?, listener: IDialogListener?) {
        showTwoBtnDialog(tips, title, leftTxt, rightTxt, R.color.black, R.color.colorTheme, 0, 0, R.color.black, R.color.gray_98, !TextUtils.isEmpty(tips), listener, null)
    }

    protected fun showOneDialog(title: String?, leftTxt: String?, listener: () -> Unit) {
        showOneBtnDialog(null, title, leftTxt, R.color.colorTheme, 0, 0, R.color.black, false, true, listener)
    }

    protected fun showOneDialog(title: String?, content: String?, leftTxt: String?, listener: IOneDialogListener?) {
        showOneBtnDialog(title, content, leftTxt, R.color.colorTheme, 0, 0, R.color.black, true, true, listener)
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            when (mActivity.componentName.className) {
                VideoNotarizationActivity::class.java.name -> {
                    showOneDialog("是否退出当前公证", "退出") {
                        AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
                    }
                }
                VideoNotarizationUSBActivity::class.java.name -> {
                    showOneDialog("是否退出当前公证", "退出") {
                        AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
                    }
                }
                RemoteNotarizationActivity::class.java.name -> {
                    showOneDialog("是否退出当前公证", "退出") {
                        AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
                    }
                }
                PayActivity::class.java.name -> {
                    return true
                }
                TakePhotoActivity::class.java.name -> {
                    return super.onKeyDown(keyCode, event)
                }
                else -> {
                    AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
                }
            }
        }
        return false
    }


}