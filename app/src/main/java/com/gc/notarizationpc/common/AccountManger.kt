package com.gc.notarizationpc.common

import android.content.Context
import com.gc.notarizationpc.AppApplication
import com.gc.notarizationpc.model.UserInfo

/**
 * Created by lenovo on 2017/3/4.
 */
class AccountManger(private val context: Context) {
    private var application: AppApplication?


    fun clearUserInfo() {
        if (application == null) {
            application = context.applicationContext as AppApplication
        }
        application?.userInfo = null
    }

    fun initUserInfo() {
        application?.let {
//            application?.userInfo = UserInfo("32088219950921361X", "叶平凡", "1995-09-21", 1, "汉族", "熊猫新型软件园16栋东", "熊猫新型软件园16栋东", "***********","44e4590e-5625-4ed2-96f1-5ee6ad685300")
            application?.userInfo = UserInfo()
        }
                ?: let { application = context.applicationContext as AppApplication }
    }

    fun updateNotaryId(notaryId: String?,notaryPublicId: String?) {
        application?.let {
            application?.notaryId = notaryId
            application?.notaryPublicId = notaryPublicId
        }?: let {
            application = context.applicationContext as AppApplication
        }
    }
    fun updateNotaryId(notaryId: String?) {
        application?.let {
            application?.notaryId = notaryId
        }?: let {
            application = context.applicationContext as AppApplication
        }
    }

    companion object {
        private var instance: AccountManger? = null

        /**
         * 创建一个单例类
         */
        @JvmStatic
        fun getInstance(context: Context): AccountManger? {
            if (instance == null) {
                synchronized(AccountManger::class.java) {
                    if (instance == null) {
                        instance = AccountManger(context)
                    }
                }
            }
            return instance
        }
    }

    init {
        application = context.applicationContext as AppApplication
    }
}