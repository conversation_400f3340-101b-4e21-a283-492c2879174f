package com.gc.notarizationpc.common;


import android.content.Context;

import com.gc.mininotarization.BuildConfig;
import com.gc.notarizationpc.util.MacUtils;

import java.io.File;

import me.goldze.mvvmhabit.common.BaseAppConfig;
import me.goldze.mvvmhabit.utils.CommonUtil;


/**
 * Created by lenovo on 2017/2/22.
 */
public class AppConfig extends BaseAppConfig {
    public static final int SERVER_TYPE_OLINE = 2;
    public static final int SERVER_TYPE = BuildConfig.SERVER_TYPE;
    public static String RTMP_PATH = "rtmp://shicheng.njguochu.com:9212/tv_file/";
    public static int video_width = 640;
    public static int video_height = 480;
    public static String OBTAINEVIDENCE = "/obtainevidence/";
    public static String USERAPI = "/user-api/";
    public static String isFrom = "qzz";
    public static boolean isUpload = false;
    public static boolean isAbort = false;
    public static boolean isCantClose = false;
    public static final String AccessKey = "e41c7e9f66ce0100d4b2";
    public static final String SecretKey = "11a6700836618860a94569b52823b8cc628c7964";
    //    public static final String bucketName = "shicheng-prod";
    public static final String endPoint = "oos-ahwh.ctyunapi.cn";
    public static boolean uploadStatus = false;
    public static boolean isQzf = false;
    public static String macAddress;

    /**正式**/
//    public static final String bucketName = "shicheng-prod";
//    public static final String bucketUrl = "https://oos-ahwh.ctyunapi.cn/shicheng-prod/";
//    public static String checkMaintain = "https://njscgzc.com:9000/apistatus/";
    /**
     * 测式
     **/
    public static String bucketName = "";
    public static String bucketUrl = "";
    public static String checkMaintain = "";
    public static boolean isUploading = false;
    public static boolean isLocking = false;

    public static String TOKEN = "";

    /**
     * 纬度
     */
    public static double lat = 39.916527;//纬度,默认北京
    /**
     * 经度
     */
    public static double lng = 116.397128;//经度，默认北京
    /**
     * 地址
     */
    public static String address = "";

    public static String orderId = "";

    public static int videoType = 0;

    public static String RGBPID = "c013";

    public static String HIGHPID = "";
    public static String USBPNAME = "";
    public static String USBPNAME_DEFAULT = "USB Color camera";
    public static String LOCAL_USB_NAME = "localusb";

    public static String HOST = ""; //服务器地址（协议+地址+端口号）开发环境


    //    var HOST = "ws://*************:15675/ws" //本地服务器地址（协议+地址+端口号）

    // public static String username = "admin";//用户名 正式
//    public static String password = "n#o0Scmdamg6";//密码 正式
//    public static String username = "admin";//用户名 测试
//    public static String password = "n#o0Scmdamg6";//密码 测试
    public static String username = "admin";//用户名 开发环境
    public static String password = "mNBji5V$vV";//密码 开发环境

    /**
     * 必须在Application初始化  为服务器地址赋值
     */
    //http://************:8081/scgz/
    //"https://shicheng.njguochu.com:9215/scgz/"
    public static void initServerSpServices(Context context) {
        if (SERVER_TYPE == SERVER_TYPE_OLINE) {
            SERVICE_PATH = "https://notary2.njguochu.com:8010";
            bucketName = "shicheng-prod";
            bucketUrl = "https://oos-ahwh.ctyunapi.cn/shicheng-prod/";
            checkMaintain = "https://njscgzc.com:9000/apistatus/";
            HOST = "wss://notary2.njguochu.com:8016/mqtt";
            username = "admin";
            password = "n#o0Scmdamg6";
            CommonUtil.IS_DEBUG = false;
        } else {
            SERVICE_PATH = "https://testgz.njguochu.com:8128";//测试
            HOST = "wss://testgz.njguochu.com:8129/mqtt";//测试
//            HOST = "wss://testdesk.njguochu.com:28003/mqtt";//开发
//            SERVICE_PATH = "https://testdesk.njguochu.com:28002";//开发

//            SERVICE_PATH = "http://*************:9999/";
//            SERVICE_PATH = "https://testdesk.njguochu.com:28002";//开发

            // 周楠楠
//            SERVICE_PATH = "http://************:9999/";
//            SERVICE_PATH = "http://*************:8081/scgz/";
//            SERVICE_PATH = "http://*************:8081/scgz/";
            bucketName = "sc-dev";
            bucketUrl = "https://oos-ahwh.ctyunapi.cn/sc-dev/";
            checkMaintain = "https://testgz.njguochu.com:22203/apistatus/";
            username = "admin";//测试
            password = "mNBji5V$vV";//测试
            CommonUtil.IS_DEBUG = true;
//            username = "admin";//t 开发环境
//            password = "1qaz@WSX3edc";//密码 开发环境
        }

        APP_PATH_ROOT = context.getFilesDir().getAbsolutePath() + File.separator;
        macAddress = MacUtils.getMacAddress(context);
    }
}
