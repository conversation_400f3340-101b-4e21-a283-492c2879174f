package com.gc.notarizationpc.common

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.example.framwork.adapter.CommonQuickAdapter
import com.example.framwork.utils.SuperRecyclerViewUtils
import com.example.framwork.widget.LoadDataLayout
import com.gc.notarizationpc.R
import com.scwang.smartrefresh.layout.SmartRefreshLayout

abstract class BaseRecyclerViewFragment : BaseFragment(), OnItemClickListener {
    var rvContent: RecyclerView? = null
    var loaddataLayout: LoadDataLayout? = null
    var refreshLayout: SmartRefreshLayout? = null
    protected var recyclerViewUtils: SuperRecyclerViewUtils? = null
    protected var mAdapter: CommonQuickAdapter<*>? = null
    override fun initViewsAndEvents(view: View, savedInstanceState: Bundle) {
        rvContent = view.findViewById(R.id.rv_content)
        loaddataLayout = view.findViewById(R.id.loaddata_layout)
        refreshLayout = view.findViewById(R.id.refresh_layout)
        initRecycler()
    }

    fun initRecycler() {
        initAdapter()
        mAdapter?.setOnItemClickListener(this)
        recyclerViewUtils = SuperRecyclerViewUtils(mActivity, rvContent, refreshLayout, loaddataLayout, mAdapter, initCallBack())
        setRecyclerLayoutManager()
        recyclerViewUtils?.setEmptyRes(setEmptyTxt(), setEmptyRes())
        loaddataLayout?.setAllPageBackgroundColor(setLoaddataLayoutBackground())
        recyclerViewUtils?.setLoginRes(R.color.white, R.drawable.btn_login_bg)
        recyclerViewUtils?.setLoginCall { Goto.goLogin(mActivity) }
    }

    override fun lazyInit(view: View, savedInstanceState: Bundle) {
        recyclerViewUtils?.call()
    }

    protected fun setRecyclerLayoutManager() {
        recyclerViewUtils?.setRecyclerViewForList(mActivity, 0, 0)
    }

    protected abstract fun initAdapter()
    protected fun setEmptyRes(): Int {
        return 0
    }

    protected fun setEmptyTxt(): String {
        return "空空如也~"
    }

    protected fun setLoaddataLayoutBackground(): Int {
        return R.color.white
    }

    override fun loginRefreshView() {
        recyclerViewUtils?.call()
    }

    override fun logoutRefreshView() {
        recyclerViewUtils?.call()
    }

    protected abstract fun initCallBack(): SuperRecyclerViewUtils.CallBack<*>?
}