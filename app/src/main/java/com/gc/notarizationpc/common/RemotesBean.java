package com.gc.notarizationpc.common;

import java.io.Serializable;
import java.util.List;

public class RemotesBean implements Serializable {

    /**
     * msg : 成功
     * code : 200
     * page : {"currentPage":1,"pageSize":10,"total":1}
     * items : [{"interestRate":"利率","borrowerId":"借款人性质Id","mortgagorType":"抵押人性质类别","loanStartDate":"开始时间","purpose":"吃吃喝喝","idCard":"身份证号","bankOrderId":"订单号","mortgagorId":"抵押人性质Id","wordId":"合同模板id","fee":"金额","borrower":"借款人","userList":[{"unitGuid":"用户id","userName":"用户名","idCard":"用户身份证号","mobile":"手机号","filePath":"文件主键","filePathList":null,"bankOrderId":"订单主键","createDate":"创建时间","contractPath":null,"contractList":null,"userType":0,"pdfList":null,"pdfPathList":null,"signPdf":null,"sign":null,"bankorder":null,"keyWordList":null,"content":null,"wordId":null,"wordTitle":null,"contractPathBack":null,"state":null,"address":null,"nationality":null,"identity":null,"natureType":1,"propertyId":null,"enterpriseName":null,"organizationCode":null,"isDaiBan":"是否代办（1是 0否）","gender":null,"resBankProperty":null,"contractName":"合同名称","borrowerId":"借款人性质id","mortgagorId":null,"rstSignPath":null}],"repaymentType":"还款方式","keyWordList":null,"imgPath":[{"path":"材料地址","unitGuid":"材料主键"}],"html":[],"term":"期限","loanEndDate":"合同结束时间"}]
     */

    private String msg;
    private int code;
    private PageDTO page;
    private List<ItemsDTO> items;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public PageDTO getPage() {
        return page;
    }

    public void setPage(PageDTO page) {
        this.page = page;
    }

    public List<ItemsDTO> getItems() {
        return items;
    }

    public void setItems(List<ItemsDTO> items) {
        this.items = items;
    }

    public static class PageDTO implements Serializable {
        /**
         * currentPage : 1
         * pageSize : 10
         * total : 1
         */

        private int currentPage;
        private int pageSize;
        private int total;

        public int getCurrentPage() {
            return currentPage;
        }

        public void setCurrentPage(int currentPage) {
            this.currentPage = currentPage;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        @Override
        public String toString() {
            return "PageDTO{" +
                    "currentPage=" + currentPage +
                    ", pageSize=" + pageSize +
                    ", total=" + total +
                    '}';
        }
    }

    public static class ItemsDTO implements Serializable {
        /**
         * interestRate : 利率
         * borrowerId : 借款人性质Id
         * mortgagorType : 抵押人性质类别
         * loanStartDate : 开始时间
         * purpose : 吃吃喝喝
         * idCard : 身份证号
         * bankOrderId : 订单号
         * mortgagorId : 抵押人性质Id
         * wordId : 合同模板id
         * fee : 金额
         * borrower : 借款人
         * userList : [{"unitGuid":"用户id","userName":"用户名","idCard":"用户身份证号","mobile":"手机号","filePath":"文件主键","filePathList":null,"bankOrderId":"订单主键","createDate":"创建时间","contractPath":null,"contractList":null,"userType":0,"pdfList":null,"pdfPathList":null,"signPdf":null,"sign":null,"bankorder":null,"keyWordList":null,"content":null,"wordId":null,"wordTitle":null,"contractPathBack":null,"state":null,"address":null,"nationality":null,"identity":null,"natureType":1,"propertyId":null,"enterpriseName":null,"organizationCode":null,"isDaiBan":"是否代办（1是 0否）","gender":null,"resBankProperty":null,"contractName":"合同名称","borrowerId":"借款人性质id","mortgagorId":null,"rstSignPath":null}]
         * repaymentType : 还款方式
         * keyWordList : null
         * imgPath : [{"path":"材料地址","unitGuid":"材料主键"}]
         * html : []
         * term : 期限
         * loanEndDate : 合同结束时间
         */

        private String interestRate;
        private String borrowerId;
        private String mortgagorType;
        private String loanStartDate;
        private String purpose;
        private String idCard;
        private String bankOrderId;
        private String mortgagorId;
        private String wordId;
        private String fee;
        private String borrower;
        private List<UserListDTO> userList;
        private String repaymentType;

        public String getMaterial() {
            return material;
        }

        public void setMaterial(String material) {
            this.material = material;
        }

        private String material;
        private Object keyWordList;
        private List<ImgPathDTO> imgPath;
        private List<PropertyInformation> html;
        private String term;
        private String loanEndDate;

        public String getInterestRate() {
            return interestRate;
        }

        public void setInterestRate(String interestRate) {
            this.interestRate = interestRate;
        }

        public String getBorrowerId() {
            return borrowerId;
        }

        public void setBorrowerId(String borrowerId) {
            this.borrowerId = borrowerId;
        }

        public String getMortgagorType() {
            return mortgagorType;
        }

        public void setMortgagorType(String mortgagorType) {
            this.mortgagorType = mortgagorType;
        }

        public String getLoanStartDate() {
            return loanStartDate;
        }

        public void setLoanStartDate(String loanStartDate) {
            this.loanStartDate = loanStartDate;
        }

        public String getPurpose() {
            return purpose;
        }

        public void setPurpose(String purpose) {
            this.purpose = purpose;
        }

        public String getIdCard() {
            return idCard;
        }

        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }

        public String getBankOrderId() {
            return bankOrderId;
        }

        public void setBankOrderId(String bankOrderId) {
            this.bankOrderId = bankOrderId;
        }

        public String getMortgagorId() {
            return mortgagorId;
        }

        public void setMortgagorId(String mortgagorId) {
            this.mortgagorId = mortgagorId;
        }

        public String getWordId() {
            return wordId;
        }

        public void setWordId(String wordId) {
            this.wordId = wordId;
        }

        public String getFee() {
            return fee;
        }

        public void setFee(String fee) {
            this.fee = fee;
        }

        public String getBorrower() {
            return borrower;
        }

        public void setBorrower(String borrower) {
            this.borrower = borrower;
        }

        public List<UserListDTO> getUserList() {
            return userList;
        }

        public void setUserList(List<UserListDTO> userList) {
            this.userList = userList;
        }

        public String getRepaymentType() {
            return repaymentType;
        }

        public void setRepaymentType(String repaymentType) {
            this.repaymentType = repaymentType;
        }

        public Object getKeyWordList() {
            return keyWordList;
        }

        public void setKeyWordList(Object keyWordList) {
            this.keyWordList = keyWordList;
        }

        public List<ImgPathDTO> getImgPath() {
            return imgPath;
        }

        public void setImgPath(List<ImgPathDTO> imgPath) {
            this.imgPath = imgPath;
        }

        public List<PropertyInformation> getHtml() {
            return html;
        }

        public void setHtml(List<PropertyInformation> html) {
            this.html = html;
        }

        public String getTerm() {
            return term;
        }

        public void setTerm(String term) {
            this.term = term;
        }

        public String getLoanEndDate() {
            return loanEndDate;
        }

        public void setLoanEndDate(String loanEndDate) {
            this.loanEndDate = loanEndDate;
        }

        @Override
        public String toString() {
            return "ItemsDTO{" +
                    "interestRate='" + interestRate + '\'' +
                    ", borrowerId='" + borrowerId + '\'' +
                    ", mortgagorType='" + mortgagorType + '\'' +
                    ", loanStartDate='" + loanStartDate + '\'' +
                    ", purpose='" + purpose + '\'' +
                    ", idCard='" + idCard + '\'' +
                    ", bankOrderId='" + bankOrderId + '\'' +
                    ", mortgagorId='" + mortgagorId + '\'' +
                    ", wordId='" + wordId + '\'' +
                    ", fee='" + fee + '\'' +
                    ", borrower='" + borrower + '\'' +
                    ", userList=" + userList +
                    ", repaymentType='" + repaymentType + '\'' +
                    ", keyWordList=" + keyWordList +
                    ", imgPath=" + imgPath +
                    ", html=" + html +
                    ", term='" + term + '\'' +
                    ", loanEndDate='" + loanEndDate + '\'' +
                    '}';
        }

        public static class UserListDTO implements Serializable {
            /**
             * unitGuid : 用户id
             * userName : 用户名
             * idCard : 用户身份证号
             * mobile : 手机号
             * filePath : 文件主键
             * filePathList : null
             * bankOrderId : 订单主键
             * createDate : 创建时间
             * contractPath : null
             * contractList : null
             * userType : 0
             * pdfList : null
             * pdfPathList : null
             * signPdf : null
             * sign : null
             * bankorder : null
             * keyWordList : null
             * content : null
             * wordId : null
             * wordTitle : null
             * contractPathBack : null
             * state : null
             * address : null
             * nationality : null
             * identity : null
             * natureType : 1
             * propertyId : null
             * enterpriseName : null
             * organizationCode : null
             * isDaiBan : 是否代办（1是 0否）
             * gender : null
             * resBankProperty : null
             * contractName : 合同名称
             * borrowerId : 借款人性质id
             * mortgagorId : null
             * rstSignPath : null
             */

            private String unitGuid;
            private String userName;
            private String idCard;
            private String mobile;
            private String filePath;
            private Object filePathList;
            private String bankOrderId;
            private String createDate;
            private Object contractPath;
            private Object contractList;
            private int userType;
            private Object pdfList;
            private Object pdfPathList;
            private Object signPdf;
            private Object sign;
            private Object bankorder;
            private Object keyWordList;
            private Object content;
            private Object wordId;
            private Object wordTitle;
            private Object contractPathBack;
            private Object state;
            private Object address;
            private Object nationality;
            private Object identity;
            private int natureType;
            private Object propertyId;
            private Object enterpriseName;
            private Object organizationCode;
            private String isDaiBan;
            private int gender = -1;
            private Object resBankProperty;
            private String contractName;
            private String borrowerId;
            private Object mortgagorId;
            private Object rstSignPath;

            public String getUnitGuid() {
                return unitGuid;
            }

            public void setUnitGuid(String unitGuid) {
                this.unitGuid = unitGuid;
            }

            public String getUserName() {
                return userName;
            }

            public void setUserName(String userName) {
                this.userName = userName;
            }

            public String getIdCard() {
                return idCard;
            }

            public void setIdCard(String idCard) {
                this.idCard = idCard;
            }

            public String getMobile() {
                return mobile;
            }

            public void setMobile(String mobile) {
                this.mobile = mobile;
            }

            public String getFilePath() {
                return filePath;
            }

            public void setFilePath(String filePath) {
                this.filePath = filePath;
            }

            public Object getFilePathList() {
                return filePathList;
            }

            public void setFilePathList(Object filePathList) {
                this.filePathList = filePathList;
            }

            public String getBankOrderId() {
                return bankOrderId;
            }

            public void setBankOrderId(String bankOrderId) {
                this.bankOrderId = bankOrderId;
            }

            public String getCreateDate() {
                return createDate;
            }

            public void setCreateDate(String createDate) {
                this.createDate = createDate;
            }

            public Object getContractPath() {
                return contractPath;
            }

            public void setContractPath(Object contractPath) {
                this.contractPath = contractPath;
            }

            public Object getContractList() {
                return contractList;
            }

            public void setContractList(Object contractList) {
                this.contractList = contractList;
            }

            public int getUserType() {
                return userType;
            }

            public void setUserType(int userType) {
                this.userType = userType;
            }

            public Object getPdfList() {
                return pdfList;
            }

            public void setPdfList(Object pdfList) {
                this.pdfList = pdfList;
            }

            public Object getPdfPathList() {
                return pdfPathList;
            }

            public void setPdfPathList(Object pdfPathList) {
                this.pdfPathList = pdfPathList;
            }

            public Object getSignPdf() {
                return signPdf;
            }

            public void setSignPdf(Object signPdf) {
                this.signPdf = signPdf;
            }

            public Object getSign() {
                return sign;
            }

            public void setSign(Object sign) {
                this.sign = sign;
            }

            public Object getBankorder() {
                return bankorder;
            }

            public void setBankorder(Object bankorder) {
                this.bankorder = bankorder;
            }

            public Object getKeyWordList() {
                return keyWordList;
            }

            public void setKeyWordList(Object keyWordList) {
                this.keyWordList = keyWordList;
            }

            public Object getContent() {
                return content;
            }

            public void setContent(Object content) {
                this.content = content;
            }

            public Object getWordId() {
                return wordId;
            }

            public void setWordId(Object wordId) {
                this.wordId = wordId;
            }

            public Object getWordTitle() {
                return wordTitle;
            }

            public void setWordTitle(Object wordTitle) {
                this.wordTitle = wordTitle;
            }

            public Object getContractPathBack() {
                return contractPathBack;
            }

            public void setContractPathBack(Object contractPathBack) {
                this.contractPathBack = contractPathBack;
            }

            public Object getState() {
                return state;
            }

            public void setState(Object state) {
                this.state = state;
            }

            public Object getAddress() {
                return address;
            }

            public void setAddress(Object address) {
                this.address = address;
            }

            public Object getNationality() {
                return nationality;
            }

            public void setNationality(Object nationality) {
                this.nationality = nationality;
            }

            public Object getIdentity() {
                return identity;
            }

            public void setIdentity(Object identity) {
                this.identity = identity;
            }

            public int getNatureType() {
                return natureType;
            }

            public void setNatureType(int natureType) {
                this.natureType = natureType;
            }

            public Object getPropertyId() {
                return propertyId;
            }

            public void setPropertyId(Object propertyId) {
                this.propertyId = propertyId;
            }

            public Object getEnterpriseName() {
                return enterpriseName;
            }

            public void setEnterpriseName(Object enterpriseName) {
                this.enterpriseName = enterpriseName;
            }

            public Object getOrganizationCode() {
                return organizationCode;
            }

            public void setOrganizationCode(Object organizationCode) {
                this.organizationCode = organizationCode;
            }

            public String getIsDaiBan() {
                return isDaiBan;
            }

            public void setIsDaiBan(String isDaiBan) {
                this.isDaiBan = isDaiBan;
            }

            public int getGender() {
                return gender;
            }

            public void setGender(int gender) {
                this.gender = gender;
            }

            public Object getResBankProperty() {
                return resBankProperty;
            }

            public void setResBankProperty(Object resBankProperty) {
                this.resBankProperty = resBankProperty;
            }

            public String getContractName() {
                return contractName;
            }

            public void setContractName(String contractName) {
                this.contractName = contractName;
            }

            public String getBorrowerId() {
                return borrowerId;
            }

            public void setBorrowerId(String borrowerId) {
                this.borrowerId = borrowerId;
            }

            public Object getMortgagorId() {
                return mortgagorId;
            }

            public void setMortgagorId(Object mortgagorId) {
                this.mortgagorId = mortgagorId;
            }

            public Object getRstSignPath() {
                return rstSignPath;
            }

            public void setRstSignPath(Object rstSignPath) {
                this.rstSignPath = rstSignPath;
            }

            @Override
            public String toString() {
                return "UserListDTO{" +
                        "unitGuid='" + unitGuid + '\'' +
                        ", userName='" + userName + '\'' +
                        ", idCard='" + idCard + '\'' +
                        ", mobile='" + mobile + '\'' +
                        ", filePath='" + filePath + '\'' +
                        ", filePathList=" + filePathList +
                        ", bankOrderId='" + bankOrderId + '\'' +
                        ", createDate='" + createDate + '\'' +
                        ", contractPath=" + contractPath +
                        ", contractList=" + contractList +
                        ", userType=" + userType +
                        ", pdfList=" + pdfList +
                        ", pdfPathList=" + pdfPathList +
                        ", signPdf=" + signPdf +
                        ", sign=" + sign +
                        ", bankorder=" + bankorder +
                        ", keyWordList=" + keyWordList +
                        ", content=" + content +
                        ", wordId=" + wordId +
                        ", wordTitle=" + wordTitle +
                        ", contractPathBack=" + contractPathBack +
                        ", state=" + state +
                        ", address=" + address +
                        ", nationality=" + nationality +
                        ", identity=" + identity +
                        ", natureType=" + natureType +
                        ", propertyId=" + propertyId +
                        ", enterpriseName=" + enterpriseName +
                        ", organizationCode=" + organizationCode +
                        ", isDaiBan='" + isDaiBan + '\'' +
                        ", gender=" + gender +
                        ", resBankProperty=" + resBankProperty +
                        ", contractName='" + contractName + '\'' +
                        ", borrowerId='" + borrowerId + '\'' +
                        ", mortgagorId=" + mortgagorId +
                        ", rstSignPath=" + rstSignPath +
                        '}';
            }
        }

        public static class ImgPathDTO implements Serializable {
            /**
             * path : 材料地址
             * unitGuid : 材料主键
             */

            private String path;
            private String unitGuid;

            public String getMaterialName() {
                return materialName;
            }

            public void setMaterialName(String materialName) {
                this.materialName = materialName;
            }

            private String materialName;

            public String getPath() {
                return path;
            }

            public void setPath(String path) {
                this.path = path;
            }

            public String getUnitGuid() {
                return unitGuid;
            }

            public void setUnitGuid(String unitGuid) {
                this.unitGuid = unitGuid;
            }

            @Override
            public String toString() {
                return "ImgPathDTO{" +
                        "path='" + path + '\'' +
                        ", unitGuid='" + unitGuid + '\'' +
                        '}';
            }
        }
    }

    @Override
    public String toString() {
        return "RemotesBean{" +
                "msg='" + msg + '\'' +
                ", code=" + code +
                ", page=" + page +
                ", items=" + items +
                '}';
    }

    public static class PropertyInformation implements Serializable {
        private String mortgageType;
        private String name;
        private String property;

        public String getMortgageType() {
            return mortgageType;
        }

        public void setMortgageType(String mortgageType) {
            this.mortgageType = mortgageType;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getProperty() {
            return property;
        }

        public void setProperty(String property) {
            this.property = property;
        }
    }
}
