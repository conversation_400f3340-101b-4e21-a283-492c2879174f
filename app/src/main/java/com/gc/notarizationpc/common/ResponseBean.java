package com.gc.notarizationpc.common;

import android.text.TextUtils;

import com.alibaba.fastjson.annotation.JSONField;
import com.example.framwork.noHttp.Bean.BaseResponseBean;

import java.util.List;

public class ResponseBean extends BaseResponseBean {
    public int code;

    public String msg;
    @JSONField(alternateNames = {"item", "items", "unitGuid", "result", "order", "pdfImgPaths"})
    public String data;

    @JSONField(alternateNames = {"annexId"})
    public String extraData;

    public String message;

    public List<String> pdfImgPaths;

    @Override
    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getData() {
        return data;
    }

    public List<String> getPdfImgPaths() {
        return pdfImgPaths;
    }

    @Override
    public String getMessage() {
        return TextUtils.isEmpty(msg) ? message : msg;
    }

    @Override
    public boolean isSuccess() {
        return code == 200;
    }
}
