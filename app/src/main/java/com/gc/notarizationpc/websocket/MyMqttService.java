package com.gc.notarizationpc.websocket;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.util.Log;

import com.gc.notarizationpc.common.AppConfig;

import org.eclipse.paho.android.service.MqttAndroidClient;
import org.eclipse.paho.client.mqttv3.IMqttActionListener;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttClientPersistence;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.utils.CommonUtil;

/**
 * Desc         ${MQTT服务}
 */

public class MyMqttService {
    public final String TAG = "MyMqttService";
    private MqttAndroidClient mqttAndroidClient;
    private MqttConnectOptions mMqttConnectOptions;
    private String HOST = "";//服务器地址（协议+地址+端口号）

    private String publishTopic = "";//发布主题
    private String responseTopic = "";//响应主题
    private String clientid = AppConfig.macAddress;
    private Context context;
    private WebSocketCallBack mSocketIOCallBack;
    private static MyMqttService myMqttService = null;



    public MyMqttService(Context context, String publishTopic, String responseTopic, String HOST, WebSocketCallBack mSocketIOCallBack) {
        this.context = context;
        this.publishTopic = publishTopic;
        this.responseTopic = responseTopic;
        this.mSocketIOCallBack = mSocketIOCallBack;
        this.HOST = HOST;
    }

    public MyMqttService(Context context) {
        this.context = context;
    }

    public static MyMqttService getInstance(Context context) {
        if (myMqttService == null) {
            myMqttService = new MyMqttService(context);
        }
        return myMqttService;
    }

    public void setData(Context context, String publishTopic, String responseTopic, String HOST, WebSocketCallBack mSocketIOCallBack) {
        this.context = context;
        this.publishTopic = publishTopic;
        this.responseTopic = responseTopic;
        this.mSocketIOCallBack = mSocketIOCallBack;
        this.HOST = HOST;
    }

    public void setResponseTopic(String responseTopic) {
        this.responseTopic = responseTopic;
    }

    public void start() {
        init();
    }

    /**
     * 发布 （模拟其他客户端发布消息）
     *
     * @param message 消息
     */
    public void publish(String message) {
        String topic = publishTopic;
        Integer qos = 1;
        Boolean retained = false;
        try {
            //参数分别为：主题、消息的字节数组、服务质量、是否在服务器保留断开连接后的最后一条消息
            mqttAndroidClient.publish(topic, message.getBytes(), qos.intValue(), retained.booleanValue());
            if(CommonUtil.IS_DEBUG) {
                Log.i(TAG, "发送消息Socket----" + new String(message.getBytes()));
            }
        } catch (MqttException e) {
            e.printStackTrace();
        }

        Map<String, String> map = new HashMap<>();
        map.put("", "");
    }


    /**
     * 初始化
     */
    private void init() {
        String serverURI = HOST; //服务器地址（协议+地址+端口号）
        MqttClientPersistence persistence = new MemoryPersistence();
        mqttAndroidClient = new MqttAndroidClient(context, serverURI, clientid + System.currentTimeMillis());
        mqttAndroidClient.setCallback(mqttCallback); //设置监听订阅消息的回调
        mMqttConnectOptions = new MqttConnectOptions();
        mMqttConnectOptions.setCleanSession(true); //设置是否清除缓存
        mMqttConnectOptions.setConnectionTimeout(60); //设置超时时间，单位：秒
        mMqttConnectOptions.setKeepAliveInterval(10); //设置心跳包发送间隔，单位：秒
        mMqttConnectOptions.setUserName(AppConfig.username); //设置用户名
        mMqttConnectOptions.setPassword(AppConfig.password.toCharArray()); //设置密码

        // last will message
        boolean doConnect = true;
        String message = "{\"terminal_uid\":\"" + clientid + "\"}";
        String topic = publishTopic;
        Integer qos = 2;
        Boolean retained = false;
        if ((!message.equals("")) || (!topic.equals(""))) {
            // 最后的遗嘱
//            try {
//                mMqttConnectOptions.setWill(topic, message.getBytes(), qos.intValue(), retained.booleanValue());
//            } catch (Exception e) {
//                Log.i(TAG, "Socket----Exception Occured", e);
//                doConnect = false;
//                mqttAndroidClient.close();
//                iMqttActionListener.onFailure(null, e);
//            }
        }
        if (doConnect) {
            doClientConnection();
        }
//        handler.sendEmptyMessage(0x01);
    }

    /**
     * 连接MQTT服务器
     */
    private void doClientConnection() {
        try {
            if (mqttAndroidClient != null && !mqttAndroidClient.isConnected() && isConnectIsNomarl()) {
//            if (mqttAndroidClient != null && !mqttAndroidClient.isConnected()) {
                mqttAndroidClient.connect(mMqttConnectOptions, null, iMqttActionListener);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    boolean hasNet = true;

    /**
     * 判断网络是否连接
     */
    private boolean isConnectIsNomarl() {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = connectivityManager.getActiveNetworkInfo();
        if (info != null && info.isAvailable()) {
            String name = info.getTypeName();
            Log.i(TAG, "Socket----当前网络名称：" + name);
            return true;
        } else {
            Log.i(TAG, "Socket----没有可用网络");
            /*没有可用网络的时候，延迟3秒再尝试重连*/
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    doClientConnection();
                }
            }, 3000);
            return false;
        }
    }

    public void subscribe() {
        try {
            Log.e("Test", mqttAndroidClient + "mqttAndroidClient" + responseTopic);
            mqttAndroidClient.subscribe(responseTopic, 1, 1, new IMqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    Log.e("Test", "subscribe onSuccess====");
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    Log.e("Test", "subscribe error====" + exception.getMessage());
                }
            });//订阅主题，参数：主题、服务质量
        } catch (MqttException e) {
            e.printStackTrace();
        }
    }

    //MQTT是否连接成功的监听
    private IMqttActionListener iMqttActionListener = new IMqttActionListener() {

        @Override
        public void onSuccess(IMqttToken arg0) {
            Log.i(TAG, "Socket----连接成功,主题 " + responseTopic);
            try {
                mqttAndroidClient.subscribe(responseTopic, 1);//订阅主题，参数：主题、服务质量
            } catch (MqttException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onFailure(IMqttToken arg0, Throwable arg1) {
            arg1.printStackTrace();
            Log.i(TAG, "Socket----连接失败 "+arg1.getMessage());
//            init();//连接失败，重连（可关闭服务器进行模拟）
            doClientConnection();
        }
    };

    //订阅主题的回调
    private MqttCallback mqttCallback = new MqttCallback() {

        @Override
        public void messageArrived(String topic, MqttMessage message) throws Exception {
            if(CommonUtil.IS_DEBUG) {
                Log.i(TAG, "Socket----收到消息： " + new String(message.getPayload()));
            }
            mSocketIOCallBack.onSocketMessage(new String(message.getPayload()));
//            MyLogUtils.i(TAG, "onSocketMessage" + new String(message.getPayload()));
        }

        @Override
        public void deliveryComplete(IMqttDeliveryToken arg0) {

        }

        @Override
        public void connectionLost(Throwable arg0) {
            Log.i(TAG, "Socket----连接断开 ");
            doClientConnection();//连接断开，重连
        }
    };

    public void onlyUnSubscribe(String topic, SubscribeOrNotListener listener) {
        try {
            if (null != mqttAndroidClient) {
                Log.d(TAG, "onlyUnSubscribe");
                mqttAndroidClient.unsubscribe(responseTopic, 1, new IMqttActionListener() {
                    @Override
                    public void onSuccess(IMqttToken asyncActionToken) {
                        if (listener != null) {
                            listener.success();
                        }
                        Log.e("Test", "onlyUnSubscribe onSuccess====");
                    }

                    @Override
                    public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                        Log.e("Test", "onlyUnSubscribe error====" + exception.getMessage());
                    }
                }); //断开连接
            }
        } catch (MqttException e) {
            e.printStackTrace();
        }
    }

    public interface SubscribeOrNotListener {
        void success();
    }

    public void unSubscribe() {
        try {
            if (null != mqttAndroidClient) {
                Log.d(TAG, "unSubscribe");
                mqttAndroidClient.setCallback(null);
//                mqttAndroidClient.unregisterResources();
                mqttAndroidClient.unsubscribe(responseTopic); //断开连接
                mqttAndroidClient.disconnect(); //断开连接
                mqttAndroidClient = null;
//                mqttAndroidClient.setCallback(null);
            }
        } catch (MqttException e) {
            e.printStackTrace();
        }
    }
}