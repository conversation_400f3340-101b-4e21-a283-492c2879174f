package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.NotaryMatterItem;
import com.gc.notarizationpc.data.model.response.VideoOrderDetailModel;

import me.goldze.mvvmhabit.base.ItemViewModel;

/**
 * Created by goldze on 2017/7/17.
 */

public class SelfFeeItemViewModel extends ItemViewModel<FragmentSelfServiceHasBeenFinishedViewModel> {

    public ObservableField<NotaryMatterItem.MattersInfosDTO> entity = new ObservableField<>();

    public SelfFeeItemViewModel(@NonNull FragmentSelfServiceHasBeenFinishedViewModel viewModel, NotaryMatterItem.MattersInfosDTO entity) {
        super(viewModel);
        Log.e("FragmentGridItemViewModel", "FragmentGridItemViewModel");
        this.entity.set(entity);
    }



}
