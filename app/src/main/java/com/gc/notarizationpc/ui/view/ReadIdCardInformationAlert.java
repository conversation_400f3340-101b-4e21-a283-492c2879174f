package com.gc.notarizationpc.ui.view;

import static me.goldze.mvvmhabit.utils.ToastUtils.toastError;

import android.app.Dialog;
import android.content.Context;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.KeyEvent;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.example.scarx.idcardreader.utils.IdCardRenderUtils;
import com.example.scarx.idcardreader.utils.imp.MyCallBack;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.umeng.analytics.MobclickAgent;
import com.zkteco.android.biometric.core.device.ParameterHelper;
import com.zkteco.android.biometric.core.device.TransportType;
import com.zkteco.android.biometric.core.utils.ToolUtils;
import com.zkteco.android.biometric.module.idcard.IDCardReader;
import com.zkteco.android.biometric.module.idcard.IDCardReaderFactory;
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo;

import java.util.HashMap;
import java.util.concurrent.CountDownLatch;

import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;

public class ReadIdCardInformationAlert extends Dialog {

    private String TAG = "ReadIdCardInformationAlert";
    private Context mContext;

    private PopwindowUtil.ButtonClickListenerWithBackValue myButtonClickListener;

    private PopwindowUtil.ResultListener myResultListener;
    private IdCardRenderUtils idCardReaderUtils = new IdCardRenderUtils();
    private SoundPool soundPool = new SoundPool(100, AudioManager.STREAM_MUSIC, 0);
    private IDCardInfo idcardinfo = null;

    private boolean isNoCard = true;
    private Integer readSuccess = null;
    private Integer readTips = null;
    private Integer validateSuccess = null;
    private Integer validateFail = null;
    private Integer retry = null;
    private Integer faceCamera = null;


    public ReadIdCardInformationAlert(@NonNull Context context, PopwindowUtil.ResultListener resultListener) {
        super(context);
        mContext = context;
        myResultListener = resultListener;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.dialog_scan_idcard);
        TextView textView = findViewById(R.id.readIdCardInformationTitle);
        ImageView imageView = findViewById(R.id.image_close);
        imageView.setOnClickListener(v -> {
            dismiss();
        });
        startReadIdCard();
//       new Handler().postDelayed(new Runnable() {
//           @Override
//           public void run() {
//               textView.setText(mContext.getText(R.string.idcard_reading));
//           }
//       }, 1000);
    }

    private void startIdCardRead() {
        try {
            Log.i("Test", "startIdCardRead");
//        LogHelper.setLevel(8); // 身份证打印等级
            HashMap<String, Object> ideograms = new HashMap<>();
            ideograms.put(ParameterHelper.PARAM_KEY_VID, 1024);
            ideograms.put(ParameterHelper.PARAM_KEY_PID, 50010);
            IDCardReader idCardReader = IDCardReaderFactory.createIDCardReader(ToolUtils.getApplicationContext(), TransportType.USB, ideograms);
            idCardReaderUtils.readerIdCard(idCardReader, new CountDownLatch(1), new MyCallBack() {
                @Override
                public void onSuccess(IDCardInfo idCardInfo) {
                    Log.i(TAG, "readerIdCard success 读卡成功" + (null == idcardinfo));
                    if (isNoCard && null == idcardinfo) {
                        idcardinfo = idCardInfo;
                        Log.e(TAG, "播放身份识别成功的声音");
                        playSound(1);
                        isNoCard = false;
//                        CustomIdCardInfo customIdCardInfo = new CustomIdCardInfo();
//                        customIdCardInfo.setAddress(idcardinfo.getAddress());
//                        customIdCardInfo.setBirthday(idcardinfo.getBirth());
//                        customIdCardInfo.setIdCard(idcardinfo.getId());
//                        customIdCardInfo.setName(idcardinfo.getName());
//                        customIdCardInfo.setNation(idcardinfo.getNation());
//                        customIdCardInfo.setGender(idcardinfo.getSex());
                        idCardReaderUtils.setbStoped(true);
//                        Bundle mBundle = new Bundle();
//                        mBundle.putString("idCard", idcardinfo.getId());
                        myResultListener.result(idcardinfo.getId());
//                        startActivity(InquiryOrderListHomeActivity.class, mBundle);
                    }
                }

                @Override
                public void onFail(String error) {
                    toastError(Utils.getContext().getString(R.string.readIdCardMachineAbnoramlPleaseCheck));
                    Log.e(TAG, "身份证读卡器出现异常,请检查设备");

                    Log.i(TAG, "readerIdCard fail");
                }

                @Override
                public void onRequestDevicePermission() {
                }

                @Override
                public void onNoCards() {
                    Log.i(TAG, "onNoCards");
                    isNoCard = true;
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            MobclickAgent.reportError(mContext, e);
        }
    }

    private void loadSoundRes() {
        Log.i(TAG, "loadSoundRes");
        new Handler(Looper.getMainLooper()).post(() -> {
            if (SPUtils.getInstance().getString("changeLanguage", "zh").equals("bo")) {
                //咚
                readSuccess = soundPool.load(mContext, R.raw.readcard_success_z, 1);
                //请将身份证放置于阅读器上
                readTips = soundPool.load(mContext, R.raw.idcardread_z, 1);
                //请正对摄像头
                faceCamera = soundPool.load(mContext, R.raw.face_camera_z, 1);
                //请重试
                retry = soundPool.load(mContext, R.raw.retry_z, 1);
                //比对成功
                validateSuccess = soundPool.load(mContext, R.raw.validate_success_z, 1);
                //比对失败
                validateFail = soundPool.load(mContext, R.raw.validate_fail_z, 1);
            } else {
                //咚
                readSuccess = soundPool.load(mContext, R.raw.readcard_success, 1);
                //请将身份证放置于阅读器上
                readTips = soundPool.load(mContext, R.raw.idcardread, 1);
                //请正对摄像头
                faceCamera = soundPool.load(mContext, R.raw.face_camera, 1);
                //请重试
                retry = soundPool.load(mContext, R.raw.retry, 1);
                //比对成功
                validateSuccess = soundPool.load(mContext, R.raw.validate_success, 1);
                //比对失败
                validateFail = soundPool.load(mContext, R.raw.validate_fail, 1);
            }
        });
    }

    /**
     * 1: 读卡成功   2：请将身份证放置到阅读器上  3：请正对摄像头   4：比对失败 5：比对成功  6：请重试
     */
    public void playSound(int index) {
        Log.i(TAG, "playSound index" + index);
        switch (index) {
            case 1:
                soundPool.play(readSuccess, 1f, 1f, 1, 0, 1f);
                break;
            case 2:
                soundPool.play(readTips, 1f, 1f, 1, 0, 1f);
                break;
            case 3:
                soundPool.play(faceCamera, 1f, 1f, 1, 0, 1f);
                break;
            case 4:
                soundPool.play(validateFail, 1f, 1f, 1, 0, 1f);
                break;
            case 5:
                soundPool.play(validateSuccess, 1f, 1f, 1, 0, 1f);
                break;
            case 6:
                soundPool.play(retry, 1f, 1f, 1, 0, 1f);
                break;
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        try {
            idCardReaderUtils.setbStoped(true);
            soundPool.release();
        } catch (Exception e) {
            MobclickAgent.reportError(mContext, e);
            Log.e(TAG, e.getMessage());
        }
    }

    // 开始读取身份证
    private void startReadIdCard() {
        loadSoundRes();
//        isNoCard = true;
//        idcardinfo = null;
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                playSound(2);
            }
        }, 400);
        // 开启身份证识别
        if (idCardReaderUtils.isbStoped()) {
            Log.e("Test", "idCardReaderUtils.setbStoped(false)");
            idCardReaderUtils.setbStoped(false);
        } else {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    startIdCardRead();
                }
            }, 3500);
        }
    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            //do something.
            return true;
        } else {
            return super.dispatchKeyEvent(event);
        }
    }

}
