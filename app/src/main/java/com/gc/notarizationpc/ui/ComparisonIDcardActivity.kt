package com.gc.notarizationpc.ui

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.media.AudioManager
import android.media.SoundPool
import android.os.Handler
import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.example.framwork.baseapp.AppManager
import com.example.scarx.idcardreader.utils.IdCardRenderUtils
import com.example.scarx.idcardreader.utils.imp.MyCallBack
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseActivity
import com.umeng.analytics.MobclickAgent
import com.zkteco.android.biometric.core.device.ParameterHelper
import com.zkteco.android.biometric.core.device.TransportType
import com.zkteco.android.biometric.core.utils.LogHelper
import com.zkteco.android.biometric.core.utils.ToolUtils
import com.zkteco.android.biometric.module.idcard.IDCardReaderFactory
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo
import kotlinx.android.synthetic.main.activity_comparison_idcard.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.joda.time.format.DateTimeFormat
import java.util.concurrent.CountDownLatch

/**
 * @author: wjw
 * @create：2020/7/14
 * @describe：
 */
class ComparisonIDcardActivity : BaseActivity() {
    companion object {
        const val TAG = "ComparisonIDcardActivity"
    }

    private val idCardReaderUtils: IdCardRenderUtils = IdCardRenderUtils()
    private var soundPool: SoundPool = SoundPool(100, AudioManager.STREAM_MUSIC, 0) //构建对象
    private var isActivity = true
    private var isNoCard = true
    private var idcardinfo: IDCardInfo? = null
    private var idCardBmp: Bitmap? = null
    private var readSuccess: Int? = null
    private var readTips: Int? = null
    private var validateSuccess: Int? = null
    private var validateFail: Int? = null
    private var retry: Int? = null
    private var faceCamera: Int? = null


    override fun getIntentData(intent: Intent?) {

    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_comparison_idcard

    }

    override fun initViewsAndEvents() {
        loadSoundRes()
        Handler().postDelayed(Runnable { playSound(2) }, 300)
        isActivity = true
        //开启身份证识别
        if (idCardReaderUtils.isbStoped()) {
            idCardReaderUtils.setbStoped(false)
        } else {
            Handler().postDelayed(Runnable { startIdCardRead() }, 3500)

        }

        idcardBackClicked.setOnClickListener { _ ->
            AppManager.getAppManager().finishActivity(ComparisonIDcardActivity::class.java)
        }
    }


    override fun onStart() {
        super.onStart()
        Log.i(TAG, "onStart")

    }

    /**
     * 加载语音文件
     */
    private fun loadSoundRes() {
        Log.i(TAG, "loadSoundRes")
        lifecycleScope.launch(Dispatchers.IO) {
            //咚
            readSuccess = soundPool.load(mActivity, R.raw.readcard_success, 1)
            //请将身份证放置于阅读器上
            readTips = soundPool.load(mActivity, R.raw.idcardread, 1)
            //请正对摄像头
            faceCamera = soundPool.load(mActivity, R.raw.face_camera, 1)
            //请重试
            retry = soundPool.load(mActivity, R.raw.retry, 1)
            //比对成功
            validateSuccess = soundPool.load(mActivity, R.raw.validate_success, 1)
            //比对失败
            validateFail = soundPool.load(mActivity, R.raw.validate_fail, 1)
        }
    }

    /**
     * 1: 读卡成功   2：请将身份证放置到阅读器上  3：请正对摄像头   4：比对失败 5：比对成功  6：请重试
     */
    fun playSound(index: Int) {
        Log.i(TAG, "playSound index$index")
        when (index) {
            1 -> soundPool.play(readSuccess!!, 1f, 1f, 1, 0, 1f)
            2 -> soundPool.play(readTips!!, 1f, 1f, 1, 0, 1f)
            3 -> soundPool.play(faceCamera!!, 1f, 1f, 1, 0, 1f)
            4 -> soundPool.play(validateFail!!, 1f, 1f, 1, 0, 1f)
            5 -> soundPool.play(validateSuccess!!, 1f, 1f, 1, 0, 1f)
            6 -> soundPool.play(retry!!, 1f, 1f, 1, 0, 1f)
        }
    }

    private fun startIdCardRead() {
        Log.i(TAG, "startIdCardRead")
        LogHelper.setLevel(8)//身份证打印等级
        val ideograms = HashMap<String, Any>()
        ideograms[ParameterHelper.PARAM_KEY_VID] = 1024
        ideograms[ParameterHelper.PARAM_KEY_PID] = 50010
        val idCardReader = IDCardReaderFactory.createIDCardReader(
                ToolUtils.getApplicationContext(),
                TransportType.USB,
                ideograms
        )
        idCardReaderUtils.readerIdCard(idCardReader, CountDownLatch(1), object : MyCallBack {
            override fun onSuccess(idCardInfo: IDCardInfo?) {
                Log.i(TAG, "readerIdCard success 读卡成功" + (null == idcardinfo))
                if (isNoCard && null == idcardinfo) {
                    idcardinfo = idCardInfo
                    playSound(1)
                    isNoCard = false
                    showIdCardInfo(idcardinfo!!)
                }
            }

            override fun onFail(error: String?) {
                toastError("身份证读卡器出现异常,请检查设备")
                Log.i(TAG, "readerIdCard fail")
            }

            override fun onRequestDevicePermission() {
            }

            override fun onNoCards() {
                Log.i(TAG, "onNoCards")
                isNoCard = true
            }
        })
    }

    @SuppressLint("SetTextI18n")
    fun showIdCardInfo(idcardinfo: IDCardInfo) {
        Log.i(TAG, "showIdCardInfo")
        userInfo?.birthday = dateStringToString(idcardinfo.birth)
        if (idcardinfo.sex == "男") {
            userInfo?.gender = 1
        } else {
            userInfo?.gender = 2
        }
        userInfo?.idCard = idcardinfo.id
        userInfo?.nation = idcardinfo.nation
        userInfo?.name = idcardinfo.name
        userInfo?.address = idcardinfo.address
        Log.d(TAG, idcardinfo?.name + " / " + idcardinfo?.id)
        val intent = Intent(this, MobileActivity::class.java);
        intent?.putExtra("type", 2);
        startActivity(intent)
        finish()
    }

    override fun onStop() {
        Log.i(TAG, "onStop")
        isActivity = false
        idCardReaderUtils.setbStoped(true)
        super.onStop()
    }

    override fun onDestroy() {
        Log.i(TAG, "onDestroy")
        isActivity = false
        try {
            idCardReaderUtils.setbStoped(true)
            soundPool.release()
        } catch (e: Exception) {

            //写入到e的日志文件中
            MobclickAgent.reportError(this, e.message!!)
            Log.e(TAG, e.message);
        }
        super.onDestroy()
    }


    private fun dateStringToString(date: String?): String {
        Log.i(TAG, "dateStringToString")
        val dtf = DateTimeFormat.forPattern("yyyy年MM月dd日")
        val dateTime = dtf.parseDateTime(date)
        return dateTime?.toString("yyyy-MM-dd")!!
    }


}