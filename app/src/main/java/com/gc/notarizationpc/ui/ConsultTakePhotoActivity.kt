package com.gc.notarizationpc.ui

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.hardware.usb.UsbDevice
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.ImageView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.fastjson.JSON
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.eloam.gaopaiyi.util.UVCCameraUtil
import com.example.framwork.adapter.CommonQuickAdapter
import com.example.framwork.utils.DLog
import com.example.framwork.utils.MyLogUtils
import com.example.framwork.widget.kprogresshud.KProgressHUD
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.AppConfig
import com.gc.notarizationpc.common.BaseActivity
import com.gc.notarizationpc.model.*
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter
import com.gc.notarizationpc.utils.*
import com.gc.notarizationpc.websocket.MyMqttService
import com.gc.notarizationpc.websocket.WebSocketCallBack
import com.serenegiant.usb.IFrameCallback
import com.serenegiant.usb.USBMonitor
import es.dmoral.toasty.Toasty
import kotlinx.android.synthetic.main.dialog_take_photo.*
import kotlinx.android.synthetic.main.layout_information_photo.*
import kotlinx.android.synthetic.main.layout_information_photo.informationPhoto
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import java.nio.ByteBuffer

//公正咨询拍照
class ConsultTakePhotoActivity : BaseActivity(), OnItemClickListener, WebSocketCallBack {
    private var TAG = "ConsultTakePhotoActivity"
    private var mUSBMonitor: USBMonitor? = null
    private var come: Int = 0
    private var materialName: String? = ""
    private var pidGPY: String = AppConfig.HIGHPID //10a0 高拍仪原ID
    private var captureStillTwo = false //高拍  拍照
    protected var progressTakeHUD: KProgressHUD? = null
    private val WIDTH: Int = 1280
    private val HEIGHT: Int = 720
    private val PHOTO_SIZE = WIDTH * HEIGHT * 3 / 2
    private var imgKjStack = ImageStack(WIDTH, HEIGHT)
    var annexId: String = ""
    private var updateP: NotarizationPresenter? = null
    private var uploadedFilePaths = ArrayList<ImageRemoteBean>()
    private var consultImgAdapter: ConsultUploadImgAdapter? = null

    //全局定义
    private var lastClickTime = 0L
    private var lastClickTimeT = 0L
    private var isBack: Boolean = false

    private var initTime: Long = 0;
    private var unitGuid: String = ""
    private var roomId: String = ""
    private var myMqttService: MyMqttService? = null

    // 两次点击间隔不能少于1000ms
    private val FAST_CLICK_DELAY_TIME = 1800
    override fun getContentViewLayoutID(): Int {
        return if (AppConfig.RGBPID == "c013") {
            R.layout.dialog_take_photo;
        } else {
            R.layout.dialog_take_photo;
        }
    }

    override fun initViewsAndEvents() {
        runOnUiThread {
            showProgress(false, "高拍仪加载中")
            val timer: CountDownTimer = object : CountDownTimer(4 * 1000, 1000) {
                @SuppressLint("SetTextI18n")
                override fun onTick(millisUntilFinished: Long) {

                }

                override fun onFinish() {
                    hideProgress()
                    this.cancel()
                }
            }
            timer.start() // 开始计时
        }

        Log.i(TAG, "initViewsAndEvents")
        initTime = System.currentTimeMillis();
        uploadedFilePaths = ArrayList()
        updateP = NotarizationPresenter(mActivity)
        consultImgAdapter = ConsultUploadImgAdapter()
        consultImgAdapter?.setOnItemClickListener(this)
        val linearLayoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        rnl_imgs.layoutManager = linearLayoutManager
        myMqttService = MyMqttService(this, ".topic.consulting.collect." + userInfo?.idCard,
                ".topic.consulting." + userInfo?.idCard, AppConfig.HOST, this)
        myMqttService?.start()


//        hintPhoto.text = "点击下方拍照按钮, 上传文件"
//        if (come == 1) {
//            btnTake.visibility = View.INVISIBLE
//            hintPhoto.text = "公证员正在拍照, 请勿操作机器"
//            captureStillTwo = true
//        } else if (come == 3) {
//            hintPhoto.visibility = View.VISIBLE
//        } else if (come == 4) {
//            hintPhoto.visibility = View.VISIBLE
//        } else
//            hintPhoto.visibility = View.VISIBLE
        YuvUtils.init(this)

        tv_upload.setOnClickListener(object : MyOnclickClickListener() {
            override fun mOnClick(view: View) {
                //防止重复点击
                tv_upload.isClickable = false
//
                if (uploadedFilePaths?.size <= 0) {
                    toastWarning("请先拍摄材料后在上传")
                    tv_upload.isClickable = true
                    return
                }
                var filepaths = ""
                for (img in uploadedFilePaths) {
                    annexId = annexId?.plus(img.unitGuid).plus(",")
                    filepaths = filepaths?.plus(img.filePath).plus(",")
                }
                pushImage(filepaths.substring(0, filepaths.length - 1), annexId.substring(0, annexId.length - 1))
            }
        })
        ivClose.setOnClickListener {
            if (System.currentTimeMillis() - initTime > 2000) {
                if (!isBack) {
                    isBack = true
                    finish()
                }
            } else {
                Toasty.info(this, "正在退出相机").show()
            }
        }
        //拍照
        tv_capture.setOnClickListener(object : MyOnclickClickListener() {
            override fun mOnClick(v: View) {
                if (uploadedFilePaths?.size >= 20) {
                    toastWarning("单次最多拍摄20张图片")
                    return
                }
                if (System.currentTimeMillis() - lastClickTime >= FAST_CLICK_DELAY_TIME) {
                    Log.i(TAG, "takePhotoClick")
                    captureStillTwo = true
                    lastClickTime = System.currentTimeMillis()


                } else {
                    Log.i(TAG, "double takePhotoClick")
                }
            }
        })
        if (uploadedFilePaths?.size < 20) {
            openCamera()
        }

    }

    /**
     * 接收到websocket消息
     */
    override fun onSocketMessage(next: String?) {
        DLog.d("Socket:$next")
        Log.i(TAG, "Socket:$next")
        if (next?.contains("{") == true) {
            lifecycleScope.launch(Dispatchers.Main) {
                val info: SocketMessageInfo = JSON.parseObject(next, SocketMessageInfo::class.java)
                if (info.code == "deleteConsultingFile") {//删除公证咨询照片

                    val imageInfo = JSON.parseObject(info.info, GzzxImageBean::class.java)
                    val eventFlagBean = EventFlagBean();
                    eventFlagBean.flag = 1
                    eventFlagBean.content = imageInfo
                    EventBus.getDefault().post(eventFlagBean)
                }
            }
        }
    }


    /**
     * 图片路径，图片id
     */
    fun pushImage(imagePath: String, annexID: String) {
        if (System.currentTimeMillis() - CommonUtil.time_t < 3000)
            return
        CommonUtil.time_t = System.currentTimeMillis()

        Log.e("Test", "pushImage的参数:" + annexID);
        updateP?.addConsultBasicImage(unitGuid, annexID, object : NotarizationPresenter.CommonView {
            override fun commonSuccess(msg: String?) {
                tv_upload.isClickable = true //恢复上传图片的点击按钮
                //将上传成功的图片信息带回到前一个页面刷新展示
                var imagebean = ImageRemoteBean()
                imagebean.filePath = imagePath
                imagebean.unitGuid = annexID
                val eventFlagBean = EventFlagBean();
                eventFlagBean.flag = 2
                eventFlagBean.content = imagebean
                EventBus.getDefault().post(eventFlagBean)
                finish()
            }

            override fun commonFail() {
                tv_upload.isClickable = true //恢复上传图片的点击按钮
                toastError("上传图片失败")
            }
        })

    }

    override fun showProgress(isCancel: Boolean?, hint: String?) {
        if (!isFinishing) {
            if (progressTakeHUD == null) {
                progressTakeHUD = KProgressHUD.create(this).setStyle(KProgressHUD.Style.SPIN_INDETERMINATE).setDimAmount(0.5f)
            }
            if (!progressTakeHUD?.isShowing!!) {
                if (!TextUtils.isEmpty(hint)) {
                    progressTakeHUD!!.setLabel(hint)
                } else {
                    progressTakeHUD!!.setLabel("正在拍照上传...")
                }
                if (isCancel != null) {
                    progressTakeHUD!!.setCancellable(isCancel)
                }
                if (!progressTakeHUD!!.isShowing) progressTakeHUD!!.show()
            }
        }
    }

    override fun hideProgress() {
        Log.e("Test", "hideProgress");
        if (progressTakeHUD != null && progressTakeHUD!!.isShowing) {
            progressTakeHUD!!.dismiss()
        }
    }

    /**
     * 打开相机
     */
    private fun openCamera() {
        Log.i(TAG, "openCamera")

        mUSBMonitor =
                UVCCameraLiceo.initUSBMonitor(mActivity, object : UVCCameraLiceo.OnMyDevConnectListener {
                    override fun onConnectDev(
                            device: UsbDevice,
                            ctrlBlock: USBMonitor.UsbControlBlock
                    ) {
                        if (System.currentTimeMillis() - lastClickTimeT >= FAST_CLICK_DELAY_TIME) {
                            Log.i(TAG, "takePhotoClick")
                            val pid = String.format("%x", device.productId)
                            Log.i(TAG, "onConnectDev() pid:" + pid)
                            Log.i(TAG, "onConnectDev() pidGPY:" + pidGPY)
                            if (pid == pidGPY) { //打开高拍 1054
                                UVCCameraLiceo.openRGBCamera(
                                        informationPhoto,
                                        ctrlBlock,
                                        object : IFrameCallback {
                                            override fun onFail() {
                                                Log.i(TAG, "openCamera fail")
                                                hideProgress()
                                                MyLogUtils.i(TAG, "高拍仪打开失败")
                                                toastError("高拍仪打开失败")
                                                finish()
                                            }

                                            override fun onFrame(frame: ByteBuffer?) {
//                                                imgKjStack.pushImageInfo(frame!!, System.currentTimeMillis())
                                                if (captureStillTwo) {
                                                    captureStillTwo = false
                                                    takePhoto()
                                                } else
                                                    hintPhoto.visibility = View.VISIBLE
                                            }
                                        })

                            }
                            lastClickTimeT = System.currentTimeMillis()
                        } else {
                            Log.i(TAG, "double takePhotoClick")
                        }
                    }
                })
        var hasHigh = false;
        val devList = UVCCameraUtil.getUsbDeviceList(this, mUSBMonitor!!)
        for (usbDevice in devList) {
            val productid = String.format("%x", usbDevice?.productId)
            Log.e("zzkong", "找到了: $productid")
            if (productid == "1054") {
                hasHigh = true
            }
        }
        if (!hasHigh) {
            toastError("高拍仪连接出现异常")
        }
    }

    @Subscribe
    fun onEventMainThread(eb: TakePhotoInfo) {
        Log.i(TAG, "onEventMainThread")
        if (eb.info == 0) {
            captureStillTwo = true
        } else if (eb.info == 1) {
            finish()
        }
    }


    /**
     * 拍照
     */
    private fun takePhoto() {
        Log.i(TAG, "takePhoto")
        if (uploadedFilePaths.size >= 30) {
            toastWarning("单次最多上传30张图片")
            return
        }
        lifecycleScope.launch(Dispatchers.IO) {
            runOnUiThread {
                showProgress(false, "图片保存中")
            }

            val faceBitmap = informationPhoto?.getBitmap()
//                    val faceBitmap = YuvUtils.nv21ToBitmap(data, WIDTH, HEIGHT)
            if (faceBitmap != null && !faceBitmap.isRecycled) {
                // 修复图片被旋转的角度
                val bitmap: Bitmap? = rotateBitmap2(faceBitmap!!, 180)

                updateP?.updateBitmap(BitmapInfo(bitmap).path, object : NotarizationPresenter.IUpdateImageView {
                    override fun updateImageSuccess(bean: ImageBean?) {
                        val imageBean = ImageRemoteBean()
                        imageBean.filePath = bean?.filePath
                        imageBean.unitGuid = bean?.unitGuid

                        Log.e("Test", "图片上传成功" + bean?.unitGuid);

                        uploadedFilePaths.add(imageBean);
                        rnl_imgs.adapter = consultImgAdapter
                        //后拍的照片在上面
                        consultImgAdapter?.addNewData(uploadedFilePaths.reversed())
                        runOnUiThread {
                            hideProgress()
                            toastInfo("图片保存成功")
                        }
                    }

                    override fun updateImageFail() {
                        hideProgress()
                        toastError("图片保存失败")
                    }
                })
//
            }
        }

    }

    inner class ConsultUploadImgAdapter() : CommonQuickAdapter<ImageRemoteBean>(R.layout.item_img_with_del) {
        override fun convert(holder: BaseViewHolder, item: ImageRemoteBean?) {
            ImageLoaderManager.getInstance().displayImage(holder?.getView(R.id.imageView), item?.filePath)
            var imgDel = holder.getView<ImageView>(R.id.imageDelete);
            imgDel.setOnClickListener(object : MyOnclickClickListener() {
                override fun mOnClick(v: View?) {
                    uploadedFilePaths.remove(item)
                    consultImgAdapter?.addNewData(uploadedFilePaths)
                }

            })
        }
    }

    private fun rotateBitmap2(bm: Bitmap, orientationDegree: Int): Bitmap? {
        val ma = Matrix()
        ma.setRotate(orientationDegree.toFloat(), bm.width.toFloat() / 2, bm.height.toFloat() / 2)

        try {
            return Bitmap.createBitmap(bm, 0, 0, bm.width, bm.height, ma, true)
        } catch (ex: OutOfMemoryError) {
            Log.e(TAG, "位图旋转出错")
        }
        //方便判断，角度都转换为正值
        var degree = orientationDegree
        if (degree < 0) {
            degree = 360 + orientationDegree
        }
        val srcW = bm.width
        val srcH = bm.height
        val m = Matrix()
        m.setRotate(degree.toFloat(), srcW.toFloat() / 2, srcH.toFloat() / 2)
        val targetX: Float
        val targetY: Float
        var destH = srcH
        var destW = srcW

        //根据角度计算偏移量，原理不明
        if (degree == 90) {
            targetX = srcH.toFloat()
            targetY = 0f
            destH = srcW
            destW = srcH
        } else if (degree == 270) {
            targetX = 0f
            targetY = srcW.toFloat()
            destH = srcW
            destW = srcH
        } else if (degree == 180) {
            targetX = srcW.toFloat()
            targetY = srcH.toFloat()
        } else {
            return bm
        }
        val values = FloatArray(9)
        m.getValues(values)
        val x1 = values[Matrix.MTRANS_X]
        val y1 = values[Matrix.MTRANS_Y]
        m.postTranslate(targetX - x1, targetY - y1)

        //注意destW 与 destH 不同角度会有不同
        val bm1 = Bitmap.createBitmap(destW, destH, Bitmap.Config.ARGB_8888)
        val paint = Paint()
        val canvas = Canvas(bm1)
        canvas.drawBitmap(bm, m, paint)
        return bm1

    }

    override fun onResume() {
        super.onResume()
        UVCCameraLiceo.registerUsbMonitor(mUSBMonitor!!)
        UVCCameraLiceo.requestPermissionRGB(mActivity, mUSBMonitor!!, pidGPY)
    }

    override fun onPause() {
        super.onPause()
        try {
            UVCCameraLiceo.releaseRGBCamera()
            UVCCameraLiceo.unRegisterUsbMonitor(mUSBMonitor!!)
        } catch (e: Exception) {
            Log.e(TAG, "onPause:" + e.message)
        }
    }

    //--------------------------
    override fun onStart() {
        Log.i(TAG, "onStart")
        super.onStart()
    }

    override fun onStop() {
        Log.i(TAG, "onStop")
        /*mUSBMonitor?.unregister()*/
        super.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            YuvUtils.destroy()
            if (come != 1) {
                UVCCameraLiceo.destroyUSBMonitor(mUSBMonitor!!)
                mUSBMonitor = null
            }
        } catch (e: Exception) {
            Log.i(TAG, "onDestroy")
        }
        progressTakeHUD?.dismiss()

        Log.i(TAG, "onDestroy")
    }

    override fun getIntentData(intent: Intent?) {
        come = intent?.getIntExtra("come", 0)!!
        roomId = intent?.getStringExtra("roomId")
        unitGuid = intent?.getStringExtra("unitGuid")
        if (come == 3) {
            materialName = intent.getStringExtra("materialName")
        }
    }

    override fun isUseEventBus(): Boolean {
        return true
    }

    override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {

    }
}