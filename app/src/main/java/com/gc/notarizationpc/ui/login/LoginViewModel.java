package com.gc.notarizationpc.ui.login;

import android.app.Application;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.SerachBean;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.request.ImageBean;
import com.gc.notarizationpc.data.model.request.LoginOnRequest;
import com.gc.notarizationpc.myview.SearchDialog;

import java.util.ArrayList;
import java.util.List;

import es.dmoral.toasty.Toasty;
import me.goldze.mvvmhabit.base.AppManager;
import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.binding.command.BindingConsumer;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.greendao.model.LoginInfo;
import me.goldze.mvvmhabit.http.BaseResponse;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.MD5Util;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;
import okhttp3.MultipartBody;

/**
 * Created by goldze on 2017/7/17.
 */
//BaseViewModel与BaseActivity通过LiveData来处理常用UI逻辑，即可在ViewModel中使用父类的showDialog()、startActivity()等方法。在这个LoginViewModel中就可以尽情的写你的逻辑了！
public class LoginViewModel extends BaseViewModel {
    //输入框中输入了什么，userName.get()的内容就是什么，userName.set("")设置什么，输入框中就显示什么。 注意： @符号后面需要加=号才能达到双向绑定效果；userName需要是public的，不然viewModel无法找到它。
    //用户名的绑定
    public ObservableField<String> userName = new ObservableField<>("");
    //密码的绑定
    public ObservableField<String> password = new ObservableField<>("");
    //用户名清除按钮的显示隐藏绑定
    public ObservableInt clearBtnVisibility = new ObservableInt();

    public ObservableField<String> time = new ObservableField<>("");

    public ObservableInt num = new ObservableInt();

    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    public SingleLiveEvent<BaseResponse> loginInfoSingleLiveEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<String> errorStr = new SingleLiveEvent<>();

    public class UIChangeObservable {
        //密码开关观察者
        public SingleLiveEvent<Boolean> pSwitchEvent = new SingleLiveEvent<>();

    }

    public LoginViewModel(@NonNull Application application) {
        super(application);
        //从本地取得数据绑定到View层
        userName.set(SPUtils.getInstance().getString("username"));
        password.set(SPUtils.getInstance().getString("password"));
    }

    //清除用户名的点击事件, 逻辑从View层转换到ViewModel层
    public BindingCommand clearUserNameOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            userName.set("");
        }
    });
    //密码显示开关  (你可以尝试着狂按这个按钮,会发现它有防多次点击的功能)
    public BindingCommand passwordShowSwitchOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            //让观察者的数据改变,逻辑从ViewModel层转到View层，在View层的监听则会被调用
            uc.pSwitchEvent.setValue(uc.pSwitchEvent.getValue() == null || !uc.pSwitchEvent.getValue());
        }
    });

    public BindingCommand showTimePick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {

        }
    });


    //用户名输入框焦点改变的回调事件
    public BindingCommand<Boolean> onFocusChangeCommand = new BindingCommand<>(new BindingConsumer<Boolean>() {
        @Override
        public void call(Boolean hasFocus) {
            if (hasFocus) {
                clearBtnVisibility.set(View.VISIBLE);
            } else {
                clearBtnVisibility.set(View.INVISIBLE);
            }
        }
    });
    //登录按钮的点击事件
    public BindingCommand loginOnClickCommand = new BindingCommand(new BindingAction() {
        @RequiresApi(api = Build.VERSION_CODES.M)
        @Override
        public void call() {
//            login();
            loginOn();
        }
    });


    public void uploadSign(MultipartBody.Part body) {
        RequestUtil.uploadSingleFile(body, new MyObserver<ImageBean>() {
            @Override
            public void onSuccess(ImageBean result) {
                Log.d("Test", "文件上传成功:" + result.filePath);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                Log.d("Test", "文件上传失败:" + errorMsg);
            }
        });
    }


    private void loginOn() {
        Log.e("Test", "时间==" + time.get() + "num==" + num.get());
        if (TextUtils.isEmpty(userName.get())) {
            ToastUtils.showShort("请输入账号！");
            return;
        }
        if (TextUtils.isEmpty(password.get())) {
            ToastUtils.showShort("请输入密码！");
            return;
        }

        showDialog(Utils.getContext().getString(R.string.loading)+"...");
        LoginOnRequest request = new LoginOnRequest(userName.get(), MD5Util.getMD5Str(password.get()), 1, 1);
        RequestUtil.LoginOn(request, new MyObserver<LoginInfo>() {
            @Override
            public void onSuccess(LoginInfo result) {
                dismissDialog();
                BaseResponse<LoginInfo> response=new BaseResponse<>();
                response.setResult(result);
                response.setSuccess(true);
                loginInfoSingleLiveEvent.setValue(response);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                BaseResponse<String> response=new BaseResponse<>();
                response.setResult(errorMsg);
                response.setSuccess(false);
                loginInfoSingleLiveEvent.setValue(response);
            }
        });
    }


    /**
     * 网络模拟一个登陆操作
     **/
    @RequiresApi(api = Build.VERSION_CODES.M)
    private void login() {

        List<SerachBean> serachBeans = new ArrayList<>();
        String data = "{\"code\":200,\"message\":\"\",\"success\":\"true\",\n" +
                "\"data\":{\"#\":[{\"name\":\"666\",\"id\":\"1\"}, {\"name\":\"777\",\"id\":\"2\"}],\n" +
                "\"A\":[{\"name\":\"哎的香\",\"id\":\"3\"}],\n" +
                "\"B\":[{\"name\":\"把的香\",\"id\":\"4\"}],\n" +
                "\"C\":[{\"name\":\"吃的香\",\"id\":\"5\"}],\n" +
                "\"D\":[{\"name\":\"都的香\",\"id\":\"6\"}],\n" +
                "\"E\":[{\"name\":\"饿的香\",\"id\":\"7\"}],\n" +
                "\"F\":[{\"name\":\"分的香\",\"id\":\"8\"}],\n" +
                "\"G\":[{\"name\":\"搞的香\",\"id\":\"9\"}]\n" +
                "       }     \n" +
                "}";
        //Feature.OrderedField 防止array顺序错乱
        JSONObject jsonObject = JSON.parseObject(data, Feature.OrderedField);
        JSONObject jsonObject1 = jsonObject.getJSONObject("data");

        for (String key : jsonObject1.keySet()) {
            SerachBean beanList = new SerachBean();
            Log.d("zpzp", key);
            beanList.setKey(key);
            JSONArray array = jsonObject1.getJSONArray(key);
            List<SerachBean.Bean> bean = JSONObject.parseArray(array.toJSONString(), SerachBean.Bean.class);
            beanList.setBean(bean);
            serachBeans.add(beanList);
        }
        //展示搜索dialog
        SearchDialog dialog = new SearchDialog(AppManager.getAppManager().currentActivity(), serachBeans, true, "", 3);
        dialog.setInterface(o -> {
            String text = "";
            for (int i = 0; i < o.size(); i++) {
                if (i == o.size() - 1) {
                    text += o.get(i);
                } else
                    text += o.get(i) + ",";
            }
            Toasty.info(AppManager.getAppManager().currentActivity(), "当前选择的是" + text, 3000).show();
        });
        dialog.show();


    }


    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
