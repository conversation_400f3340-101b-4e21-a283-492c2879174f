package com.gc.notarizationpc.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.example.framwork.adapter.CommonQuickAdapter
import com.gc.notarizationpc.R
import com.gc.notarizationpc.model.DeviceInfo
import com.gc.notarizationpc.ui.viewholder.BannerHolder
import com.youth.banner.adapter.BannerAdapter

/**
 * 自定义布局，网络图片
 */
class DeviceAdapter() : CommonQuickAdapter<DeviceInfo>(R.layout.item_device) {
    override fun convert(holder: BaseViewHolder?, item: DeviceInfo?) {
        holder?.setText(R.id.tvAddress, item?.address)
        holder?.setText(R.id.tvNotary, item?.notarialName)
    }
}