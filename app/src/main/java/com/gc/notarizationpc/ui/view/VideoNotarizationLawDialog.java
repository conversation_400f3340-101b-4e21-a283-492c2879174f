package com.gc.notarizationpc.ui.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Environment;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.downloader.Error;
import com.downloader.OnDownloadListener;
import com.downloader.PRDownloader;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.DocInfoResponse;
import com.gc.notarizationpc.ui.video.CounselingRoomActivity;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.util.RecyclerViewPageChangeListenerHelper;
import com.github.barteksc.pdfviewer.PDFView;
import com.github.barteksc.pdfviewer.listener.OnPageErrorListener;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import cc.shinichi.library.ImagePreview;
import cc.shinichi.library.bean.ImageInfo;
import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.base.BaseAdapter;
import me.goldze.mvvmhabit.base.BaseViewHolder;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.GlideRoundedCornersTransform;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.goldze.mvvmhabit.utils.thread.ThreadPoolManager;
import me.goldze.mvvmhabit.widget.kprogresshud.KProgressHUD;

/**
 * 受理室 法律文书
 */
public class VideoNotarizationLawDialog extends Dialog {
    private Context mContext;
    private RecyclerView rvParent;
    TextFlowLayout flowLayout;
    private Handler mHandler;
    private ProgressBar progressBar;
    private PDFView pdfView;
    int currentItem;
    RecyclerView vpImage;
    ImagePreviewAdapter imagePreviewAdapter;
    ImageAdapter applySheetAdapter;
    private List<DocInfoResponse> mDocInfoResponse;
    private TextView tvSign;
    private DocInfoResponse mCurrentPersonDoc;//选中的当事人所需要签字的材料
    private DocInfoResponse.DocTypeListDTO.DocumentVOListDTO chooseDocInfo;//选中的文书

    PopwindowUtil.ResultListener myResultListener;
    private List<DocInfoResponse.DocTypeListDTO> mCurrentDocLists;
    private String mCaseInfoId;
    private ParentAdatper adatper;

    // 当前被邀请的当事人身份证号
    private String mCurrentIdCard;

    // 当前公证会员是否加入受理室
    private boolean isRemoteJoin;

    private KProgressHUD progressHUD;

    private CountDownTimer timer;

    public VideoNotarizationLawDialog(Context context, Handler handler, List<DocInfoResponse> docInfoResponse, String caseInfoId, String currentIdCard, boolean isJoin, PopwindowUtil.ResultListener resultListener) {
        super(context);
        mContext = context;
        mHandler = handler;
        myResultListener = resultListener;
        mDocInfoResponse = docInfoResponse;
        mCurrentIdCard = currentIdCard;
        mCaseInfoId = caseInfoId;
        isRemoteJoin = isJoin;
    }

    public void showProgress(Boolean isCancel, String hint) {
        if (progressHUD == null) {
            progressHUD = KProgressHUD.create(mContext).setStyle(KProgressHUD.Style.SPIN_INDETERMINATE).setDimAmount(0.5f);
        }
        if (!progressHUD.isShowing()) {

            if (!TextUtils.isEmpty(hint)) {
                progressHUD.setLabel(hint);
            } else {
                progressHUD.setLabel(Utils.getContext().getString(R.string.loading));
            }
            progressHUD.setCancellable(isCancel);
            if (!progressHUD.isShowing() && !((BaseActivity) mContext).isFinishing())
                progressHUD.show();
        }
    }

    public void hideProgress() {
        if (progressHUD != null) {
            progressHUD.dismiss();
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_video_notarization_law);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();
        showData();
    }

    private void showData() {
        if (mDocInfoResponse != null && mDocInfoResponse.size() > 0) {
            if (TextUtils.isEmpty(mCurrentIdCard)) {
                mDocInfoResponse.get(0).setChoose(true);
                flowLayout.setTextList(mDocInfoResponse);
                mCurrentPersonDoc = mDocInfoResponse.get(0);
                mCurrentDocLists = mDocInfoResponse.get(0).getDocTypeList();
            } else {
                List<DocInfoResponse> tempList = new ArrayList<>();
                for (DocInfoResponse docInfoResponse : mDocInfoResponse) {
                    if (docInfoResponse.getCredentialNum().equals(mCurrentIdCard)) {
                        docInfoResponse.setChoose(true);
                        tempList.add(docInfoResponse);
                        flowLayout.setTextList(tempList);
                        mCurrentPersonDoc = docInfoResponse;
                        mCurrentDocLists = docInfoResponse.getDocTypeList();
                    }
                }

            }
        } else {
            return;
        }
        if (checkDocIsNull(mCurrentPersonDoc)) {
            tvSign.setVisibility(View.GONE);
        } else {
            tvSign.setVisibility(View.VISIBLE);
        }
        flowLayout.setOnFlowTextItemClickListener(new TextFlowLayout.OnFlowTextItemClickListener() {
            @Override
            public void onFlowItemClick(DocInfoResponse item) {
                if (item != null && item.isChoose()) {
                    mCurrentPersonDoc = item;
                    mCurrentDocLists = item.getDocTypeList();
                    adatper.refreshAdapter(mCurrentDocLists);
                    adatper.notifyDataSetChanged();
                    rvParent.setAdapter(adatper);
                    if (checkDocIsNull(mCurrentPersonDoc)) {
                        tvSign.setVisibility(View.GONE);
                    } else {
                        tvSign.setVisibility(View.VISIBLE);
                    }
                    judgeCurrentPersonSignStatus();
                }
            }
        });
        judgeCurrentPersonSignStatus();
//
//        mCurrentPersonDoc = mDocInfoResponse.get(0);
//        mCurrentDocLists = mDocInfoResponse.get(0).getDocTypeList();
////        //签字按钮初始化文字
////        if (mCurrentDocLists.getPersonSignStatus() == 1) {
////            tvSign.setText(mContext.getString(R.string.read_sign_again));
////        } else if (chooseDocInfo.getPersonSignStatus() == -1) {
////            tvSign.setText(mContext.getString(R.string.read_and_sign));
////        }

        adatper.refreshAdapter(mCurrentDocLists);
        rvParent.setAdapter(adatper);
        vpImage.setVisibility(View.GONE);

    }

    /**
     * 判断当事人是否要重新签字
     */
    private void judgeCurrentPersonSignStatus() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        Integer readSignAgain = 0;
        for (int i = 0; i < mCurrentDocLists.size(); i++) {
            if (mCurrentDocLists.get(i).getDocumentVOList() != null) {
                for (int j = 0; j < mCurrentDocLists.get(i).getDocumentVOList().size(); j++) {
                    if (mCurrentDocLists.get(i).getDocumentVOList().get(j).getPersonSignStatus() == 1) {
                        readSignAgain = 1;
                    }
                }
            }
        }
        if (readSignAgain == 1) {
            tvSign.setText(mContext.getString(R.string.read_sign_again));
        } else {
            tvSign.setText(mContext.getString(R.string.read_and_sign));
        }
    }

    /**
     * 校验文书 如果子类都为空 屏蔽 签字按钮
     *
     * @param docInfoResponse
     * @return
     */
    private boolean checkDocIsNull(DocInfoResponse docInfoResponse) {
        int count = 0;
        for (DocInfoResponse.DocTypeListDTO docTypeListDTO : docInfoResponse.getDocTypeList()) {
            if (docTypeListDTO.getDocumentVOList() == null || docTypeListDTO.getDocumentVOList().size() == 0) {
                count++;
            }
        }
        if (count == docInfoResponse.getDocTypeList().size()) {
            return true;
            //当前当事人下面所有文书材料为空
        }
        return false;
    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        rvParent = findViewById(R.id.rv_parent);
        flowLayout = findViewById(R.id.flow_names);
        tvSign = findViewById(R.id.tv_sign);
//        pdfView = findViewById(R.id.pdfView);
//        progressBar = findViewById(R.id.progressBar);

        rvParent.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        adatper = new ParentAdatper(mContext, R.layout.item_list_law_material_parent, 0);

        findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowing()) {
                    dismiss();
                }
            }
        });

        findViewById(R.id.iv_refresh).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showProgress(true, "");
                //刷新
                RequestUtil.listApplicantsAndDocuments(mCaseInfoId, new MyObserver<List<DocInfoResponse>>() {
                    @Override
                    public void onSuccess(List<DocInfoResponse> result) {
                        hideProgress();
                        if (result != null) {
                            mDocInfoResponse = result;
                            showData();
                        }
                    }

                    @Override
                    public void onFailure(Throwable e, String errorMsg) {
                        hideProgress();
                        ToastUtils.toastError(errorMsg);
                    }
                });
            }
        });

//        try {
//            if (mDocInfoResponse.get(0).getDocTypeList().get(0).getDocumentVOList().get(0).getPersonSignStatus() == 1) {
//                tvSign.setText(mContext.getString(R.string.read_sign_again));
//            } else if (mDocInfoResponse.get(0).getDocTypeList().get(0).getDocumentVOList().get(0).getPersonSignStatus() == -1) {
//                tvSign.setText(mContext.getString(R.string.read_and_sign));
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }


        tvSign.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtil.disabledView(v);
                ////阅读文书倒计时结束后倒计时按钮变为完成阅读按钮，点击后显示签字页面
                if (((TextView) v).getText().toString().equals(mContext.getString(R.string.complete_read))) {
                    dismiss();
                    myResultListener.result(mCurrentPersonDoc);
                } else if (((TextView) v).getText().toString().equals(mContext.getString(R.string.read_and_sign))
                        || ((TextView) v).getText().toString().equals(mContext.getString(R.string.read_sign_again))) {
                    //阅读并批量签字/重新阅读并签字 点击后默认全选所有文书进行左侧预览
                    getAllLawFiles();

                    //文书预览时阅读并批量签字/重新阅读并签字按钮变为文书须阅读倒计时按钮，倒计时应为所有文书阅读时间的总和
                    if (chooseDocInfo != null) {
                        timer = new CountDownTimer(getAllLawMinRead() * 1000, 1000) {
                            @Override
                            public void onTick(long l) {
                                tvSign.setText(mContext.getString(R.string.must_read) + l / 1000 + "s");
                            }

                            @Override
                            public void onFinish() {
                                //阅读文书倒计时结束后倒计时按钮变为完成阅读按钮，点击后显示签字页面
                                tvSign.setText(mContext.getString(R.string.complete_read));
                            }
                        };
                        timer.start();
                    } else {
                        tvSign.setText(mContext.getString(R.string.complete_read));

                    }
                }


            }
        });

        vpImage = findViewById(R.id.viewPager);

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(mContext);
        linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        vpImage.setLayoutManager(linearLayoutManager);
        PagerSnapHelper snapHelper = new PagerSnapHelper();
        snapHelper.attachToRecyclerView(vpImage);
        imagePreviewAdapter = new ImagePreviewAdapter(mContext, R.layout.item_image_perview, false);
        imagePreviewAdapter.setOnItemClickListener(new BaseViewHolder.OnItemClickListener<String>() {
            @Override
            public void onItemClick(BaseViewHolder viewHolder, int position, String itemData, Object type) {
                //点击文书图片放大
                if (TextUtils.isEmpty(itemData) || chooseDocInfo == null) {
                    return;
                }
                List<String> filePaths = new ArrayList<>();
                List<ImageInfo> imgs = new ArrayList<>();
                vpImage.setVisibility(View.VISIBLE);
                if (chooseDocInfo.getSignStatus() == -1)//-1-不能签，1-可以签
                {
                    filePaths = chooseDocInfo.getUnsignDocumentFileUrl();
                } else {
                    filePaths = chooseDocInfo.getSignDocumentFileUrl();
                }

                for (String item : filePaths) {
                    ImageInfo imageInfo = new ImageInfo();
                    imageInfo.setThumbnailUrl(item);
                    imageInfo.setOriginUrl(item);
                    imgs.add(imageInfo);
                }
                ImageInfo imageInfo = new ImageInfo();
                imageInfo.setThumbnailUrl("https://testgz.njguochu.com:48002/tmp/773bb318c58b4a7aa0b63449e009836c.jpg?response-content-disposition=attachment&response-content-type=application%2Foctet-stream&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241204T054509Z&X-Amz-SignedHeaders=host&X-Amz-Expires=604800&X-Amz-Credential=axpHdwSzaK44eWuBcCeG%2F20241204%2Fnanjing%2Fs3%2Faws4_request&X-Amz-Signature=deeb2f5bff16fc40d380efb53d32299b143cf1ceec2b541227ec82866a4925f3");
                imageInfo.setOriginUrl("https://testgz.njguochu.com:48002/tmp/773bb318c58b4a7aa0b63449e009836c.jpg?response-content-disposition=attachment&response-content-type=application%2Foctet-stream&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241204T054509Z&X-Amz-SignedHeaders=host&X-Amz-Expires=604800&X-Amz-Credential=axpHdwSzaK44eWuBcCeG%2F20241204%2Fnanjing%2Fs3%2Faws4_request&X-Amz-Signature=deeb2f5bff16fc40d380efb53d32299b143cf1ceec2b541227ec82866a4925f3");
                imgs.add(imageInfo);
                ImagePreview.getInstance().setContext(mContext).setShowCloseButton(false).setShowDownButton(false).setImageInfoList(imgs).setTransitionShareElementName("shared_element_container").start();

            }

            @Override
            public void onItemLongClick(BaseViewHolder viewHolder, int position, String itemData, Object type) {

            }
        });

        vpImage.addOnScrollListener(new RecyclerViewPageChangeListenerHelper(snapHelper,
                new RecyclerViewPageChangeListenerHelper.OnPageChangeListener() {

                    @Override
                    public void onScrollStateChanged(RecyclerView recyclerView, int newState) {

                    }

                    @Override
                    public void onScrolled(RecyclerView recyclerView, int dx, int dy) {

                    }

                    @Override
                    public void onPageSelected(int position) {
                        currentItem = position;

                    }
                }));

    }


    //获取所选择的当事人所有的文书列表
    private void getAllLawFiles() {
        if (mCurrentPersonDoc == null) {
            return;
        }
        List<String> allFilesPath = new ArrayList<>();
        if (mCurrentPersonDoc.getDocTypeList() != null) {
            for (DocInfoResponse.DocTypeListDTO docTypeListDTO : mCurrentPersonDoc.getDocTypeList()) {
                if (docTypeListDTO.getDocumentVOList() != null) {
                    for (DocInfoResponse.DocTypeListDTO.DocumentVOListDTO documentVOListDTO : docTypeListDTO.getDocumentVOList()) {
                        if (documentVOListDTO != null && !TextUtils.isEmpty(documentVOListDTO.getId())) {
                            if (documentVOListDTO.getSignStatus() == -1)//-1-不能签，1-可以签
                            {
                                allFilesPath.addAll(documentVOListDTO.getUnsignDocumentFileUrl());
                            } else {
                                allFilesPath.addAll(documentVOListDTO.getSignDocumentFileUrl());
                            }
                        }
                    }
                }
            }
        }
        vpImage.setVisibility(View.VISIBLE);
        imagePreviewAdapter.refreshAdapter(allFilesPath);
        vpImage.setAdapter(imagePreviewAdapter);
        vpImage.scrollToPosition(0);//初次划到位置
        imagePreviewAdapter.notifyDataSetChanged();
    }


    //获取所选择的当事人所有的文书阅读时间
    private long getAllLawMinRead() {
        long minReadTime = 0;
        if (mCurrentPersonDoc != null) {
            if (mCurrentPersonDoc.getDocTypeList() != null) {
                for (DocInfoResponse.DocTypeListDTO docTypeListDTO : mCurrentPersonDoc.getDocTypeList()) {
                    if (docTypeListDTO.getDocumentVOList() != null) {
                        for (DocInfoResponse.DocTypeListDTO.DocumentVOListDTO documentVOListDTO : docTypeListDTO.getDocumentVOList()) {
                            if (documentVOListDTO != null && !TextUtils.isEmpty(documentVOListDTO.getId())) {
                                minReadTime += documentVOListDTO.getMinReadTime();
                            }
                        }
                    }
                }
            }
        }
        return minReadTime;
    }

    private void downloadPdfFromInternet(String url, String dirPath, String fileName) {
        Log.i("Test", "downloadPdfFromInternet");
        ((CounselingRoomActivity) mContext).showProgress();
        ThreadPoolManager.getInstance().startThread(new Runnable() {
            @Override
            public void run() {
                PRDownloader.download(url, dirPath, fileName)
                        .build()
                        .start(new OnDownloadListener() {
                            @Override
                            public void onDownloadComplete() {
                                File downloadedFile = new File(dirPath, fileName);
                                mHandler.post(new Runnable() {
                                    @Override
                                    public void run() {
                                        showPdfFromFile(downloadedFile);
                                        ((CounselingRoomActivity) mContext).hideProgress();

                                    }
                                });
                            }

                            @Override
                            public void onError(Error error) {
                                ((CounselingRoomActivity) mContext).toastError(Utils.getContext().getString(R.string.failedToLoadDocumentAndTryLater));
                            }
                        });

            }
        });

    }

    private void showPdfFromFile(File file) {
        pdfView.fromFile(file)
                .password(null)
                .defaultPage(0)
                .enableSwipe(true)
                .swipeHorizontal(false)
                .enableDoubletap(true)
                .onPageError(new OnPageErrorListener() {
                    @Override
                    public void onPageError(int page, Throwable t) {
                        // Handle page error if needed
                    }
                })
                .load();
    }


    private class ParentAdatper extends BaseAdapter<DocInfoResponse.DocTypeListDTO> {
        public ParentAdatper(Context context, int itemLayoutRes, @Nullable Object type) {
            super(context, itemLayoutRes, type);
            mContext = context;
        }

        @Override
        public void bind(@NonNull BaseViewHolder viewHolder, DocInfoResponse.DocTypeListDTO itemData, int position) {
            if (itemData == null) {
                return;
            }
            viewHolder.setText(R.id.tv_name, itemData.getTypeName());

            if (itemData.getDocumentVOList() != null && itemData.getDocumentVOList().size() > 0) {
                RecyclerView rvChild = viewHolder.getView(R.id.rv_child);
                ImageAdapter noticeAdapter = new ImageAdapter(mContext, R.layout.item_img, 0);
                rvChild.setAdapter(noticeAdapter);
                noticeAdapter.setDocumentVOListDTOS(itemData.getDocumentVOList());
                noticeAdapter.refreshAdapter(itemData.getDocumentVOList());
                rvChild.setLayoutManager(new GridLayoutManager(mContext, 3, LinearLayoutManager.VERTICAL, false) {
                    @Override
                    public boolean canScrollVertically() {
                        return false;
                    }
                });
                noticeAdapter.setOnItemClickListener(new BaseViewHolder.OnItemClickListener<DocInfoResponse.DocTypeListDTO.DocumentVOListDTO>() {
                    @Override
                    public void onItemClick(BaseViewHolder viewHolder, int position, DocInfoResponse.DocTypeListDTO.DocumentVOListDTO itemData, Object type) {
                        chooseDocInfo = itemData;
                    }

                    @Override
                    public void onItemLongClick(BaseViewHolder viewHolder, int position, DocInfoResponse.DocTypeListDTO.DocumentVOListDTO itemData, Object type) {

                    }
                });

            }

        }

        @Override
        public int viewType(DocInfoResponse.DocTypeListDTO itemData) {
            return 0;
        }
    }


    public class ImageAdapter extends BaseAdapter<DocInfoResponse.DocTypeListDTO.DocumentVOListDTO> {
        List<String> filePaths = new ArrayList<>();
        List<DocInfoResponse.DocTypeListDTO.DocumentVOListDTO> documentVOListDTOS = new ArrayList<>();

        public void setDocumentVOListDTOS(List<DocInfoResponse.DocTypeListDTO.DocumentVOListDTO> documentVOListDTOS) {
            this.documentVOListDTOS = documentVOListDTOS;
        }

        public ImageAdapter(Context context, int itemLayoutRes, @Nullable Object type) {
            super(context, itemLayoutRes, type);
            mContext = context;
        }

        @Override
        public void bind(@NonNull BaseViewHolder viewHolder, DocInfoResponse.DocTypeListDTO.DocumentVOListDTO itemData, int position) {
            if (itemData == null) {
                return;
            }
            //不会返回没有设置签字位置的文书
            //-1-全部未签署，1-部分已签署，2-全部已签署
            if (itemData.getSignStatus() == -1)//-1-不能签，1-可以签
            {
                filePaths = itemData.getUnsignDocumentFileUrl();
            } else {
                filePaths = itemData.getSignDocumentFileUrl();
            }
            if (filePaths == null || filePaths.size() == 0) {
                return;
            }
            RequestOptions options = new RequestOptions();
            //圆角
            options = options.transform(new
                    GlideRoundedCornersTransform(mContext, 6f, GlideRoundedCornersTransform.CornerType.ALL));
//            if (!TextUtils.isEmpty(itemData.getName())) {
            Glide.with(viewHolder.itemView.getContext())
                    .load(filePaths.get(0))
                    .apply(options).error(R.mipmap.erroimage)
                    .into((ImageView) viewHolder.getView(R.id.imageView));
            chooseDocInfo = documentVOListDTOS.get(0);
//            if (chooseDocInfo.getPersonSignStatus() == 1) {
//                tvSign.setText(mContext.getString(R.string.read_sign_again));
//            } else if (chooseDocInfo.getPersonSignStatus() == -1) {
//                tvSign.setText(mContext.getString(R.string.read_and_sign));
//            }

            if (itemData.isChoose()) {
                viewHolder.getView(R.id.imageView).setBackgroundResource(R.drawable.shape_corner5_3c6af4);
            } else {
                viewHolder.getView(R.id.imageView).setBackgroundResource(R.drawable.shape_corner5_f1f3f6_all);
            }
            if (!isRemoteJoin) {
                tvSign.setBackgroundResource(R.drawable.shape_corner8_f5f5f7);
            }
            tvSign.setClickable(isRemoteJoin);

//            }
            viewHolder.getView(R.id.imageView).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    chooseDocInfo = documentVOListDTOS.get(position);
                    vpImage.setVisibility(View.VISIBLE);
                    if (chooseDocInfo.getSignStatus() == -1)//-1-不能签，1-可以签
                    {
                        filePaths = chooseDocInfo.getUnsignDocumentFileUrl();
                    } else {
                        filePaths = chooseDocInfo.getSignDocumentFileUrl();
                    }
                    imagePreviewAdapter.refreshAdapter(filePaths);
                    Log.e("Test", filePaths + "===itemData");
                    vpImage.setAdapter(imagePreviewAdapter);
//                    vpImage.scrollToPosition(0);//初次划到位置
                    imagePreviewAdapter.notifyDataSetChanged();
                    //这里需要保持单选
                    for (int i = 0; i < documentVOListDTOS.size(); i++) {
                        if (documentVOListDTOS.get(i) != null && position == i) {
                            documentVOListDTOS.get(i).setChoose(true);
                        } else {
                            documentVOListDTOS.get(i).setChoose(false);
                        }
                    }


//                    //canSign	当事人签署状态;本系统中：-1-不能签，1-可以签
//                    //initSign	设置签字位置 -1 未设置,1 已设置
//                    //personSignStatus	当事人签署状态;本系统中：-1-未签署，1-已签署
//                    if (chooseDocInfo == null) {
//                        return;
//                    }
//                    if (chooseDocInfo.getCanSign() == -1) {
//                        tvSign.setClickable(false);
//                        tvSign.setBackgroundResource(R.drawable.shape_corner8_f5f5f7);
//                    } else {
//                        tvSign.setClickable(true);
//                        tvSign.setBackgroundResource(R.drawable.shape_corner5_3c6af4);
//                    }
//                    //当事人未签署按钮显示   阅读并批量签字；当事人完成文书按钮显示   重新阅读并签署 -1-未签署，1-已签署
//                    if (chooseDocInfo.getPersonSignStatus() == 1) {
//                        tvSign.setText(mContext.getString(R.string.read_sign_again));
//                    } else if (chooseDocInfo.getPersonSignStatus() == -1) {
//                        tvSign.setText(mContext.getString(R.string.read_and_sign));
//                    }

                }
            });
        }

        @Override
        public int viewType(DocInfoResponse.DocTypeListDTO.DocumentVOListDTO itemData) {
            return 0;
        }
    }

    @Override
    public void show() {
        super.show();
    }

    private String getRootDirPath(Context context) {
        if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())) {
            File file = ContextCompat.getExternalFilesDirs(context.getApplicationContext(), null)[0];
            return file.getAbsolutePath();
        } else {
            return context.getApplicationContext().getFilesDir().getAbsolutePath();
        }
    }


    //大图预览
    public class ImagePreviewAdapter extends BaseAdapter<String> {

        public ImagePreviewAdapter(Context context, int itemLayoutRes, @Nullable Object type) {
            super(context, itemLayoutRes, type);
            mContext = context;
        }

        @Override
        public void bind(@NonNull BaseViewHolder viewHolder, String itemData, int position) {
            if (itemData != null) {

                ImageView view = viewHolder.getView(R.id.pv);
                RequestOptions requestOptions = new RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .override(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL)//关键代码，加载原始大小
                        .format(DecodeFormat.PREFER_RGB_565)//设置为这种格式去掉透明度通道，可以减少内存占有
                        .transform(new
                                GlideRoundedCornersTransform(mContext, 6f, GlideRoundedCornersTransform.CornerType.ALL));
                Glide.with(mContext)
                        .load(itemData)
                        .apply(requestOptions).error(R.mipmap.erroimage).placeholder(R.mipmap.erroimage)
                        .into(view);
            }

        }

        @Override
        public int viewType(String itemData) {
            return 0;
        }

    }


}
