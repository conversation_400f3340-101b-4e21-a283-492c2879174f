package com.gc.notarizationpc.ui.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.gc.notarizationpc.R;
import com.gc.notarizationpc.model.ImageRemoteBean;
import com.luck.picture.lib.entity.LocalMedia;

import java.io.File;
import java.util.ArrayList;
import java.util.List;


public class GridImageNewAdapter extends RecyclerView.Adapter<GridImageNewAdapter.ViewHolder> {
    public static final int TYPE_CAMERA = 1;
    public static final int TYPE_PICTURE = 2;
    private LayoutInflater mInflater;
    private List<ImageRemoteBean> list = new ArrayList<>();
    private Context context;
    private boolean isShowName;
    private int baseSize = -1;
    private boolean isNeedDelete = true;
    /**
     * 点击添加图片跳转
     */
    private onAddPicClickListener mOnAddPicClickListener;

    private onDelPicClickListener mOnDelPicClickListener;

    public interface onAddPicClickListener {
        void onAddPicClick();
    }

    public interface onDelPicClickListener {
        void onDelPicClick(int index);
    }

    public GridImageNewAdapter(Context context, onAddPicClickListener mOnAddPicClickListener, onDelPicClickListener mOnDelPicClickListener) {
        this.context = context;
        mInflater = LayoutInflater.from(context);
        this.mOnAddPicClickListener = mOnAddPicClickListener;
        this.mOnDelPicClickListener = mOnDelPicClickListener;
    }

    public GridImageNewAdapter(Context context, boolean isNeedDelete, onAddPicClickListener mOnAddPicClickListener,
                               onDelPicClickListener mOnDelPicClickListener, boolean isShowName) {
        this.context = context;
        this.isShowName = isShowName;
        mInflater = LayoutInflater.from(context);
        this.isNeedDelete = isNeedDelete;
        this.mOnAddPicClickListener = mOnAddPicClickListener;
        this.mOnDelPicClickListener = mOnDelPicClickListener;
    }

    public void setList(List<ImageRemoteBean> list) {
        this.list = list;
        notifyDataSetChanged();
    }

    public void setBaseSize(int baseSize) {
        this.baseSize = baseSize;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {

        ImageView mImg;
        LinearLayout ll_del;
        TextView name;

        ViewHolder(View view) {
            super(view);
            setIsRecyclable(true);
            mImg = view.findViewById(R.id.img_details);
            ll_del = view.findViewById(R.id.ll_del);
            name = view.findViewById(R.id.detail_name);
        }
    }

    @Override
    public int getItemCount() {
        return list.size() + 1;
    }

    @Override
    public int getItemViewType(int position) {
        if (isShowAddItem(position)) {
            return TYPE_CAMERA;
        } else {
            return TYPE_PICTURE;
        }
    }

    /**
     * 创建ViewHolder
     */
    @Override
    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = mInflater.inflate(R.layout.item_remote_details_list, viewGroup, false);
        return new ViewHolder(view);
    }

    private boolean isShowAddItem(int position) {
        int size = list.size();
        return position == size;
    }

    /**
     * 设置值
     */
    @Override
    public void onBindViewHolder(final ViewHolder viewHolder, final int position) {
        int pos = viewHolder.getAdapterPosition();
        if (getItemViewType(position) == TYPE_CAMERA) {
            viewHolder.mImg.setImageResource(R.drawable.icon_file);
            viewHolder.mImg.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mOnAddPicClickListener.onAddPicClick();
                }
            });
            viewHolder.ll_del.setVisibility(View.GONE);
            if (!isShowName) {
                viewHolder.name.setVisibility(View.GONE);

            }
        } else {
            viewHolder.name.setVisibility(View.GONE);
            if (isNeedDelete) {
                viewHolder.ll_del.setVisibility(View.VISIBLE);
                viewHolder.ll_del.setOnClickListener(view -> {
                    int index = viewHolder.getAdapterPosition();
                    mOnDelPicClickListener.onDelPicClick(index);
                });
            } else {
                viewHolder.ll_del.setVisibility(View.GONE);
            }
            LocalMedia media = new LocalMedia();
            media.setPath(list.get(position).filePath);
            String path;
            if (media.isCut() && !media.isCompressed()) {
                // 裁剪过
                path = media.getCutPath();
            } else if (media.isCompressed() || (media.isCut() && media.isCompressed())) {
                // 压缩过,或者裁剪同时压缩过,以最终压缩过图片为准
                path = media.getCompressPath();
            } else {
                // 原图
                path = media.getPath();
            }
            // 图片
            if (media.isCompressed()) {
                Log.i("compress image result:", new File(media.getCompressPath()).length() / 1024 + "k");
                Log.i("压缩地址::", media.getCompressPath());
            }

            Log.i("原图地址::", media.getPath() + "");
            if (media.isCut()) {
                Log.i("裁剪地址::", media.getCutPath() + "");
            }
            RequestOptions options = new RequestOptions()
                    .centerCrop()
                    .placeholder(R.color.white)
                    .diskCacheStrategy(DiskCacheStrategy.ALL);
            Glide.with(viewHolder.itemView.getContext())
                    .load(path)
                    .apply(options)
                    .into(viewHolder.mImg);
            //itemView 的点击事件
            if (mItemClickListener != null) {
                viewHolder.itemView.setOnClickListener(v -> {
                    int adapterPosition = viewHolder.getAdapterPosition();
                    mItemClickListener.onItemClick(adapterPosition, v);
                });
            }
        }
    }

    private OnItemClickListener mItemClickListener;

    public interface OnItemClickListener {
        void onItemClick(int position, View v);
    }

    void setOnItemClickListener(OnItemClickListener listener) {
        this.mItemClickListener = listener;
    }

}
