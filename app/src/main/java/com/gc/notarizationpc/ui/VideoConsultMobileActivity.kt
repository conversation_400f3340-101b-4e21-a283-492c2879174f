package com.gc.notarizationpc.ui

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Intent
import android.location.LocationManager
import android.os.Handler
import android.os.Message
import android.text.Html
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.fastjson.JSON
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.example.framwork.utils.DialogUtils
import com.example.framwork.utils.MyLogUtils
import com.example.framwork.utils.RegexUtils
import com.example.framwork.utils.SPUtils
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.AppConfig
import com.gc.notarizationpc.common.BaseActivity
import com.gc.notarizationpc.common.CommonInfo
import com.gc.notarizationpc.common.NotarizationBean
import com.gc.notarizationpc.model.DeviceInfo
import com.gc.notarizationpc.model.NotarizationInfo
import com.gc.notarizationpc.model.SocketMessageInfo
import com.gc.notarizationpc.model.UserBean
import com.gc.notarizationpc.ui.adapter.DeviceAdapter
import com.gc.notarizationpc.ui.presenter.AccountPresenter
import com.gc.notarizationpc.ui.presenter.MainPresenter
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter
import com.gc.notarizationpc.utils.CommonUtil
import com.gc.notarizationpc.utils.MacUtils
import com.gc.notarizationpc.utils.MapUtils
import com.gc.notarizationpc.websocket.MyMqttService
import com.gc.notarizationpc.websocket.WebSocketCallBack
import com.ldoublem.loadingviewlib.view.LVPlayBall
import kotlinx.android.synthetic.main.activity_mobile.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStream
import java.io.InputStreamReader
import java.lang.reflect.Method

/**
 * 公证咨询 的手机号验证页面
 */
class VideoConsultMobileActivity : BaseActivity(), AccountPresenter.IMobileView, NotarizationPresenter.INotarizationView,
        WebSocketCallBack, MainPresenter.IHomeView, OnItemClickListener {

    private var TAG = "VideoConsultMobileActivity"
    private var myMqttService: MyMqttService? = null
    private var notarizationP: NotarizationPresenter? = null
    private var queryP: MainPresenter? = null
    private var codeP: AccountPresenter? = null
    var stopP: NotarizationPresenter? = null
    var REFRESH_NOTARIZATION: Int? = 0x01
    private var deviceAdapter: DeviceAdapter? = null
    private var deviceDialog: Dialog? = null
    private var changelocation: TextView? = null
    var waitDialog: Dialog? = null
    var playBall: LVPlayBall? = null
    private var protocolDialog: Dialog? = null
    private var isWaiting: Boolean? = true
    private var type: Int? = 0
    var longitude: String = ""//经度
    var latitude: String = ""//纬度
    var address: String = ""//具体地址
    var ipaddress: String = ""//省市
    var adcode: String = ""//区域码
    var country: String = ""//国家
    var province: String = ""//省
    var city: String = ""//市
    var locationManager: LocationManager? = null
    var notarizationInfo: NotarizationInfo? = null
    var isConfirm: Boolean = false
    val handler: Handler = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message?) {
            super.handleMessage(msg)
            when (msg?.what) {
                REFRESH_NOTARIZATION -> {
                    changelocation?.text = city
                    Log.d("zpzp", "refresh adcode == " + adcode)
                    queryP?.getNotaryListNew(adcode, CommonUtil.bd_decrypt(longitude, latitude), false)

                }
            }
        }
    }

    override fun getIntentData(intent: Intent?) {
        codeP = AccountPresenter(mActivity, this)
        type = intent?.getIntExtra("type", 0)
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_mobile
    }

    @SuppressLint("MissingPermission")
    override fun initViewsAndEvents() {
        Log.i(TAG, "initViewsAndEvents")
//        disableShowSoftInput()
        editMobile.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_UNSPECIFIED) {
//                codeP?.sendCode()
            }
            false
        }
        editCode.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_UNSPECIFIED) {
                submit()
            }
            false
        }
        if (type == 10) {
            //todo xhf
//            userInfo?.mobile="***********"
//            userInfo?.idCard="321081198908153024"
//            userInfo?.name="徐海凤"

            mobile_service?.visibility = View.VISIBLE
            type_vedio?.visibility = View.GONE
            type_mobile?.visibility = View.VISIBLE
            Mobile_name?.text = userInfo?.name
            try {
                Mobile_idcard?.text = userInfo?.idCard?.substring(0, 5) + "***********" +
                        userInfo?.idCard?.substring(userInfo?.idCard!!.length - 2, userInfo?.idCard!!.length)
            } catch (e: Exception) {
                Mobile_idcard?.text = userInfo?.idCard
            }
            notarizationP = NotarizationPresenter(mActivity, this)
            queryP = MainPresenter(mActivity, this)
            deviceAdapter = DeviceAdapter()
            deviceAdapter?.setOnItemClickListener(this)
            myMqttService = MyMqttService(this, ".topic.consulting.collect." + userInfo?.idCard, ".topic.consulting." + userInfo?.idCard, AppConfig.HOST, this)
            stopP = NotarizationPresenter(mActivity)
            tvProtocolCK.text = Html.fromHtml(getString(R.string.description_cb))
            initDialog()
        } else if (type == 1) {
            type_vedio?.visibility = View.VISIBLE
            type_mobile?.visibility = View.GONE
            mobile_service?.visibility = View.VISIBLE
            tvProtocolCK.text = Html.fromHtml(getString(R.string.description_cb))
            initDialog()
        } else {
            type_vedio?.visibility = View.VISIBLE
            type_mobile?.visibility = View.GONE
            mobile_next?.background = resources.getDrawable(R.drawable.next_step)
        }

    }

    private fun initDialog() {
        if (protocolDialog == null) {
            protocolDialog = DialogUtils.getInstance().getCenterDialog(mActivity, false, R.layout.dialog_protocol)
            val tvContent = protocolDialog?.findViewById<TextView>(R.id.dialog_content)
            val btnOk = protocolDialog?.findViewById<Button>(R.id.btn_ok)
            readFromRaw(tvContent)
            btnOk?.setOnClickListener { dismissDialog() }
        }
    }

    fun protocolClickCK(view: View?) {
        showDialog()
    }

    private fun showDialog() {
        if (protocolDialog?.isShowing == false) {
            protocolDialog?.show()
        }
    }

    private fun dismissDialog() {
        if (protocolDialog?.isShowing == true) {
            protocolDialog?.dismiss()
        }
    }


    /**
     * 从raw中读取txt
     */
    private fun readFromRaw(textView: TextView?) {
        try {
            val `is` = resources.openRawResource(R.raw.video_protocol)
            val text: String? = readTextFromRaw(`is`)
            textView?.text = text
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 按行读取txt
     *
     * @param is
     * @return
     * @throws Exception
     */
    @Throws(Exception::class)
    private fun readTextFromRaw(`is`: InputStream): String? {
        val reader = InputStreamReader(`is`)
        val bufferedReader = BufferedReader(reader)
        val buffer = StringBuffer("")
        var str: String?
        while (bufferedReader.readLine().also { str = it } != null) {
            buffer.append(str)
            buffer.append("\n")
        }
        return buffer.toString()
    }

    fun sendCodeClick(view: View?) {
        Log.i(TAG, "sendCodeClick")
        isMobile = false
        CommonUtil.disabledView(view)
//        editCode.requestFocus()
        if (TextUtils.isEmpty(editMobile.text.toString())) {
            toastInfo("请先输入手机号")
        } else {
            codeP?.sendCaptchaCode(editMobile.text.toString())
        }
    }

    private var clickTime: Long? = 0;
    fun submitClick(view: View?) {
        Log.i(TAG, "submitClick")
        if (System.currentTimeMillis() - clickTime!! > 1000) {
            Log.i(TAG, "notarizationClick")
            clickTime = System.currentTimeMillis()
            if (!cbProtocolCK.isChecked) {
                toastError("请阅读并同意《在线受理服务使用规则》")
                return
            }
            submit()
        }


    }

    private fun submit() {
        Log.i(TAG, "submit")
        if (isMobile && mobile_phone.equals(editMobile!!.text.toString())
                && mobile_sms.equals(editCode!!.text.toString())) {
            if ("3" == SPUtils.getInstance().get(mActivity, "institutionType", "1") && !isConfirm) {
                toastInfo("请先选择公证处")
                queryP?.getNotaryListNew(adcode, CommonUtil.bd_decrypt(CommonInfo.longitude, CommonInfo.latitude), false)
            } else
                notarizationP!!.sendNotarization(userInfo, notaryId, CommonInfo.address, CommonInfo.longitude, CommonInfo.latitude, AppConfig.SN, 10)
        } else {
            userInfo?.mobile = editMobile.text.toString()
            //TODO XHF 为了方便测试，屏蔽掉了 手机号验证码
            codeP?.register(userInfo, true)
//            codeP?.authCode(userInfo, true)
        }
    }

    /**
     * 禁止Edittext弹出软件盘，光标依然正常显示。
     */
    private fun disableShowSoftInput() {
        Log.i(TAG, "disableShowSoftInput")
        val cls = EditText::class.java
        var method: Method
        try {
            method = cls.getMethod("setShowSoftInputOnFocus", Boolean::class.javaPrimitiveType)
            method.isAccessible = true
            method.invoke(editMobile, false)
            method.invoke(editCode, false)
        } catch (e: Exception) {
        }
        try {
            method = cls.getMethod("setSoftInputShownOnFocus", Boolean::class.javaPrimitiveType)
            method.isAccessible = true
            method.invoke(editMobile, false)
            method.invoke(editCode, false)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onDestroy() {
        Log.i(TAG, "onDestroy")
        buttonCode.stop()
        Log.i(TAG, "onDestroy")
        if (deviceDialog != null && deviceDialog?.isShowing == true) {
            deviceDialog?.dismiss()
        }
        dismissWait()
        if (handler != null)
            REFRESH_NOTARIZATION?.let { handler?.removeMessages(it) }
        super.onDestroy()
    }

    override fun getMobile(): String {
        Log.i(TAG, "getMobile")
        if (TextUtils.isEmpty(editMobile.text.trim()) || editMobile.text.length < 11 || !RegexUtils.isMobile(editMobile.text.toString())) {
            toastError("请输入有效手机号")
            return ""
        }
        userInfo?.mobile = editMobile.text.trim().toString()
        return userInfo?.mobile.toString()
    }

    override fun getSmsCode(): String {
        Log.i(TAG, "getSmsCode")
        if (TextUtils.isEmpty(editCode.text.trim())) {
            toastError("请输入验证码")
            return ""
        }
        return editCode.text.trim().toString()
    }

    override fun sendSuccess() {
        Log.i(TAG, "sendSuccess")
        buttonCode.start()
        toastSuccess("验证码已发送，请注意查收")
    }

    fun backCK(view: View) {
        finish()
    }

    var isMobile: Boolean = false
    var mobile_phone: String = ""
    var mobile_sms: String = ""

    override fun goModelSelectActivity(userBean: UserBean) {
        Log.i(TAG, "goModelSelectActivity")
        userInfo?.userId = userBean.userInfo.unitGuid
        mApplication?.userInfo = userInfo
        if (type == 1) {
            readyGo(NotarizationSelfActivity::class.java)
            finish()
        } else if (type == 10) {
            isMobile = true
            mobile_phone = editMobile!!.text.toString()
            mobile_sms = editCode!!.text.toString()
            //判断是否是公证处
            if ("3" == SPUtils.getInstance().get(mActivity, "institutionType", "1") && !isConfirm) {
                toastInfo("请先选择公证处")
                queryP?.getNotaryListNew(CommonInfo.adcode, CommonUtil.bd_decrypt(CommonInfo.longitude, CommonInfo.latitude), false)
            } else
                notarizationP!!.sendNotarization(userInfo, notaryId, CommonInfo.address, CommonInfo.longitude, CommonInfo.latitude, AppConfig.SN, 10)
        } else if (type == 3) {
            val intent = Intent(this, JinRongActivity::class.java)
            intent?.putExtra("name", userInfo?.name)
            intent?.putExtra("idCard", userInfo?.idCard)
            startActivity(intent)
            finish()
        } else if (type == 4) {
            //公正咨询
            val intent = Intent(this, VideoConsultNotarizationActivity::class.java)
            intent?.putExtra("name", userInfo?.name)
            intent?.putExtra("idCard", userInfo?.idCard)
            startActivity(intent)
            finish()
        }

    }


    override fun sendNotarizationSuccess(info: NotarizationInfo?) {
        Log.i(TAG, "sendNotarizationSuccess")
        MyLogUtils.i(TAG, "发起公证成功")
        if (null != info) {
            notarizationInfo = info
            waitNotarization()
            myMqttService?.start()
        }
    }

    private fun waitNotarization() {
        Log.i(TAG, "waitNotarization")
        showWaitDialog()
        lifecycleScope.launch(Dispatchers.IO) {
            delay(60000L)
            withContext(Dispatchers.Main) {
                if (isWaiting == true) {
                    myMqttService?.unSubscribe()
                    stopP?.stopNotarization(notarizationInfo?.unitGuid)
                    dismissWait()
                    toastError("公证员正忙，请稍候再试！")
                    MyLogUtils.i(TAG, "公证员正忙，请稍候再试！")
                }
            }
        }
    }

    private fun showWaitDialog() {
        Log.i(TAG, "showWaitDialog")
        if (waitDialog == null) {
            waitDialog = DialogUtils.getInstance().getCenterDialog(mActivity, false, R.layout.dialog_wait)
            playBall = waitDialog?.findViewById(R.id.lv_playball)
            playBall?.setViewColor(R.color.black)
            playBall?.setBallColor(ContextCompat.getColor(mActivity, R.color.colorPrimary))
        }
        if (waitDialog?.isShowing == false) {
            playBall?.startAnim()

            waitDialog?.show()
        }
    }

    private fun dismissWait() {
        Log.i(TAG, "dismissWait")
        if (waitDialog != null && waitDialog?.isShowing == true) {
            playBall?.stopAnim()
            waitDialog?.dismiss()
            waitDialog == null
        }
    }

    override fun onSocketMessage(next: String?) {
        Log.i(TAG, "onSocketMessage")
        try {
            if (next?.contains("{") == true) {
                val info: SocketMessageInfo = JSON.parseObject(next, SocketMessageInfo::class.java)
                if (info.code == "dontWiting") {
                    isWaiting = false
                    if (null != info.roomId && null != info.greffierName) {
                        val roomId = info.roomId
                        val greffierName = info.greffierName
                        lifecycleScope.launch(Dispatchers.Main) {
                            goVideoNotarization(greffierName!!, roomId!!)
                        }
                    } else {
                        dismissWait()
                        toastError("未接收到房间号")
                        MyLogUtils.i(TAG, "未接收到房间号")
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun goVideoNotarization(greffierName: String, roomId: String) {
        Log.i(TAG, "goVideoNotarization")
        myMqttService?.unSubscribe()
        dismissWait()
        MyLogUtils.i(TAG, "进入视频公证页面")
        var intent: Intent? = null
//        if (SPMgr.getInstance(mActivity).getElement(AppConfig.LOCAL_USB_NAME, "").isNullOrEmpty())
        intent = Intent(mActivity, VideoConsultNotarizationActivity::class.java)
//        else
//            intent = Intent(mActivity, VideoNotarizationUSBActivity::class.java)

        intent.putExtra("roomId", roomId)
        intent.putExtra("greffierName", greffierName)
        intent.putExtra("notarizationInfo", notarizationInfo)
        readyGo(intent)
        finish()
    }


    override fun getMacAddress(): String {
        return MacUtils.getMacAddress(mActivity)
    }

    override fun showNotary(notaryId: String?, success: Boolean) {
    }

    override fun showNotaryList(bean: NotarizationBean?) {
        if (null != bean?.getItems()) {
            showDeviceDialog(bean?.getItems() as List<DeviceInfo>)
        } else {
            MyLogUtils.i(TAG, "该地区暂无在线公证人员")
            toastInfo("该地区暂无在线公证人员")
            showDeviceDialog(listOf())
        }
    }

    override fun getAccessPublicKeySuccess() {
    }

    override fun getAccessPublicKeyFail(error: String?) {
    }

    override fun mechineAddSuccess() {
    }

    override fun mechineAddFail() {
    }

    private fun showDeviceDialog(l: List<DeviceInfo>) {
        if (deviceDialog == null) {
            deviceDialog = DialogUtils.getInstance().getRightDialog(mActivity, true, R.layout.dialog_divers)
            var dialogDeviceRv: RecyclerView? = deviceDialog?.findViewById(R.id.rvDevices)
            changelocation = deviceDialog?.findViewById(R.id.change_location)!!
            if (!TextUtils.isEmpty(city))
                changelocation?.setText(city)
            dialogDeviceRv?.adapter = deviceAdapter
        }
        deviceAdapter?.addNewData(l)
//        deviceDialog?.setCancelable(false)
//        deviceDialog?.setCanceledOnTouchOutside(false)
        deviceDialog?.show()
    }

    fun change_location_text(newcity: String?) {
        if (deviceDialog != null)
            deviceDialog?.show()
        //根据城市获取地区码和经纬度
        Thread(Runnable {
            try {
                var ll = MapUtils.getCoordinate(newcity)
                longitude = ll[0].toString()
                latitude = ll[1].toString()
                adcode = CommonUtil.getcode(mActivity, newcity)
                if (changelocation != null && !TextUtils.isEmpty(latitude) && !TextUtils.isEmpty(latitude)) {
                    //有网络 获取到经纬度的条件下 赋值  刷新
                    if (newcity != null) {
                        this.city = newcity
                    }
                    REFRESH_NOTARIZATION?.let { handler.sendEmptyMessage(it) }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }).start()
    }

    fun changeLocation(view: View?) {
        if (null != deviceDialog && deviceDialog?.isShowing == true)
            deviceDialog?.dismiss()
        var pop = com.gc.notarizationpc.utils.PopwindowUtil()
        pop?.showpop(mActivity, findViewById(R.id.mobile_mian), mActivity)
    }

    override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
        if (deviceDialog != null && deviceDialog?.isShowing == true) {
            deviceDialog?.dismiss()
        }
        var device: DeviceInfo? = adapter.getItem(position) as DeviceInfo?
//        queryP?.addNotary(device?.unitGuid, device?.notarialName)
        if (!"3".equals(device?.institutionType))
            isConfirm = true
        notaryId = device?.unitGuid
    }
}