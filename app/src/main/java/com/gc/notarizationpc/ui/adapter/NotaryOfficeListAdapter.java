package com.gc.notarizationpc.ui.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.response.BindingNotaryOfficeModel;

import me.goldze.mvvmhabit.base.BaseAdapter;
import me.goldze.mvvmhabit.base.BaseViewHolder;
import me.goldze.mvvmhabit.utils.MaterialDialogUtils;
import me.goldze.mvvmhabit.utils.Utils;

//搜索公证处适配器
public class NotaryOfficeListAdapter extends BaseAdapter<BindingNotaryOfficeModel> {
    private BindingNotaryOfficeModel mItemData;

    public NotaryOfficeListAdapter(Context context, int itemLayoutRes, @Nullable Object type) {
        super(context, itemLayoutRes, type);
        mContext = context;
    }

    @Override
    public void bind(@NonNull BaseViewHolder viewHolder, BindingNotaryOfficeModel itemData, int position) {
        if (itemData == null) {
            return;
        }
        mItemData = itemData;
        viewHolder.setText(R.id.tv_name, itemData.getMechanismName());
        viewHolder.setText(R.id.tv_address, itemData.getAddress());
        viewHolder.setText(R.id.tv_time, itemData.getBusiTime());
        if (!itemData.isSelected()) {
            viewHolder.getView(R.id.rnl_content).setBackgroundResource(R.drawable.shape_corner10_white_all);
            viewHolder.setTextColor(R.id.tv_name, getContext().getColor(me.goldze.mvvmhabit.R.color.color3333));
            viewHolder.setTextColor(R.id.tv_address, getContext().getColor(me.goldze.mvvmhabit.R.color.color3333));
            viewHolder.setTextColor(R.id.tv_time, getContext().getColor(me.goldze.mvvmhabit.R.color.color3333));
            TextView textView = viewHolder.itemView.findViewById(R.id.tv_name);
            Drawable weather = Utils.getContext().getResources().getDrawable(R.mipmap.icon_office_normal);
            weather.setBounds(0, 0, weather.getMinimumWidth(), weather.getMinimumWidth());
            textView.setCompoundDrawables(weather, null, null, null);
        } else {
            viewHolder.getView(R.id.rnl_content).setBackgroundResource(R.drawable.shape_corner8_2568ff);
            viewHolder.setTextColor(R.id.tv_name, Color.WHITE);
            viewHolder.setTextColor(R.id.tv_address, Color.WHITE);
            viewHolder.setTextColor(R.id.tv_time, Color.WHITE);
            TextView textView = viewHolder.itemView.findViewById(R.id.tv_name);
            Drawable weather = Utils.getContext().getResources().getDrawable(R.mipmap.icon_office_press);
            weather.setBounds(0, 0, weather.getMinimumWidth(), weather.getMinimumWidth());
            textView.setCompoundDrawables(weather, null, null, null);
        }

        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                notifyDataSetChanged();
                mOnclick.OnClicklistener(position, viewHolder.getItemViewType());
            }
        });
    }


    @Override
    public int viewType(BindingNotaryOfficeModel itemData) {
        return 0;
    }

    public interface Onclick {
        void OnClicklistener(int position, int flag);
    }

    Onclick mOnclick;

    public void setOnClick(Onclick onClick) {
        this.mOnclick = onClick;
    }
}
