package com.gc.notarizationpc.ui.viewmodel;

import static me.goldze.mvvmhabit.utils.Utils.getContext;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.request.AddSelfServiceRequest;
import com.gc.notarizationpc.data.model.request.MacAddressRequest;
import com.gc.notarizationpc.data.model.request.OfficeListRequest;
import com.gc.notarizationpc.data.model.response.AreaModel;
import com.gc.notarizationpc.data.model.response.BindingNotaryOfficeModel;
import com.gc.notarizationpc.data.model.response.EnterpriseCertificateTypeResponse;
import com.gc.notarizationpc.data.model.response.LanguageModel;
import com.gc.notarizationpc.data.model.response.MachineBindAreaResponse;
import com.gc.notarizationpc.data.model.response.NotaryItemModel;
import com.gc.notarizationpc.data.model.response.NotaryMatterItem;
import com.gc.notarizationpc.data.model.response.PersonDocumentType;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.MacUtils;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.binding.viewadapter.spinner.IKeyAndValue;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.http.BaseResponse;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * 自助办证，申请信息
 */
public class SelfApplyInfoViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<Integer> clickEvent = new SingleLiveEvent<Integer>();
    public SingleLiveEvent<Boolean> macAddressRegisterSuccess = new SingleLiveEvent<Boolean>();
    public SingleLiveEvent<String> getCfgFail = new SingleLiveEvent<String>();
    public SingleLiveEvent<String> linkToWebView = new SingleLiveEvent<String>();
    public SingleLiveEvent<String> nextStepEvent = new SingleLiveEvent<String>();
    public SingleLiveEvent<Map<String, Object>> applicantOperateEvent = new SingleLiveEvent<Map<String, Object>>();
    public SingleLiveEvent<Map<String, Object>> notaryItemOperateEvent = new SingleLiveEvent<Map<String, Object>>();
    public SingleLiveEvent<Map<String, Object>> companyOperateEvent = new SingleLiveEvent<Map<String, Object>>();

    public class UIChangeObservable {

    }

    public SelfApplyInfoViewModel(@NonNull Application application) {
        super(application);
    }

    //给RecyclerView添加ObservableList
    public ObservableArrayList<SelfApplyPersonalListItemViewModel> observablePersonalList = new ObservableArrayList<>();
    //给RecyclerView添加ItemBinding
    public ItemBinding<SelfApplyPersonalListItemViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.list_item_self_apply_personal);//这里指定了item的布局

    public ObservableList<SelfApplyCompanylListItemViewModel> observableCompanyList = new ObservableArrayList<>();
    public ItemBinding<SelfApplyCompanylListItemViewModel> itemCompanyBinding = ItemBinding.of(BR.viewModel, R.layout.list_item_self_apply_company);//这里指定了item的布局

    public ObservableList<SelfApplyNorItemListItemViewModel> observableNorItemList = new ObservableArrayList<>();
    public ItemBinding<SelfApplyNorItemListItemViewModel> itemNorItemBinding = ItemBinding.of(BR.viewModel, R.layout.list_item_self_notarization_item);//这里指定了item的布局

    public SingleLiveEvent<List<LanguageModel>> notaryLanguageOfficeListSingleLiveEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<List<NotaryItemModel>> notaryPurposeListSingleLiveEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<List<AreaModel>> areaListSingleLiveEvent = new SingleLiveEvent<>();
    public List<AreaModel> areaModelList = new ArrayList<AreaModel>();

    public List<LanguageModel> notaryLanguageOffice = new ArrayList<LanguageModel>();

    // 选择的公证的区域
    public ObservableField<IKeyAndValue> selectedNotaryArea = new ObservableField<IKeyAndValue>();
    // 选择的公证语言
    public ObservableField<IKeyAndValue> selectedNotaryLanguage = new ObservableField<IKeyAndValue>();
    // 选择的公证语言
    public ObservableField<IKeyAndValue> selectedNotaryPurpose = new ObservableField<IKeyAndValue>();

    public SingleLiveEvent<Boolean> areaClickSingleLiveEvent = new SingleLiveEvent<>();//点击区域的

    public SingleLiveEvent<Boolean> lanClickSingleLiveEvent = new SingleLiveEvent<>();//点击语言的

    public SingleLiveEvent<Boolean> purPoseClickSingleLiveEvent = new SingleLiveEvent<>();//点击用途的

    public SingleLiveEvent<List<PersonDocumentType>> personDocumentTypeListSingleLiveEvent = new SingleLiveEvent<>();//个人证件类型
    public SingleLiveEvent<List<EnterpriseCertificateTypeResponse>> enterpriseCertificateListSingleLiveEvent = new SingleLiveEvent<>();//企业证件类型

    public MachineBindAreaResponse.ProvinceDTO mSelectProvince;
    public MachineBindAreaResponse.ProvinceDTO.GroupDTO.CityListDTO mCity;
    public SingleLiveEvent<MachineBindAreaResponse> machineBindAreaResponseSingleLiveEvent = new SingleLiveEvent<>();

    public List<BindingNotaryOfficeModel> notarialOfficeListBeans = new ArrayList<>();
    public SingleLiveEvent<List<BindingNotaryOfficeModel>> notaryListSingleLiveEvent = new SingleLiveEvent<>();
    public BindingNotaryOfficeModel mCurrentNotaryOffice = new BindingNotaryOfficeModel();//当前选择的公证处
    public SingleLiveEvent<List<NotaryMatterItem>> notaryItemLiveEvent = new SingleLiveEvent<>();//公证事项
    public List<NotaryMatterItem> notarialMatterItems = new ArrayList<>();
    public List<NotaryMatterItem.MattersInfosDTO> selectNotaryItemList = new ArrayList<>();//选择的公证事项


    public List<NotaryItemModel> notaryItemModelList = new ArrayList<>();


    /**
     * 获取公证用途
     */
    public void getPurposeList() {
//        showDialog("");
        RequestUtil.getPurPoseList(new MyObserver<List<NotaryItemModel>>() {
            @Override
            public void onSuccess(List<NotaryItemModel> result) {
                dismissDialog();
                notaryItemModelList = result;
                if (result != null && result.size() > 0) {
                    notaryPurposeListSingleLiveEvent.setValue(notaryItemModelList);
                }

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? "" : errorMsg);

            }
        });

    }

    /**
     * 根据mac地址查询关联的所属区域
     */
    public void findIntegratedMachineBindArea() {
        showDialog(Utils.getContext().getString(R.string.loading));
        MacAddressRequest request = new MacAddressRequest();
        request.setMacAddress(AppConfig.macAddress);
        RequestUtil.findIntegratedMachineBindArea(request, new MyObserver<MachineBindAreaResponse>() {
            @Override
            public void onSuccess(MachineBindAreaResponse result) {
                dismissDialog();
                if (result != null) {
                    machineBindAreaResponseSingleLiveEvent.setValue(result);
                }

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? "" : errorMsg);
            }
        });
    }

    /**
     * 根据mac地址登录
     */
    public void getListNotaryByRegion() {
        String macAddress = MacUtils.getMacAddress(Utils.getContext());
        if (TextUtils.isEmpty(macAddress)) {
            toastError(Utils.getContext().getString(R.string.getMacAdressFail));
            return;
        }
        OfficeListRequest request = new OfficeListRequest();
        request.setMacAddress(macAddress);
        if (mCity != null) {
            request.setCity(mCity.getCode());
        }
        if (mSelectProvince != null) {
            request.setProvince(mSelectProvince.getCode());
        }
        request.setSearchType(1);//,1-初始化列表，2-搜索
        RequestUtil.getListNotaryByRegion(request, new MyObserver<List<BindingNotaryOfficeModel>>() {
            @Override
            public void onSuccess(List<BindingNotaryOfficeModel> result) {
                dismissDialog();
                if (result != null && result.size() > 0) {
                    notarialOfficeListBeans = result;
                    notaryListSingleLiveEvent.setValue(notarialOfficeListBeans);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(TextUtils.isEmpty(errorMsg) ? Utils.getContext().getString(R.string.getNotaryOfficeFail) : errorMsg);
            }
        });
    }

    //点击个人 申请人列表时
    public void applicantClickHandler(String flag, AddSelfServiceRequest.RecordApplicantsDTO recordApplicantsDTO, int pos) {
        Map<String, Object> map = new HashMap<>();
        map.put("flag", flag);
        map.put("value", recordApplicantsDTO);
        map.put("pos", pos);
        applicantOperateEvent.setValue(map);
    }

    //点击单位 申请人列表时
    public void companyClickHandler(String flag, AddSelfServiceRequest.RecordCorpApplicantsDTO recordApplicantsDTO, int pos) {
        Map<String, Object> map = new HashMap<>();
        map.put("flag", flag);
        map.put("value", recordApplicantsDTO);
        map.put("pos", pos);
        companyOperateEvent.setValue(map);
    }

    //点击公证事项列表 操作项时
    public void notaryItemClickHandler(String flag, NotaryMatterItem.MattersInfosDTO mattersInfosDTO, int pos) {
        Map<String, Object> map = new HashMap<>();
        map.put("flag", flag);
        map.put("value", mattersInfosDTO);
        map.put("pos", pos);
        notaryItemOperateEvent.setValue(map);
    }

    //点击公证事项列表 + - 时
    public void notaryItemOperateHandler(String flag, NotaryMatterItem.MattersInfosDTO mattersInfosDTO, int pos) {
        if (TextUtils.isEmpty(flag)) {
            return;
        }
        if ("add".equals(flag)) {
//            observableNorItemList.remove(pos);
//            observableNorItemList.get(pos).entity.get().setNotarizationNumber(observableNorItemList.get(pos).entity.get().getNotarizationNumber() + 1);
            NotaryMatterItem.MattersInfosDTO tmpDto = mattersInfosDTO;
            tmpDto.setNotarizationNumber(String.valueOf(Integer.parseInt(TextUtils.isEmpty(tmpDto.getNotarizationNumber()) ? "0" : tmpDto.getNotarizationNumber()) + 1));
            observableNorItemList.set(pos, new SelfApplyNorItemListItemViewModel(this, tmpDto));

        } else if ("sub".equals(flag)) {
            if (Integer.parseInt(observableNorItemList.get(pos).entity.get().getNotarizationNumber()) >= 2) {
//                observableNorItemList.remove(pos);
                NotaryMatterItem.MattersInfosDTO tmpDto = mattersInfosDTO;
                tmpDto.setNotarizationNumber((Integer.parseInt(tmpDto.getNotarizationNumber()) - 1) + "");
                observableNorItemList.set(pos, new SelfApplyNorItemListItemViewModel(this, tmpDto));
            } else {
                clickEvent.setValue(1003);
            }
        } else if ("input".equals(flag)) {
            if (Integer.parseInt(mattersInfosDTO.getNotarizationNumber()) <= 0) {
                clickEvent.setValue(1003);
                return;
            }
//            observableNorItemList.remove(pos);
            NotaryMatterItem.MattersInfosDTO tmpDto = mattersInfosDTO;
            observableNorItemList.set(pos, new SelfApplyNorItemListItemViewModel(this, tmpDto));
        }

    }

    /**
     * 选择公证处
     */
    public BindingCommand selectNotaryOfficeClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            clickEvent.setValue(1001);
        }
    });

    /**
     * 选择使用地区点击事件
     */
    public BindingCommand areaOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            //获取地区接口数据
            areaClickSingleLiveEvent.setValue(true);
        }
    });
    /**
     * 选择语言点击事件
     */
    public BindingCommand languageOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            //获取语言接口数据
            lanClickSingleLiveEvent.setValue(true);
        }
    });

    /**
     * 选择用途点击事件
     */
    public BindingCommand purPoseOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            //获取用途接口数据
            purPoseClickSingleLiveEvent.setValue(true);
        }
    });


    /**
     * 获取公证事项
     */
    public void getSelfMatters() {
//        if (mCurrentNotaryOffice == null || TextUtils.isEmpty(mCurrentNotaryOffice.getId())) {
//            toastError(Utils.getContext().getString(R.string.select_notary_office_first_hint));
//            return;
//        }
        showDialog();
        RequestUtil.getSelfMatters(new MyObserver<List<NotaryMatterItem>>() {
            @Override
            public void onSuccess(List<NotaryMatterItem> response) {
                dismissDialog();
                if (response != null && response.size() > 0) {
                    notarialMatterItems = response;
                    notaryItemLiveEvent.setValue(notarialMatterItems);
                } else {
                    toastError(Utils.getContext().getString(R.string.getNotaryOfficeMattersFail));
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.getNotaryOfficeMattersFail) : errorMsg);
            }
        });
    }

    /**
     * 获取公证地区网络请求
     */
    public void getNotaryAreaList() {
//        请求网络数据
        showDialog();
        RequestUtil.getNotaryAreaList(new MyObserver<List<AreaModel>>() {
            @Override
            public void onSuccess(List<AreaModel> response) {
                dismissDialog();
                areaModelList.clear();
                areaModelList = response;
                //获取地区接口数据
                areaListSingleLiveEvent.setValue(areaModelList);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.getNotaryOfficeAreaFail) : errorMsg);
            }
        });
    }

    /**
     * 获取公证处语言列表网络请求
     */
    public void getNotaryLanguageList() {
//        请求网络数据
        RequestUtil.getNotaryLanguageList(new MyObserver<List<LanguageModel>>() {
            @Override
            public void onSuccess(List<LanguageModel> response) {
                notaryLanguageOffice.clear();
                notaryLanguageOffice = response;
                notaryLanguageOfficeListSingleLiveEvent.setValue(notaryLanguageOffice);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.getNotaryOfficeLanguageFail) : errorMsg);
            }
        });
    }

    /**
     * 获取个人证件类型
     */
    public void getTypePerson() {
//        请求网络数据
        RequestUtil.getTypePerson(new MyObserver<List<PersonDocumentType>>() {
            @Override
            public void onSuccess(List<PersonDocumentType> response) {
                personDocumentTypeListSingleLiveEvent.setValue(response);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.get_document_type_fail) : errorMsg);
            }
        });
    }

    /**
     * 获取企业证件类型
     */
    public void getEnterpriseCertificateType() {
//        请求网络数据
        RequestUtil.getEnterpriseCertificateType(new MyObserver<List<EnterpriseCertificateTypeResponse>>() {
            @Override
            public void onSuccess(List<EnterpriseCertificateTypeResponse> response) {
                enterpriseCertificateListSingleLiveEvent.setValue(response);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.get_document_type_fail) : errorMsg);
            }
        });
    }

    public void showData() {
        AddSelfServiceRequest.RecordApplicantsDTO applicantsDTO = new AddSelfServiceRequest.RecordApplicantsDTO();
        //当前刷身份证的申请人信息
        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(getContext(), "idcardinfo");
        if (idCardUserInfo != null) {
            applicantsDTO.setApplicantName(idCardUserInfo.getName());
            applicantsDTO.setBirthday(idCardUserInfo.getBirthday());
            applicantsDTO.setGender(idCardUserInfo.getGender());
            applicantsDTO.setContactNum(idCardUserInfo.getMobile());
            applicantsDTO.setCredentialNum(idCardUserInfo.getIdCard());
            applicantsDTO.setCredentialType(1);//身份证
//            applicantsDTO.setSubstitute(-1);//是否代办 1:是 -1:否
            applicantsDTO.setAddress(idCardUserInfo.getAddress());
            applicantsDTO.setCanOperater(false);
            SelfApplyPersonalListItemViewModel itemViewModel = new SelfApplyPersonalListItemViewModel(SelfApplyInfoViewModel.this, applicantsDTO);
            observablePersonalList.add(itemViewModel);
        }

    }

    public void addSelfOrder(String useType) {
        if (useType == null) {
            return;
        }
        AddSelfServiceRequest request = new AddSelfServiceRequest();
        List temp = new ArrayList();
        for (int i = 0; i < selectNotaryItemList.size(); i++) {
            Map tempMap = new HashMap<>();
            tempMap.put("mattersName", selectNotaryItemList.get(i).getMattersName());
            tempMap.put("mattersId", selectNotaryItemList.get(i).getMattersId());
            tempMap.put("notarialFee", selectNotaryItemList.get(i).getNotarizationFee());
            tempMap.put("copyFee", selectNotaryItemList.get(i).getCopyFee());
            tempMap.put("bookNo", "");
            tempMap.put("copyNumber", 0);
            tempMap.put("notarizationNumber", selectNotaryItemList.get(i).getNotarizationNumber());
            tempMap.put("detailMattersId", CommonUtil.randomNum());
            tempMap.put("newNotarial",selectNotaryItemList.get(i).getNewNotarial());
            tempMap.put("hadProtectedContent",selectNotaryItemList.get(i).getHadProtectedContent());
            tempMap.put("protectedContent",selectNotaryItemList.get(i).getProtectedContent());
            tempMap.put("wrongRelatedMatters",selectNotaryItemList.get(i).getWrongRelatedMatters());
            temp.add(tempMap);
        }
        if (temp.size() == 0) {
            ToastUtils.toastError(Utils.getContext().getString(R.string.add_notary_item_first));
            return;
        }
        request.setApplicationItem(new Gson().toJson(temp));
        request.setCreatedOrgan(mCurrentNotaryOffice.getId());//所属公证处ID
        request.setSource(4);//1-青桐智盒app，2-小程序，4-小一体机
        request.setTranslateLanguage(selectedNotaryLanguage.get().getKey());
        request.setUsedPlace(selectedNotaryArea.get().getKey());
        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
        if (idCardUserInfo != null) {
            request.setSysUserId(idCardUserInfo.getUserId());
        }
        request.setUsedPurpose(selectedNotaryPurpose.get().getKey());
        //申请人列表
        List<AddSelfServiceRequest.RecordApplicantsDTO> recordApplicantsDTOS = new ArrayList<>();
        List<AddSelfServiceRequest.RecordAgentApplicantsDTO> recordProxyDTOS = new ArrayList<>();
        for (int i = 0; i < observablePersonalList.size(); i++) {
            if (observablePersonalList.get(i).entity.get().getSubstitute() == -1) {
                AddSelfServiceRequest.RecordApplicantsDTO tmpApplicant = observablePersonalList.get(i).entity.get();
                if (!TextUtils.isEmpty(tmpApplicant.getContactNum()) && !tmpApplicant.getContactNum().contains(CommonUtil.PHONE_PREFIX)) {
                    tmpApplicant.setContactNum(CommonUtil.PHONE_PREFIX + tmpApplicant.getContactNum());
                }
                recordApplicantsDTOS.add(tmpApplicant);
            } else if (observablePersonalList.get(i).entity.get().getSubstitute() == 1) {
                AddSelfServiceRequest.RecordAgentApplicantsDTO recordAgentApplicantsDTO = new AddSelfServiceRequest.RecordAgentApplicantsDTO();
                AddSelfServiceRequest.RecordApplicantsDTO tmp = observablePersonalList.get(i).entity.get();
                recordAgentApplicantsDTO.setAddress(tmp.getAddress());
                recordAgentApplicantsDTO.setAgentName(tmp.getApplicantName());
                recordAgentApplicantsDTO.setBirthday(tmp.getBirthday());
                recordAgentApplicantsDTO.setGender(tmp.getGender());
                recordAgentApplicantsDTO.setCredentialNum(tmp.getCredentialNum());
                if (!TextUtils.isEmpty(tmp.getContactNum()) && !tmp.getContactNum().contains(CommonUtil.PHONE_PREFIX)) {
                    recordAgentApplicantsDTO.setContactNum(CommonUtil.PHONE_PREFIX + tmp.getContactNum());
                }
                recordAgentApplicantsDTO.setCredentialType(tmp.getCredentialType());
                recordProxyDTOS.add(recordAgentApplicantsDTO);
            }
        }
        String selfId = SPUtils.getInstance().getString("self_recordId");
        request.setSelfId(selfId);
        request.setRecordApplicants(recordApplicantsDTOS);

        if (recordProxyDTOS.size() > 0) {
            request.setSubstitute(1);//是否代办 1:是 -1:否
        } else {
            request.setSubstitute(-1);
        }
        //申请 代理人列表
        request.setRecordAgentApplicants(recordProxyDTOS);
//        request.setSubstitute(-1);//是否代办 1:是 -1:否 只要有代理人列表，就是代办
        //申请单位申请列表
        List<AddSelfServiceRequest.RecordCorpApplicantsDTO> recordCorpApplicantsDTOS = new ArrayList<>();
        for (int i = 0; i < observableCompanyList.size(); i++) {
            AddSelfServiceRequest.RecordCorpApplicantsDTO tmpCorp = observableCompanyList.get(i).entity.get();
            if (!TextUtils.isEmpty(tmpCorp.getContactNum()) && !tmpCorp.getContactNum().contains(CommonUtil.PHONE_PREFIX)) {
                tmpCorp.setContactNum(CommonUtil.PHONE_PREFIX + tmpCorp.getContactNum());
            }
            recordCorpApplicantsDTOS.add(tmpCorp);
        }
        request.setRecordCorpApplicants(recordCorpApplicantsDTOS);
        //当事人内必须添加申请人，否则点击下一步进行提示：请添加申请人(包含申请人，申请单位)
        if (recordApplicantsDTOS.size() == 0 && recordCorpApplicantsDTOS.size() == 0) {
            ToastUtils.toastError(Utils.getContext().getString(R.string.add_applyer_first));
            return;
        }

        RequestUtil.addSelfService(request, new MyObserver<String>() {
            @Override
            public void onSuccess(String response) {
                if (TextUtils.isEmpty(response)) {
                    return;
                }
                String selfId = response;//自助办证记录ID
                SPUtils.getInstance().put("self_recordId", selfId);
                SPUtils.getInstance().saveObject(Utils.getContext(), "self_selectNotary", mCurrentNotaryOffice);
                nextStepEvent.setValue("");
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(TextUtils.isEmpty(errorMsg) ? Utils.getContext().getString(R.string.add_self_order_error) : errorMsg);
            }
        });
    }


    /**
     * @param netWorkItemViewModel
     * @return
     */
    public int getPersonalItemPosition(SelfApplyPersonalListItemViewModel netWorkItemViewModel) {
        return observablePersonalList.indexOf(netWorkItemViewModel);
    }

    /**
     * @param netWorkItemViewModel
     * @return
     */
    public int getCompanyItemPosition(SelfApplyCompanylListItemViewModel netWorkItemViewModel) {
        return observableCompanyList.indexOf(netWorkItemViewModel);
    }

    /**
     * @param netWorkItemViewModel
     * @return
     */
    public int getNorItemPosition(SelfApplyNorItemListItemViewModel netWorkItemViewModel) {
        return observableNorItemList.indexOf(netWorkItemViewModel);
    }


    //点击新增申请人
    public BindingCommand applicantClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            clickEvent.setValue(1004);
        }
    });

    //点击下一步
    public BindingCommand nextStep = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            clickEvent.setValue(1002);

        }
    });

    //点击新增事项
    public BindingCommand notaryItemAddClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            clickEvent.setValue(1005);
//            notaryItemLiveEvent.setValue(notarialMatterItems);
        }
    });


}
