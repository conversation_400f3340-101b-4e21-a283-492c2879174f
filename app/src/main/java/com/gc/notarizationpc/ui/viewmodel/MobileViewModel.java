package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.ui.common.OnLineRulesActivity;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 *
 */
public class MobileViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    public ObservableField<String> phone = new ObservableField<>("");
    public ObservableField<String> verifyCode = new ObservableField<>("");
    public ObservableField<Boolean> isCheck = new ObservableField<>(false);
    public SingleLiveEvent<String> verifySuccess = new SingleLiveEvent<>();
    public SingleLiveEvent<String> getCodeTextView = new SingleLiveEvent<>();
    public SingleLiveEvent<Integer> getInfoErrorEvent = new SingleLiveEvent<>();

    public class UIChangeObservable {

    }

    public MobileViewModel(@NonNull Application application) {
        super(application);
    }


    /**
     * 返回
     */
    public BindingCommand goBackEvent = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            finish();
        }
    });

    /**
     * 获取短信验证码
     */
    public BindingCommand getVerifyCode = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            if (TextUtils.isEmpty(phone.get())) {
                ToastUtils.toastError(Utils.getContext().getString(R.string.input_phone_hint));
                return;
            } else if (phone.get().length() != 11) {
                ToastUtils.toastError(Utils.getContext().getString(R.string.pleaseInputCorrectPhoneNumber));
                return;
            }

            getCodeTextView.setValue(phone.get());

        }
    });

    /**
     * 校验验证码
     */
    public BindingCommand decideEvent = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            //todo 校验后期放开
            if (TextUtils.isEmpty(verifyCode.get())) {
                getInfoErrorEvent.setValue(WorkstatusEnum.VERIFY_CODE_HINT.code);
                return;
            }
            if (!isCheck.get()) {
                getInfoErrorEvent.setValue(WorkstatusEnum.READ_RULE_HINT.code);
                return;
            }
            Map<String, Object> data = new HashMap<>();
            data.put("mobileNum", "+86-" + phone.get());
            data.put("smsCode", verifyCode.get());
            showDialog("");
            RequestUtil.verifyPhoneCode(data, new MyObserver() {
                @Override
                public void onSuccess(Object result) {
                    addUser();
                }

                @Override
                public void onFailure(Throwable e, String errorMsg) {
                    dismissDialog();
                    toastError(errorMsg == null ? Utils.getContext().getString(R.string.verifyMessageCodeFail) : errorMsg);
                }
            });

        }
    });

    public void addUser() {
        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
        if (idCardUserInfo == null) {
            dismissDialog();
            return;
        }
        Map<String, Object> data = new HashMap<>();
        data.put("mobile", "+86-" + phone.get());
        data.put("userName", idCardUserInfo.getName());
        data.put("credentialNum", idCardUserInfo.getIdCard());
        data.put("credentialType", 1);//证件类型;
        RequestUtil.userAdd(data, new MyObserver() {
            @Override
            public void onSuccess(Object result) {
                dismissDialog();
                if (result == null) {
                    toastError(Utils.getContext().getString(R.string.selfAddUserFail));
                    return;
                }
                idCardUserInfo.setUserId(result.toString());
                SPUtils.getInstance().saveObject(Utils.getContext(), "idcardinfo", idCardUserInfo);
                verifySuccess.setValue("");

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.selfAddUserFail) : errorMsg);
            }
        });
    }

    public BindingCommand showRule = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            startActivity(OnLineRulesActivity.class);
        }
    });

    /**
     * 获取短信验证码
     */
    public void getMessageCode(String mobile) {
        HashMap<String, String> param = new HashMap<>();
        param.put("mobile", "+86-" + mobile);
        RequestUtil.getMessageCode(param, new MyObserver() {
            @Override
            public void onSuccess(Object response) {
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                getInfoErrorEvent.setValue(WorkstatusEnum.GET_VERIFY_CODE_FAIL.code);
            }
        });
    }


}
