package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;

/**
 * 首页快捷入口 viewmodel
 */

public class InquiryOrderListHomeViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    public class UIChangeObservable {

        public SingleLiveEvent finishRefreshing ;
        public SingleLiveEvent loadMoreFinish;
    }

    public InquiryOrderListHomeViewModel(@NonNull Application application) {
        super(application);
    }

    public SingleLiveEvent<Integer> tabClickEvent = new SingleLiveEvent<>();
    // 手机号
    public ObservableField<String> mPhoneNumber = new ObservableField<>();

    // 身份证号
    public ObservableField<String> mIdCardNumber = new ObservableField<>();

    // 验证码
    public ObservableField<String> mValidNum = new ObservableField<>();

    private  int pageSize = 10;

    private  int pageNum = 1;


    /**
     * 网络请求方法，在ViewModel中调用Model层，通过Okhttp+Retrofit+RxJava发起请求
     */
    public void requestNetWork() {
        Map dataSource = new HashMap();
        if (!TextUtils.isEmpty(mPhoneNumber.get())) {
            dataSource.put("phone", mPhoneNumber.get());
        }
        if (!TextUtils.isEmpty(mIdCardNumber.get())) {
            dataSource.put("idCard", mIdCardNumber.get());
        }
        dataSource.put("pageSize",pageSize);
        dataSource.put("pageNum",pageNum);
        if (tabClickEvent.getValue()==1){

        }else if (tabClickEvent.getValue()==2){

        }else if (tabClickEvent.getValue()==3){

        }
    }

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        //请求网络数据
        pageNum = 1;
        requestNetWork();
        uc.finishRefreshing.call();
    });

    public BindingCommand onLoadMoreCommand = new BindingCommand(() -> {
        //上拉加载
        pageNum++;
        requestNetWork();
        uc.loadMoreFinish.call();
    });

    //公证订单
    public BindingCommand notaryOrderClick = new BindingCommand(() -> {
        //请求网络数据
        tabClickEvent.setValue(1);
    });

    // 自助订单
    public BindingCommand selfServiceOrderClick = new BindingCommand(() -> {
        //请求网络数据
        tabClickEvent.setValue(2);
    });

    // 我的预约
    public BindingCommand myReservationClick = new BindingCommand(() -> {
        //请求网络数据
        tabClickEvent.setValue(3);
    });

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
