package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;

import me.goldze.mvvmhabit.base.BaseModel;
import me.goldze.mvvmhabit.base.BaseViewModel;

/**
 * Created by goldze on 2017/7/17.
 */

public class FragmentOrderDetailApplyInformationViewModel extends BaseViewModel {


    public FragmentOrderDetailApplyInformationViewModel(@NonNull Application application) {
        super(application);
    }

    public FragmentOrderDetailApplyInformationViewModel(@NonNull Application application, BaseModel model) {
        super(application, model);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }


}
