package com.gc.notarizationpc.ui.viewmodel;

import android.graphics.drawable.Drawable;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.bean.MenuInfo;
import com.gc.notarizationpc.data.model.response.NotarialOfficeListBean;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * 匹配公证员，公证处列表
 */
public class NortaryOfficeListItemViewModel extends ItemViewModel<SelectNotaryViewModel> {
    public ObservableField<NotarialOfficeListBean.GroupVoDTO.OfficerListDTO> entity = new ObservableField<>();
    public ObservableField<Integer> currentNotary  = new ObservableField<>();
    public Drawable drawableImg;

    public NortaryOfficeListItemViewModel(@NonNull SelectNotaryViewModel viewModel, NotarialOfficeListBean.GroupVoDTO.OfficerListDTO entity) {
        super(viewModel);
        this.entity.set(entity);
    }

    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
    public int getPosition() {
        return viewModel.getItemPosition(this);
    }

    //条目的点击事件
    public BindingCommand itemClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
//            mOnclick.OnClicklistener(entity);
//            currentNotary.set(entity.get().getId());
            entity.get().setChoose(true);
            viewModel.mCurrentNotaryOfficeId=entity.get().getOfficeId();
            Log.e("Test","entity.get().getId()"+entity.get().getOfficeId());
            //这里可以通过一个标识,做出判断，已达到跳入不同界面的逻辑
//            if (entity.get().getId() == -1) {
//                viewModel.deleteItemLiveData.setValue(FragmentListItemViewModel.this);
//            } else {
//                //跳转到详情界面,传入条目的实体对象
//                Bundle mBundle = new Bundle();
//                mBundle.putParcelable("entity", entity.get());
////                viewModel.startContainerActivity(DetailFragment.class.getCanonicalName(), mBundle);
//            }
        }
    });

    public interface Onclick {
        void OnClicklistener(ObservableField<MenuInfo> entity);
    }

    Onclick mOnclick;

    public void setOnClick(Onclick onClick) {
        this.mOnclick = onClick;
    }


}
