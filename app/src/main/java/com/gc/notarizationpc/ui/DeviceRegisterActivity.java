package com.gc.notarizationpc.ui;

import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.FileProvider;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityMacRegisterBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.data.model.response.UpgradeResponse;
import com.gc.notarizationpc.ui.viewmodel.DeviceRegisterViewModel;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.MacUtils;

import java.io.File;
import java.io.IOException;

import me.goldze.mvvmhabit.base.AppManager;
import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.http.DownLoadManager;
import me.goldze.mvvmhabit.http.download.ProgressCallBack;
import okhttp3.ResponseBody;

/**
 * 设备注册页
 */
public class DeviceRegisterActivity extends BaseActivity<ActivityMacRegisterBinding, DeviceRegisterViewModel> {
    private TextView tvRestart;
    private TextView tvShutDown;
    private String macAddress;
    private ProgressDialog mProgressDialog;

    //ActivityLoginBinding类是databinding框架自定生成的,对应activity_login.xml
    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_mac_register;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public DeviceRegisterViewModel initViewModel() {
//        使用自定义的ViewModelFactory来创建ViewModel，如果不重写该方法，则默认会调用LoginViewModel(@NonNull Application application)构造方法
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(DeviceRegisterViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        //设置状态栏深浅模式false为浅色
        changeStatusBarLight(true);
    }

    @Override
    public void initData() {
        super.initData();

        ((TextView) findViewById(R.id.tv_home)).setVisibility(View.GONE);
        ((TextView) findViewById(R.id.tv_back)).setVisibility(View.GONE);
        tvRestart = ((TextView) findViewById(R.id.tv_restart));
        tvRestart.setVisibility(View.VISIBLE);
        tvShutDown = ((TextView) findViewById(R.id.tv_shutdown));
        tvShutDown.setVisibility(View.VISIBLE);
        tvRestart.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击重启，重新获取配置接口，获取成功后跳转首页界面;未获取到，仍停留在此页面，提示“请稍后再试”。
                viewModel.loginByMac(macAddress);
            }
        });

        tvShutDown.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击关机，关闭此应用
                AppManager.getAppManager().AppExit();
            }
        });

    }

    public void getMacAddress(View view) {
        if (!TextUtils.isEmpty(macAddress)) {
            binding.tvMacAddress.setText(macAddress);
            binding.tvMacAddress.setVisibility(View.VISIBLE);
        } else {
            binding.tvMacAddress.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        macAddress = MacUtils.getMacAddress(this);
        viewModel.upgradeApp(macAddress, CommonUtil.getVersionName(this));

    }

    @Override
    public void initViewObservable() {
        viewModel.macAddressRegisterFail.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean result) {
                toastError(getString(R.string.try_later));
            }
        });

        viewModel.macAddressRegisterSuccess.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean result) {
                startActivity(new Intent(DeviceRegisterActivity.this, HomeActivity.class));
            }
        });


        viewModel.upgradeResponse.observe(this, new Observer<UpgradeResponse>() {
            @Override
            public void onChanged(UpgradeResponse upgradeResponse) {
                //展示升级提示框
                if (upgradeResponse != null) {
                    showDialogUpdate(upgradeResponse);
                }
            }
        });
    }


    AlertDialog alertDialog = null;

    public void showDialogUpdate(UpgradeResponse upgradeResponse) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setCancelable(false); //开启强制更新，无法关闭
        builder.setTitle(getString(R.string.version_update)).
                // 设置提示框的图标
//                        setIcon(R.mipmap.ic_launcher).
                // 设置要显示的信息
                        setMessage(upgradeResponse.getRenewExplain()).
                // 设置确定按钮
                        setPositiveButton(getString(R.string.update), (dialog, which) -> {
                    if (alertDialog != null && alertDialog.isShowing()) {
                        alertDialog.dismiss();
                        if (!TextUtils.isEmpty(upgradeResponse.getApkPackageUrl())) {
                            downloadApk(upgradeResponse.getApkPackageUrl());
                        }

                    }
                });
        // 生产对话框
        alertDialog = builder.create();
        // 显示对话框
        if (!alertDialog.isShowing()) {
            alertDialog.show();
        }

        initProgress();
    }

    private void initProgress() {
        mProgressDialog = new ProgressDialog(this);
        mProgressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        mProgressDialog.setProgressDrawable(this.getDrawable(R.drawable.common_progressdialog_progressbar_background));
        mProgressDialog.setCancelable(false);
    }

    String path;

    private void downloadApk(String url) {
        path = getFilesDir() + "";
        try {
            path = isExistDir(path);
        } catch (Exception ex) {
            toastError(getString(R.string.save_path_error));
            return;
        }
        String filePath = System.currentTimeMillis() + ".apk";
//        try {
//            if (!new File(filePath).exists()) {
//                new File(filePath).createNewFile();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        if (mProgressDialog != null) {
            mProgressDialog.show();
        }
        DownLoadManager.getInstance().load(url, new ProgressCallBack<ResponseBody>(path, filePath) {
            @Override
            public void onStart() {
                super.onStart();
            }

            @Override
            public void onCompleted() {
                mProgressDialog.dismiss();
            }

            @Override
            public void onSuccess(ResponseBody responseBody) {
                toastSuccess(getString(R.string.apk_download_complete));
                mProgressDialog.dismiss();
                installApk(path + "/" + filePath);
                Log.e("Test", path + "/" + filePath);
            }

            @Override
            public void progress(final long progress, final long total) {
                double percentage = ((double) progress / total) * 100;
                mProgressDialog.setProgress((int) percentage);
                mProgressDialog.setSecondaryProgress((int) percentage);
            }

            @Override
            public void onError(Throwable e) {
                e.printStackTrace();
                toastError(getString(R.string.upgrade_error));
                mProgressDialog.dismiss();
            }
        });
    }

    /**
     * 安装Apk
     */
    public void installApk(String filePath) {
        try {
            File apkFile = new File(filePath);
            Uri data = null;
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                data = FileProvider.getUriForFile(this, getPackageName() + ".fileprovider", apkFile);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            } else {
                data = Uri.fromFile(apkFile);
            }
            intent.setDataAndType(data, "application/vnd.android.package-archive");
            startActivity(intent);
        } catch (Exception ex) {
            ex.printStackTrace();
//            Toasty.error(mContext, ex.getMessage());
        }
    }

    private String isExistDir(String saveDir) throws IOException, IOException {
        File downloadFile = new File(saveDir);
        if (!downloadFile.mkdirs()) {
            downloadFile.createNewFile();
        }
        String savePath = downloadFile.getAbsolutePath();
        return savePath;
    }


}
