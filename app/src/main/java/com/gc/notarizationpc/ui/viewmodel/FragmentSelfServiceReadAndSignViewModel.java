package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.graphics.Bitmap;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.ListDocumentResponse;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.util.CommonUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

/**
 * 这个是自助办证流程中上传材料fragment中
 */
public class FragmentSelfServiceReadAndSignViewModel extends BaseViewModel {
    public FragmentSelfServiceReadAndSignViewModel(@NonNull Application application) {
        super(application);
    }


    //右侧文书展示的ItemBinding
    public ItemBinding<SelfServiceReadAndSignGridItemViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.item_self_service_read_and_sign_grid);
    // 右侧文文书展示的数据源
    public ObservableList<SelfServiceReadAndSignGridItemViewModel> documentList = new ObservableArrayList();

    // 左侧文书预览的itemBinding
    public ItemBinding<SelfServiceReadAndSignDocumentGridItemViewModel> documentItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_self_service_read_and_sign_document_grid);
    // 左侧文书展示的数据源
    public ObservableList<SelfServiceReadAndSignDocumentGridItemViewModel> documentPreViewList = new ObservableArrayList();

    public SingleLiveEvent firstButtonClickEvent = new SingleLiveEvent();


    public SingleLiveEvent secondButtonClickEvent = new SingleLiveEvent();

    public SingleLiveEvent<List<String>> previewDocEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<String> amplifyDocEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<Boolean> uploadFileSingleLiveEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<Boolean> getDocumentListEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<Integer> getInfoErrorEvent = new SingleLiveEvent<>();

    public List<ListDocumentResponse> documentSourceList;

    public List<String> tempDoc = new ArrayList<>();

    public SingleLiveEvent<Integer> currentDocumentIndex = new SingleLiveEvent<>();

    /**
     * 获取文书
     */
    public void listDocument(String recordId, String userId) {
        documentList.clear();
        Map<String, Object> param = new HashMap<>();
        param.put("recordId", recordId);
        param.put("sysUserId", userId);
//        请求网络数据
        RequestUtil.listDocument(param, new MyObserver<List<ListDocumentResponse>>() {
            @Override
            public void onSuccess(List<ListDocumentResponse> response) {
                if (response != null && response.size() > 0) {
                    documentSourceList = response;
                    tempDoc.clear();
                    for (ListDocumentResponse listDocumentResponse : response) {
                        if (listDocumentResponse.getSignDocumentVOList() != null &&
                                listDocumentResponse.getSignDocumentVOList().size() > 0 && listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages() != null && listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages().size() > 0) {
                            listDocumentResponse.setFirstDocUrl(listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages().get(0).toString());
                            tempDoc.addAll(listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages());
                        }
                        SelfServiceReadAndSignGridItemViewModel documentGridItemViewModel = new SelfServiceReadAndSignGridItemViewModel(FragmentSelfServiceReadAndSignViewModel.this, listDocumentResponse);
                        documentList.add(documentGridItemViewModel);
                    }
                    previewDoc(tempDoc);
                }
                getDocumentListEvent.setValue(true);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.failedToLoadDocumentAndTryLater) : errorMsg);
            }
        });
    }

    //左侧预览文书
    public void previewDoc(List<String> listDocumentResponse) {
        if (listDocumentResponse != null) {
            previewDocEvent.setValue(listDocumentResponse);
        }
    }

    // 获取右侧的点击位置
    public void getCurrentIndex(ListDocumentResponse listDocumentResponse){
        if (listDocumentResponse.getSignDocumentVOList() != null &&
                listDocumentResponse.getSignDocumentVOList().size() > 0 && listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages() != null && listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages().size() > 0){
            Integer currentIndex = tempDoc.indexOf(listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages().get(0));
            currentDocumentIndex.setValue(currentIndex);

        }
    }

    //放大图片
    public void amplifyDoc(String filePath) {
        amplifyDocEvent.setValue(filePath);
    }

    public BindingCommand firstButtonClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            firstButtonClickEvent.setValue("");
        }
    });

    public BindingCommand secondButtonClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            secondButtonClickEvent.setValue("");
        }
    });

    public void uploadSignPic(Bitmap bitmap) {
        showDialog(Utils.getContext().getString(R.string.commiting));
        File file = CommonUtil.bitmapToFile(bitmap);
        RequestBody requestFile = RequestBody.create(MediaType.parse("image/png"), file);
        MultipartBody.Part body = MultipartBody.Part.createFormData("file", file.getName(), requestFile);
        RequestUtil.uploadFile(body, new MyObserver<UploadFileBean>() {
            @Override
            public void onSuccess(UploadFileBean result) {
                if (result != null) {
                    Log.e("Test", result.getId());
                    uploadSignImage(result);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                Log.e("Test", "图片上传失败");
                getInfoErrorEvent.setValue(WorkstatusEnum.UPLOAD_SIGN_FAIL.code);
            }
        });
    }

    /**
     * 上传签字信息
     */
    private void uploadSignImage(UploadFileBean response) {
        if (documentPreViewList == null || documentSourceList == null) {
            toastError(Utils.getContext().getString(R.string.data_empty));
            dismissDialog();
            return;
        }
        Map<String, Object> dataSource = new HashMap<>();
        List<String> tempDocumentId = new ArrayList<>();
        for (ListDocumentResponse listDocumentResponse : documentSourceList) {
            if (listDocumentResponse.getSignDocumentVOList() != null &&
                    listDocumentResponse.getSignDocumentVOList().size() > 0 && listDocumentResponse.getSignDocumentVOList().get(0) != null && listDocumentResponse.getSignDocumentVOList().get(0).getDocumentId() != null) {
                tempDocumentId.add(listDocumentResponse.getSignDocumentVOList().get(0).getDocumentId());
            }
        }
        String recordId = SPUtils.getInstance().getString("self_recordId");
        dataSource.put("documentIds", tempDocumentId);
        dataSource.put("orderId", recordId);
        dataSource.put("signImg", response.getId());
        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
        if (idCardUserInfo != null) {
            dataSource.put("sysUserId", idCardUserInfo.getUserId());
        }
        RequestUtil.signDocument(dataSource, new MyObserver() {
            @Override
            public void onSuccess(Object result) {
                dismissDialog();
                uploadFileSingleLiveEvent.setValue(true);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                Log.e("Test", "图片上传失败");
                getInfoErrorEvent.setValue(WorkstatusEnum.UPLOAD_SIGN_FAIL.code);
            }
        });
    }
}
