package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.alibaba.fastjson.JSON;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.request.MacAddressRequest;
import com.gc.notarizationpc.data.model.request.MakeAppointmentRequest;
import com.gc.notarizationpc.data.model.request.OfficeListRequest;
import com.gc.notarizationpc.data.model.response.AreaModel;
import com.gc.notarizationpc.data.model.response.BindingNotaryOfficeModel;
import com.gc.notarizationpc.data.model.response.LanguageModel;
import com.gc.notarizationpc.data.model.response.MachineBindAreaResponse;
import com.gc.notarizationpc.data.model.response.NotaryAffairResponseModel;
import com.gc.notarizationpc.ui.common.ElectronicRulesActivity;
import com.gc.notarizationpc.ui.common.OnLineRulesActivity;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.EventListener;
import com.gc.notarizationpc.util.MacUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui.viewmodel
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/12
 */
public class ReservationViewModel extends BaseViewModel {

    // 公证书份数
    public ObservableField<String> notarizationCopies = new ObservableField<>("1");
    // 选择的公证的区域
    public ObservableField<String> selectedNotaryArea = new ObservableField<>("");
    // 选择的公证语言
    public ObservableField<String> selectedNotaryLanguage = new ObservableField<>("");

    // 选择的公证处
    public ObservableField<String> selectedNotaryOffice = new ObservableField<>("");

    // 获取手机号
    public ObservableField<String> phoneNum = new ObservableField<>("");
    // 短信验证码
    public ObservableField<String> messageCode = new ObservableField<>("");
    // 公证事项
    public ObservableField<String> notaryMatters = new ObservableField<>();
    // 备注
    public ObservableField<String> remark = new ObservableField<>("");
    // 是否阅读
    public ObservableBoolean isRead = new ObservableBoolean(false);

    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<List<BindingNotaryOfficeModel>> notaryListSingleLiveEvent = new SingleLiveEvent<>();

    public List<AreaModel> areaModelList = new ArrayList<AreaModel>();

    public List<LanguageModel> notaryLanguageOffice = new ArrayList<LanguageModel>();

    public List<NotaryAffairResponseModel> notaryMattersList = new ArrayList<NotaryAffairResponseModel>();

    public SingleLiveEvent<String> notaryOfficeSingleLiveEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<List<LanguageModel>> notaryLanguageOfficeListSingleLiveEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<List<AreaModel>> areaListSingleLiveEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<List<NotaryAffairResponseModel>> notaryMattersListSingleLiveEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<MachineBindAreaResponse> machineBindAreaResponseSingleLiveEvent = new SingleLiveEvent<>();

    public MachineBindAreaResponse.ProvinceDTO mSelectProvince;
    public MachineBindAreaResponse.ProvinceDTO.GroupDTO.CityListDTO mCity;

    public SingleLiveEvent<String> getCodeEvent = new SingleLiveEvent<>();

    private List<BindingNotaryOfficeModel> notarialOfficeListBeans = new ArrayList<>();

    public BindingNotaryOfficeModel mCurrentNotaryOffice = new BindingNotaryOfficeModel();

    public SingleLiveEvent showShenbanClickEvent = new SingleLiveEvent();

    public SingleLiveEvent reservationSuccessClickEvent = new SingleLiveEvent();

    // 预约的开始时间
    public String startTime;
    // 预约的结束时间
    public String endTime;

    public class UIChangeObservable {

        //显示公证处
        public ObservableBoolean showNotaryAddressDialog;

        public UIChangeObservable() {
            showNotaryAddressDialog = new ObservableBoolean(false);
        }
    }


    public ReservationViewModel(@NonNull Application application) {
        super(application);
    }


//    public BindingCommand notaryOnClickCommand = new BindingCommand(new BindingAction() {
//        @Override
//        public void call() {
//            //获取公证处地区接口数据
//            notaryListSingleLiveEvent.setValue(new BaseResponse());
//        }
//    });

    /**
     * 获取公证地区网络请求
     */
    public void getNotaryAreaList() {
//        请求网络数据
        RequestUtil.getNotaryAreaList(new MyObserver<List<AreaModel>>() {
            @Override
            public void onSuccess(List<AreaModel> response) {
                areaModelList.clear();
                areaModelList = response;
                for (int i = 0; i < areaModelList.size(); i++) {
                    if (areaModelList.get(i).getZhName().equals("中国")) {
                        selectedNotaryArea.set(areaModelList.get(i).getZhName());
                    }
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.getNotaryOfficeAreaFail) : errorMsg);
            }
        });
    }

    /**
     * 根据mac地址查询关联的所属区域
     */
    public void findIntegratedMachineBindArea() {
        showDialog(Utils.getContext().getString(R.string.loading));
        MacAddressRequest request = new MacAddressRequest();
        request.setMacAddress(AppConfig.macAddress);
        RequestUtil.findIntegratedMachineBindArea(request, new MyObserver<MachineBindAreaResponse>() {
            @Override
            public void onSuccess(MachineBindAreaResponse result) {
                dismissDialog();
                if (result != null) {
                    machineBindAreaResponseSingleLiveEvent.setValue(result);
//                    if (result != null) {
//                        if (result.getProvince() != null) {
//                            mSelectProvince = result.getProvince();
//                        }
//                        if (result.getProvince().getGroup() != null && result.getProvince().getGroup().size() > 0) {
//                            mCity = result.getProvince().getGroup().get(0).getCityList().get(0);
//                        }
//                    }


                }

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? "" : errorMsg);
            }
        });
    }

    /**
     * 获取公证处列表
     */
    public void requestNotary(Integer type) {
        String macAddress = MacUtils.getMacAddress(Utils.getContext());
        if (TextUtils.isEmpty(macAddress)) {
            toastError(Utils.getContext().getString(R.string.getMacAdressFail));
            return;
        }
        showDialog(Utils.getContext().getString(R.string.loading));
        OfficeListRequest request = new OfficeListRequest();
        request.setMacAddress(macAddress);
        if (mCity != null) {
            request.setCity(mCity.getCode());
        }
        if (mSelectProvince != null) {
            request.setProvince(mSelectProvince.getCode());
        }
        request.setSearchType(type);//,1-初始化列表，2-搜索

        RequestUtil.getListNotaryByRegion(request, new MyObserver<List<BindingNotaryOfficeModel>>() {
            @Override
            public void onSuccess(List<BindingNotaryOfficeModel> result) {
                dismissDialog();
                notarialOfficeListBeans = result;
                if (notarialOfficeListBeans != null && notarialOfficeListBeans.size() == 1) {
                    mCurrentNotaryOffice = notarialOfficeListBeans.get(0);
                    selectedNotaryOffice.set(mCurrentNotaryOffice.getMechanismName());
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? "" : errorMsg);
            }
        });
    }

    /**
     * 获取公证处语言列表网络请求
     */

    public void getNotaryLanguageList() {
//        请求网络数据
        RequestUtil.getNotaryLanguageList(new MyObserver<List<LanguageModel>>() {
            @Override
            public void onSuccess(List<LanguageModel> response) {
                notaryLanguageOffice.clear();
                notaryLanguageOffice = response;
                for (int i = 0; i < notaryLanguageOffice.size(); i++) {
                    if (notaryLanguageOffice.get(i).getLabel().equals("中文（简体）")) {
                        selectedNotaryLanguage.set(notaryLanguageOffice.get(i).getLabel());
                    }
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.getNotaryOfficeLanguageFail) : errorMsg);
            }
        });
    }

    /**
     * 预约办证网络请求
     */
    public void reservationNotarization() {
        if (phoneNum.get() == null || phoneNum.get().isEmpty() || phoneNum != null && phoneNum.get().length() != 11) {
            toastInfo(Utils.getContext().getResources().getString(R.string.pleaseInputCorrectPhoneNumber));
            return;
        }
        if (messageCode.get() == null || messageCode.get().isEmpty() || messageCode != null && messageCode.get().length() != 6) {
            toastInfo(Utils.getContext().getResources().getString(R.string.pleaseInputCorrectMessageCode));
            return;
        }
        if (startTime == null || startTime.isEmpty() || TextUtils.isEmpty(endTime)) {
            toastInfo(Utils.getContext().getResources().getString(R.string.pleaseChooseTime));
            return;
        }
        if (mCurrentNotaryOffice.getMechanismName() == null || mCurrentNotaryOffice.getMechanismName().isEmpty()) {
            toastInfo(Utils.getContext().getResources().getString(R.string.notary_office_hint));
            return;
        }
        if (selectedNotaryArea.get() == null || selectedNotaryArea.get().isEmpty()) {
            toastInfo(Utils.getContext().getResources().getString(R.string.getNotaryOfficeAreaFail));
            return;
        }
        if (selectedNotaryLanguage.get() == null || selectedNotaryLanguage.get().isEmpty()) {
            toastInfo(Utils.getContext().getResources().getString(R.string.getNotaryOfficeLanguageFail));
            return;
        }
        if (!isRead.get()) {
            // "请阅读并同意《在线受理服务使用规则》及《电子签名服务告知条款》"
            toastInfo(Utils.getContext().getResources().getString(R.string.pleaseReadAndAgree));
            return;
        }
        List temp = new ArrayList();
        if (!TextUtils.isEmpty(notaryMatters.get())) {
            String[] temPListData = notaryMatters.get().split("、");
            for (String string : temPListData) {
                for (int i = 0; i < notaryMattersList.size(); i++) {
                    if (string.equals(notaryMattersList.get(i).getMattersName())) {
                        Map tempMap = new HashMap<>();
                        tempMap.put("mattersName", notaryMattersList.get(i).getMattersName());
                        tempMap.put("mattersId", notaryMattersList.get(i).getMattersId());
                        tempMap.put("notarialFee", notaryMattersList.get(i).getNotarialFee());
                        tempMap.put("copyFee", notaryMattersList.get(i).getCopyFee());
                        tempMap.put("bookNo", "");
                        tempMap.put("copyNumber", 0);
                        tempMap.put("notarizationNumber", 1);
                        tempMap.put("detailMattersId", CommonUtil.randomNum());
                        tempMap.put("newNotarial",notaryMattersList.get(i).getNewNotarial());
                        tempMap.put("hadProtectedContent",notaryMattersList.get(i).getHadProtectedContent());
                        tempMap.put("protectedContent",notaryMattersList.get(i).getProtectedContent());
                        tempMap.put("wrongRelatedMatters",notaryMattersList.get(i).getWrongRelatedMatters());
                        temp.add(tempMap);
                    }
                }

            }
        }

        String usedPlace = "";
        for (int i = 0; i < areaModelList.size(); i++) {
            if (areaModelList.get(i).getZhName() == selectedNotaryArea.get()) {
                usedPlace = areaModelList.get(i).getId();
            }

        }


        String translateLanguage = "";
        for (int i = 0; i < notaryLanguageOffice.size(); i++) {
            if (notaryLanguageOffice.get(i).getLabel() == selectedNotaryLanguage.get()) {
                translateLanguage = notaryLanguageOffice.get(i).getValue();
            }

        }
        showDialog(Utils.getContext().getResources().getString(R.string.loading));
        //请求网络数据
        MakeAppointmentRequest request = new MakeAppointmentRequest();
        request.setPhoneNum("+86-" + phoneNum.get());
        request.setVerificationCode(Integer.parseInt(messageCode.get()));
        request.setBookOrgan(mCurrentNotaryOffice.getId());
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setUsedPlace(usedPlace);
        request.setApplicationItem(JSON.toJSONString(temp));
        request.setPredetermineSource(4);
        request.setTranslateLanguage(translateLanguage);
        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
        if (idCardUserInfo != null) {
            request.setSysUserId(idCardUserInfo.getUserId());
        }
//        request.setManufactureNum(Integer.parseInt(notarizationCopies.get()));
        if (remark.get() != null || !remark.get().isEmpty()) {
            request.setRemark(remark.get());
        }
//        request.set
        RequestUtil.addNotaryMatters(request, new MyObserver() {
            @Override
            public void onSuccess(Object response) {
                dismissDialog();
                reservationSuccessClickEvent.setValue("");
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                ToastUtils.showLong(errorMsg == null ? Utils.getContext().getResources().getString(R.string.reservationFail) : errorMsg);
            }
        });
    }

    /**
     * 获取短信验证码
     */
    public void getMessageCode(String mobile) {

        HashMap<String, String> param = new HashMap<>();
        param.put("mobile", "+86-" + mobile);
        RequestUtil.getMessageCode(param, new MyObserver() {
            @Override
            public void onSuccess(Object response) {
                toastSuccess(Utils.getContext().getResources().getString(R.string.getMessageCodeSuccess));
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.getMessageCodeFail) : errorMsg);
            }
        });
    }

    /**
     * 获取公证事项网络接口
     */
    public void getNotaryMatters(String notaryId, EventListener listener) {
        if (TextUtils.isEmpty(notaryId)) {
            toastError(Utils.getContext().getString(R.string.select_notary_office_first_hint));
            return;
        }
        RequestUtil.getNotaryMatters(new MyObserver<List<NotaryAffairResponseModel>>() {
            @Override
            public void onSuccess(List<NotaryAffairResponseModel> response) {
                notaryMattersList.clear();
                notaryMattersList = response;
                listener.voidEventCallBack();
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getResources().getString(R.string.getNotaryOfficeMattersFail) : errorMsg);
            }
        });
    }

    /**
     * 短信验证码点击事件
     */
    public BindingCommand messageCodeOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            if (phoneNum.get() == null || phoneNum.get().isEmpty() || phoneNum != null && phoneNum.get().length() != 11) {
                toastInfo(Utils.getContext().getResources().getString(R.string.pleaseInputCorrectPhoneNumber));
                return;
            }
            getCodeEvent.setValue(phoneNum.get());

        }
    });

    /**
     * 确认预约点击事件
     */
    public BindingCommand confirmReservationOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            //预约办证
            reservationNotarization();
        }
    });


    /**
     * 公证事项点击事件
     */
    public BindingCommand notaryMattersOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            if (notaryMattersList == null || notaryMattersList.size() == 0) {
                //获取公证事项接口数据
                getNotaryMatters(mCurrentNotaryOffice.getId(), new EventListener() {
                    @Override
                    public void voidEventCallBack() {
                        notaryMattersListSingleLiveEvent.setValue(notaryMattersList);
                    }

                    @Override
                    public void voidEventCallBack(Object... objects) {
                    }
                });
            } else {
                notaryMattersListSingleLiveEvent.setValue(notaryMattersList);
            }

        }
    });

    /**
     * 预约时间点击事件
     */
//    public BindingCommand reservationDateOnClickCommand = new BindingCommand(new BindingAction() {
//        @Override
//        public void call() {
//            //获取预约时间接口数据
//            notaryListSingleLiveEvent.setValue(new BaseResponse());
//        }
//    });

    /**
     * 选择使用地区点击事件
     */
    public BindingCommand areaOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            //获取地区接口数据
            areaListSingleLiveEvent.setValue(areaModelList);
        }
    });
    /**
     * 选择语言点击事件
     */
    public BindingCommand languageOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            //获取语言接口数据
            notaryLanguageOfficeListSingleLiveEvent.setValue(notaryLanguageOffice);
        }
    });

    /**
     * 加公证书份数
     */
    public BindingCommand addNotarizationCopiesCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            int copies = Integer.parseInt(notarizationCopies.get());
            copies++;
            notarizationCopies.set(String.valueOf(copies));
        }
    });

    /**
     * 减公证书份数
     */
    public BindingCommand reduceNotarizationCopiesCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            int copies = Integer.parseInt(notarizationCopies.get());
            if (copies > 1) {
                copies--;
            } else {
                copies = 1;
            }
            notarizationCopies.set(String.valueOf(copies));
        }
    });

    public BindingCommand chooseNotaryOffice = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            if (notarialOfficeListBeans.size() > 1) {
                notaryListSingleLiveEvent.setValue(notarialOfficeListBeans);
            } else {
                toastError(Utils.getContext().getString(R.string.no_find_notary_office_please_contact));
            }
        }
    });

    public BindingCommand lookOnLineRule = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            startActivity(OnLineRulesActivity.class);
        }
    });

    public BindingCommand lookElectronicRule = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            startActivity(ElectronicRulesActivity.class);
        }
    });

    public BindingCommand showShenban = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            showShenbanClickEvent.setValue("");
        }
    });


    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}

