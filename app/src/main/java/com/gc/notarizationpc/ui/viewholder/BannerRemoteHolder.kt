package com.gc.notarizationpc.ui.viewholder

import android.view.View
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.gc.notarizationpc.R
import xyz.zpayh.hdimage.HDImageView

class BannerRemoteHolder(view: View) : RecyclerView.ViewHolder(view) {
    var imageView: HDImageView = view.findViewById(R.id.bannerIv)
    var bannerLeft: ImageView = view.findViewById(R.id.bannerLeft)
    var bannerRight: ImageView = view.findViewById(R.id.bannerRight)

}