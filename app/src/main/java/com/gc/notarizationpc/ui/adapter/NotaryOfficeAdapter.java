package com.gc.notarizationpc.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.response.NotarialOfficeListBean;
import com.gc.notarizationpc.util.CommonUtil;

import java.util.List;

//
public class NotaryOfficeAdapter extends RecyclerView.Adapter<NotaryOfficeAdapter.ViewHolder> {

    private List<NotarialOfficeListBean> mList;
    private Context mContext;

    public NotaryOfficeAdapter(List<NotarialOfficeListBean> list, Context context) {
        this.mList = list;
        this.mContext=context;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        //用来创建ViewHolder实例，再将加载好的布局传入构造函数，最后返回ViewHolder实例
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_list_notary_office, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        if (null != mList && mList.size() > 0 && position < mList.size()) {
            NotarialOfficeListBean notarialOfficeListBean=mList.get(position);
            if (notarialOfficeListBean.isChoose()) {
                holder.rnlContent.setBackgroundResource(R.drawable.shape_corner5_2568ff);
                holder.mName.setTextColor(mContext.getResources().getColor(R.color.white));
                holder.mDate.setTextColor(mContext.getResources().getColor(R.color.white));
                holder.mAddress.setTextColor(mContext.getResources().getColor(R.color.white));
                CommonUtil.setDrawableLeft(mContext,holder.mName,R.mipmap.icon_office_press);

            } else {
                holder.rnlContent.setBackgroundResource(R.drawable.shape_corner5_f5f5f7);
                holder.mName.setTextColor(mContext.getResources().getColor(R.color.color6666));
                holder.mDate.setTextColor(mContext.getResources().getColor(R.color.color6666));
                holder.mAddress.setTextColor(mContext.getResources().getColor(R.color.color6666));
                CommonUtil.setDrawableLeft(mContext,holder.mName,R.mipmap.icon_office_normal);
            }

            holder.mName.setText(notarialOfficeListBean.getMechanismName());
            holder.mDate.setText(notarialOfficeListBean.getBusiTime());
            holder.mAddress.setText(notarialOfficeListBean.getAddress());
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    for (int i = 0; i < mList.size(); i++) {
                        if (mList.get(i) != null &&  i==position) {
                            mList.get(i).setChoose(true);
                        } else {
                            mList.get(i).setChoose(false);
                        }
                    }
                    mOnclick.OnClicklistener(position);
                    notifyDataSetChanged();
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        return mList.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {

        private TextView mName,mDate,mAddress;
        private RelativeLayout rnlContent;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mName = itemView.findViewById(R.id.tv_name);
            mDate=itemView.findViewById(R.id.tv_time);
            mAddress=itemView.findViewById(R.id.tv_address);
            rnlContent=itemView.findViewById(R.id.rnl_content);

        }
    }

    public interface Onclick {
        void OnClicklistener(int position);
    }

    Onclick mOnclick;

    public void setOnClick(Onclick onClick) {
        this.mOnclick = onClick;
    }
}
