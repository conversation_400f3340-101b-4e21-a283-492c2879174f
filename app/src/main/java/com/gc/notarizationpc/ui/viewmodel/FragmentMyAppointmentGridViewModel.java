package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.NotaryOrderModel;
import com.gc.notarizationpc.data.model.response.OrderAppointmentListModel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * Created by goldze on 2017/7/17.
 */

public class FragmentMyAppointmentGridViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    private int pageNo = 1;
    private int pageSize = 10;

    public class UIChangeObservable {
        //下拉刷新完成
        public SingleLiveEvent finishRefreshing = new SingleLiveEvent<>();
        //上拉加载完成
        public SingleLiveEvent finishLoadmore = new SingleLiveEvent<>();
    }

    public FragmentMyAppointmentGridViewModel(@NonNull Application application) {
        super(application);
        Log.e("FragmentGridViewModel", "FragmentGridViewModel");
//        requestNetWork();
    }

    // 手机号
    public ObservableField<String> mPhoneNumber = new ObservableField<>();

    // 身份证号
    public ObservableField<String> mIdCardNumber = new ObservableField<>();

    //验证码
    public ObservableField<String> mValidNum = new ObservableField<>();

    // 进入受理室
    public SingleLiveEvent<OrderAppointmentListModel> enterRoomEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<List<NotaryOrderModel>> notaryOrderListEvent = new SingleLiveEvent<>();


    public SingleLiveEvent<Boolean> comeIntoOrderDetail = new SingleLiveEvent<>();

    public SingleLiveEvent<Boolean> pageEmptyEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<Integer> refreshEvent = new SingleLiveEvent<>();

    //给RecyclerView添加ObservableList
    public ObservableList<FragmentMyAppointmentGridItemViewModel> observableList = new ObservableArrayList<>();
    //给RecyclerView添加ItemBinding
    public ItemBinding<FragmentMyAppointmentGridItemViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.item_grid_my_appointment_order);//这里指定了item的布局
    //下拉刷新
    public BindingCommand onRefreshCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            pageNo = 1;
            requestNetWork();
            refreshEvent.setValue(0);
        }
    });
    //上拉加载
    public BindingCommand onLoadMoreCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            pageNo++;
            requestNetWork();
            refreshEvent.setValue(1);
        }
    });

    /**
     * 网络请求方法，在ViewModel中调用Model层，通过Okhttp+Retrofit+RxJava发起请求
     */
    public void requestNetWork() {
        Map dataSource = new HashMap();
        if (!TextUtils.isEmpty(mPhoneNumber.get())) {
            dataSource.put("phoneNum", mPhoneNumber.get());
            dataSource.put("searchType", 2);
        }
        if (!TextUtils.isEmpty(mIdCardNumber.get())) {
            dataSource.put("idCardNum", mIdCardNumber.get());
            dataSource.put("searchType", 1);
        }
//        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
//        if (idCardUserInfo != null) {
//            dataSource.put("userId", idCardUserInfo.getUserId());
//        }
        showDialog(Utils.getContext().getString(R.string.loading));
        RequestUtil.getAppointmentList(dataSource, new MyObserver<List<OrderAppointmentListModel>>() {
            @Override
            public void onSuccess(List<OrderAppointmentListModel> result) {
                dismissDialog();
                if (result != null && result.size() > 0) {
                    if (pageNo == 1) {
                        observableList.clear();
                    }
                    for (OrderAppointmentListModel info : result) {
                        FragmentMyAppointmentGridItemViewModel itemViewModel = new FragmentMyAppointmentGridItemViewModel(FragmentMyAppointmentGridViewModel.this, info);
                        if (info.getStatus() == 1) {
                            itemViewModel.stateImg = R.mipmap.yuyue_success;
                            info.setStatusString("预约成功");
                            itemViewModel.stateColor = Utils.getContext().getResources().getColor(R.color.color24B26B);
                            itemViewModel.isHide = View.VISIBLE;
                        } else if (info.getStatus() == -1) {
                            itemViewModel.stateImg = R.mipmap.waiting_confirm;
                            info.setStatusString("待公证员确认");
                            itemViewModel.stateColor = Utils.getContext().getResources().getColor(R.color.colorF2A50C);
                            itemViewModel.isHide = View.GONE;
                        }
                        try {
                            if (!TextUtils.isEmpty(info.getStartTime()) && !TextUtils.isEmpty(info.getEndTime())) {
                                String[] tempList = info.getEndTime().split(" ");
                                itemViewModel.appointmentTime = info.getStartTime() + " - " + tempList[1];
                            } else {
                                itemViewModel.appointmentTime = info.getStartTime();
                            }
                        } catch (Exception e) {
                            itemViewModel.appointmentTime = info.getStartTime();
                        }
                        //双向绑定动态添加Item
                        observableList.add(itemViewModel);
                    }
                } else {
                    pageEmptyEvent.setValue(true);
                }

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(Utils.getContext().getString(R.string.requestFailTrylater));
            }
        });
    }


    /**
     * 删除条目
     *
     * @param netWorkItemViewModel
     */
    public void deleteItem(SelfServiceOrderGridItemViewModel netWorkItemViewModel) {
        //点击确定，在 observableList 绑定中删除，界面立即刷新
        observableList.remove(netWorkItemViewModel);
    }

    /**
     * 获取条目下标
     *
     * @param netWorkItemViewModel
     * @return
     */
    public int getItemPosition(FragmentMyAppointmentGridItemViewModel netWorkItemViewModel) {
        return observableList.indexOf(netWorkItemViewModel);
    }

    public int getItemCount() {
        return observableList.size();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    // 进入详情
    public void itemClick() {
        comeIntoOrderDetail.setValue(true);
    }

    public void enterRoom(OrderAppointmentListModel entity) {
        enterRoomEvent.setValue(entity);
    }
}
