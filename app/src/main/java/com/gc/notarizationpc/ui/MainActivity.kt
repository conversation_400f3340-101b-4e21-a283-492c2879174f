package com.gc.notarizationpc.ui

import android.annotation.SuppressLint
import android.app.Dialog
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Paint
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Message
import android.provider.Settings
import android.text.Html
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import android.widget.*
import androidx.annotation.RequiresApi
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.amap.api.location.AMapLocation
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.eloam.gaopaiyi.util.UVCCameraUtil
import com.example.framwork.glide.ImageLoaderUtils
import com.example.framwork.utils.DateUtil
import com.example.framwork.utils.DialogUtils
import com.example.framwork.utils.NetUtil
import com.example.framwork.utils.ToastUtils
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.*
import com.gc.notarizationpc.common.AppConfig.REGISTERTOKEN
import com.gc.notarizationpc.model.DeviceInfo
import com.gc.notarizationpc.model.RegionBean
import com.gc.notarizationpc.ui.adapter.DeviceAdapter
import com.gc.notarizationpc.ui.adapter.RegisterNotryAdapter
import com.gc.notarizationpc.ui.myview.SelfDialog
import com.gc.notarizationpc.ui.presenter.MainPresenter
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter.CommonView
import com.gc.notarizationpc.utils.*
import com.gc.notarizationpc.utils.CommonUtil.setSystemVoice
import com.gc.notarizationpc.utils.GpsUtil.MapGPS
import com.hjq.permissions.OnPermission
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.umeng.analytics.MobclickAgent
import kotlinx.android.synthetic.main.activity_main.*
import org.json.JSONObject
import java.lang.reflect.Method

@Route(path = "/app/mainactivity")
class MainActivity : BaseActivity(), MainPresenter.IHomeView, OnItemClickListener {
    var REFRESH_NOTARIZATION: Int? = 0x01
    var hasCardModule = false
    private var queryP: MainPresenter? = null
    private var toastTxt: String = "设备未注册，暂时无法使用，请联系工作人员"

    companion object {
        const val ACTION_USB_PERMISSION = "com.eloam.gaopaiyi.USB_PERMISSION"
    }

    private var dialogDate: TextView? = null
    private var announcementDialog: Dialog? = null
    private var deviceDialog: Dialog? = null
    private var changelocation: TextView? = null
    private var selectTitle: TextView? = null
    private var deviceAdapter: DeviceAdapter? = null
    private var registerNotryAdapter: RegisterNotryAdapter? = null
    private lateinit var usbManager: UsbManager
    var hasPermission = false
    var isFirst = true
    var jigouyichang = false
    var longitude: String = ""//经度
    var latitude: String = ""//纬度
    var address: String = ""//具体地址
    var adcode: String = ""//区域码
    var province: String = ""//省
    var city: String = ""//市
    val TAG: String = MainActivity.javaClass.simpleName
    private var count = 0
    private var startMillis: Long = 0L
    private var remindDialog: Dialog? = null
    var sys_remind: String? = ""
    val handler: Handler = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message?) {
            super.handleMessage(msg)
            when (msg?.what) {
                REFRESH_NOTARIZATION -> {
                    changelocation?.text = city
                    changelocation?.tag = province
                    regionBean?.text
                    try {
                        for (i in 0 until regionBean?.text!!.size) {
                            if (regionBean?.text!![i].text.equals(province)) {
                                for (j in 0 until regionBean?.text!![i].children!!.size) {
                                    if (regionBean?.text!![i].children[j].text.equals(city)) {
                                        if (regionBean?.text!![i].children[j]?.children?.size!! > 0) {
                                            regionBean?.text!![i].children[j]?.children?.let { showDeviceDialogRegister(it) }
                                        } else {
                                            showDeviceDialogRegister(listOf())
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        MobclickAgent.reportError(mActivity, e.message!!)
                        Log.e(TAG, "省公协mini一体机：" + e.message)
                    }
                    Log.d("zpzp", "refresh adcode == " + adcode)
                    //该接口废弃
//                    queryP?.getNotaryListNew(adcode, bd_decrypt(longitude, latitude), false)

                }
            }
        }
    }

    override fun getIntentData(intent: Intent) {
        AccountManger.getInstance(mActivity)?.initUserInfo()
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_main
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun initViewsAndEvents() {

//        Log.e("Test","decryptSM2=="+SM2Util.decryptSM2("00d0f4632f3379550366ee0971c3f8a0e3a71016ded3fb2ba0aad2bf6584319519","04f93ecf78dd67dbb816b13cf38928bcbef7e29b080623afbd09dfd25e25881597ad6db2ae68dc8b094fd37d8b4a25f3e919071fbafc55ec943e09c0fe1a774574de22a64a274db9b49d98643211ae0f6a4a6d67efb10582da9242d71d11d56ead53781b12f39e3429c4e80c040e10bd7df77de017d98d8242df0bc9a44b87bd90bb653c295b6c9377bef836ea89d58dc152a1927d22ddbb854a38b3c52eb33dd9"));

//        Log.e("Test","SM2Util.encrypt"+SM2Util.encrypt("{\"editionType\":19}","9068fe4d7f01994f4a0ddf7b3c9485e1"));
//        Log.e("Test","SM2Util.encryptSM2"+SM2Util.encryptSM2("04af713a2034875488c001fc4d124fda14a426cd5a05d0d024ccb17474f8f1b8aea1bb5bff4a0b2263ee2162597670f4d758096c6dc8a301021628c9e094665d0a","1ab3acedfa994ceb1d34cfd2e10e4db70d4ee532e8d550c1f62275c0076a4353"));
//        Log.e("Test","加密result=="+SM2Util.encryptSM2("04af713a2034875488c001fc4d124fda14a426cd5a05d0d024ccb17474f8f1b8aea1bb5bff4a0b2263ee2162597670f4d758096c6dc8a301021628c9e094665d0a",SM2Util.encrypt("{\"editionType\":19}","9068fe4d7f01994f4a0ddf7b3c9485e1")));
//        Log.e("Test","解密result=="+SM2Util.decrypt(SM2Util.decryptSM2("00d0f4632f3379550366ee0971c3f8a0e3a71016ded3fb2ba0aad2bf6584319519",
//                SM2Util.encryptSM2("04af713a2034875488c001fc4d124fda14a426cd5a05d0d024ccb17474f8f1b8aea1bb5bff4a0b2263ee2162597670f4d758096c6dc8a301021628c9e094665d0a",
//                        SM2Util.encrypt("{\"editionType\":19}","9068fe4d7f01994f4a0ddf7b3c9485e1"))),"9068fe4d7f01994f4a0ddf7b3c9485e1"));

//        Log.e("Test","==="+SM2Util.decrypt("0473b662cb20b855924ea48986fcd0e0d9da7f712181a708dc1f3c1d806ca688eebee3ecc2301ace2f62a7ffcfdd2e4a8b5ccb152c613955e4baeca78a1d7d7955dc8bc63acfb5bc0dcd01842511abeea84390f56ef6865f9e206e4e75e9e43d1b700b254b7e67f2d1cc12861452f75fbdc9648b7700d145e5023b243cd3f7fa2621f2d2f6c64d3da11137470155ee2e7ae982c11c778a99c8fadfabfc123f4743","9068fe4d7f01994f4a0ddf7b3c9485e1"));
        ShowWindow.checkMaintain(this)
        deviceAdapter = DeviceAdapter()
        registerNotryAdapter = RegisterNotryAdapter()
        registerNotryAdapter?.setOnItemClickListener(this)
        deviceAdapter?.setOnItemClickListener(this)
        queryP = MainPresenter(mActivity, this)
//        queryP?.getAccessPublicKey(this);

        tv_upgrade.setOnClickListener(object : View.OnClickListener {
            override fun onClick(v: View?) {
                tv_upgrade.isClickable = false;
                up?.checkUpdate(object : IUpgradeListener {
                    override fun checkUpgrade() {
                        tv_upgrade.isClickable = true;
                    }

                    override fun cancel() {
                        tv_upgrade.isClickable = true;
                    }

                })
            }

        })
//        initUSB()

        ivSplash.visibility = View.VISIBLE
        AppConfig.HIGHPID = ""
        UVCCameraUtil.initUSBMonitorForPid(mActivity)
        if (AppConfig.RGBPID == "c013") {
            ImageLoaderUtils.display(mActivity, ivSplash, R.drawable.qidong)
        } else {
            ImageLoaderUtils.display(mActivity, ivSplash, R.drawable.qidong)
            if (sys_remind.isNullOrEmpty())
                sys_remind = "当事人摄像头连接异常"
            else
                "$sys_remind\n当事人摄像头连接异常".also { sys_remind = it }
        }
        initUSB()
        if (AppConfig.HIGHPID != "1054") {
            if (sys_remind.isNullOrEmpty())
                sys_remind = "高拍仪连接异常"
            else
                "$sys_remind\n高拍仪连接异常".also { sys_remind = it }
        }

        AppConfig.SN = getSN().toString()

//        val intent = Intent(mActivity, CheckTokenService::class.java)
//        if (mActivity != null) {
//            mActivity.startService(intent)
//        }
        //设置媒体音量百分之50
        setSystemVoice(this)
        //打开悬浮窗权限
        openFloatPermission()



        tvVersion.setOnClickListener {
            val time = System.currentTimeMillis()
            if (startMillis == 0L || time - startMillis > 3000) {
                startMillis = time
                count = 1
            } else {
                count++
            }
            if (count == 5) {
                //展示app内部系统弹框
                SysDialog()
            }
        }
        //应用更新
//        up!!.checkUpdate()
        GpsUtil.getlocation(mActivity, object : MapGPS {
            override fun success(aMapLocation: AMapLocation?) {
                try {
                    Log.i("zpzp", aMapLocation!!.toString())

                    address = aMapLocation.address + ""

                    longitude = aMapLocation.longitude.toString() + ""

                    latitude = aMapLocation.latitude.toString() + ""

                    if (!TextUtils.isEmpty(aMapLocation.city))
                        adcode = CommonUtil.getcode(mActivity, aMapLocation.city)

                    if (!address.isNullOrEmpty())
                        CommonInfo.address = address

                    if (aMapLocation.longitude != 0.0)
                        CommonInfo.longitude = longitude

                    if (aMapLocation.latitude != 0.0)
                        CommonInfo.latitude = latitude

                    if (!aMapLocation.province.isNullOrEmpty())
                        CommonInfo.province = aMapLocation.province

                    if (!aMapLocation.city.isNullOrEmpty())
                        CommonInfo.city = aMapLocation.city

                    if (!adcode.isNullOrEmpty()) {
                        CommonInfo.adcode = adcode
                        CommonInfo.isGetGps = true
                    }

                    if (TextUtils.isEmpty(aMapLocation.country))
                        CommonInfo.country = "中国"
                    else
                        CommonInfo.country = aMapLocation.country

                } catch (e: Exception) {
                    MobclickAgent.reportError(mActivity, e.message!!)
                    Log.e(TAG, "石城mini一体机：" + e.message)
                }

            }

            override fun failed() {
                Log.e(TAG, "定位失败了，继续定位")
            }

        })

    }


    /**
     * 获取SN
     * @return
     */
    @SuppressLint("MissingPermission")
    fun getSN(): String? {
        var serial = ""
        //通过android.os获取sn号
        try {
            serial = Build.SERIAL
            if (serial != "" && serial != "unknown") return serial
        } catch (e: Exception) {
            MobclickAgent.reportError(this, e.message!!)
            serial = ""
        }

        //通过反射获取sn号
        try {
            val c = Class.forName("android.os.SystemProperties")
            val get: Method = c.getMethod("get", String::class.java)
            serial = get.invoke(c, "ro.serialno").toString()
            if (serial != "" && serial != "unknown") return serial

            //9.0及以上无法获取到sn，此方法为补充，能够获取到多数高版本手机 sn
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) serial = Build.getSerial()
        } catch (e: Exception) {
            MobclickAgent.reportError(this, e.message!!)
            serial = ""
        }
        return serial
    }


    /**
     * 自助公证
     * @param view
     */
    fun certificateClick(view: View?) {

        showProgress()
        Thread(Runnable {
            if (!NetUtil.ping()) {
                runOnUiThread(Runnable {
                    hideProgress()
                    toastError("网络异常")
                })
            } else {
                up?.checkUpdate(object : IUpgradeListener {
                    override fun checkUpgrade() {
                        if (jigouyichang) {
                            runOnUiThread(Runnable {
                                hideProgress()
                                toastError("未查询到服务公证处，请联系客服人员：**********")
                            })
                        } else {
                            runOnUiThread(Runnable {
                                hideProgress()
                                notaryId?.let {
                                    hasCardModule = true
                                    if (!hasCardModule) {
                                        Toast.makeText(mActivity, "缺少身份证模块，无法进行该操作！", Toast.LENGTH_LONG).show()
                                    } else {
                                        val intent = Intent(mActivity, ComparisonActivity::class.java)
                                        intent?.putExtra("toWhere", "mobilePage");
                                        intent?.putExtra("type", 1);
//                                        intent?.putExtra("isProxy", true)
                                        startActivity(intent)
                                    }
                                }
                                        ?: showOneDialog(toastTxt + "！\n${getString(R.string.online_phone)}", "知道了") {
                                            dismissQuickDialog()
                                            queryP?.getNotary()
                                        }
                            })

                        }
                    }

                    override fun cancel() {
                    }
                })

            }
        }).start()
    }

    /**
     * 视频公证
     * @param view
     */
    fun vedioClick(view: View?) {
        showProgress()
        Thread(Runnable {
            if (!NetUtil.ping()) {
                runOnUiThread(Runnable {
                    hideProgress()
                    toastError("网络异常")
                })
            } else {
                up?.checkUpdate(object : IUpgradeListener {
                    override fun checkUpgrade() {
                        if (jigouyichang) {
                            runOnUiThread(Runnable {
                                hideProgress()
                                toastError("未查询到服务公证处，请联系客服人员：**********")
                            })
                        } else {
                            runOnUiThread(Runnable {
                                hideProgress()
                                notaryId?.let {
                                    hasCardModule = true
                                    if (!hasCardModule) {
                                        Toast.makeText(mActivity, "缺少身份证模块，无法进行该操作！", Toast.LENGTH_LONG).show()
                                    } else {
                                        val intent = Intent(mActivity, ComparisonIDcardActivity::class.java)
                                        startActivity(intent)
                                    }
                                }
                                        ?: showOneDialog(toastTxt + "！\n${getString(R.string.online_phone)}", "知道了") {
                                            dismissQuickDialog()
                                            queryP?.getNotary()
                                        }
                            })

                        }
                    }

                    override fun cancel() {

                    }
                })

            }
        }).start()


    }

    /**
     * 金融公证
     * @param view
     */
    fun bankClick(view: View?) {
        showProgress()
        Thread(Runnable {
            if (!NetUtil.ping()) {
                runOnUiThread(Runnable {
                    hideProgress()
                    toastError("网络异常")
                })
            } else {
                if (jigouyichang) {
                    runOnUiThread(Runnable {
                        hideProgress()
                        toastError("未查询到服务公证处，请联系客服人员：**********")
                    })
                } else {
                    runOnUiThread(Runnable {
                        hideProgress()
                        notaryId?.let {
                            hasCardModule = true
                            if (!hasCardModule) {
                                Toast.makeText(mActivity, "缺少身份证模块，无法进行该操作！", Toast.LENGTH_LONG).show()
                            } else {
                                val intent = Intent(this, ComparisonActivity::class.java)
                                intent?.putExtra("toWhere", "mobilePage");
                                intent?.putExtra("type", 3);
                                startActivity(intent)
                            }
                        }
                                ?: showOneDialog(toastTxt + "！\n${getString(R.string.online_phone)}", "知道了") {
                                    dismissQuickDialog()
                                    queryP?.getNotary()
                                }
                    })

                }
            }
        }).start()

    }

    fun splashClick(view: View?) {
        //检测前置摄像头是否正常
        if (AppConfig.RGBPID != "c013") {
            UVCCameraUtil.initUSBMonitorForPid(mActivity)
            if (AppConfig.RGBPID != "c013") {
                cameraErroDialog("当事人摄像头连接异常")
            } else {
                ivSplash.visibility = View.GONE
                showAnnouncementDialog()
                isFirst = false
            }
        } else {
            ivSplash.visibility = View.GONE
            showAnnouncementDialog()
            isFirst = false
        }
    }

    /**
     * 自助办证
     */
    fun advisoryClick(view: View?) {

        showProgress()
        Thread(Runnable {
            if (!NetUtil.ping()) {
                runOnUiThread(Runnable {
                    hideProgress()
                    toastError("网络异常")
                })
            } else {
                up?.checkUpdate(object : IUpgradeListener {
                    override fun checkUpgrade() {
                        if (jigouyichang) {
                            runOnUiThread(Runnable {
                                hideProgress()
                                toastError("未查询到服务公证处，请联系客服人员：**********")
                            })
                        } else {
                            runOnUiThread(Runnable {
                                hideProgress()
                                notaryId?.let {
                                    val intent = Intent(mActivity, ComparisonActivity::class.java)
                                    intent?.putExtra("toWhere", "CertReportPage");
                                    startActivity(intent)
                                }
                                        ?: showOneDialog(toastTxt + "！\n${getString(R.string.online_phone)}", "知道了") {
                                            dismissQuickDialog()
                                            queryP?.getNotary()
                                        }
                            })

                        }
                    }

                    override fun cancel() {
                    }
                })

            }
        }).start()
    }

    fun inquireClick(view: View?) {
        toastInfo("功能升级中...")
    }

    fun suggestClick(view: View?) {
        up?.checkUpdate(object : IUpgradeListener {
            override fun checkUpgrade() {
                var intent = Intent(mActivity, VideoConsultActivity::class.java)
                intent.putExtra("jigouyichang", jigouyichang)
                intent.putExtra("notaryId", notaryId)
                intent.putExtra("hasCardModule", hasCardModule);
                startActivity(intent)
            }

            override fun cancel() {
            }
        })


//        val intent = Intent(this, VideoConsultMobileActivity::class.java)
//        intent.putExtra("fromVideoConsult", true)
//        intent.putExtra("toWhere", "mobilePage")
//        intent.putExtra("type", 10)
//        startActivity(intent)
    }

    var regionBean: RegionBean? = null

    fun registerClick(view: View?) {
        showProgress()
        Thread(Runnable {
            if (!NetUtil.ping()) {
                runOnUiThread(Runnable {
                    hideProgress()
                    toastError("网络异常")
                })
            } else {
                runOnUiThread(Runnable {
                    hideProgress()
                    if (!TextUtils.isEmpty(adcode) && adcode.length > 4)
                        adcode = adcode?.substring(0, 4) + "00"
                    else
                        adcode = "320100"
//                    queryP?.getTempToken(object : getToken {
//                        override fun success() {
                    queryP?.getCity(object : MainPresenter.CityView {
                        override fun getCitySuccess(bean: RegionBean?) {
                            regionBean = bean
                            if (regionBean?.text!!.size > 0) {
                                showDeviceDialogRegister(regionBean?.text!![0].children[0].children)
                                changelocation?.text = regionBean?.text!![0].children[0].text
                            } else {
                                showDeviceDialogRegister(listOf())
                            }
                        }

                        override fun getCityFailed(msg: String?) {
                            toastError("获取公证处列表失败");
                        }

                    });
                    //原先的接口废弃
//                            queryP?.getNotaryListNew(adcode, bd_decrypt(longitude, latitude), false)
//                        }

//                        override fun failed() {
//                            toastError("获取公证处列表失败");
//                        }
//                    })
                })

            }

        }).start()

    }


    private fun showAnnouncementDialog() {
        if (announcementDialog == null) {
            announcementDialog = DialogUtils.getInstance().getCenterDialog(mActivity, false, R.layout.dialog_announcement)
            val tvContent = announcementDialog?.findViewById<TextView>(R.id.dialog_content)
            val btnOk = announcementDialog?.findViewById<Button>(R.id.btn_ok)
            dialogDate = announcementDialog?.findViewById(R.id.dialog_date)
            tvContent?.text = Html.fromHtml(getString(R.string.dialog_announcement))
            btnOk?.setOnClickListener { dismissDialog() }
        }
        dialogDate?.text = DateUtil.getInstance().getCurDateStr("yyyy年MM月dd日")
        window.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        if (ivSplash.visibility == View.GONE) {
            announcementDialog?.show()
        }
//        announcementDialog?.show()
        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_HIDE_NAVIGATION);
        window.decorView.setOnSystemUiVisibilityChangeListener {
            var uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or  //布局位于状态栏下方
                    View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or  //全屏
                    View.SYSTEM_UI_FLAG_FULLSCREEN or  //隐藏导航栏
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            uiOptions = if (Build.VERSION.SDK_INT >= 19) {
                uiOptions or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            } else {
                uiOptions or View.SYSTEM_UI_FLAG_LOW_PROFILE
            }
            window.decorView.systemUiVisibility = uiOptions
        }
        window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)

    }

    private fun showDeviceDialog(l: List<DeviceInfo>) {
        try {
            if (deviceDialog == null) {
                deviceDialog = DialogUtils.getInstance().getRightDialog(mActivity, true, R.layout.dialog_divers)
                var dialogDeviceRv: RecyclerView? = deviceDialog?.findViewById(R.id.rvDevices)
                changelocation = deviceDialog?.findViewById(R.id.change_location)
                selectTitle = deviceDialog?.findViewById(R.id.tivle)
                if (!TextUtils.isEmpty(city))
                    changelocation?.setText(city)
                dialogDeviceRv?.adapter = deviceAdapter
            }
            if (selectTitle != null)
                if (l == null || l.isEmpty()) {
                    selectTitle?.text = "暂无合作公证处"
                } else {
                    selectTitle?.text = "选择公证处"
                }

            deviceAdapter?.addNewData(l)
//        deviceDialog?.setCancelable(false)
//        deviceDialog?.setCanceledOnTouchOutside(false)
            deviceDialog?.show()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun showDeviceDialogRegister(l: List<RegionBean.ProvinceBean.CityBean.NortryDTO>) {
        if (deviceDialog == null) {
            deviceDialog = DialogUtils.getInstance().getRightDialog(mActivity, true, R.layout.dialog_divers)
            var dialogDeviceRv: RecyclerView? = deviceDialog?.findViewById(R.id.rvDevices)
            changelocation = deviceDialog?.findViewById(R.id.change_location)!!
            if (!TextUtils.isEmpty(city))
                changelocation?.setText(city)
            dialogDeviceRv?.adapter = registerNotryAdapter
        }
        registerNotryAdapter?.addNewData(l)
//        deviceDialog?.setCancelable(false)
//        deviceDialog?.setCanceledOnTouchOutside(false)
        deviceDialog?.show()
    }


    fun change_location_text(newcity: String?, newprovince: String?) {
        if (deviceDialog != null)
            deviceDialog?.show()
        //根据城市获取地区码和经纬度
        Thread(Runnable {
            try {
                var ll = MapUtils.getCoordinate(newcity)
                longitude = ll[0].toString()
                latitude = ll[1].toString()
                adcode = CommonUtil.getcode(mActivity, newcity)
                if (changelocation != null && !TextUtils.isEmpty(latitude) && !TextUtils.isEmpty(latitude)) {
                    //有网络 获取到经纬度的条件下 赋值  刷新
                    if (newcity != null) {
                        this.city = newcity
                    }
                    if (newprovince != null) {
                        this.province = newprovince
                    }
                    REFRESH_NOTARIZATION?.let { handler.sendEmptyMessage(it) }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }).start()

    }

    private fun dismissDialog() {
        if (deviceDialog != null && deviceDialog?.isShowing == true) {
            deviceDialog?.dismiss()
        }
        if (announcementDialog != null && announcementDialog?.isShowing == true) {
            announcementDialog?.dismiss()
        }
        if (remindDialog != null && remindDialog?.isShowing == true) {
            remindDialog?.dismiss()
        }
    }

    override fun onDestroy() {
        Log.d("zpzp", "onDestroy")
        dismissDialog()
        if (null != mUsbReceiver) {
            unregisterReceiver(mUsbReceiver)
        }
        if (handler != null) {
            REFRESH_NOTARIZATION?.let { handler.removeMessages(it) }
        }
        super.onDestroy()
    }

    var up: UpdateHelper? = UpdateHelper(this)

    override fun onResume() {
        super.onResume()
//        var flag = intent.getBooleanExtra("flag", false)
//        if (flag) {
//            Log.e("Test","调用了recreate")
//            recreate()
//        }
        handler.postDelayed(Runnable {
            //更新
            var up: UpdateHelper? = UpdateHelper(this)
            up?.checkUpdate()
            tvVersion.text = "版本号:" + up!!.versionName
        }, 2000)
//        queryP?.getAccessPublicKeyNoCallback();

    }

    @SuppressLint("NewApi")
    fun initUSB() {
        usbManager = getSystemService(Context.USB_SERVICE) as UsbManager
        val filter = IntentFilter()
        filter.addAction(ACTION_USB_PERMISSION)
        filter.addAction(UsbManager.ACTION_USB_ACCESSORY_ATTACHED)
        this.registerReceiver(mUsbReceiver, filter)
        for (device in usbManager.deviceList.values) {
            if ((device.vendorId == 1024 && device.productId == 50010)) {    //身份证模块
                hasCardModule = true
                if (!usbManager.hasPermission(device)) {
                    val intent = Intent(ACTION_USB_PERMISSION)
                    val pendingIntent = PendingIntent.getBroadcast(this, 0, intent, 0)
                    usbManager.requestPermission(device, pendingIntent)
                } else {
                    getPermission()
                }
            }
        }
        if (!hasCardModule) { //没有身份证模块
            getPermission()
            if (sys_remind.isNullOrEmpty())
                sys_remind = "身份证读卡器连接异常"
            else
                "$sys_remind\n身份证读卡器连接异常".also { sys_remind = it }
        }
    }

    fun getPermission() {
        XXPermissions.with(this)
                .permission(Permission.CAMERA) //不指定权限则自动获取清单中的危险权限
                .request(object : OnPermission {
                    override fun hasPermission(granted: List<String>, isAll: Boolean) {
                        hasPermission = true
                    }

                    override fun noPermission(denied: List<String>, quick: Boolean) {

                    }
                })
    }

    private val mUsbReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (ACTION_USB_PERMISSION == action) {
                synchronized(this) {
                    val device = intent.getParcelableExtra<UsbDevice>(UsbManager.EXTRA_DEVICE)
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        Log.e("zzkong", "USB授权成功")
                        getPermission()
                    } else {
                        Log.e("zzkong", "USB授权失败")
                        finish()
                    }
                }
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        return true
    }

    override fun getMacAddress(): String {
        return MacUtils.getMacAddress(mActivity)
//        return "98:EE:CB:92:2D:E3"
    }

    override fun showNotary(notary: String?, success: Boolean) {
        jigouyichang = false;
        notaryId = null
        if (success) {
            if (TextUtils.isEmpty(notary)) {
//                tvRegister.visibility = View.VISIBLE
            } else {
                tvRegister.visibility = View.GONE
                try {
                    var notaries = JSONObject(notary)
                    var notaryIdt = notaries.getString("notaryId")
                    var notaryPublicId: String? = null
                    if (notaries.has("notaryPublicId")) {
                        notaryPublicId = notaries.getString("notaryPublicId")
                    } else {
                        notaryPublicId = ""
                    }
                    AccountManger.getInstance(mActivity)?.updateNotaryId(notaryIdt, notaryPublicId)
                    this.notaryId = notaryIdt
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } else {
            when (notary) {
                "11020" -> {
                    toastTxt = "设备未注册，暂时无法使用，请联系工作人员"
//                    tvRegister.visibility = View.VISIBLE
                }
                "11021" -> {
                    toastTxt = "该机构没有合作公证处，暂时无法使用，请联系工作人员"
//                    tvRegister.visibility = View.GONE
                }
                "11022" -> {
                    toastTxt = "该机构未绑定默认人员，暂时无法使用，请联系工作人员"
//                    tvRegister.visibility = View.GONE
                }
                "11023" -> {
                    toastTxt = "该默认人员未绑定角色，暂时无法使用，请联系工作人员"
//                    tvRegister.visibility = View.GONE
                }
                "11024" -> {
                    toastTxt = "未查询到服务公证处，请联系客服人员：**********"
                    jigouyichang = true
//                    tvRegister.visibility = View.GONE
                }
            }
        }
    }

    override fun showNotaryList(bean: NotarizationBean?) {
        Log.d("Test", "执行了showNotryList 方法")
//        var deviceList: List<DeviceInfo> = bean?.parseList(DeviceInfo::class.java) as List<DeviceInfo>
        if (bean?.getItems() != null) {
//            showDeviceDialog(bean?.getItems() as List<DeviceInfo>)
        } else {
            showDeviceDialog(listOf())
        }
    }

    override fun getAccessPublicKeySuccess() {
        var usbname = SPMgr.getInstance(mActivity).getElement(AppConfig.LOCAL_USB_NAME, "")
        if (!TextUtils.isEmpty(usbname)) {
            //启用的是usb摄像头
            if (AppConfig.USBPNAME != usbname) {
                //环境摄像头连接异常
                if (sys_remind.isNullOrEmpty())
                    sys_remind = "环境摄像头连接异常"
                else
                    "$sys_remind\n环境摄像头连接异常".also { sys_remind = it }
            }
            cameraErroDialog(sys_remind!!)
        } else {
            //启用的是萤石云摄像头  调接口查询是否绑定了萤石云摄像头
            MainPresenter(mActivity).queryYinshiyunByMac(MacUtils.getMacAddress(this), object : CommonView {
                override fun commonSuccess(msg: String?) {
                    Log.d(TAG, "mac binded yingshiyun")
                    if (msg == "200")
                        cameraErroDialog(sys_remind!!)
                    else {
                        if (sys_remind.isNullOrEmpty())
                            sys_remind = "未绑定环境摄像头"
                        else
                            "$sys_remind\n未绑定环境摄像头".also { sys_remind = it }
                        cameraErroDialog(sys_remind!!)
                    }

                }

                override fun commonFail() {
                    cameraErroDialog(sys_remind!!)
                }

            })
        }
//
//        queryP?.getAccessPublicKeyNoCallback();


        REGISTERTOKEN = "";
        ivSplash.visibility = View.VISIBLE
        if (isFirst)
            queryP?.getNotary()
        else
            queryP?.getNotaryNew()
    }

    override fun getAccessPublicKeyFail(error: String?) {
        ToastUtils.getInstance(this).toastError("获取公钥失败！")
    }

    override fun mechineAddSuccess() {
        tvRegister.visibility = View.GONE
    }

    override fun mechineAddFail() {
        tvRegister.visibility = View.VISIBLE
    }


    override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
        dismissDialog()
        var device: RegionBean.ProvinceBean.CityBean.NortryDTO? = adapter.getItem(position) as RegionBean.ProvinceBean.CityBean.NortryDTO?
//        queryP?.addNotary(device?.unitGuid, device?.notarialName)
        val selfDialog = SelfDialog(this, true)
        selfDialog.setTitle("")
        selfDialog.setMessage("您选择的是：" + device?.text)
        selfDialog.setYesOnclickListener("确定") {
            selfDialog.dismiss()
            queryP?.addNotary(device?.unitGuid, device?.text)
        }
        selfDialog.setNoOnclickListener("点错了") {
            selfDialog.dismiss()
            if (deviceDialog != null) {
                deviceDialog?.show()
            }
        }
        selfDialog.show()
    }

    fun changeLocation(view: View?) {
        if (null != deviceDialog && deviceDialog?.isShowing == true)
            deviceDialog?.dismiss()
        var pop = com.gc.notarizationpc.utils.PopwindowUtil()
//        pop?.showpop(mActivity, findViewById(R.id.mains), mActivity)
        pop?.showpopNewNotaryListw(mActivity, findViewById(R.id.mains), this)
    }

    fun show_dialog() {
        if (deviceDialog != null)
            deviceDialog?.show()
    }

    private fun openFloatPermission() {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N && !Settings.canDrawOverlays(this)) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + this.packageName))
            startActivityForResult(intent, 1234)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        Log.d("zpzp", "code == $requestCode")
        if (requestCode == 1234) {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N && !Settings.canDrawOverlays(this)) {
                Toast.makeText(applicationContext, "请在设置-权限设置里打开悬浮窗权限", Toast.LENGTH_SHORT).show()
            }
        }
    }

    fun setWindow() {
        runOnUiThread(Runnable {
            val view = window.decorView
            val paint = Paint()
            val cm = ColorMatrix()
            cm.setSaturation(0f)
            paint.colorFilter = ColorMatrixColorFilter(cm)
            view.setLayerType(View.LAYER_TYPE_HARDWARE, paint)
        })

    }

    /**
     * 禁止Edittext弹出软件盘，光标依然正常显示。
     */
    private fun disableShowSoftInput(view: View) {
        Log.i(TAG, "disableShowSoftInput")
        val cls = EditText::class.java
        var method: Method
        try {
            method = cls.getMethod("setShowSoftInputOnFocus", Boolean::class.javaPrimitiveType)
            method.isAccessible = true
            method.invoke(view, false)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        try {
            method = cls.getMethod("setSoftInputShownOnFocus", Boolean::class.javaPrimitiveType)
            method.isAccessible = true
            method.invoke(view, false)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 展示app内部系统设置弹窗
     */
    fun SysDialog() {
        val dialog = DialogUtils.getInstance().getCenterDialog(this, R.layout.systemset_layout)
        var sys_into_layout = dialog.findViewById<LinearLayout>(R.id.sys_into_layout)
        var sys_pwd = dialog.findViewById<EditText>(R.id.sys_pwd)
        var sys_cancel = dialog.findViewById<Button>(R.id.sys_cancel)
        var sys_sure = dialog.findViewById<Button>(R.id.sys_sure)

        var sys_camera_setting = dialog.findViewById<LinearLayout>(R.id.sys_camera_setting)
        var localusb_layout = dialog.findViewById<LinearLayout>(R.id.localusb_layout)
        var yinshiyun = dialog.findViewById<CheckBox>(R.id.yinshiyun)
        var localusb = dialog.findViewById<CheckBox>(R.id.localusb)
        var sys_usbcamera = dialog.findViewById<EditText>(R.id.sys_usbcamera)
        var sys_cam_cancel = dialog.findViewById<Button>(R.id.sys_cam_cancel)
        var sys_cam_sure = dialog.findViewById<Button>(R.id.sys_cam_sure)
        //不弹出软件键盘
        disableShowSoftInput(sys_pwd)
        dialog.setCanceledOnTouchOutside(false)
//        sys_pwd.setInputType(InputType.TYPE_NULL)


        sys_into_layout.visibility = View.VISIBLE
        sys_camera_setting.visibility = View.GONE
        sys_cancel.setOnClickListener(object : MyOnclickClickListener() {
            override fun mOnClick(v: View?) {
                tvVersion.isClickable = true;
                dialog.dismiss()
            }
        })
        sys_cam_cancel.setOnClickListener(object : MyOnclickClickListener() {
            override fun mOnClick(v: View?) {
                tvVersion.isClickable = true;
                dialog.dismiss()
            }
        })
        sys_sure.setOnClickListener(object : MyOnclickClickListener() {
            override fun mOnClick(v: View?) {
                if (TextUtils.isEmpty(sys_pwd.text.toString())) {
                    toastInfo("请输入密码")
                } else if (!"000111".equals(sys_pwd.text.toString())) {
                    sys_pwd.setText("")
                    toastInfo("密码错误，请重新输入")
                } else {
                    sys_into_layout.visibility = View.GONE
                    sys_camera_setting.visibility = View.VISIBLE
                    if (!TextUtils.isEmpty(SPMgr.getInstance(mActivity).getElement(AppConfig.LOCAL_USB_NAME, ""))) {
                        localusb.isChecked = true
                        yinshiyun.isChecked = false
                        localusb_layout.visibility = View.VISIBLE
                    } else {
                        yinshiyun.isEnabled = false
                        localusb_layout.visibility = View.INVISIBLE
                    }
                }
            }
        })
        sys_cam_sure.setOnClickListener(object : MyOnclickClickListener() {
            override fun mOnClick(v: View?) {
                if (yinshiyun.isChecked) {
                    SPMgr.getInstance(mActivity).putElement(AppConfig.LOCAL_USB_NAME, "")
                    dialog.dismiss()
                    tvVersion.isClickable = true;
                    return
                }
                if (localusb.isChecked) {
                    if (TextUtils.isEmpty(sys_usbcamera.text.toString().trim())) {
                        toastInfo("请输入摄像头名称")
                        return
                    } else if ("USB RGB Camera".equals(sys_usbcamera.text.toString()) ||
                            "USB 2.0 Camera".equals(sys_usbcamera.text.toString()) ||
                            "null".equals(sys_usbcamera.text.toString())) {
                        toastInfo("摄像头名称设置错误")
                        return
                    } else if (sys_usbcamera.text.toString().trim().length < 2) {
                        toastInfo("输入长度请控制在2~50位")
                        return
                    } else if (!UVCCameraUtil.checkUsbDevice(mActivity, sys_usbcamera.text.toString().trim())) {
                        toastInfo("未检测到本地环境摄像头设备，请检查确认后重试")
                        return
                    } else {
                        SPMgr.getInstance(mActivity).putElement(AppConfig.LOCAL_USB_NAME, sys_usbcamera.text.toString())
                    }
                    dialog.dismiss()
                    tvVersion.isClickable = true;
                }
            }
        })
        yinshiyun.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                localusb.isChecked = false
                yinshiyun.isChecked = true
                localusb_layout.visibility = View.INVISIBLE
                sys_usbcamera.setText("")
                yinshiyun.isEnabled = false
                localusb.isEnabled = true
            }
        }
        localusb.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                localusb.isChecked = true
                yinshiyun.isChecked = false
                localusb_layout.visibility = View.VISIBLE
                if (TextUtils.isEmpty(SPMgr.getInstance(mActivity).getElement(AppConfig.LOCAL_USB_NAME, "")))
                    sys_usbcamera.setText(AppConfig.USBPNAME_DEFAULT)
                else
                    sys_usbcamera.setText(SPMgr.getInstance(mActivity).getElement(AppConfig.LOCAL_USB_NAME, ""))
                yinshiyun.isEnabled = true
                localusb.isEnabled = false
            }
        }
        dialog.show()
        //当弹框出的时候，不可以再次点击版本号
        tvVersion.isClickable = false;
    }

    /**
     * 展示相机连接错误弹窗
     */
    fun cameraErroDialog(text: String) {
        if (text.isNullOrEmpty() || text.trim().isNullOrEmpty())
            return
        if (remindDialog == null)
            remindDialog = DialogUtils.getInstance().getCenterDialog(this, R.layout.camera_erro)
//        if (remindDialog!!.isShowing) {
//            return
//        }
        var cancel = remindDialog!!.findViewById<TextView>(R.id.camera_erro_cancel)
        var context = remindDialog!!.findViewById<TextView>(R.id.camera_erro_content)
        context.setText(text)
        cancel.setOnClickListener(object : MyOnclickClickListener() {
            override fun mOnClick(v: View?) {
                remindDialog!!.dismiss()
            }
        })
        remindDialog!!.show()
    }
}
