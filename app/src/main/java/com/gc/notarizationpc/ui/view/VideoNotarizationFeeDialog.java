package com.gc.notarizationpc.ui.view;

import static me.goldze.mvvmhabit.utils.ToastUtils.toastInfo;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.MenuInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.FeeInfoResponse;
import com.gc.notarizationpc.ui.adapter.FeeInfoAdapter;
import com.gc.notarizationpc.ui.adapter.FeeParentAdapter;
import com.gc.notarizationpc.widget.BaseDialog;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * 受理室 缴费清单
 */
public class VideoNotarizationFeeDialog extends BaseDialog {
    private Context mContext;
    private TextView tvOfflinePay, tvTotal;
    private RecyclerView rvFeeParent;
    private List<FeeInfoResponse> mFeeInfoResponse;
    private List<MenuInfo> dataList;
    private FeeInfoAdapter feeInfoAdapter;
    private LinearLayout lnlTotal;
    private String mCaseInfoId;

    public VideoNotarizationFeeDialog(Context context, String caseInfoId, List<FeeInfoResponse> feeInfoResponse) {
        super(context);
        mContext = context;
        mFeeInfoResponse = feeInfoResponse;
        this.mCaseInfoId = caseInfoId;
    }

//    private void showData() {
//        if (mFeeInfoResponse != null && mFeeInfoResponse.getHadSetBilling() == 1) {
//            lnlTotal.setVisibility(View.VISIBLE);
//            rvFeeParent.setVisibility(View.VISIBLE);
//            dataList=new ArrayList<>();
//            feeParentAdapter.refreshAdapter(dataList);
//            //事项费用
//            List<FeeInfoResponse.MattersFeeListDTO> mattersFeeListDTOS = mFeeInfoResponse.getMattersFeeList();
//            if (mattersFeeListDTOS != null) {
//                for (FeeInfoResponse.MattersFeeListDTO mattersFeeListDTO : mattersFeeListDTOS) {
//                    MenuInfo parent = new MenuInfo();
//                    parent.setKey(mattersFeeListDTO.getMattersName());
//                    parent.setId(mattersFeeListDTO.getMattersId());
//                    List<MenuInfo> childs = new ArrayList<>();
//                    //事项公证费
//                    MenuInfo child1 = new MenuInfo();
//                    child1.setKey(mContext.getString(R.string.gz_fee));
//                    child1.setName(mattersFeeListDTO.getNotarizationFee() + mContext.getString(R.string.chineseUnit));
//                    //副本费
//                    MenuInfo child2 = new MenuInfo();
//                    child2.setKey(mContext.getString(R.string.fb_fee));
//                    child2.setName(mattersFeeListDTO.getCopyFee() + mContext.getString(R.string.chineseUnit));
//                    childs.add(child1);
//                    childs.add(child2);
//                    parent.setChild(childs);
//                    dataList.add(parent);
//                }
//            }
//            //其他费用
//            List<FeeInfoResponse.OtherFeeListDTO> otherFeeList = mFeeInfoResponse.getOtherFeeList();
//            if (otherFeeList != null) {
//                MenuInfo parent = new MenuInfo();
//                parent.setKey(getContext().getString(R.string.other_fee));
//                List<MenuInfo> childs = new ArrayList<>();
//                for (FeeInfoResponse.OtherFeeListDTO otherFeeListDTO : otherFeeList) {
//                    MenuInfo child1 = new MenuInfo();
//                    child1.setKey(convertBillingItem(otherFeeListDTO.getBillingItem()));
//                    child1.setName(otherFeeListDTO.getTotalCost() + mContext.getString(R.string.chineseUnit));
//                    childs.add(child1);
//                }
//                parent.setChild(childs);
//                dataList.add(parent);
//            }
////            else {
////                MenuInfo parent = new MenuInfo();
////                parent.setKey(getContext().getString(R.string.other_fee));
////                parent.setName("0"+ mContext.getString(R.string.chineseUnit));
////                parent.setChild(new ArrayList<>());
////                dataList.add(parent);
////            }
//            //减免费用
//            MenuInfo reduceFee = new MenuInfo();
//            reduceFee.setKey(mContext.getString(R.string.reduce_fee));
//            reduceFee.setName(mFeeInfoResponse.getReductionFee() + mContext.getString(R.string.chineseUnit));
//            //合计费用
//            MenuInfo totalFee = new MenuInfo();
//            totalFee.setKey(mContext.getString(R.string.total_fee));
//            totalFee.setName(mFeeInfoResponse.getTotalFee() + mContext.getString(R.string.chineseUnit));
//            dataList.add(reduceFee);
////            dataList.add(totalFee);
//            tvTotal.setText(mFeeInfoResponse.getTotalFee() + mContext.getString(R.string.chineseUnit));
//            feeParentAdapter.refreshAdapter(dataList);
//            feeParentAdapter.notifyDataSetChanged();
//        } else {
//            lnlTotal.setVisibility(View.GONE);
//            rvFeeParent.setVisibility(View.GONE);
//        }
//    }

    private void showListData() {
        feeInfoAdapter.setData(mFeeInfoResponse);
        feeInfoAdapter.refreshAdapter(mFeeInfoResponse);
        rvFeeParent.setAdapter(feeInfoAdapter);
        feeInfoAdapter.notifyDataSetChanged();

        //计算合计费用
        double total = 0f;
        if (mFeeInfoResponse != null && mFeeInfoResponse.size() > 0) {
            for (FeeInfoResponse tmp : mFeeInfoResponse) {
                if (tmp != null) {
                    total += tmp.getAmountReceivable();
                }
            }
        }
        tvTotal.setText(mContext.getString(R.string.chinese_unit_with_value, new BigDecimal(total).divide(new BigDecimal("100")).toString()));
    }

    private String convertBillingItem(Integer billingItem) {
        String billingItemStr = "其他费用";
        //收费项目;本系统中：01-公证费,02-公证服务费,03-公证书译文费,04-原件译文费,05-副本费,06-调查费,07-代书费,08-调卷费,09-涉台邮费,10-调解费,11-送达费,99-其它费用
        switch (billingItem) {
            case 1:
                billingItemStr = Utils.getContext().getString(R.string.gz_fee);
                break;
            case 2:
                billingItemStr = Utils.getContext().getString(R.string.notarization_service_fee);
                break;
            case 3:
                billingItemStr = Utils.getContext().getString(R.string.notarialTranslationFee);
                break;
            case 4:
                billingItemStr = Utils.getContext().getString(R.string.originalTranslationFee);
                break;
            case 5:
                billingItemStr = Utils.getContext().getString(R.string.fb_fee);
                break;
            case 6:
                billingItemStr = Utils.getContext().getString(R.string.investigationCharge);
                break;
            case 7:
                billingItemStr = Utils.getContext().getString(R.string.bookAgencyFee);
                break;
            case 8:
                billingItemStr = Utils.getContext().getString(R.string.certiorariFee);
                break;
            case 9:
                billingItemStr = Utils.getContext().getString(R.string.postageForTaiwan);
                break;
            case 10:
                billingItemStr = Utils.getContext().getString(R.string.mediationFee);
                break;
            case 11:
                billingItemStr = Utils.getContext().getString(R.string.deliveryCharge);
                break;
            default:
                billingItemStr = Utils.getContext().getString(R.string.other_fee);
                break;
        }
        return billingItemStr;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_video_notarization_fee);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();
        showListData();

    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        tvOfflinePay = findViewById(R.id.tv_offline_pay);
        tvTotal = findViewById(R.id.tv_total);
        rvFeeParent = findViewById(R.id.rv_fee);
        lnlTotal = findViewById(R.id.lnl_total);

        rvFeeParent.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });

        feeInfoAdapter = new FeeInfoAdapter(mContext, R.layout.item_list_fee, 0);
        rvFeeParent.setAdapter(feeInfoAdapter);
        feeInfoAdapter.notifyDataSetChanged();

        findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowing()) {
                    dismiss();
                }
            }
        });

        findViewById(R.id.iv_refresh).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //刷新
                getPaymentList();
            }
        });


    }

    /**
     * 网络请求方法，在ViewModel中调用Model层，通过Okhttp+Retrofit+RxJava发起请求
     */
    public void getPaymentList() {
        showProgress();
        Map<String, Object> params = new HashMap<>();
        params.put("caseInfoId", mCaseInfoId);
        RequestUtil.payMentRecords(params, new MyObserver<List<FeeInfoResponse>>() {
            @Override
            public void onSuccess(List<FeeInfoResponse> result) {
                hideProgress();
                if (result != null) {
                    mFeeInfoResponse = result;
                    showListData();
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                hideProgress();
                toastInfo(WorkstatusEnum.FEE_INFO_FAIL.msg);
            }
        });

    }

    @Override
    public void show() {
        super.show();
    }


}
