package com.gc.notarizationpc.ui.fragment

import android.annotation.SuppressLint
import android.app.Dialog
import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.text.*
import android.text.InputFilter.LengthFilter
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import android.view.animation.AnimationUtils
import android.view.animation.LayoutAnimationController
import android.widget.EditText
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.fastjson.JSON
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.example.framwork.noHttp.Bean.BaseResponseBean
import com.example.framwork.utils.DialogUtils
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.*
import com.gc.notarizationpc.common.CommonInfo
import com.gc.notarizationpc.model.*
import com.gc.notarizationpc.ui.ComparisonIDcardActivity
import com.gc.notarizationpc.ui.NotarizationSelfActivity
import com.gc.notarizationpc.ui.adapter.DeviceAdapter
import com.gc.notarizationpc.ui.adapter.SelectedInnerTwoGridItemsAdapter
import com.gc.notarizationpc.ui.presenter.MainPresenter
import com.gc.notarizationpc.ui.presenter.NotarizaSelfPresenter
import com.gc.notarizationpc.utils.*
import com.gc.notarizationpc.widget.CustomSelectPicker
import com.gc.notarizationpc.widget.MyLayoutNoScrollManager
import com.gc.notarizationpc.widget.editspinner.AppCompatEditSpinner
import kotlinx.android.synthetic.main.fragment_self_proxy_step_two.*
import kotlinx.android.synthetic.main.fragment_self_step_two.*
import kotlinx.android.synthetic.main.fragment_self_step_two.addressChose
import kotlinx.android.synthetic.main.fragment_self_step_two.addressDetail
import kotlinx.android.synthetic.main.fragment_self_step_two.birthday
import kotlinx.android.synthetic.main.fragment_self_step_two.cardId
import kotlinx.android.synthetic.main.fragment_self_step_two.gzcChose
import kotlinx.android.synthetic.main.fragment_self_step_two.mobilePhone
import kotlinx.android.synthetic.main.fragment_self_step_two.realName
import kotlinx.android.synthetic.main.fragment_self_step_two.recyclerView
import kotlinx.android.synthetic.main.fragment_self_step_two.sexs
import kotlinx.android.synthetic.main.fragment_self_step_two.tvNextStep
import kotlinx.android.synthetic.main.fragment_self_step_two.tvUpStep
import kotlinx.android.synthetic.main.fragment_self_step_two.ywyyChose
import org.joda.time.format.DateTimeFormat
import org.json.JSONArray
import org.json.JSONObject
import java.text.ParseException
import java.text.SimpleDateFormat


/**
 *  自助公证，填写申请信息，代办
 */
class SelfStepProxyTwoFragment : BaseFragment(), NotarizaSelfPresenter.INotarizaSelfView, MainPresenter.IHomeView, OnItemClickListener, CustomSelectPicker.Callback {
    private var currentActivity: NotarizationSelfActivity? = null
    private var itemList = ArrayList<NotrayBean.ChildrenDTO>()
    private var countyList = ArrayList<CountyBean>()
    private var langList = ArrayList<LanguarBean>()
    private var notarHomeList = ArrayList<NotarhomeBean>()
    private var mAdapter: SelectedInnerTwoGridItemsAdapter? = null;
    private var notarizationP: NotarizaSelfPresenter? = null
    private var queryP: MainPresenter? = null
    private var customSelectPicker: CustomSelectPicker? = null;
    private var indexSelect: Int? = null;
    private var curLanguage: LanguarBean? = null;
    private var currCounty: CountyBean? = null;
    private var curGzs: NotarhomeBean? = null;
    private var mNotaryitems: String? = "";
    private var mApplyUsers: String? = "";
    private var TAG = "SelfStepTwoFragment"
    private var deviceDialog: Dialog? = null
    private var deviceAdapter: DeviceAdapter? = null
    var REFRESH_NOTARIZATION: Int? = 0x01
    private var changelocation: TextView? = null
    var city: String = ""//市
    var adcode: String = ""//区域码
    var longitude: String = ""
    var latitude: String = ""
    var address: String = ""
    private var countyListStr = ArrayList<String>()
    private var langListStr = ArrayList<String>()
    val handler: Handler = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message?) {
            super.handleMessage(msg)
            when (msg?.what) {
                REFRESH_NOTARIZATION -> {
                    changelocation?.text = city
                    Log.d("zpzp", "refresh adcode == " + adcode)
                    queryP?.getNotaryListNewOnline(adcode, CommonUtil.bd_decrypt(longitude, latitude), false)

                }
            }
        }
    }

    private var sexList: ArrayList<String?>? = ArrayList()
    private var timePickUtil: TimePickUtil? = null

    private var apply_recyclerview: CusRecycleview? = null

    //添加申请人
    private fun addApply() {
        if (applyList?.size >= 10) {
            toastInfo("当前申请人最多可添加10个人")
            return
        }
        showTwoBtnDialog("请选择添加申请人的方式", "扫描身份证", "手动录入", object : IDialogListener {
            override fun rightClick() {
                var info = CustomIdCardInfo()
                Log.e("Test", "添加的空人员信息" + info.toString())
                applyList.add(info)
                quickAdapter?.setList(applyList)
                quickAdapter?.notifyDataSetChanged()
                if (applyList.size >= 1) {
                    apply_recyclerview?.scrollToPosition(applyList.size - 1)
                }

            }

            override fun leftClick() {
                PopIdCardComparison(mActivity).showIdCardComparison(mActivity, object : PopIdCardComparison.IdCardNotarizationiListener {
                    override fun getResult(idCardInfo: CustomIdCardInfo?) {

                        var idCardInfo = idCardInfo
                        mActivity.runOnUiThread {
                            if (idCardInfo != null) {
                                applyList.add(idCardInfo)
                                quickAdapter?.setList(applyList)
                                quickAdapter?.notifyDataSetChanged()
                                if (applyList.size >= 1) {
                                    apply_recyclerview?.scrollToPosition(applyList.size - 1)
                                }
                            }

                        }
                    }

                    override fun getError(error: String?) {
                        mActivity.runOnUiThread {
                            toastError(error)
                        }
                    }


                })
            }

            override fun closeClick() {

            }

        })
    }

    fun getSpannable(str: String?, color: String?, start: Int, end: Int): Spannable {
        val spannable: Spannable = SpannableString(str)
        spannable.setSpan(ForegroundColorSpan(Color.parseColor(color)), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        return spannable
    }

    private fun initSomeDataOnPage() {
        Log.i(TAG, "initSomeDataOnPage")
        var gzc = (mActivity as NotarizationSelfActivity?)?.notaryEntity;
        Log.d(TAG, "gzc?.institutionType == " + gzc?.institutionType)
        if (!"3".equals(gzc?.institutionType)) {
            gzcChose.text = gzc?.notarialName;
        } else {
            gzcChose?.setOnClickListener {
                indexSelect = 3;
                if (!TextUtils.isEmpty(adcode) && adcode.length > 4)
                    adcode = adcode?.substring(0, 4) + "00"
                else
                    adcode = "320000"
                queryP?.getNotaryListNewOnline(adcode, CommonUtil.bd_decrypt(longitude, latitude), false)
            };
        }

        sexs.text = "女";
        if ((mActivity as NotarizationSelfActivity?)?.sexsCard == 1) {
            sexs.text = "男";
        }
        realName.text = (mActivity as NotarizationSelfActivity?)?.realNameCard;
        var birthdayTem = (mActivity as NotarizationSelfActivity?)?.birthdayCard?.split(" ");
        if (birthdayTem?.size!! > 0) {
            birthday.text = birthdayTem?.get(0);
        } else {
            birthday.text = (mActivity as NotarizationSelfActivity?)?.birthdayCard;
        }
        cardId.text = (mActivity as NotarizationSelfActivity?)?.cardNumberCard;
        mobilePhone.text = (mActivity as NotarizationSelfActivity?)?.mobilePhoneCard;
        addressDetail.text = (mActivity as NotarizationSelfActivity?)?.addressCard;
    }

    fun initWithTwoParamsToString(): Boolean {
        Log.i(TAG, "initWithTwoParamsToString")
        mNotaryitems = "";
        var jsonNotaryitemsArray = JSONArray();
        for (obj in itemList) {
            if (obj.isChecked) {
                var jsonNotaryitemsObj = JSONObject();
                jsonNotaryitemsObj.put("notaryItemId", obj.unitGuid);
                jsonNotaryitemsObj.put("notaryItemName", obj.name);
                jsonNotaryitemsObj.put("notaryNum", "1");
                jsonNotaryitemsArray.put(jsonNotaryitemsObj);
            }
        }
        mNotaryitems = jsonNotaryitemsArray.toString();
//        var jsonApplyUsersArray = JSONArray();
//        var jsonApplyUsersObj = JSONObject();
//        jsonApplyUsersObj.put("name", (mActivity as NotarizationSelfActivity?)?.realNameCard);
//        jsonApplyUsersObj.put("gender", ((mActivity as NotarizationSelfActivity?)?.sexsCard).toString());
//        jsonApplyUsersObj.put("idCard", (mActivity as NotarizationSelfActivity?)?.cardNumberCard);
//        jsonApplyUsersObj.put("mobile", (mActivity as NotarizationSelfActivity?)?.mobilePhoneCard);
//        jsonApplyUsersObj.put("birthday", (mActivity as NotarizationSelfActivity?)?.birthdayCard);
//        jsonApplyUsersObj.put("address", (mActivity as NotarizationSelfActivity?)?.addressCard);
//        jsonApplyUsersArray.put(jsonApplyUsersObj)
//        mApplyUsers = jsonApplyUsersArray.toString();
        var allUserList = ArrayList<CustomIdCardInfo>()

        for (tmp in applyList) {
            allUserList.add(tmp)
            var proxy = CustomIdCardInfo();
            proxy.idCard = (mActivity as NotarizationSelfActivity?)?.cardNumberCard
            //这里性别的转换是因为，后台默认0女1男，但是小一体机身份识别性别为1男 2女，为了保持给后端的性别一致，不改动到之前代码，这里提交申请人信息的时候，手动转换一下
            proxy.gender = "0"
            if ((mActivity as NotarizationSelfActivity?)?.sexsCard == 1) {
                proxy.gender = "1"
            }
            proxy.mobile = (mActivity as NotarizationSelfActivity?)?.mobilePhoneCard
            proxy.birthday = (mActivity as NotarizationSelfActivity?)?.birthdayCard
            proxy.name = (mActivity as NotarizationSelfActivity?)?.realNameCard
            proxy.address = addressDetail.text.toString()
            proxy.principal = tmp?.name
            proxy.isDaiBan = 1
            allUserList.add(proxy)
        }

        allUserList.reverse()//代理人排最上面
        mApplyUsers = JSON.toJSONString(allUserList)

        return jsonNotaryitemsArray.length() == 0
    }

    override fun onResume() {
        super.onResume()
        val window = activity?.window
        window?.decorView?.viewTreeObserver?.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                val r = Rect()
                window.decorView.getWindowVisibleDisplayFrame(r)
                val screenHeight = window.decorView.rootView.height
                val keyboardHeight = screenHeight - r.bottom
                // 如果键盘高度小于屏幕高度的1/4，则认为软键盘已经关闭
                if (keyboardHeight < screenHeight / 4) {
                    // 软键盘已经关闭
                    // 在这里执行你的操作

                }
            }
        })

    }

    private var quickAdapter: BaseQuickAdapter<CustomIdCardInfo?, BaseViewHolder>? = null
    private var isDelMode = false;
    private var applyList = ArrayList<CustomIdCardInfo>();

    //删除申请人
    fun delApply() {
        isDelMode = !isDelMode
        quickAdapter?.setList(applyList)
        quickAdapter?.notifyDataSetChanged()
    }


    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {
        Log.i(TAG, "initViewsAndEvents")
        applyList = ArrayList<CustomIdCardInfo>();
        deviceAdapter = DeviceAdapter()
        deviceAdapter?.setOnItemClickListener(this)
        currentActivity = mActivity as NotarizationSelfActivity?;
        itemList = (mActivity as NotarizationSelfActivity?)?.choseList!!;
        notarizationP = NotarizaSelfPresenter(mActivity, this);
        queryP = MainPresenter(mActivity, this)
        notarizationP?.queryCountryArea();
        notarizationP?.queryCountryLanguage();
        notarizationP?.queryDepartmentTree();

        iv_add.setOnClickListener { addApply() }

        iv_del.setOnClickListener { delApply() }

        sexList?.add("男")
        sexList?.add("女")
        tv_proxy_address.text = getSpannable(tv_proxy_address.text.toString(), "#DF2518", 0, 1)

        tvNextStep?.setOnClickListener {
            if (addressChose?.text.toString().isEmpty() || ywyyChose?.text.toString().isEmpty()) {
                toastInfo("请选择地区和语言")
            } else if (gzcChose?.text.toString().isEmpty()) {
                toastInfo("请选择公证处")
            }
//            else if (CommonUtil.compareListWithStr(countyListStr,addressChose.text)) {
////                toastInfo("请输入完整使用地区")
////            } else if (CommonUtil.compareListWithStr(langListStr,ywyyChose.text)) {
////                toastInfo("请输入完整译文语言")
////            }
            else if (!countyListStr.contains(addressChose.text)) {
                toastInfo("请输入完整使用地区")
            } else if (!langListStr.contains(ywyyChose.text)) {
                toastInfo("请输入完整译文语言")
            } else {
                val isNullItems = initWithTwoParamsToString();
                if (isNullItems) {
                    toastInfo("至少选择一个公证事项")
                } else if (applyList?.size <= 0) {
                    toastInfo("申请人不可为空")
                } else  {
                    if (!checkApplyPerson()) {
                        toastInfo("申请人信息填写有误")
                    } else if (TextUtils.isEmpty(addressDetail.text)) {
                        toastInfo("代理人地址不能为空")
                    } else {
                        val userId: String? = (mActivity as NotarizationSelfActivity?)?.userInfo?.userId;
                        val name: String? = (mActivity as NotarizationSelfActivity?)?.realNameCard;
                        val useArea: String? = addressChose?.text.toString();
                        val useLanguage: String? = ywyyChose?.text.toString();
                        val purposeName: String? = "";
                        val notaryId: String? = (mActivity as NotarizationSelfActivity?)?.notaryId;
                        val notaryitems: String? = mNotaryitems;
                        val applyUsers: String? = mApplyUsers;
                        val mac: String? = MacUtils.getMacAddress(mActivity)
                        val remarks: String? = edit_input?.text.toString();
                        notarizationP?.addNotaryorder(userId, name, useArea, useLanguage, purposeName, notaryId, notaryitems, applyUsers, mac, "1",remarks);
                    }
                }
            }
        };
        tvUpStep?.setOnClickListener {
            currentActivity?.onNavigationItemSelected(0)
        }
        initByRecyclerViewGrid();
        initSomeDataOnPage();
        customSelectPicker = CustomSelectPicker(mActivity, this);

        if (TextUtils.isEmpty(CommonInfo.city) || TextUtils.isEmpty(CommonInfo.adcode) ||
                TextUtils.isEmpty(CommonInfo.longitude) || TextUtils.isEmpty(CommonInfo.latitude) ||
                TextUtils.isEmpty(CommonInfo.address)) {
            Log.d(TAG, "gps info less: " + CommonInfo.tostring())
        }
        city = CommonInfo.city
        adcode = CommonInfo.adcode
        longitude = CommonInfo.longitude
        latitude = CommonInfo.latitude
        address = CommonInfo.address

        addressChose.callback = AppCompatEditSpinner.SelectItemCallback() { values: String, index: Int ->
            if (indexSelect == 1) {
                addressChose.text = values;
                currCounty = countyList[index];
            }
        }

        ywyyChose.callback = AppCompatEditSpinner.SelectItemCallback() { values: String, index: Int ->
            if (indexSelect == 2) {
                ywyyChose.text = values;
                curLanguage = langList[index];
            }
        }

        val linearLayoutManager: LinearLayoutManager = object : LinearLayoutManager(mActivity) {
            override fun canScrollVertically(): Boolean {
                return true
            }
        }
        apply_recyclerview = view?.findViewById(R.id.apply_list)
        apply_recyclerview?.isNestedScrollingEnabled = false
        apply_recyclerview?.layoutManager = object : LinearLayoutManager(mActivity, VERTICAL, false) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
        apply_recyclerview?.adapter = object : BaseQuickAdapter<CustomIdCardInfo?, BaseViewHolder>(R.layout.item_apply_selfnor_list) {

            override fun convert(holder: BaseViewHolder, idcardinfo: CustomIdCardInfo?) {
                holder.setText(R.id.tv_name, getSpannable(holder.getView<TextView>(R.id.tv_name).text.toString(), "#DF2518", 0, 1))
                holder.setText(R.id.tv_sex, getSpannable(holder.getView<TextView>(R.id.tv_sex).text.toString(), "#DF2518", 0, 1))
                holder.setText(R.id.tv_idcard, getSpannable(holder.getView<TextView>(R.id.tv_idcard).text.toString(), "#DF2518", 0, 1))
                holder.setText(R.id.tv_phone, getSpannable(holder.getView<TextView>(R.id.tv_phone).text.toString(), "#DF2518", 0, 1))
                holder.setText(R.id.tv_address, getSpannable(holder.getView<TextView>(R.id.tv_address).text.toString(), "#DF2518", 0, 1))
                holder.setText(R.id.tv_birth, getSpannable(holder.getView<TextView>(R.id.tv_birth).text.toString(), "#DF2518", 0, 1))

                if (applyList.size == 0) {
                    return;
                }
                Log.e("Test", "applyList===" + applyList.get(0).toString())
                try {
                    if (isDelMode) {
                        holder.setVisible(R.id.tv_del, true)
                    } else {
                        holder.setVisible(R.id.tv_del, false)
                    }
                    //说明从身份证识别过来的数据
//                    if (!TextUtils.isEmpty(idcardinfo?.idCard)) {

//                    holder.setText(R.id.editSex, )
                    if (idcardinfo?.gender == "1") {
                        holder.getView<TextView>(R.id.editSex).text = "男"
                    } else if (idcardinfo?.gender == "0" || idcardinfo?.gender == "2") {
                        holder.getView<TextView>(R.id.editSex).text = "女"
                    } else {
                        holder.getView<TextView>(R.id.editSex).text = idcardinfo?.gender
                    }

                    if (idcardinfo?.gender == "男") {
                        applyList[holder.adapterPosition].gender = "1"
                    } else if (idcardinfo?.gender == "女") {
                        applyList[holder.adapterPosition].gender = "0"
                    }
                    holder.setText(R.id.editIdCard, idcardinfo?.idCard)
                    holder.setText(R.id.editRealName, idcardinfo?.name)
                    holder.setText(R.id.editAddress, idcardinfo?.address)
                    holder.setText(R.id.editPhone, idcardinfo?.mobile)
                    Log.d(ComparisonIDcardActivity.TAG, idcardinfo?.name + " / " + idcardinfo?.idCard)
//                    } else {
//                        holder.getView<TextView>(R.id.editSex).text = ""
//                        holder.setText(R.id.editRealName, "")
//                        holder.setText(R.id.editIdCard, "")
//                        holder.setText(R.id.editAddress, "")
//                        holder.setText(R.id.editBirthday, "")
//                    }
                    //标题*标红
                    Log.e("Test", "每次赋值的出生日期" + idcardinfo?.birthday);
                    if (!TextUtils.isEmpty(idcardinfo?.birthday) && (idcardinfo?.birthday?.contains("年")!! || idcardinfo?.birthday?.contains("月")!!)) {
                        holder.setText(R.id.editBirthday, dateStringToString(idcardinfo?.birthday))
                        applyList[holder.adapterPosition].birthday = dateStringToString(idcardinfo?.birthday)
                    } else {
                        Log.e("Test", "idcardinfo?.birthday" + idcardinfo?.birthday);
                        holder.setText(R.id.editBirthday, idcardinfo?.birthday)
                    }

                    holder.getView<EditText>(R.id.editRealName).addTextChangedListener(object : TextWatcher {
                        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

                        override fun afterTextChanged(s: Editable?) {
                            // 更新数据模型中对应字段的值
                            if (holder.adapterPosition < applyList.size) {
                                applyList[holder.adapterPosition].name = s.toString()
                            }

                        }
                    })
                    //过滤姓名只能输入中英文,最大输入30位
                    val filters = arrayOf<InputFilter>(OnlyChineseEnglishInputFilter(), LengthFilter(30))
                    holder.getView<EditText>(R.id.editRealName).filters = filters

                    holder.getView<EditText>(R.id.editIdCard).addTextChangedListener(object : TextWatcher {
                        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

                        override fun afterTextChanged(s: Editable?) {
                            // 更新数据模型中对应字段的值
                            if (holder.adapterPosition < applyList.size) {
                                applyList[holder.adapterPosition].idCard = s.toString()
                                //输入身份证号后自动填充性别，出生日期,男1
                                var gender = getGenderFromIDCard(s.toString());
                                applyList[holder.adapterPosition].gender = gender
                                if (gender == "1") {
                                    holder.setText(R.id.editSex, "男")
                                } else {
                                    holder.setText(R.id.editSex, "女")
                                }

                                val birthDate = getBirthDateFromIDCard(s.toString())
                                val birthDay = if (birthDate.isNullOrEmpty()) "" else getBirthDateFromIDCard(s.toString())
                                applyList[holder.adapterPosition].birthday = birthDay
                                holder.setText(R.id.editBirthday, birthDay)
                            }
                        }
                    })

                    holder.getView<EditText>(R.id.editBirthday).addTextChangedListener(object : TextWatcher {
                        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

                        override fun afterTextChanged(s: Editable?) {
                            // 更新数据模型中对应字段的值
                            if (holder.adapterPosition < applyList.size) {
                                applyList[holder.adapterPosition].birthday = s.toString()
                            }
                        }
                    })

                    holder.getView<EditText>(R.id.editPhone).addTextChangedListener(object : TextWatcher {
                        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

                        override fun afterTextChanged(s: Editable?) {
                            // 更新数据模型中对应字段的值
                            if (holder.adapterPosition < applyList.size) {
                                applyList[holder.adapterPosition].mobile = s.toString()
                            }
                        }
                    })

                    //地址
                    holder.getView<EditText>(R.id.editAddress).addTextChangedListener(object : TextWatcher {
                        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

                        override fun afterTextChanged(s: Editable?) {
                            // 更新数据模型中对应字段的值
                            if (holder.adapterPosition < applyList.size) {
                                applyList[holder.adapterPosition].address = s.toString()
                            }
                        }
                    })

                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }.also { quickAdapter = it }
        quickAdapter?.addChildClickViewIds(R.id.editSex, R.id.tv_del, R.id.editBirthday)

        //列表子控件的点击事件
        quickAdapter?.setOnItemChildClickListener { adapter, view, position ->
            if (view.id == R.id.tv_del) {
                (view as TextView).isClickable = false;
                if (applyList?.size > 0) {
                    showTwoBtnDialog("此操作将删除该数据，是否继续", "取消", "确定", object : IDialogListener {
                        override fun rightClick() {
//                            quickAdapter?.setList(applyList)//更新数据 .setNewInstance(data) //或者setNewData()，初始化数据
                            quickAdapter?.remove(applyList[position])
                            applyList.removeAt(position)
//                            quickAdapter?.notifyItemRemoved(position)
//                            quickAdapter?.notifyItemRangeChanged(position, applyList.size)
                            (view as TextView).isClickable = true;

                            val controller = LayoutAnimationController(AnimationUtils.loadAnimation(mActivity, R.anim.layout_animation_from_bottom))
                            apply_recyclerview?.layoutAnimation = controller
                            apply_recyclerview?.scheduleLayoutAnimation()
                            quickAdapter?.notifyDataSetChanged()
                            toastSuccess("删除成功")
                        }

                        override fun leftClick() {
                            (view as TextView).isClickable = true;
                        }

                        override fun closeClick() {
                            (view as TextView).isClickable = true;
                        }
                    })
                }

            } else if (view.id == R.id.editSex) {
                (view as TextView).isClickable = false;
                var sexPicker: CustomSelectPicker? = CustomSelectPicker(mActivity, object : CustomSelectPicker.Callback {
                    override fun onSelectItems(values: String?, index: Int) {
                        (view as TextView).text = values
                        if (values?.contains("男") == true) {
                            applyList.get(position)?.gender = "1"
                        } else {
                            applyList.get(position)?.gender = "0"
                        }
                        (view as TextView).isClickable = true;
                    }

                    override fun outCancel() {
                        (view as TextView).isClickable = true;
                    }
                });
                sexPicker?.show(sexList!!)

            } else if (view.id == R.id.editBirthday) {
                (view as TextView).isClickable = false;
                timePickUtil = TimePickUtil(mActivity)
                timePickUtil!!.initTimePickerDialog("", false)
                timePickUtil!!.setTimeListener(object : TimePickUtil.TimeListener {
                    override fun setTime(text: String?) {
                        (view as TextView).text = text
                        applyList.get(position)?.birthday = text
                        (view as TextView).isClickable = true;
                    }

                })
                timePickUtil!!.showDialog()

            }
        }

    }

    private fun dateStringToString(date: String?): String {
        Log.i(ComparisonIDcardActivity.TAG, "dateStringToString")
        val dtf = DateTimeFormat.forPattern("yyyy年MM月dd日")
        val dateTime = dtf.parseDateTime(date)
        return dateTime?.toString("yyyy-MM-dd")!!
    }

    /**
     * GCSWRW5067
     * 迷你一体机修改自助公证页面
     */
    private fun initByRecyclerViewGrid() {
        Log.i(TAG, "initByRecyclerViewGrid")
        addressChose.text = "";
        ywyyChose.text = "";
        gzcChose.text = "";


        realName.text = "";
        sexs.text = "";
        birthday.text = "";
        cardId.text = "";
        mobilePhone.text = "";
        addressDetail.text = "";

        val layout = MyLayoutNoScrollManager()
        layout.isAutoMeasureEnabled = true;//防止recyclerview高度为wrap时测量item高度0(一定要加这个属性，否则显示不出来）
//        recyclerView.isNestedScrollingEnabled = false
        recyclerView.layoutManager = layout
        mAdapter = SelectedInnerTwoGridItemsAdapter(true, mActivity);
        recyclerView.adapter = mAdapter;
        mAdapter?.itemsList = itemList;
    }


    //校验申请人信息
    fun checkApplyPerson(): Boolean {
        for (tmp in applyList) {
            if (TextUtils.isEmpty(tmp?.name)) {
                return false;
            } else if (TextUtils.isEmpty(tmp?.gender)) {
                return false;
            } else if (TextUtils.isEmpty(tmp?.idCard)) {
                return false;
            } else if (TextUtils.isEmpty(tmp?.mobile)) {
                return false;
            } else if (TextUtils.isEmpty(tmp?.address)) {
                return false;
            }
        }
        return true;
    }

    //校验身份证号是否与性别，出生年月符合
    fun checkIdCardInfo(): Boolean {
        try {
            for (tmp in applyList) {
//            if (getGenderFromIDCard(tmp?.idCard)!! != tmp?.gender) {
//                toastInfo("性别与身份证不符")
//                return false;
//            } else
//                if (!TextUtils.isEmpty(tmp?.idCard) && !CommonUtil.isIdCard(tmp?.idCard)) {
//                    toastInfo("身份证号格式有误")
//                    return false;
//                }
//                if (!TextUtils.isEmpty(tmp?.idCard) && getBirthDateFromIDCard(tmp?.idCard)!! != tmp?.birthday) {
//                    toastInfo("出生日期与身份证不符")
//                    return false;
//                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return true;
    }

    override fun lazyInit(view: View?, savedInstanceState: Bundle?) {

    }

    override fun getContentViewLayoutID(): Int {
        return if (AppConfig.RGBPID == "c013") {
            R.layout.fragment_self_proxy_step_two;
        } else {
            R.layout.fragment_self_step_two_portrait;
        }
    }

    override fun sendNotarizationSuccess(items: List<NotrayBean>?) {
    }


    override fun queryResultLangeuList(bean: BaseResponseBean?) {
        Log.i(TAG, "queryResultLangeuList")
        var languageList: List<LanguarBean> = bean?.parseList(LanguarBean::class.java) as List<LanguarBean>
        langList = languageList as ArrayList<LanguarBean>;
        indexSelect = 2;
        langListStr = ArrayList<String>();
        for (obj in langList) {
            langListStr.add(obj.name);
        }
        ywyyChose?.setItemData(langListStr);
    }

    override fun queryResultByOrderId(bean: OrdersNotaryEntity?) {

    }

    override fun queryResultBySetState(bean: OrderInfoEntity?) {

    }

    override fun queryResultNotarList(bean: BaseResponseBean?) {
        Log.i(TAG, "queryResultNotarList")
        var NotarHomeList: List<NotarhomeBean> = bean?.parseList(NotarhomeBean::class.java) as List<NotarhomeBean>
        notarHomeList = NotarHomeList as ArrayList<NotarhomeBean>;
    }

    override fun addResultSuccess(bean: String?) {
        Log.i(TAG, "addResultSuccess")
        (mActivity as NotarizationSelfActivity?)?.orderId = bean;
        currentActivity?.onNavigationItemSelected(2)
    }

    override fun queryResultCountyList(bean: BaseResponseBean?) {
        Log.i(TAG, "queryResultCountyList")
        var countList: List<CountyBean> = bean?.parseList(CountyBean::class.java) as List<CountyBean>
        countyList = countList as ArrayList<CountyBean>;

        indexSelect = 1;
        countyListStr = ArrayList<String>();
        for (obj in countyList) {
            countyListStr.add(obj.zh);
        }
//            customSelectPicker?.show(list);
        addressChose.setItemData(countyListStr)
    }

    override fun onSelectItems(values: String?, index: Int) {
        Log.i(TAG, "onSelectItems$index---$values")

        if (indexSelect == 3) {
            gzcChose.text = values;
            curGzs = notarHomeList[index];
        }
    }

    override fun outCancel() {
    }


    override fun getMacAddress(): String {
        return MacUtils.getMacAddress(mActivity)
    }

    override fun showNotary(notaryId: String?, success: Boolean) {
    }

    override fun showNotaryList(bean: NotarizationBean?) {
        if (bean?.getItems() != null) {
            showDeviceDialog(bean?.getItems() as List<DeviceInfo>)
        } else {
            showDeviceDialog(listOf())
        }
    }

    override fun getAccessPublicKeySuccess() {
    }

    override fun getAccessPublicKeyFail(error: String?) {
    }

    override fun mechineAddSuccess() {
    }

    override fun mechineAddFail() {
    }

    private fun showDeviceDialog(l: List<DeviceInfo>) {
        if (deviceDialog == null) {
            deviceDialog = DialogUtils.getInstance().getRightDialog(mActivity, true, R.layout.dialog_divers)
            var dialogDeviceRv: RecyclerView? = deviceDialog?.findViewById(R.id.rvDevices)
            changelocation = deviceDialog?.findViewById(R.id.change_location)
            if (!TextUtils.isEmpty(city))
                changelocation?.setText(city)
            dialogDeviceRv?.adapter = deviceAdapter
        }
        deviceAdapter?.addNewData(l)
//        deviceDialog?.setCancelable(false)
//        deviceDialog?.setCanceledOnTouchOutside(false)
        deviceDialog?.show()
    }

    fun changeLocation() {
        if (null != deviceDialog && deviceDialog?.isShowing == true)
            deviceDialog?.dismiss()
        var pop = com.gc.notarizationpc.utils.PopwindowUtil()
        pop?.showpop(mActivity, mActivity.findViewById(R.id.self_notarization), mActivity)
    }

    private fun dismissDialog() {
        if (deviceDialog != null && deviceDialog?.isShowing == true) {
            deviceDialog?.dismiss()
        }
    }

    fun change_location_text(newcity: String?) {
        if (deviceDialog != null)
            deviceDialog?.show()
        //根据城市获取地区码和经纬度
        Thread(Runnable {
            try {
                var ll = MapUtils.getCoordinate(newcity)
                longitude = ll[0].toString()
                latitude = ll[1].toString()
                adcode = CommonUtil.getcode(mActivity, newcity)
                if (changelocation != null && !TextUtils.isEmpty(latitude) && !TextUtils.isEmpty(latitude)) {
                    //有网络 获取到经纬度的条件下 赋值  刷新
                    if (newcity != null) {
                        this.city = newcity
                    }
                    REFRESH_NOTARIZATION?.let { handler.sendEmptyMessage(it) }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }).start()

    }


    override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {

//        if (view.id == R.id.editSex) {
//            var sexPicker: CustomSelectPicker? = CustomSelectPicker(mActivity, object : CustomSelectPicker.Callback {
//                override fun onSelectItems(values: String?, index: Int) {
//                    (view as TextView).text = values
//                    if (values?.contains("男") == true) {
//                        applyList.get(position)?.gender = "1"
//                    } else {
//                        applyList.get(position)?.gender = "0"
//                    }
//
//                }
//
//                override fun outCancel() {
//
//                }
//            });
//            sexPicker?.show(sexList!!)
//        } else {
        dismissDialog()
        var device: DeviceInfo? = adapter.getItem(position) as DeviceInfo?
//        if (!"3".equals(device?.institutionType))
//            isConfirm = true
//        notaryId = device?.notarialId
        Log.d("zpzp", (mActivity as NotarizationSelfActivity?)?.notaryId + "  " +
                device?.unitGuid)
        gzcChose.text = device?.notarialName
        (mActivity as NotarizationSelfActivity?)?.notaryId = device?.unitGuid
        AccountManger.getInstance(mActivity)?.updateNotaryId(device?.unitGuid)

//        }

    }

    /**
     * 根据身份证号获取性别
     */
    fun getGenderFromIDCard(idCardNumber: String): String? {
        var gender = ""
        if (idCardNumber.length == 18) {
            try {
                // 获取身份证号的倒数第二位
                val genderDigit = idCardNumber.substring(idCardNumber.length - 2, idCardNumber.length - 1).toInt()
                // 判断奇偶性
                gender = if (genderDigit % 2 == 0) {
                    "0"
                } else {
                    "1"
                }
            } catch (exc:Exception) {
                gender = "0"
                exc.printStackTrace()
            }
        } else if (idCardNumber.length == 15) {
            try{
                // 获取身份证号的最后一位
                val genderDigit = idCardNumber.substring(idCardNumber.length - 1).toInt()
                // 判断奇偶性
                gender = if (genderDigit % 2 == 0) {
                    "0"
                } else {
                    "1"
                }
            }catch (exc:Exception){
                gender = "0"
                exc.printStackTrace()
            }
        } else {
            gender = "0"
        }
        return gender
    }

    fun getBirthDateFromIDCard(idCardNumber: String): String? {
        if (TextUtils.isEmpty(idCardNumber)) {
            return ""
        }
        val dateFormat = SimpleDateFormat("yyyyMMdd")
        val format = SimpleDateFormat("yyyy-MM-dd")
        var birthDateStr = ""
        if (idCardNumber.length == 18) {
            birthDateStr = idCardNumber.substring(6, 14)
        } else if (idCardNumber.length == 15) {
            birthDateStr = "19" + idCardNumber.substring(6, 12)
        }
        try {
            birthDateStr = format.format(dateFormat.parse(birthDateStr))
            return birthDateStr
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return ""
    }

    override fun onDestroy() {
        dismissDialog()
        if (handler != null)
            REFRESH_NOTARIZATION?.let { handler?.removeMessages(it) }
        super.onDestroy()
    }
}