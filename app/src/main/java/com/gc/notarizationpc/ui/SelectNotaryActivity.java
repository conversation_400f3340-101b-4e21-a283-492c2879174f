package com.gc.notarizationpc.ui;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.alibaba.fastjson.JSON;
import com.amap.api.location.AMapLocation;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivitySelectNotaryBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.bean.SerachBean;
import com.gc.notarizationpc.bean.SocketMessageInfo;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.data.model.response.MachineBindAreaResponse;
import com.gc.notarizationpc.data.model.response.NotarialOfficeListBean;
import com.gc.notarizationpc.ui.adapter.NotaryOfficeAdapter;
import com.gc.notarizationpc.ui.video.CounselingRoomActivity;
import com.gc.notarizationpc.ui.viewmodel.SelectNotaryViewModel;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.GpsUtil;
import com.gc.notarizationpc.util.MacUtils;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.websocket.MyMqttService;
import com.gc.notarizationpc.websocket.WebSocketCallBack;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.http.BaseResponse;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class SelectNotaryActivity extends BaseActivity<ActivitySelectNotaryBinding, SelectNotaryViewModel> implements WebSocketCallBack {
    private MyMqttService myMqttService = null;
    private static final String TAG = SelectNotaryActivity.class.getSimpleName();
    private String recordId;
    private NotaryOfficeAdapter notaryOfficeAdapter;
    private List<MachineBindAreaResponse.ProvinceDTO.GroupDTO> currentCitys;//当前可切换的城市列表
    private int sourceFrom;


    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_select_notary;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public SelectNotaryViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(SelectNotaryViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }

    @Override
    public void initData() {
        super.initData();
        sourceFrom = getIntent().getIntExtra("from", 0);
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });

        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });

        String mac = MacUtils.getMacAddress(this);
        if (!TextUtils.isEmpty(mac)) {
            viewModel.findIntegratedMachineBindArea();
        }
        //查询公证处公证员列表
        viewModel.requestNotary(AppConfig.macAddress, 1, "");
    }


    private void getNotaryByOffice(int position, NotarialOfficeListBean notarialOfficeListBean) {
        List<NotarialOfficeListBean.GroupVoDTO.OfficerListDTO> allNotary = new ArrayList<>();
        if (notarialOfficeListBean.getGroupVo() != null) {
            allNotary.addAll(notarialOfficeListBean.getGroupVo().getOfficerList());
//            allNotary.addAll(notarialOfficeListBean.getGroupVo().getOfficerOnDutyList());
            viewModel.requestNotaryOffice(position, allNotary, sourceFrom);
        } else {
            binding.rnlEmpty.setVisibility(View.VISIBLE);
        }
    }

    public void finishActivity(View view) {
        finish();
    }

    private String officeId;

    @Override
    public void initViewObservable() {
        viewModel.notaryListSingleLiveEvent.observe(this, new Observer<BaseResponse>() {
            @Override
            public void onChanged(BaseResponse response) {
                if (response != null && response.isSuccess) {
                    if (response.getResult() != null) {
                        List<NotarialOfficeListBean> notarialOfficeListBeans = (List<NotarialOfficeListBean>) response.getResult();
                        if (notarialOfficeListBeans.size() > 0) {
                            notaryOfficeAdapter = new NotaryOfficeAdapter(notarialOfficeListBeans, SelectNotaryActivity.this);
                            binding.listNotarialOffice.setAdapter(notaryOfficeAdapter);
                            notaryOfficeAdapter.setOnClick(new NotaryOfficeAdapter.Onclick() {
                                @Override
                                public void OnClicklistener(int position) {
                                    if (notarialOfficeListBeans.size() > 0 && notarialOfficeListBeans.get(position) != null) {
                                        binding.tvNotaryname.setText(notarialOfficeListBeans.get(position).getMechanismName());
                                        if (!TextUtils.isEmpty(notarialOfficeListBeans.get(position).getMechanismName())) {
                                            binding.tvNotaryname.setVisibility(View.VISIBLE);
                                        } else {
                                            binding.tvNotaryname.setVisibility(View.GONE);
                                        }
                                        viewModel.mCurrentNotaryOfficeId = notarialOfficeListBeans.get(position).getId();
                                        getNotaryByOffice(position, notarialOfficeListBeans.get(position));
                                    } else {
                                        binding.rnlEmpty.setVisibility(View.VISIBLE);
                                    }
                                }
                            });
                            notaryOfficeAdapter.notifyDataSetChanged();
                            binding.rnlEmpty.setVisibility(View.GONE);
                            binding.lnlContent.setVisibility(View.VISIBLE);
                            if (notarialOfficeListBeans.size() > 0 && notarialOfficeListBeans.get(0) != null && notarialOfficeListBeans.get(0).getGroupVo() != null) {
                                notarialOfficeListBeans.get(0).setChoose(true);
                                if (!TextUtils.isEmpty(notarialOfficeListBeans.get(0).getMechanismName())) {
                                    binding.tvNotaryname.setVisibility(View.VISIBLE);
                                } else {
                                    binding.tvNotaryname.setVisibility(View.GONE);
                                }
                                binding.tvNotaryname.setText(notarialOfficeListBeans.get(0).getMechanismName());
                                getNotaryByOffice(0, notarialOfficeListBeans.get(0));
                            }

                        } else {
                            binding.rnlEmpty.setVisibility(View.VISIBLE);
                            binding.lnlContent.setVisibility(View.GONE);
                        }

                    }

                } else {
                    binding.rnlEmpty.setVisibility(View.VISIBLE);
                    binding.lnlContent.setVisibility(View.GONE);
                    toastError(response != null ? response.getMessage() : R.string.no_find_notary_office_please_contact + "!");
                }

            }
        });

        viewModel.switchCityEvent.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean response) {
                if (response) {
                    List<SerachBean> serachBeans = new ArrayList<>();
                   if (currentCitys != null && currentCitys.size() > 0 ) {
                       for (MachineBindAreaResponse.ProvinceDTO.GroupDTO groupDTO : currentCitys) {
                           if (groupDTO != null) {
                               SerachBean tmp = new SerachBean();
                               tmp.setKey(groupDTO.getFirstChar());
                               List<SerachBean.Bean> beans = new ArrayList<>();
                               for (MachineBindAreaResponse.ProvinceDTO.GroupDTO.CityListDTO tmpCity : groupDTO.getCityList()) {
                                   SerachBean.Bean tmpBean = new SerachBean.Bean();
                                   tmpBean.setId(tmpCity.getCode());
                                   tmpBean.setName(tmpCity.getName());
                                   beans.add(tmpBean);
                               }
                               tmp.setBean(beans);
                               serachBeans.add(tmp);
                           }

                       }
                       PopwindowUtil.showGroup(serachBeans, SelectNotaryActivity.this, 1, new PopwindowUtil.ResultListener() {
                           @Override
                           public void result(Object value) {
                               //城市切换后，需要刷新公证处公证员列表
                               List<SerachBean.Bean> chooseCity = (List<SerachBean.Bean>) value;
                               if (chooseCity != null && chooseCity.size() > 0) {
                                   binding.tvLocation.setText(chooseCity.get(0).getName());
                                   viewModel.mCity.setName(chooseCity.get(0).getName());
                                   viewModel.mCity.setCode(chooseCity.get(0).getId());
                                   viewModel.requestNotary(AppConfig.macAddress, 1, "");
                               }
                           }
                       });
                   } else {
                       toastError(getString(R.string.no_find_notary_office_please_contact));
                   }
                }

            }
        });


        viewModel.machineBindAreaResponseSingleLiveEvent.observe(this, new Observer<MachineBindAreaResponse>() {
            @Override
            public void onChanged(MachineBindAreaResponse result) {
                if (result.getProvince() == null) {
                    return;
                }
                try {

                    if (result.getProvince().getGroup() != null && result.getProvince().getGroup().size() > 0 && result.getProvince().getGroup().get(0).getCityList() != null
                            && result.getProvince().getGroup().get(0).getCityList().size() > 0) {
                        currentCitys = result.getProvince().getGroup();
                        //至少有一个城市
                        if (result.getProvince().getGroup().get(0) != null && result.getProvince().getGroup().get(0).getCityList().size() == 1) {
                            binding.tvLocation.setText(result.getProvince().getName() + result.getProvince().getGroup().get(0).getCityList().get(0).getName());
                            binding.tvLocation.setClickable(false);
                        } else {
                            //多个城市判断，当前机器所在地址的
                            GpsUtil.getlocation(SelectNotaryActivity.this, new GpsUtil.MapGPS() {
                                @Override
                                public void success(AMapLocation aMapLocation) {
                                    if (aMapLocation != null) {
                                        //如果机器再所在城市里面，则展示aMapLocation.getCity()，反之则随便展示第一个绑定机器的城市;点击可以切换

                                        if (new Gson().toJson(result.getProvince().getGroup()).contains(aMapLocation.getCity())) {
                                            binding.tvLocation.setText(aMapLocation.getProvince() + aMapLocation.getCity());
                                            for (MachineBindAreaResponse.ProvinceDTO.GroupDTO groupDTO : result.getProvince().getGroup()) {
                                                if (groupDTO != null) {
                                                    for (MachineBindAreaResponse.ProvinceDTO.GroupDTO.CityListDTO cityListDTO : groupDTO.getCityList()) {
                                                        if (cityListDTO != null && !TextUtils.isEmpty(cityListDTO.getName()) && cityListDTO.getName().contains(aMapLocation.getCity())) {
                                                            viewModel.mCity = cityListDTO;
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            binding.tvLocation.setText(result.getProvince() + result.getProvince().getGroup().get(0).getCityList().get(0).getName());
                                        }
                                    }

                                }

                                @Override
                                public void failed() {
                                    Log.e("Test", "定位失败");
                                    binding.tvLocation.setText(result.getProvince() + result.getProvince().getGroup().get(0).getCityList().get(0).getName());
                                }
                            });
                            binding.tvLocation.setClickable(true);
                        }
                    } else {
                        binding.tvLocation.setClickable(false);
                        //如果该一体机一个城市都没有绑定呢？
//                    if (result.getProvince().getGroup().get(0).getCityList() != null && result.getProvince().getGroup().get(0).getCityList().size() > 0) {
//                        binding.tvLocation.setText(result.getProvince().getGroup().get(0).getCityList().get(0).getName());
//                    }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });


        viewModel.lineNotarySuccess.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean response) {
                if (response) {
//                    toastSuccess("连线成功");
                    try {
                        if (sourceFrom == CommonUtil.SOURCE_FROM_SPBZ) {
                            myMqttService = new MyMqttService(SelectNotaryActivity.this, "/videoConnectingRecords/" + viewModel.recordId,
                                    "/videoConnectingRecords/" + viewModel.recordId, AppConfig.HOST, SelectNotaryActivity.this);
                        } else if (sourceFrom == CommonUtil.SOURCE_FROM_SPLX) {
                            myMqttService = new MyMqttService(SelectNotaryActivity.this, "/consultingRecords/" + viewModel.recordId,
                                    "/consultingRecords/" + viewModel.recordId, AppConfig.HOST, SelectNotaryActivity.this);
                        }
                    } catch (Exception e) {
                        Log.e("Test", e.getMessage());
                    }

                    myMqttService.start();
                } else {
                    Log.e("Test", "连线失败");
                }


            }
        });


        viewModel.showConnectingEvent.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String officeId) {

                PopwindowUtil.showConnectionVideoAlert(SelectNotaryActivity.this, new PopwindowUtil.ButtonClickListener() {
                    @Override
                    public void cancel() {
                        //20s倒计时结束
                        toastError(getString(R.string.notary_is_busing));
                        PopwindowUtil.dismissVideoConnectingDialog();
                    }

                    @Override
                    public void decide() {
                        //用户手动点击结束等待
                        viewModel.cancelConnectOffice(officeId,sourceFrom);

                    }
                });

            }
        });


        viewModel.dismissConnectingEvent.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String officeId) {
                PopwindowUtil.dismissVideoConnectingDialog();

            }
        });

    }

    private String roomId, orderId, notaryName, mechanismName;

    @Override
    public void onSocketMessage(@Nullable String next) {
        if (next.startsWith("{") && next.endsWith("}")) {
            SocketMessageInfo info = JSON.parseObject(next, SocketMessageInfo.class);
            //停止等待 进入受理室
            if ("stopWaiting".equals(info.getCode())) {
                if (!TextUtils.isEmpty(info.getRoomId())) {
                    roomId = info.getRoomId();
                    orderId = info.getOrderId();
                    notaryName = info.getNotaryName();
                    mechanismName = info.getMechanismName();
                    myMqttService.unSubscribe();
                    PopwindowUtil.dismissVideoConnectingDialog();
                    Intent intent = new Intent(this, CounselingRoomActivity.class);
                    intent.putExtra("roomId", roomId);
                    intent.putExtra("orderId", orderId);
                    intent.putExtra("mechanismName", mechanismName);
                    intent.putExtra("notaryName", notaryName);
                    intent.putExtra("caseInfoId", "");
                    intent.putExtra("from", sourceFrom);
                    intent.putExtra("recordId", viewModel.recordId);
                    intent.putExtra("notaryId", info.getNotaryId());
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    startActivity(intent);
//                    finish();
                } else {
                    PopwindowUtil.dismissVideoConnectingDialog();
                    Log.i(TAG, "onSocketMessage ==,消息体错误，不包含 公证员名称或房间号 ," + next);
                    toastError(getString(R.string.notReceivedRoomNumber));
                }
            } else if ("exit_room".equals(info.getCode())) {
                toastError(getString(R.string.notary_is_busing));
                myMqttService.unSubscribe();
                PopwindowUtil.dismissVideoConnectingDialog();
            }
        }
    }


}
