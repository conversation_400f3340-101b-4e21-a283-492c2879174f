package com.gc.notarizationpc.ui.common;

import android.os.Bundle;
import android.view.View;

import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityOnLineRuleBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.ReservationActivity;
import com.gc.notarizationpc.ui.viewmodel.OnLineRulesViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.umeng.analytics.MobclickAgent;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;

import me.goldze.mvvmhabit.base.BaseActivity;

/**
 * 在线受理服务使用规则
 */
public class OnLineRulesActivity extends BaseActivity<ActivityOnLineRuleBinding, OnLineRulesViewModel> {

    //ActivityLoginBinding类是databinding框架自定生成的,对应activity_login.xml

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_on_line_rule;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public OnLineRulesViewModel initViewModel() {
//        使用自定义的ViewModelFactory来创建ViewModel，如果不重写该方法，则默认会调用LoginViewModel(@NonNull Application application)构造方法
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(OnLineRulesViewModel.class);
    }

    /**
     * 从raw中读取txt
     */
    private void readFromRaw() {
        try {
            InputStream is = getResources().openRawResource(R.raw.protocol);
            String text = readTextFromRaw(is);
            binding.onLineRuleTextView.setText(text);
        } catch (Exception e) {
            e.printStackTrace();
            MobclickAgent.reportError(OnLineRulesActivity.this, e);
        }
    }

    /**
     * 按行读取txt
     *
     * @param is
     * @return
     * @throws Exception
     */
    private String readTextFromRaw(InputStream is) throws Exception {
        InputStreamReader reader = new InputStreamReader(is);
        BufferedReader bufferedReader = new BufferedReader(reader);
        StringBuffer buffer = new StringBuffer("");
        String str;
        while ((str = bufferedReader.readLine()) != null) {
            buffer.append(str);
            buffer.append("\n");
        }
        return buffer.toString();
    }

    @Override
    protected void onStart() {
        super.onStart();
        //设置状态栏深浅模式false为浅色
        changeStatusBarLight(true);
    }

    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });
        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });

        readFromRaw();
    }

    @Override
    public void initViewObservable() {

//        viewModel.loginInfoSingleLiveEvent.observe(this, new Observer<LoginInfo>() {
//            @Override
//            public void onChanged(LoginInfo loginInfo) {
//                Toast.makeText(HomeActivity.this, "登录成功", Toast.LENGTH_SHORT).show();
//            }
//        });
//
//        viewModel.errorStr.observe(this, new Observer<String>() {
//            @Override
//            public void onChanged(String s) {
//                Toast.makeText(HomeActivity.this, "登录失败" + s, Toast.LENGTH_SHORT).show();
//            }
//        });

    }

    public void getVersion(View view) {

    }


}
