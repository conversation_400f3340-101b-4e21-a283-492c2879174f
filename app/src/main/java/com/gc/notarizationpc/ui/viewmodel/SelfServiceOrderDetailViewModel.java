package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.NotaryPurposeModel;
import com.gc.notarizationpc.data.model.response.SelfServiceOrderDetailModel;

import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * 首页快捷入口 viewmodel
 */

public class SelfServiceOrderDetailViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    public class UIChangeObservable {

    }

    public SelfServiceOrderDetailViewModel(@NonNull Application application) {
        super(application);
    }

    public ObservableField<String> orderStatus = new ObservableField<>();

    public ObservableField<String> receiveWay = new ObservableField<>();

    // 底部状态按钮显示和隐藏
    public Boolean bottomStatusVisibility = false;

    public ObservableField<SelfServiceOrderDetailModel> selfServiceOrderDetailModel = new ObservableField<>();

    // 阅读文书
    public SingleLiveEvent<Boolean> readDocumentEvent = new SingleLiveEvent<>();

    // 补充材料
    public SingleLiveEvent<Boolean> applyMaterialEvent = new SingleLiveEvent<>();

    // 接口请求结果界面
    public SingleLiveEvent<SelfServiceOrderDetailModel> networkResult = new SingleLiveEvent<>();

    // 进入受理室
    public SingleLiveEvent<Boolean> enterAcceptanceRoomEvent = new SingleLiveEvent<>();

    // 终止案件
    public SingleLiveEvent<Boolean> stopCaseEvent = new SingleLiveEvent<>();

    // 公证用途数据
    public List<NotaryPurposeModel> notaryMattersList = new ArrayList<NotaryPurposeModel>();

    // 公证用途
    public String notaryPurpose = "";

    // 终止案件
    public SingleLiveEvent<Boolean> getStopCaseEvent = new SingleLiveEvent<>();

    // 文书预览
    public SingleLiveEvent<List<String>> documentPreViewEvent = new SingleLiveEvent<>();
    // 单位信息展示
    public SingleLiveEvent<Boolean> unitInformationVisibilityEvent = new SingleLiveEvent<>();
    // 费用显示
    public SingleLiveEvent<Boolean> feeVisibilityEvent = new SingleLiveEvent<>();
    // 自然人信息
    public ObservableList<SelfServiceOrderDetailNaturalInformationItemViewModel> observableNaturalInformationList = new ObservableArrayList<>();

    // 单位信息
    public ObservableList<SelfServiceOrderDetailUnitInformationItemViewModel> observableUnitInformationList = new ObservableArrayList<>();

    // 费用信息
    public ObservableList<SelfServiceOrderDetailFeeInformationItemViewModel> observableFeeInformationList = new ObservableArrayList<>();

    // 材料图片信息
    public ObservableList<SelfServiceOrderDetailImageItemViewModel> observableImageList = new ObservableArrayList<>();

    // 文书图片信息
    public ObservableList<SelfServiceOrderDetailDocumentItemViewModel> observableDocumentList = new ObservableArrayList<>();

    public ItemBinding<SelfServiceOrderDetailNaturalInformationItemViewModel> naturalInformationItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_self_service_order_detail_natural_information);

    public ItemBinding<SelfServiceOrderDetailUnitInformationItemViewModel> unitInformationItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_self_service_order_detail_unit_information);

    public ItemBinding<SelfServiceOrderDetailFeeInformationItemViewModel> feeInformationItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_self_service_order_detail_fee_information);

    public ItemBinding<SelfServiceOrderDetailImageItemViewModel> imageItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_self_service_order_detail_image);

    public ItemBinding<SelfServiceOrderDetailDocumentItemViewModel> documentsItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_self_service_order_detail_document);

    /**
     * 网络请求方法，在ViewModel中调用Model层，通过Okhttp+Retrofit+RxJava发起请求
     */
    public void requestNetWork(String orderId) {
        showDialog(Utils.getContext().getString(R.string.loading));
        RequestUtil.getSelfServiceOrderDetail(orderId, new MyObserver<SelfServiceOrderDetailModel>() {
            @Override
            public void onSuccess(SelfServiceOrderDetailModel response) {
                dismissDialog();
                observableNaturalInformationList.clear();
                observableUnitInformationList.clear();
                observableFeeInformationList.clear();
                observableDocumentList.clear();
                observableImageList.clear();
                selfServiceOrderDetailModel.set(response);
                networkResult.setValue(response);
                if (selfServiceOrderDetailModel.get().getPaymentListVO() == null || selfServiceOrderDetailModel.get().getPaymentListVO() != null && selfServiceOrderDetailModel.get().getPaymentListVO().getHadSetBilling() != 1) {
                    feeVisibilityEvent.setValue(false);
                } else {
                    feeVisibilityEvent.setValue(true);
                }
                if (selfServiceOrderDetailModel.get().getRecordCorporatonApplicantVos() != null && selfServiceOrderDetailModel.get().getRecordCorporatonApplicantVos().size() > 0) {
                    unitInformationVisibilityEvent.setValue(true);
                } else {
                    unitInformationVisibilityEvent.setValue(false);
                }
                if (selfServiceOrderDetailModel.get().getReceiveWay() == null) {
                    receiveWay.set("自取");
                } else {
                    if (selfServiceOrderDetailModel.get().getReceiveWay() == 2) {
                        receiveWay.set("邮寄（到付）");
                    } else {
                        receiveWay.set("自取");
                    }
                }
//                for (NotaryPurposeModel model :
//                        notaryMattersList) {
//                    if (videoOrderDetailModel.get().getUsedPurpose() == model.getMechanismId().toString()){
//                        notaryPurpose = model.getDescription();
//                    }
//                }
                notaryPurpose = "videoOrderDetailModel.get().getUsedPurpose()";

                if (response.getRecordApplicantVos() != null && !response.getRecordApplicantVos().isEmpty()) {
                    for (int i = 0; i < response.getRecordApplicantVos().size(); i++) {
                        SelfServiceOrderDetailModel.RecordApplicantVosDTO recordApplicantVosDTO = response.getRecordApplicantVos().get(i);
                        SelfServiceOrderDetailNaturalInformationItemViewModel itemViewModel = new SelfServiceOrderDetailNaturalInformationItemViewModel(SelfServiceOrderDetailViewModel.this, recordApplicantVosDTO);
                        itemViewModel.personType = "申请人";
                        itemViewModel.sexString = recordApplicantVosDTO.getGender() == null ? "" : recordApplicantVosDTO.getGender() == 1 ? "男" : "女";
                        observableNaturalInformationList.add(itemViewModel);
                    }
                }

                if (response.getRecordApplicantAgentVos() != null && !response.getRecordApplicantAgentVos().isEmpty()) {
                    for (int i = 0; i < response.getRecordApplicantAgentVos().size(); i++) {
                        SelfServiceOrderDetailModel.RecordApplicantAgentVosDTO recordApplicantAgentVosDTO = response.getRecordApplicantAgentVos().get(i);
                        SelfServiceOrderDetailModel.RecordApplicantVosDTO recordApplicantVosDTO = new SelfServiceOrderDetailModel.RecordApplicantVosDTO();
                        recordApplicantVosDTO.setAddress(recordApplicantAgentVosDTO.getAddress());
                        recordApplicantVosDTO.setApplicantName(recordApplicantAgentVosDTO.getAgentName());
                        recordApplicantVosDTO.setBirthday(recordApplicantAgentVosDTO.getBirthday());
                        recordApplicantVosDTO.setContactNum(recordApplicantAgentVosDTO.getContactNum());
                        recordApplicantVosDTO.setCredentialNum(recordApplicantAgentVosDTO.getCredentialNum());
                        recordApplicantVosDTO.setCredentialType(recordApplicantAgentVosDTO.getCredentialType() == null ? 0 : Integer.valueOf(recordApplicantAgentVosDTO.getCredentialType()));
                        recordApplicantVosDTO.setCredentialTypeStr(recordApplicantAgentVosDTO.getCredentialTypeStr());
                        recordApplicantVosDTO.setGender(recordApplicantAgentVosDTO.getGender());
                        recordApplicantVosDTO.setId(recordApplicantAgentVosDTO.getId());
                        SelfServiceOrderDetailNaturalInformationItemViewModel itemViewModel = new SelfServiceOrderDetailNaturalInformationItemViewModel(SelfServiceOrderDetailViewModel.this, recordApplicantVosDTO);
                        itemViewModel.personType = "代理人";
                        itemViewModel.sexString = recordApplicantVosDTO.getGender() == null ? "" : recordApplicantVosDTO.getGender() == 1 ? "男" : "女";
                        observableNaturalInformationList.add(itemViewModel);
                    }
                }

                if (response.getRecordCorporatonApplicantVos() != null && !response.getRecordCorporatonApplicantVos().isEmpty()) {
                    for (int i = 0; i < response.getRecordCorporatonApplicantVos().size(); i++) {
                        SelfServiceOrderDetailModel.RecordCorporatonApplicantVosDTO recordCorporatonApplicantVosDTO = response.getRecordCorporatonApplicantVos().get(i);
                        SelfServiceOrderDetailUnitInformationItemViewModel itemViewModel1 = new SelfServiceOrderDetailUnitInformationItemViewModel(SelfServiceOrderDetailViewModel.this, recordCorporatonApplicantVosDTO);
                        observableUnitInformationList.add(itemViewModel1);

                    }
                }

                if (response.getPaymentListVO() != null && response.getPaymentListVO().getMattersFeeList() != null && !response.getPaymentListVO().getMattersFeeList().isEmpty()) {
                    for (int i = 0; i < response.getPaymentListVO().getMattersFeeList().size(); i++) {
                        SelfServiceOrderDetailModel.PaymentListVODTO.MattersFeeListDTO mattersFeeListDTO = response.getPaymentListVO().getMattersFeeList().get(i);
                        SelfServiceOrderDetailFeeInformationItemViewModel itemViewModel2 = new SelfServiceOrderDetailFeeInformationItemViewModel(SelfServiceOrderDetailViewModel.this, mattersFeeListDTO);
                        observableFeeInformationList.add(itemViewModel2);

                    }
                }

                if (response.getMaterialVoList() != null && !response.getMaterialVoList().isEmpty()) {
                    for (int i = 0; i < response.getMaterialVoList().size(); i++) {
                        SelfServiceOrderDetailModel.MaterialVoListDTO materialVoListDTO = response.getMaterialVoList().get(i);
                        SelfServiceOrderDetailImageItemViewModel itemViewModel3 = new SelfServiceOrderDetailImageItemViewModel(SelfServiceOrderDetailViewModel.this, materialVoListDTO);
                        observableImageList.add(itemViewModel3);

                    }
                }

                if (response.getDocTypeList() != null && !response.getDocTypeList().isEmpty()) {
                    for (int i = 0; i < response.getDocTypeList().size(); i++) {
                        SelfServiceOrderDetailModel.DocTypeListDTO docTypeListDTO = response.getDocTypeList().get(i);
                        SelfServiceOrderDetailDocumentItemViewModel itemViewModel4 = new SelfServiceOrderDetailDocumentItemViewModel(SelfServiceOrderDetailViewModel.this, docTypeListDTO);
                        if (docTypeListDTO.getDocumentVOList().get(0).getSignStatus() > 0) {
                            itemViewModel4.documentPath = docTypeListDTO.getDocumentVOList().get(0).getSignDocumentFileUrl().get(0);
                        } else {
                            itemViewModel4.documentPath = docTypeListDTO.getDocumentVOList().get(0).getUnsignDocumentFileUrl().get(0);
                        }
                        observableDocumentList.add(itemViewModel4);

                    }
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.failedToGetOrderDetail) : errorMsg);
            }

        });

    }

    // 终止案件
    public void stopCase(String caseInfoId) {
        RequestUtil.endApply(caseInfoId, new MyObserver() {
            @Override
            public void onSuccess(Object response) {
                getStopCaseEvent.setValue(true);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.failureToTerminateCase) : errorMsg);
            }
        });
    }


    //阅读文书
    public BindingCommand readDocuments = new BindingCommand(() -> {
        //请求网络数据
        readDocumentEvent.setValue(true);
    });

    // 补充材料
    public BindingCommand applyMaterial = new BindingCommand(() -> {
        //请求网络数据
        applyMaterialEvent.setValue(true);
    });

    // 进入受理室
    public BindingCommand enterAcceptanceRoom = new BindingCommand(() -> {
        //请求网络数据
        enterAcceptanceRoomEvent.setValue(true);
    });

    // 终止案件
    public BindingCommand stopCase = new BindingCommand(() -> {
        //请求网络数据
        stopCaseEvent.setValue(true);
    });

    // 返回
    public BindingCommand back = new BindingCommand(() -> {
        //请求网络数据
        finish();
    });

    // 图片预览
    public void imagePreView(SelfServiceOrderDetailModel.MaterialVoListDTO entity) {
        List<String> filePageList = new ArrayList<>();
        if (entity.getFileUrl() != null && !entity.getFileUrl().isEmpty()) {
            filePageList.add(entity.getFileUrl());
            documentPreViewEvent.setValue(filePageList);
        }
    }

    //文书预览
    public void documentPreView(SelfServiceOrderDetailModel.DocTypeListDTO entity) {
        List<String> filePageList = new ArrayList<>();
        for (int i = 0; i < entity.getDocumentVOList().size(); i++) {
            if (entity.getDocumentVOList().get(i).getSignStatus() > 0) {
                filePageList.addAll(entity.getDocumentVOList().get(i).getSignDocumentFileUrl());
            } else {
                filePageList.addAll(entity.getDocumentVOList().get(i).getUnsignDocumentFileUrl());
            }
        }

        documentPreViewEvent.setValue(filePageList);

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
