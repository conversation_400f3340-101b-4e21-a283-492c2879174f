package com.gc.notarizationpc.ui.presenter;

import android.content.Context;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.example.framwork.mvp.BasePresenter;
import com.example.framwork.mvp.EntityType;
import com.example.framwork.noHttp.Bean.BaseResponseBean;
import com.example.framwork.noHttp.OnInterfaceRespListener;
import com.example.framwork.utils.CommonUtil;
import com.example.framwork.utils.SPUtils;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.common.BaseRequestInfo;
import com.gc.notarizationpc.common.ResponseBean;
import com.gc.notarizationpc.common.ResponseCommonBean;
import com.gc.notarizationpc.model.AliPayInfo;
import com.gc.notarizationpc.model.NotaryEntity;
import com.gc.notarizationpc.model.NotrayBean;
import com.gc.notarizationpc.model.OrderInfoEntity;
import com.gc.notarizationpc.model.OrdersNotaryEntity;
import com.gc.notarizationpc.model.PayBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


public class NotarizaSelfPresenter extends BasePresenter {

    private INotarizaSelfView selfView;
    private INotarizaSelfSecond selfSecond;
    private INotarizaSelf notarizaSelf;
    private String TAG = "NotarizaSelfPresenter";
    private OrderQueryCommonView orderQueryCommonView;
    public NotarizaSelfPresenter(Context context, Class clazz, EntityType type, INotarizaSelfView view) {
        super(context, clazz, type);
        this.selfView = view;
    }

    //解析参考这个来 xhf
    public NotarizaSelfPresenter(Context context, OrderQueryCommonView orderCCBisView) {
        super(context, PayBean.class, EntityType.ENTITY);
        this.orderQueryCommonView = orderCCBisView;
    }

    public NotarizaSelfPresenter(Context context, INotarizaSelf view) {
        super(context, NotaryEntity.class, EntityType.ENTITY);
        this.notarizaSelf = view;
    }

    public NotarizaSelfPresenter(Context context, INotarizaSelfView view, boolean ObjectBean) {
        super(context);
        this.selfView = view;
    }

    public NotarizaSelfPresenter(Context context, INotarizaSelfView view, int type) {
        super(context, OrdersNotaryEntity.class, EntityType.ENTITY);
        this.selfView = view;
    }

    public NotarizaSelfPresenter(Context context, INotarizaSelfView view, String order) {
        super(context, OrderInfoEntity.class, EntityType.ENTITY);
        this.selfView = view;
    }

    public NotarizaSelfPresenter(Context context, INotarizaSelfSecond view, String oorderrder) {
        super(context, OrderInfoEntity.class, EntityType.ENTITY);
        this.selfSecond = view;
    }

    public NotarizaSelfPresenter(Context context, INotarizaSelfSecond view, AliPayInfo oorderrder) {
        super(context, AliPayInfo.class, EntityType.ENTITY);
        this.selfSecond = view;
    }

    public NotarizaSelfPresenter(Context context, INotarizaSelfSecond view, OrderInfoEntity order) {
        super(context, OrderInfoEntity.class, EntityType.ENTITY);
        this.selfSecond = view;
    }

    public NotarizaSelfPresenter(Context context, INotarizaSelfView view) {
        super(context);
        this.selfView = view;
    }

    public NotarizaSelfPresenter(Context context, INotarizaSelfSecond view) {
        super(context);
        this.selfSecond = view;
    }

    public interface INotarizaSelf {
        void queryNotaryEntitySuccess(NotaryEntity bean);


        void getPayConfigViewSuccess(String msg);

        void getPayConfigViewFail(String msg);
    }

    public interface INotarizaSelfView {
        void sendNotarizationSuccess(List<NotrayBean> items);

        void queryResultLangeuList(BaseResponseBean bean);

        void queryResultCountyList(BaseResponseBean bean);

        void queryResultNotarList(BaseResponseBean bean);

        void addResultSuccess(String orderId);

        void queryResultByOrderId(OrdersNotaryEntity bean);

        void queryResultBySetState(OrderInfoEntity bean);


    }

    public interface INotarizaSelfSecond {
        void queryResultselectNotaryItem(BaseResponseBean bean);

        void queryWxpayGetpay(ResponseBean bean);

        void addMaterialSuccess(List<String> arrayList);

        void addNotaryDoSetSignName(OrderInfoEntity bean);

        void addalipaySetQrCode(AliPayInfo bean);

        void addalipaySetQrCodeReslut(AliPayInfo bean);

        void queryWxpayGetpayReslut(AliPayInfo bean);

        void queryResultBySetStateSecond(OrderInfoEntity bean, String notaryState);

        void saveFaceReportSuccess(BaseResponseBean bean);

        void saveFaceReportFail(String error);
    }

    public interface DosetStatus {
        void queryResultBySetStateSecond(OrderInfoEntity bean, String notaryState);

        void failed(String msg);
    }


    //获取确认信息接口：cgz/notaryorder/getByOrderId
    public void addNotaryGetByOrderId(String orderId) {
        Log.i(TAG, "addNotaryGetByOrderId");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/notaryorder/getByOrderId");
        requestInfo.put("unitGuid", orderId);
        post("确认中....", new OnInterfaceRespListener<OrdersNotaryEntity>() {
            @Override
            public void requestSuccess(OrdersNotaryEntity bean) {
                Log.i(TAG, "addNotaryGetByOrderId success");
                selfView.queryResultByOrderId(bean);
            }
        });
    }

    //确认信息提交接口:cgz/notaryorder/doSetState
    public void addNotaryDoSetState(String orderId, String takeMobile, String takeAddress, String takeStyle, int num) {
        Log.i(TAG, "addNotaryDoSetState");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/notaryorder/doSetState");
        requestInfo.put("unitGuid", orderId);
        requestInfo.put("notaryState", "101");
        requestInfo.put("takeMobile", takeMobile);
        requestInfo.put("takeAddress", takeAddress);
        requestInfo.put("takeStyle", takeStyle);
        requestInfo.put("notaryNum", num);
        post("提交中...", new OnInterfaceRespListener<OrderInfoEntity>() {
            @Override
            public void requestSuccess(OrderInfoEntity bean) {
                Log.i(TAG, "addNotaryDoSetState success");
                selfView.queryResultBySetState(bean);
            }
        });
    }

    public void getPayConfig(String notryId, INotarizaSelf view) {
        Log.i(TAG, "getPayConfig");
        String url = AppConfig.SERVICE_PATH + AppConfig.PAY + "/zrzf/getPayConfig?notaryId=" + notryId;
        get(url, CommonUtil.TOKEN, new OnInterfaceRespListener<Object>() {
            @Override
            public void requestSuccess(Object bean) {
                Log.i(TAG, "queyCcbisOrderInfo  success");
                try {
                    ResponseCommonBean beans = com.alibaba.fastjson.JSONObject.parseObject(bean.toString(), ResponseCommonBean.class);
                    if (beans.getCode() == 200) {
                        view.getPayConfigViewSuccess(beans.getCode() + "");
                    } else {
                        view.getPayConfigViewFail("获取支付配置信息失败");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    view.getPayConfigViewFail(e.getMessage());
                }
            }

            @Override
            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                super.requestFailed(errorCode, bean, exception, error);
                view.getPayConfigViewFail(error);

            }
        });
    }


    //根据公证事项获取材料列表接口：cgz/notaryitemmodel/selectNotaryItem
    public void selectNotaryItem(String orderId) {
        Log.i(TAG, "selectNotaryItem");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/notaryitemmodel/selectNotaryItem");
        requestInfo.put("unitGuid", orderId);
        postFrom("查询中...", new OnInterfaceRespListener<ResponseBean>() {
            @Override
            public void requestSuccess(ResponseBean bean) {
                Log.i(TAG, "selectNotaryItem success");
                selfSecond.queryResultselectNotaryItem(bean);
            }
        });
    }

    //新增材料（下一步）接口（会返回公证文书，需要展示）：cgz/material/add
//                {
//                "items":[
//                {
//                    "annexFileLists":[""],
//                    "orderId":"",
//                    "notaryItemId":"",
//                    "materialName":""
//                }
//                ],
//                "orderId":""
//            }
    public void addMaterial(ArrayList<HashMap<String, Object>> items, String orderId) {
        Log.i(TAG, "addMaterial");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/material/add");
        requestInfo.put("items", items);
        requestInfo.put("orderId", orderId);
        post("提交中...", new OnInterfaceRespListener<ResponseBean>() {
            @Override
            public void requestSuccess(ResponseBean bean) {
                Log.i(TAG, "addMaterial success");
                selfSecond.addMaterialSuccess(bean.getPdfImgPaths());
            }
        });
    }

    //签完字确认接口：cgz/notaryorder/doSetSignName
//    "unitGuid": "订单主键",
//            "signName": "签名图片地址",
//            "encDataFilePath":"CA签名加密包",
//            "terminalType":"终端分类：1 移动端，2 大一体机，3 Pad（小一体机）"
    public void addNotaryDoSetSignName(String unitGuid, String signName, String encDataFilePath) {
        Log.i(TAG, "addNotaryDoSetSignName");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/notaryorder/doSetSignName");
        requestInfo.put("unitGuid", unitGuid);
        requestInfo.put("signName", signName);
        JSONObject jsonObject = JSONObject.parseObject(encDataFilePath);
        requestInfo.put("encDataFilePath", jsonObject);
        requestInfo.put("terminalType", "3");
        post("确认中...", new OnInterfaceRespListener<OrderInfoEntity>() {
            @Override
            public void requestSuccess(OrderInfoEntity bean) {
                Log.i(TAG, "addNotaryDoSetSignName success");
                selfSecond.addNotaryDoSetSignName(bean);
            }

            @Override
            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                super.requestFailed(errorCode, bean, exception, error);
                Log.i(TAG, "addNotaryDoSetSignName Failed" + errorCode + error);
            }
        });
    }

    public void addNotaryDoSetSignName(String unitGuid, String signName, String IdCard, String FilePath) {
        Log.i(TAG, "addNotaryDoSetSignName");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/notaryorder/doSetSignNameJSCA");
        requestInfo.put("unitGuid", unitGuid);
        requestInfo.put("userName", signName);
        requestInfo.put("idCard", IdCard);
        requestInfo.put("signPictureUrl", FilePath);
        requestInfo.put("terminalType", "3");
        post("确认中...", new OnInterfaceRespListener<Object>() {
            @Override
            public void requestSuccess(Object bean) {
                Log.i(TAG, "addNotaryDoSetSignName success");
                try {
                    org.json.JSONObject jsonObject = new org.json.JSONObject(bean.toString());
                    OrderInfoEntity orderInfoEntity = new OrderInfoEntity();
                    orderInfoEntity.code = jsonObject.get("code") + "";
                    selfSecond.addNotaryDoSetSignName(orderInfoEntity);
                } catch (Exception e) {
                    e.printStackTrace();
                    selfSecond.addNotaryDoSetSignName(null);
                }


            }

            @Override
            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                super.requestFailed(errorCode, bean, exception, error);
                Log.i(TAG, "addNotaryDoSetSignName Failed" + errorCode + error);
                selfSecond.addNotaryDoSetSignName(null);
            }
        });
    }

    //获取支付宝支付链接接口（获取完链接生成二维码）：alipay/setQrCode
//    {"outTradeNo":"202101291548201867"}
    public void alipaySetQrCode(String outTradeNo) {
        Log.i(TAG, "alipaySetQrCode");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "alipay/setQrCode");
        requestInfo.put("outTradeNo", outTradeNo);
        post("生成二维码", new OnInterfaceRespListener<AliPayInfo>() {
            @Override
            public void requestSuccess(AliPayInfo bean) {
                Log.i(TAG, "alipaySetQrCode success");
                selfSecond.addalipaySetQrCode(bean);
            }
        });
    }

    //获取支付宝支付链接接口（获取完链接生成二维码）：alipay/setQrCode
//    {"outTradeNo":"202101291548201867"}
    public void alipayQuery(String outTradeNo) {
        Log.i(TAG, "alipayQuery");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "alipay/aipayQuery");
        requestInfo.put("outTradeNo", outTradeNo);
        postNoLoad(new OnInterfaceRespListener<AliPayInfo>() {
            @Override
            public void requestSuccess(AliPayInfo bean) {
                Log.i(TAG, "alipayQuery success");
                selfSecond.addalipaySetQrCodeReslut(bean);
            }
        }, false);
    }

    //获取微信支付链接接口（获取完链接生成二维码）：/cgz/wxpay/getpay
//    {"outTradeNo":"202101291548201867"}
    public void wxpayGetpay(String outTradeNo) {
        Log.i(TAG, "wxpayGetpay");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/wxpay/getpay");
        requestInfo.put("outTradeNo", outTradeNo);
        postFrom("生成二维码", new OnInterfaceRespListener<ResponseBean>() {
            @Override
            public void requestSuccess(ResponseBean bean) {
                Log.i(TAG, "wxpayGetpay success");
                selfSecond.queryWxpayGetpay(bean);
            }
        });
    }

    //获取微信支付链接接口（获取完链接生成二维码）：cgz/wxpay/orderQuery
//    {"outTradeNo":"202101291548201867"}
    public void wxpayOrderQuery(String outTradeNo) {
        Log.i(TAG, "wxpayOrderQuery");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/wxpay/orderQuery");
        requestInfo.put("outTradeNo", outTradeNo);
        postFromNoLoad(new OnInterfaceRespListener<AliPayInfo>() {
            @Override
            public void requestSuccess(AliPayInfo bean) {
                Log.i(TAG, "wxpayOrderQuery success");
                selfSecond.queryWxpayGetpayReslut(bean);
            }
        });
    }

    //    /cgz/notaryorder/doSetState
    public void addNotaryDoSetState(String orderId, String notaryState, String payType) {
        Log.i(TAG, "addNotaryDoSetState");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/notaryorder/doSetState");
        requestInfo.put("unitGuid", orderId);
        requestInfo.put("notaryState", notaryState);
        requestInfo.put("payType", payType);//        pt001支付宝;pt002微信
        postNoLoad(new OnInterfaceRespListener<OrderInfoEntity>() {
            @Override
            public void requestSuccess(OrderInfoEntity bean) {
                Log.i(TAG, "addNotaryDoSetState success");
                selfSecond.queryResultBySetStateSecond(bean, notaryState);
            }

            @Override
            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                super.requestFailed(errorCode, bean, exception, error);
            }
        }, false);
    }

    public void OnlineDoSetState(String orderId, String notaryState, String payType, DosetStatus view) {
        Log.i(TAG, "addNotaryDoSetState");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/notaryorder/doSetState");
        requestInfo.put("unitGuid", orderId);
        requestInfo.put("notaryState", notaryState);
        requestInfo.put("payType", payType);//pt001支付宝;pt002微信
        post(true, "正在创建订单...", new OnInterfaceRespListener<OrderInfoEntity>() {
            @Override
            public void requestSuccess(OrderInfoEntity bean) {
                Log.i(TAG, "addNotaryDoSetState success");
                view.queryResultBySetStateSecond(bean, notaryState);
            }

            @Override
            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                super.requestFailed(errorCode, bean, exception, error);
                view.failed("");
            }
        });
    }

    //cgz/notarialOffice/getById
    public void queryNotarialOfficeGById(String unitGuid) {
        Log.i(TAG, "queryNotarialOfficeGById");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.USER + "cgz/notarialOffice/getById");
        requestInfo.put("unitGuid", unitGuid);
        postFromQuiet(new OnInterfaceRespListener<NotaryEntity>() {
            @Override
            public void requestSuccess(NotaryEntity bean) {
                Log.i(TAG, "queryNotarialOfficeGById success");
                notarizaSelf.queryNotaryEntitySuccess(bean);
            }

            @Override
            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                super.requestFailed(errorCode, bean, exception, error);
                Log.i(TAG, "queryNotarialOfficeGById erro " + errorCode + error);
            }
        });
    }

    //填写申请信息下一步按钮接口：cgz/notaryorder/add
    //        "userId":"用户Id",
//        "name":"用户名",
//        "useArea":"使用地",
//        "useLanguage":"使用语言",
//        "purposeName":"公证用途",
//        "notaryId":"公证处Id",
//        "isDaiBan":"是否代办",
//        "notaryForm":"公证类型",
//        "description":"描述",
//        "notaryitems":"[{"notaryItemId":"","notaryItemName":"","notaryNum":""},{...}] ",(公证事项)
//        "applyUsers":"[{\"name\":\"\",\"gender\":\"\",\"idCard\":\"\",\"mobile\":\"\"},{\"name\":\"\",\"gender\":\"\",\"idCard\":\"\",\"mobile\":\"\",\"relationShip\":\"\"}]",（订单申请人）
//        "videolog":{"planDate":"","videoDate":""}
    public void addNotaryorder(String userId, String name, String useArea, String useLanguage, String purposeName, String notaryId,
                               String notaryitems, String applyUsers, String mac,String isDaiBan,String remark) {
        Log.i(TAG, "addNotaryorder");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/notaryorder/add");
        requestInfo.put("userId", userId);
        requestInfo.put("name", name);
        requestInfo.put("useArea", useArea);
        requestInfo.put("useLanguage", useLanguage);
        requestInfo.put("purposeName", purposeName);
        requestInfo.put("notaryId", notaryId);
        requestInfo.put("macAddress", mac);
        requestInfo.put("isDaiBan", isDaiBan);//isDaiBan 0非代办  1代办
        requestInfo.put("notaryForm", "1");
        requestInfo.put("description", "");
        requestInfo.put("notaryitems", notaryitems);
        requestInfo.put("applyUsers", applyUsers);
        requestInfo.put("terminalType", 19);// TODO: 2021/12/22 zp
        requestInfo.put("remarks",remark);
        post("下一步", new OnInterfaceRespListener<BaseResponseBean>() {
            @Override
            public void requestSuccess(BaseResponseBean bean) {
                Log.i(TAG, "addNotaryorder success");
                selfView.addResultSuccess(bean.getData());
            }

            @Override
            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                super.requestFailed(errorCode, bean, exception, error);
                Log.i(TAG, "addNotaryorder erro " + errorCode + error);
            }
        });
    }

    //获取国家接口：sys/countryArea/selectAll
    public void queryCountryArea() {
        Log.i(TAG, "queryCountryArea");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "sys/countryArea/selectAll");
        post("查询国家", new OnInterfaceRespListener<BaseResponseBean>() {
            @Override
            public void requestSuccess(BaseResponseBean bean) {
                Log.i(TAG, "queryCountryArea success");
                selfView.queryResultCountyList(bean);
            }
        });
    }

    //刷新注册过的token
    public void refreshRegistToken() {
        Log.i(TAG, "refreshRegistToken");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/department/departmentTree");
        postQuiet(new OnInterfaceRespListener<List<NotaryEntity>>() {
            @Override
            public void requestSuccess(List<NotaryEntity> bean) {
                Log.i(TAG, "refreshRegistToken success");
            }

            @Override
            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                Log.i(TAG, "refreshRegistToken fail" + error);
            }
        });
    }

    //获取语言接口：sys/countryLanguage/selectAll
    public void queryCountryLanguage() {
        Log.i(TAG, "queryCountryLanguage");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "sys/countryLanguage/selectAll");
        post("查询语言", new OnInterfaceRespListener<BaseResponseBean>() {
            @Override
            public void requestSuccess(BaseResponseBean bean) {
                Log.i(TAG, "queryCountryLanguage success");
                selfView.queryResultLangeuList(bean);
            }
        });
    }

    //获取公证处接口：/cgz/department/departmentTree
    public void queryDepartmentTree() {
        Log.i(TAG, "queryDepartmentTree");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/department/departmentTree");
        post("查询公证处", new OnInterfaceRespListener<BaseResponseBean>() {
            @Override
            public void requestSuccess(BaseResponseBean bean) {
                Log.i(TAG, "queryDepartmentTree success");
                selfView.queryResultNotarList(bean);
            }
        });
    }

    //获取公证事项接口: "cgz/notaryitemmodel/selectLetter"
    public void queryNotaryitemmodelSelectLetter(boolean isNeed) {
        Log.i(TAG, "queryNotaryitemmodelSelectLetter");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/notarypurpose/selectTree");
        post("查询公证事项", new OnInterfaceRespListener<BaseResponseBean>() {
            @Override
            public void requestSuccess(BaseResponseBean bean) {
                List<NotrayBean> parse = bean.parseList(NotrayBean.class);
                selfView.sendNotarizationSuccess(parse);
            }
        });
    }

    //保存人脸识别报告信息
    public void saveFaceReport(String notaryId) {
        Log.i(TAG, "saveFaceReport");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.NOTARIZATION + "cgz/notaryorder/saveFaceReport");
        requestInfo.put("reportFileId", SPUtils.getInstance().get(context,"annexId",""));
        requestInfo.put("notaryId",notaryId);
        post("", new OnInterfaceRespListener<BaseResponseBean>() {
            @Override
            public void requestSuccess(BaseResponseBean bean) {
                Log.i(TAG, "saveFaceReport success");
                selfSecond.saveFaceReportSuccess(bean);
            }

            @Override
            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                super.requestFailed(errorCode, bean, exception, error);
                selfSecond.saveFaceReportFail(error);
            }
        });
    }


    //查询普通支付的订单状态
    public void queyCommonOrderInfo(String bnkPyOrdrNo, String cstmPyOrdrNo) {
        Log.i(TAG, "queyCommonOrderInfo");
        requestInfo = BaseRequestInfo.getInstance().getRequestInfo(AppConfig.PAY + "zrzf/queyOrderInfo");
        requestInfo.put("bnkPyOrdrNo", bnkPyOrdrNo);
        requestInfo.put("cstmPyOrdrNo", cstmPyOrdrNo);
        post(false, "", new OnInterfaceRespListener<PayBean>() {
            @Override
            public void requestSuccess(PayBean bean) {
                Log.i(TAG, "queyCommonOrderInfo  success" + bean);
                try {
//                    Gson gson = new Gson();
//                    ResponseBean beans = gson.fromJson(bean.toString(), ResponseBean.class);
                    if (bean!=null) {
//                        PayBean payBean = gson.fromJson(com.alibaba.fastjson.JSONObject.toJSONString(beans.getData()), PayBean.class);
                        orderQueryCommonView.orderCommonSuccess(bean);
                    } else {
                        orderQueryCommonView.orderCommonFail("普通支付订单查询失败");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    orderQueryCommonView.orderCommonFail(e.getMessage());
                }
            }

            @Override
            public void requestFailed(int errorCode, BaseResponseBean bean, Exception exception, String error) {
                super.requestFailed(errorCode, bean, exception, error);
                orderQueryCommonView.orderCommonFail(error);
            }
        });
    }

    public interface OrderQueryCommonView {

        void orderCommonSuccess(PayBean bean);

        void orderCommonFail(String msg);

    }



}
