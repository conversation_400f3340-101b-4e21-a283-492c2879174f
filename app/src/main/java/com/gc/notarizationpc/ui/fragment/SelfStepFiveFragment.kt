package com.gc.notarizationpc.ui.fragment

import android.graphics.Bitmap
import android.os.Bundle
import android.text.TextUtils
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import com.example.framwork.noHttp.Bean.BaseResponseBean
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseFragment
import com.gc.notarizationpc.common.ResponseBean
import com.gc.notarizationpc.model.*
import com.gc.notarizationpc.ui.NotarizationSelfActivity
import com.gc.notarizationpc.ui.adapter.PreviewImageAdapter
import com.gc.notarizationpc.ui.presenter.NotarizaSelfPresenter
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter
import com.gc.notarizationpc.utils.DrawView
import com.gc.notarizationpc.widget.AlertSignDialog
import com.gc.notarizationpc.widget.ImageAlertDialog
import kotlinx.android.synthetic.main.fragment_self_step_five.*
import kotlinx.android.synthetic.main.fragment_self_step_five.recyclerView
import kotlinx.android.synthetic.main.fragment_self_step_five.tvUpStep
import kotlinx.android.synthetic.main.fragment_self_step_five.tvWdzdStep


class SelfStepFiveFragment : BaseFragment(), NotarizaSelfPresenter.INotarizaSelfSecond, ImageAlertDialog.OnClickBottomListener, NotarizationPresenter.IUpdateImageView {

    private var currentActivity: NotarizationSelfActivity? = null
    private var mAdapter: PreviewImageAdapter? = null;
    private var notarizaSelfPresenter: NotarizaSelfPresenter? = null;
    private var updateP: NotarizationPresenter? = null
    private var imageDialog: ImageAlertDialog? = null;

    //签名
    var drawView: DrawView? = null
    private var main_linlayout: LinearLayout? = null
    private var bt: Button? = null
    private var bt_clear: Button? = null
    private var name_pos: TextView? = null
    private var img: ImageView? = null
    private var user_name = ""
    private var positions = 0
    private var canvas_with = 0

    private var TAG = "SelfStepFiveFragment"
    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {
        Log.i(TAG, "initViewsAndEvents")
        currentActivity = mActivity as NotarizationSelfActivity?;

        tvWdzdStep?.setOnClickListener {
            currentActivity?.onNavigationItemSelected(6)
        };

        tvSignStep?.setOnClickListener {
            //调用保存人脸识别报告信息
            var orderId= (mActivity as NotarizationSelfActivity?)?.orderId
            notarizaSelfPresenter?.saveFaceReport(orderId);


        };
        tvUpStep?.setOnClickListener {
            currentActivity?.onNavigationItemSelected(3)
        }

        notarizaSelfPresenter = NotarizaSelfPresenter(mActivity, this, "ss");
        updateP = NotarizationPresenter(mActivity, this as NotarizationPresenter.IUpdateImageView)
//        initSign()
        imageDialog = ImageAlertDialog(mActivity);
        initByRecyclerViewGrid();

        bt = view?.findViewById<View>(R.id.bt) as Button
        bt_clear = view?.findViewById<View>(R.id.bt_clear) as Button
        name_pos = view?.findViewById<View>(R.id.name_pos) as TextView
        setOnclick()
        img = view?.findViewById<View>(R.id.img) as ImageView

        // 获取创建的宽度和高度
        val displayMetrics = DisplayMetrics()
        mActivity?.windowManager?.defaultDisplay?.getRealMetrics(displayMetrics)
        // 创建一个DrawView，该DrawView的宽度、高度与该Activity保持相同
        main_linlayout = view?.findViewById<View>(R.id.main_linlayout) as LinearLayout
    }

    private fun setDrawView(length: Int?) {
        var with: Int = main_linlayout!!.width * length!!
        var dou: Double? = 1.5 + length?.minus(1)!! * 0.5
        if (dou != null) {
            with = (dou * main_linlayout!!.width).toInt() + 1
        }
        drawView = DrawView(mActivity, main_linlayout!!.width, main_linlayout!!.height)
        main_linlayout!!.addView(drawView)
        drawView!!.requestFocus()
    }

    private fun initByRecyclerViewGrid() {
        Log.i(TAG, "initByRecyclerViewGrid")
        recyclerView.layoutManager = GridLayoutManager(mActivity, 6);
        mAdapter = PreviewImageAdapter(mActivity);
        recyclerView.adapter = mAdapter;
        mAdapter!!.mCallBack = object : PreviewImageAdapter.CallBack {
            override fun callBack(entity: String, position: Int) {
                var urlString = entity;
                imageDialog?.setImagePath(urlString);

                imageDialog?.show();
            }
        }
        mAdapter?.itemsList = (mActivity as NotarizationSelfActivity?)?.imgPathList;
        mAdapter?.notifyDataSetChanged();
    }

    override fun lazyInit(view: View?, savedInstanceState: Bundle?) {


    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.fragment_self_step_five;
    }


    public fun senderNextApi(bean: ImageBean?, encData: String?) {
        Log.i(TAG, "senderNextApi")
        var filePath = bean?.filePath;
        var orderId = (mActivity as NotarizationSelfActivity?)?.orderId;
        notarizaSelfPresenter?.addNotaryDoSetSignName(orderId, filePath, encData);
    }

    override fun queryResultselectNotaryItem(bean: BaseResponseBean?) {

    }

    override fun queryWxpayGetpay(bean: ResponseBean?) {

    }

    override fun addMaterialSuccess(arrayList: MutableList<String>?) {

    }

    override fun addNotaryDoSetSignName(bean: OrderInfoEntity?) {
        Log.i(TAG, "addNotaryDoSetSignName")
        if (bean == null || "200" != (bean?.code)) {
            toastError("签字文件生成失败，请重新签名")
            signatureView.visibility = View.GONE
            name_pos?.text = user_name;
//            CommonUtil.setTextColor(name_pos,"#0F41A6",user_name?.substring(0,1))
        } else {
            currentActivity?.onNavigationItemSelected(5)
        }
    }

    override fun addalipaySetQrCode(bean: AliPayInfo?) {

    }

    override fun addalipaySetQrCodeReslut(bean: AliPayInfo?) {

    }

    override fun queryWxpayGetpayReslut(bean: AliPayInfo?) {

    }

    override fun queryResultBySetStateSecond(bean: OrderInfoEntity?, notaryState: String?) {

    }

    override fun saveFaceReportSuccess(bean: BaseResponseBean?) {

        user_name = currentActivity?.userInfo?.name.toString()

        Log.i(TAG, "signatureRemote")
        if (main_linlayout!!.width == 0) {
            val vto2 = main_linlayout!!.viewTreeObserver
            vto2.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    Log.d("zpzp", "画布初始化完成")
                    main_linlayout!!.viewTreeObserver.removeGlobalOnLayoutListener(this)
                    drawView = DrawView(mActivity, main_linlayout!!.width, main_linlayout!!.height)
                    canvas_with = main_linlayout!!.width
                    setDrawView(user_name!!.length)
                }
            })
        } else {
            if (drawView == null) {
                setDrawView(user_name!!.length)
            } else {
                main_linlayout!!.removeAllViews()
                setDrawView(user_name!!.length)
            }
        }
        positions = 0;

        signatureView?.visibility = View.VISIBLE
        if (!TextUtils.isEmpty(user_name)) {
//                if (user_name.length < 2) {
            bt?.setText("完成")
//                } else {
//                    bt?.setText(getString(R.string.next_one))
//                }
        }
        name_pos?.text = user_name;
    }

    override fun saveFaceReportFail(error: String?) {
        toastError("上传身份识别报告失败:$error")
    }

    override fun onCancelClick() {

    }

    fun setOnclick() {
        bt?.setOnClickListener(View.OnClickListener {
//            if (getString(R.string.next_one) == bt!!.text) {
////                    bitmapList.add(bit)
//                positions++
//                if (positions == user_name?.length - 1) {
//                    bt!!.text = getString(R.string.done)
//                }
////                if (positions != 0) {
//                drawView!!.scrollTo((canvas_with / 2) * positions, 0)
//                drawView!!.setwith((canvas_with / 2) * positions)
////                }
//                name_pos?.text = user_name?.substring(positions, positions + 1);
//            } else {
            if (drawView?.isCanvasEmpty() == true) {
                toastError("请先签字")
            } else {
                //上传图片到服务器 fastupload接口
                val bit = drawView!!.getPaintBitmap(user_name?.length)
                updateP?.updateBitmap(bit);
                drawView!!.clear()
                if (positions != 0)
                    drawView!!.scrollTo(0, 0)
                positions = 0
            }

//            }
        })
        bt_clear?.setOnClickListener(View.OnClickListener {
            drawView!!.clear()
            if (positions != 0) {
                drawView!!.scrollTo(0, 0)
                drawView!!.setwith(0)
                positions = 0
//                if (user_name.length < 2) {
                    bt?.setText("完成")
//                } else {
//                    bt?.setText(getString(R.string.next_one))
//                }
                name_pos?.text = user_name;
            }
        })
    }

    override fun updateImageSuccess(bean: ImageBean?) {
        // TODO: 调接口传递文件路径给后台保存
        var filePath = bean?.filePath;
        var orderId = (mActivity as NotarizationSelfActivity?)?.orderId;
        notarizaSelfPresenter?.addNotaryDoSetSignName(orderId, currentActivity?.userInfo?.name, currentActivity?.userInfo?.idCard, filePath);
    }

    override fun updateImageFail() {
        Log.i(TAG, "updateImageFail")
        toastError("签名上传失败,请重签")
        bt!!.text = "完成"
    }
}