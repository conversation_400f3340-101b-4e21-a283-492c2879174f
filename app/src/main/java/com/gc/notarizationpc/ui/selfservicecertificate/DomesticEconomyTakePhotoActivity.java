package com.gc.notarizationpc.ui.selfservicecertificate;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.hardware.usb.UsbDevice;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.util.Log;
import android.view.TextureView;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProviders;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.eloam.gaopaiyi.util.UVCCameraUtil;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityTakePhotoDomesticEconomyBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.ui.fragment.DomesticEconomyUploadMaterialFragment;
import com.gc.notarizationpc.ui.viewmodel.DomesticEconomyTakePhotoViewModel;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.YuvUtils;
import com.gc.notarizationpc.utils.UVCCameraLiceo;
import com.serenegiant.arcface.util.ImageStack;
import com.serenegiant.usb.IFrameCallback;
import com.serenegiant.usb.USBMonitor;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.base.BaseAdapter;
import me.goldze.mvvmhabit.base.BaseViewHolder;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui.video
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/3/22
 */
public class DomesticEconomyTakePhotoActivity extends BaseActivity<ActivityTakePhotoDomesticEconomyBinding, DomesticEconomyTakePhotoViewModel> implements BaseViewHolder.OnItemClickListener {

    private String TAG = "Test";
    private USBMonitor mUSBMonitor = null;
    private int come = 0;
    private String materialName = "";
    private String pidGPY = AppConfig.HIGHPID; // 10a0 is the original ID for high-shooting instrument
    private boolean captureStillTwo = false;
    private final int WIDTH = 1280;
    private final int HEIGHT = 720;
    private final int PHOTO_SIZE = WIDTH * HEIGHT * 3 / 2;
    private ImageStack imgKjStack = new ImageStack(WIDTH, HEIGHT);
    private String annexId = "";
    private ArrayList<UploadFileBean> uploadedFilePaths = new ArrayList<>();
    private UploadImgAdapter consultImgAdapter = null;
    private long lastClickTime = 0L;
    private long lastClickTimeT = 0L;
    private boolean isBack = false;
    private long initTime = 0;
    private String unitGuid = "";
    private String roomId = "";
    private final int FAST_CLICK_DELAY_TIME = 1800;
    private TextureView informationPhoto;
    private RecyclerView rnlImgs;
    private ImageView ivClose;
    private TextView tvUpload, tvCamera;
    private static final int MAX_SELECT_NUMBER = 30;

    // 案件的id
    private String caseInforId;

    // 来源渠道
    private String sourceWay;

    // 材料id
    private String materialId, materialTypeId;

    // 记录当前上传材料的数量
    private int imageCount = 0;

    private int sourceFrom;
    private String recordId;//视频连线 上传图片需要用到的
    private UploadFileBean currentChooseDel;//当前点击要删除的图片


    @Override
    public DomesticEconomyTakePhotoViewModel initViewModel() {
//        使用自定义的ViewModelFactory来创建ViewModel，如果不重写该方法，则默认会调用LoginViewModel(@NonNull Application application)构造方法
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(DomesticEconomyTakePhotoViewModel.class);
    }

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_take_photo_domestic_economy;
    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();
        viewModel.uploadedFile.observe(this, fileInformation -> {
            if (fileInformation != null) {
                UploadFileBean model = (UploadFileBean) fileInformation;
                if (TextUtils.isEmpty(model.getSharedUrl())) {
                    toastError(Utils.getContext().getString(R.string.fileIsNotExist));
                } else {
                    uploadedFilePaths.add((UploadFileBean) fileInformation);
                    rnlImgs.setAdapter(consultImgAdapter);
                    rnlImgs.scrollToPosition(consultImgAdapter.getItemCount() - 1);
                    consultImgAdapter.refreshAdapter(uploadedFilePaths);
                    consultImgAdapter.notifyDataSetChanged();

                }
            }
        });

        viewModel.delMaterialEvent.observe(this, result -> {
            if (result) {
                uploadedFilePaths.remove(currentChooseDel);
                consultImgAdapter.refreshAdapter(uploadedFilePaths);
            }
        });
    }

    @Override
    public void initData() {
        Log.e("Test", "initData");
        super.initData();
        sourceFrom = getIntent().getIntExtra("from", 0);
        recordId = getIntent().getStringExtra("recordId");
        informationPhoto = findViewById(R.id.informationPhoto);
        rnlImgs = findViewById(R.id.rnl_imgs);
        tvCamera = findViewById(R.id.tv_camera);
        tvUpload = findViewById(R.id.tv_upload);
        caseInforId = getIntent().getStringExtra("caseInforId");
        sourceWay = getIntent().getStringExtra("sourceWay");
        materialId = getIntent().getStringExtra("materialId");
        materialTypeId = getIntent().getStringExtra("materialTypeId");
        imageCount = getIntent().getIntExtra("materialListCount", 0);
        showProgress(false, getString(R.string.loading));
        YuvUtils.init(this);
        CountDownTimer timer = new CountDownTimer(2 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                hideProgress();
                this.cancel();
            }
        };
        timer.start();


        Log.i(TAG, "initViewsAndEvents");
        initTime = System.currentTimeMillis();

        consultImgAdapter = new UploadImgAdapter(this, R.layout.list_item_gaopai_picture, 0);
        consultImgAdapter.setOnItemClickListener(this);

        consultImgAdapter.refreshAdapter(uploadedFilePaths);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        rnlImgs.setLayoutManager(linearLayoutManager);
        rnlImgs.setAdapter(consultImgAdapter);

        binding.tvUpload.setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                if (uploadedFilePaths == null || uploadedFilePaths.size() == 0) {
                    toastInfo(Utils.getContext().getString(R.string.pleaseUploadRelevantMaterials));
                    return;
                }
                DomesticEconomyUploadMaterialFragment.takePhotoListener.result(uploadedFilePaths);
                finish();
            }
        });

        binding.tvBack.setOnClickListener(view -> {
            if (System.currentTimeMillis() - initTime > 2000) {
                if (!isBack) {
                    isBack = true;
                    if (uploadedFilePaths == null || uploadedFilePaths.size() == 0) {
                        finish();
                    } else {
                        DomesticEconomyUploadMaterialFragment.takePhotoListener.result(uploadedFilePaths);
                        finish();
                    }
                }
            } else {
                toastInfo(Utils.getContext().getString(R.string.exitingCamera));
            }
        });

        tvCamera.setOnClickListener(new

                                            MyOnclickClickListener() {
                                                @Override
                                                public void mOnClick(View v) {
                                                    if (uploadedFilePaths.size() + imageCount >= MAX_SELECT_NUMBER) {
                                                        toastInfo(Utils.getContext().getString(R.string.takeUpTo30ImagesAtOneTime));
                                                        return;
                                                    }
                                                    if (System.currentTimeMillis() - lastClickTime >= FAST_CLICK_DELAY_TIME) {
                                                        Log.i(TAG, "takePhotoClick");
                                                        captureStillTwo = true;
                                                        lastClickTime = System.currentTimeMillis();
                                                    } else {
                                                        Log.i(TAG, "double takePhotoClick");
                                                    }
                                                }
                                            });

        if (uploadedFilePaths.size() < MAX_SELECT_NUMBER) {
            openCamera();
        }


    }

    @Override
    public int initVariableId() {
        return 0;
    }


    /**
     * 打开相机
     */
    private void openCamera() {
        Log.i(TAG, "openCamera");
        mUSBMonitor = UVCCameraLiceo.INSTANCE.initUSBMonitor(this, new UVCCameraLiceo.OnMyDevConnectListener() {
            @Override
            public void onConnectDev(UsbDevice device, USBMonitor.UsbControlBlock ctrlBlock) {
                if (System.currentTimeMillis() - lastClickTimeT >= FAST_CLICK_DELAY_TIME) {
                    Log.i(TAG, "takePhotoClick");
                    String pid = String.format("%x", device.getProductId());
                    Log.i(TAG, "onConnectDev() pid:" + pid);
                    Log.i(TAG, "onConnectDev() pidGPY:" + pidGPY);
                    if (pid.equals(pidGPY)) { // 打开高拍 1054
                        UVCCameraLiceo.INSTANCE.openRGBCamera(informationPhoto, ctrlBlock, new IFrameCallback() {
                            @Override
                            public void onFail() {

                                hideProgress();
                                toastError(Utils.getContext().getString(R.string.failedToOpenTheHighCamera));
//                                finish();
                            }

                            @Override
                            public void onFrame(ByteBuffer frame) {
//                                Log.i(TAG, "openCamera success");
                                // imgKjStack.pushImageInfo(frame!!, System.currentTimeMillis())
                                if (captureStillTwo) {
                                    captureStillTwo = false;
                                    takePhoto();
                                } else {
//                                    hintPhoto.setVisibility(View.VISIBLE);
                                }
                            }
                        });
                    }
                    lastClickTimeT = System.currentTimeMillis();
                } else {
                    Log.i(TAG, "double takePhotoClick");
                }
            }
        });

        boolean hasHigh = false;
        List<UsbDevice> devList = UVCCameraUtil.INSTANCE.getUsbDeviceList(this, mUSBMonitor);
        for (UsbDevice usbDevice : devList) {
            String productid = String.format("%x", usbDevice.getProductId());
            Log.e(TAG, "找到了: " + productid);
            if (productid.equals("1054")) {
                hasHigh = true;
            }
        }
        if (!hasHigh) {
            toastError(Utils.getContext().getString(R.string.highCameraConnectAbnormal));
        }
    }


    private void takePhoto() {
        Log.i(TAG, "takePhoto");
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (uploadedFilePaths.size() >= MAX_SELECT_NUMBER) {
                    toastError(Utils.getContext().getString(R.string.takeUpTo30ImagesAtOneTime));
                    return;
                }

            }
        });
        Log.e("Test", informationPhoto.getBitmap() + "informationPhoto.getBitmap()");
        Bitmap faceBitmap = informationPhoto != null ? informationPhoto.getBitmap() : null;

        // Bitmap faceBitmap = YuvUtils.nv21ToBitmap(data, WIDTH, HEIGHT);
        if (faceBitmap != null && !faceBitmap.isRecycled()) {
            // Rotate the image to fix the orientation
            Bitmap bitmap = rotateBitmap2(faceBitmap, 180);
            viewModel.uploadSignPic(bitmap);
        }

    }

    class UploadImgAdapter extends BaseAdapter<UploadFileBean> {

        public UploadImgAdapter(Context context, int itemLayoutRes, @Nullable Object type) {
            super(context, itemLayoutRes, type);
        }

        @Override
        public void bind(@NonNull BaseViewHolder viewHolder, UploadFileBean itemData, int position) {
            ImageView imgDel = viewHolder.getView(R.id.work_iv_wip_delete);
            if (null != itemData) {
                viewHolder.setVisibility(R.id.work_iv_wip_picture, true);

                CommonUtil.displayImage(((ImageView) viewHolder.getView(R.id.work_iv_wip_picture)), itemData.getSharedUrl());
            }
            imgDel.setOnClickListener(new MyOnclickClickListener() {
                @Override
                public void mOnClick(View v) {
                    currentChooseDel = itemData;
                    uploadedFilePaths.remove(currentChooseDel);
                    consultImgAdapter.refreshAdapter(uploadedFilePaths);
                }
            });
        }

        @Override
        public int viewType(UploadFileBean itemData) {
            return 0;
        }
    }

    private Bitmap rotateBitmap2(Bitmap bm, int orientationDegree) {
        Matrix ma = new Matrix();
        ma.setRotate(orientationDegree, bm.getWidth() / 2, bm.getHeight() / 2);
        try {
            return Bitmap.createBitmap(bm, 0, 0, bm.getWidth(), bm.getHeight(), ma, true);
        } catch (OutOfMemoryError ex) {
            Log.e(TAG, "位图旋转出错");
            MobclickAgent.reportError(this, ex);
        }

        int degree = orientationDegree;
        if (degree < 0) {
            degree = 360 + orientationDegree;
        }

        int srcW = bm.getWidth();
        int srcH = bm.getHeight();

        Matrix m = new Matrix();
        m.setRotate(degree, srcW / 2, srcH / 2);

        float targetX;
        float targetY;
        int destH = srcH;
        int destW = srcW;

        if (degree == 90) {
            targetX = srcH;
            targetY = 0;
            destH = srcW;
            destW = srcH;
        } else if (degree == 270) {
            targetX = 0;
            targetY = srcW;
            destH = srcW;
            destW = srcH;
        } else if (degree == 180) {
            targetX = srcW;
            targetY = srcH;
        } else {
            return bm;
        }

        float[] values = new float[9];
        m.getValues(values);
        float x1 = values[Matrix.MTRANS_X];
        float y1 = values[Matrix.MTRANS_Y];
        m.postTranslate(targetX - x1, targetY - y1);

        Bitmap bm1 = Bitmap.createBitmap(destW, destH, Bitmap.Config.ARGB_8888);
        Paint paint = new Paint();
        Canvas canvas = new Canvas(bm1);
        canvas.drawBitmap(bm, m, paint);

        return bm1;
    }

    @Override
    public void onResume() {
        super.onResume();
        uploadedFilePaths = new ArrayList<>();
        UVCCameraLiceo.INSTANCE.registerUsbMonitor(mUSBMonitor);
        UVCCameraLiceo.INSTANCE.requestPermissionRGB(this, mUSBMonitor, pidGPY);
        Log.e("Test", "onresume");
    }

    @Override
    public void onPause() {
        super.onPause();
        try {
            UVCCameraLiceo.INSTANCE.releaseRGBCamera();
            UVCCameraLiceo.INSTANCE.unRegisterUsbMonitor(mUSBMonitor);
        } catch (Exception e) {
            Log.e(TAG, "onPause:" + e.getMessage());
            MobclickAgent.reportError(this, e);
        }
    }

    @Override
    public void onStart() {
        Log.i(TAG, "onStart");
        super.onStart();
    }

    @Override
    public void onStop() {
        Log.i(TAG, "onStop");
        super.onStop();
        /*mUSBMonitor?.unregister()*/
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        try {
            YuvUtils.destroy();
            UVCCameraLiceo.INSTANCE.destroyUSBMonitor(mUSBMonitor);
            mUSBMonitor = null;

            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        } catch (Exception e) {
            Log.i(TAG, "onDestroy");
            MobclickAgent.reportError(this, e);
        }

        Log.i(TAG, "onDestroy");
    }

    @Override
    public void onItemClick(BaseViewHolder viewHolder, int position, Object itemData, Object type) {

    }

    @Override
    public void onItemLongClick(BaseViewHolder viewHolder, int position, Object itemData, Object type) {

    }


    // Other methods and inner classes remain the same


}
