package com.gc.notarizationpc.ui.adapter

import android.view.View
import android.widget.LinearLayout
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.example.framwork.adapter.CommonQuickAdapter
import com.gc.notarizationpc.R
import com.gc.notarizationpc.model.RoomCompanyInfo
import com.gc.notarizationpc.model.RoomUserInfo

class VideoCompanyAdapter() : CommonQuickAdapter<RoomCompanyInfo>(R.layout.item_video_company) {

    override fun convert(holder: BaseViewHolder?, item: RoomCompanyInfo?) {
        holder?.setText(R.id.tvCompanyName, item?.companyName)
        holder?.setText(R.id.tvContacts, item?.statutoryPerson)
        holder?.setText(R.id.tvAddress, item?.companyAdd)
        holder?.setText(R.id.tvMobile, item?.statutoryMobile)

    }
}