package com.gc.notarizationpc.ui.viewmodel;

import static me.goldze.mvvmhabit.utils.Utils.getContext;

import android.app.Application;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.FileInformationModel;
import com.google.gson.Gson;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.CommonUtil;
import me.goldze.mvvmhabit.utils.SM2Util;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

/**
 * 高拍
 */
public class TakePhotoViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<Boolean> delMaterialEvent = new SingleLiveEvent<>();//视频咨询连线 删除图片

    public class UIChangeObservable {

    }

    public TakePhotoViewModel(@NonNull Application application) {
        super(application);
    }

    public SingleLiveEvent uploadedFile = new SingleLiveEvent<>();

    /**
     * 视频公证受理室上传材料
     */
    public void uploadFile(Bitmap bitmap, String caseInforId, String sourceWay, String id, String materialTypeId) {
        if (bitmap != null) {
            File file = bitmapToFile(bitmap);
            RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);
            MultipartBody.Part body = MultipartBody.Part.createFormData("file", file.getName(), requestFile);
            IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(getContext(), "idcardinfo");
            HashMap<String, String> tmp = new HashMap<>();
            if (idCardUserInfo != null) {
                tmp.put("userId", idCardUserInfo.getUserId());
            }
            tmp.put("caseInfoId", caseInforId);
            tmp.put("sourceChannel", "4");
            tmp.put("sourceWay", sourceWay);
            tmp.put("id", id);
            Gson gson = new Gson();

            showDialog(Utils.getContext().getString(R.string.savingImage));
            RequestUtil.uploadMaterial(RequestBody.create(MediaType.parse("encryptStr"), gson.toJson(tmp)), body, new MyObserver<FileInformationModel>() {
                @Override
                public void onSuccess(FileInformationModel result) {
                    toastSuccess(Utils.getContext().getString(R.string.uploadImagesSuccess));
                    dismissDialog();
                    if (result != null) {
                        result.setMaterialTypeId(materialTypeId);
                        uploadedFile.setValue(result);
                    }
                }

                @Override
                public void onFailure(Throwable e, String errorMsg) {
                    dismissDialog();
                    Log.e("Test", "图片上传失败");
                    toastError(errorMsg == null ? Utils.getContext().getString(R.string.uploadImagesFail) : errorMsg);
//                    uploadedFile.setValue("");
                }
            });
        }

    }

    //返回
    public BindingCommand backClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            finish();
        }
    });

    /**
     * 视频办证删除材料
     */
    public void clearMaterialAndReUploadByUser(Map<String, Object> param) {
        showDialog(Utils.getContext().getString(R.string.deletingImage));
        RequestUtil.clearMaterialAndReUploadByUser(param, new MyObserver<Object>() {
            @Override
            public void onSuccess(Object result) {
                dismissDialog();
                delMaterialEvent.setValue(true);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                ToastUtils.toastError(Utils.getContext().getString(R.string.deleteFailed));
            }
        });
    }

    /**
     * 视频咨询连线删除材料
     */
    public void delOtherMaterial(String recordId, String fileId) {
        RequestUtil.delOtherMaterial(recordId, fileId, new MyObserver<Object>() {
            @Override
            public void onSuccess(Object result) {
                try {
                    delMaterialEvent.setValue(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                ToastUtils.toastError(Utils.getContext().getString(R.string.deleteFailed));
            }
        });
    }

    /**
     * 视频咨询连线上传材料
     */
    public void uploadLineFile(Bitmap bitmap, String recordId) {
        if (bitmap != null) {
            File file = bitmapToFile(bitmap);
            HashMap<String, String> tmp = new HashMap<>();
            RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);
            MultipartBody.Part body = MultipartBody.Part.createFormData("file", file.getName(), requestFile);
            IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(getContext(), "idcardinfo");
            if (idCardUserInfo != null) {
                tmp.put("userId", idCardUserInfo.getUserId());
            }

            tmp.put("recordId", recordId);
            Gson gson = new Gson();

            showDialog("图片保存中");
            RequestUtil.uploadOtherMaterialBindRecord(RequestBody.create(MediaType.parse("encryptStr"), gson.toJson(tmp)), body, new MyObserver<FileInformationModel>() {
                @Override
                public void onSuccess(FileInformationModel result) {
                    toastSuccess(Utils.getContext().getString(R.string.uploadImagesSuccess));
                    dismissDialog();
                    if (result != null) {
//                        result.setMaterialTypeId(materialTypeId);
                        uploadedFile.setValue(result);
                    }
                }

                @Override
                public void onFailure(Throwable e, String errorMsg) {
                    dismissDialog();
                    Log.e("Test", "图片上传失败");
                    toastError(errorMsg == null ? Utils.getContext().getString(R.string.uploadImagesFail) : errorMsg);
//                    uploadedFile.setValue("");
                }
            });
        }

    }

    private File bitmapToFile(Bitmap bitmap) {
        File file = new File(AppConfig.APP_PATH_ROOT + System.currentTimeMillis() + ".jpg");
        try {
            file.createNewFile();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, bos);
            byte[] bitmapData = bos.toByteArray();
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(bitmapData);
            fos.flush();
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
