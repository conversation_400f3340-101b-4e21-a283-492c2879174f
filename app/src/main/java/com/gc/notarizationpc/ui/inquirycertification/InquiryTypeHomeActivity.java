package com.gc.notarizationpc.ui.inquirycertification;

import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityInquiryTypeBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.common.OnLineRulesActivity;
import com.gc.notarizationpc.ui.view.InputIdCardNumberAlert;
import com.gc.notarizationpc.ui.view.InputPhoneNumberAlert;
import com.gc.notarizationpc.ui.view.ReadIdCardInformationAlert;
import com.gc.notarizationpc.ui.viewmodel.InquiryTypeViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopwindowUtil;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:办证查询
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class InquiryTypeHomeActivity extends BaseActivity<ActivityInquiryTypeBinding, InquiryTypeViewModel> {
    private String TAG = InquiryTypeHomeActivity.class.getSimpleName();
    private Double msgCode;
    private ReadIdCardInformationAlert readIdCardInformationAlert;
    private InputPhoneNumberAlert inputPhoneNumberAlert;
    private InputIdCardNumberAlert inputIdCardNumberAlert;


    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_inquiry_type;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public InquiryTypeViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(InquiryTypeViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }

    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });
        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });
    }


    @Override
    public void initViewObservable() {
        viewModel.isRefreshing.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    Log.e("Test", "isRefreshing");
                    readIdCardInformationAlert = new ReadIdCardInformationAlert(InquiryTypeHomeActivity.this, new PopwindowUtil.ResultListener() {
                        @Override
                        public void result(Object idCard) {
                            if (idCard != null) {
                                Map<String, Object> data = new HashMap<>();
                                data.put("idCardNum", idCard.toString());
                                data.put("searchType", 1);
                                data.put("credentialType", 1);
                                viewModel.getUserInfor(data, new PopwindowUtil.ResultSecondListener() {
                                    @Override
                                    public void result(Object value) {
                                        if (value != null && value.toString().length() > 0) {
                                            IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(InquiryTypeHomeActivity.this, "idcardinfo");
                                            if (idCardUserInfo == null) {
                                                idCardUserInfo = new IdCardUserInfo();
                                            }
                                            idCardUserInfo.setUserId(value.toString());
                                            SPUtils.getInstance().saveObject(InquiryTypeHomeActivity.this, "idcardinfo", idCardUserInfo);
                                            Bundle mBundle = new Bundle();
                                            mBundle.putString("idCard", idCard.toString());
                                            readIdCardInformationAlert.dismiss();
                                            startActivity(InquiryOrderListHomeActivity.class, mBundle);
                                        }
                                    }

                                    @Override
                                    public void secondResult(Object value) {

                                    }
                                });
                            }
                        }
                    });
                    readIdCardInformationAlert.show();
                }
            }
        });

        viewModel.showPhoneAlert.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    inputPhoneNumberAlert = new InputPhoneNumberAlert(InquiryTypeHomeActivity.this, new InputPhoneNumberAlert.EventsListener() {
                        @Override
                        public void verifyCodeEvent(Object value) {
                                    viewModel.getMessageCode(value.toString(), new PopwindowUtil.ResultListener<Double>() {
                                        @Override
                                        public void result(Double value) {
                                            msgCode = value;
                                        }
                            });


                        }

                        @Override
                        public void readAgreeEvent(Object value) {
                            startActivity(OnLineRulesActivity.class);
                        }

                        @Override
                        public void cancelEvent() {

                        }

                        @Override
                        public void decideEvent(Object value) {
                            if (value != null) {
                                Map<String, Object> myValue = Map.class.cast(value);
                                if (myValue.get("phone") == null || myValue.get("phone") != null && myValue.get("phone").toString().length() != 11 || myValue.get("phone") != null && myValue.get("phone").toString().isEmpty()) {
                                    ToastUtils.showLong(getString(R.string.pleaseInputCorrectPhoneNumber));
                                    return;
                                }
                                if (myValue.get("verifyCode") == null || msgCode == null) {
                                    ToastUtils.showLong(getString(R.string.pleaseInputCorrectMessageCode));
                                    return;
                                } else {
                                    if (myValue.get("verifyCode").toString().length() != 6 || Double.parseDouble(myValue.get("verifyCode").toString()) != msgCode) {
                                        ToastUtils.showLong(getString(R.string.pleaseInputCorrectMessageCode));
                                        return;
                                    }
                                }

                                if (myValue.get("agree") == null || myValue.get("agree").toString() != "1") {
                                    ToastUtils.showLong(Utils.getContext().getString(R.string.pleaseAgreeUserRules));
                                    return;
                                }

                                viewModel.checkSMSCode(myValue.get("phone").toString(), myValue.get("verifyCode").toString(), new PopwindowUtil.ResultSecondListener() {
                                    @Override
                                    public void result(Object value1) {
                                        if (value1 != null && (Boolean) value1 == true) {
                                            showProgress();
                                            Map<String, Object> data = new HashMap<>();
                                            data.put("phoneNum", "+86-" + myValue.get("phone"));
                                            data.put("searchType", 2);
                                            data.put("credentialType", 1);
                                            viewModel.getUserInfor(data, new PopwindowUtil.ResultSecondListener() {
                                                @Override
                                                public void result(Object value) {
                                                    hideProgress();
                                                    if (value != null && value.toString().length() > 0) {
                                                        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(InquiryTypeHomeActivity.this, "idcardinfo");
                                                        if (idCardUserInfo == null) {
                                                            idCardUserInfo = new IdCardUserInfo();
                                                        }
                                                        idCardUserInfo.setUserId(value.toString());
                                                        SPUtils.getInstance().saveObject(InquiryTypeHomeActivity.this, "idcardinfo", idCardUserInfo);
                                                        inputPhoneNumberAlert.dismiss();
                                                        Bundle mBundle = new Bundle();
                                                        mBundle.putString("phoneNumber", "+86-" + myValue.get("phone").toString());
                                                        mBundle.putString("verifyCode", myValue.get("verifyCode").toString());
                                                        startActivity(InquiryOrderListHomeActivity.class, mBundle);
                                                    }
                                                }

                                                @Override
                                                public void secondResult(Object value) {
                                                }
                                            });
                                        }
                                    }

                                    @Override
                                    public void secondResult(Object value) {
                                        hideProgress();
                                    }

                                });
                            }
                        }
                    });
                    inputPhoneNumberAlert.show();
                }
            }
        });

        viewModel.showIdCardInformationAlert.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    Log.e("Test", "showIdCardInformationAlert");
                    inputIdCardNumberAlert = new InputIdCardNumberAlert(InquiryTypeHomeActivity.this, new InputIdCardNumberAlert.ButtonClickListenerWithBackValue() {

                        @Override
                        public void cancel() {

                        }

                        @Override
                        public void decide(Object value) {
                            if (value != null) {
                                Map<String, Object> myValue = Map.class.cast(value);
                                if (myValue.get("idCard") == null || myValue.get("idCard") != null && myValue.get("idCard").toString().length() != 18) {
                                    ToastUtils.showLong(Utils.getContext().getString(R.string.pleaseInputCorrectIdCardNumber));
                                    return;
                                }

                                if (myValue.get("agree") == null || myValue.get("agree").toString() != "1") {
                                    ToastUtils.showLong(Utils.getContext().getString(R.string.pleaseAgreeUserRules));
                                    return;
                                }
                                Map<String, Object> data = new HashMap<>();
                                data.put("idCardNum", myValue.get("idCard"));
                                data.put("searchType", 1);
                                data.put("credentialType", 1);
                                showProgress();
                                viewModel.getUserInfor(data, new PopwindowUtil.ResultSecondListener() {
                                    @Override
                                    public void result(Object value) {
                                        hideProgress();
                                        if (value != null && value.toString().length() > 0) {
                                            IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(InquiryTypeHomeActivity.this, "idcardinfo");
                                            if (idCardUserInfo == null) {
                                                idCardUserInfo = new IdCardUserInfo();
                                            }
                                            idCardUserInfo.setUserId(value.toString());
                                            SPUtils.getInstance().saveObject(InquiryTypeHomeActivity.this, "idcardinfo", idCardUserInfo);
                                            inputIdCardNumberAlert.dismiss();
                                            Bundle mBundle = new Bundle();
                                            mBundle.putString("idCard", myValue.get("idCard").toString());
                                            startActivity(InquiryOrderListHomeActivity.class, mBundle);
                                        }
                                    }

                                    @Override
                                    public void secondResult(Object value) {
                                        hideProgress();
                                    }
                                });
                            }
                        }

                        @Override
                        public void otherEvent(Object value) {
                            // TODO 在线受理服务使用规则
                            startActivity(OnLineRulesActivity.class);
                        }
                    });
                    inputIdCardNumberAlert.show();
                }
            }
        });

    }
}
