package com.gc.notarizationpc.ui.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.util.PopwindowUtil;


public class VideoExitTimeDecreaseAlert extends Dialog {

    private CountDownTimer countDownTimer;

    private PopwindowUtil.ResultSecondListener myResultListener;
    public VideoExitTimeDecreaseAlert(@NonNull Context context,PopwindowUtil.ResultSecondListener resultListener) {
        super(context);
        myResultListener = resultListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.dialog_video_exit_time_decrease_alert);
        init();
    }

    private void init() {
      TextView cancel = findViewById(R.id.dialog_video_exit_time_decrease_alert_back);
      cancel.setOnClickListener(v -> {
                  dismiss();
                  if (countDownTimer!=null){
                      countDownTimer.cancel();
                  }
                  myResultListener.result(null);
              });
        countDownTimer = new CountDownTimer(5000, 1000) {
            @Override
            public void onTick(long l) {
                cancel.setText("返回(" + l / 1000 + "s)");
            }

            @Override
            public void onFinish() {
                dismiss();
                countDownTimer.cancel();
                myResultListener.secondResult(null);

            }
        };
        countDownTimer.start();
    }

    @Override
    public void show() {
        Log.e("Test","dialog show");
        super.show();
    }


}
