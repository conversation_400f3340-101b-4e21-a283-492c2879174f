package com.gc.notarizationpc.ui;

import android.app.AlertDialog;
import android.app.PendingIntent;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.FileProvider;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.eloam.gaopaiyi.util.UVCCameraUtil;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityHomeBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.data.model.response.UpgradeResponse;
import com.gc.notarizationpc.ui.inquirycertification.InquiryTypeHomeActivity;
import com.gc.notarizationpc.ui.selfservicecertificate.CompariseAcitivity;
import com.gc.notarizationpc.ui.video.IDCardCompariseMobileActivity;
import com.gc.notarizationpc.ui.video.VideoConnectionActivity;
import com.gc.notarizationpc.ui.view.BlockPuzzleDialog;
import com.gc.notarizationpc.ui.viewmodel.HomeViewModel;
import com.gc.notarizationpc.util.MacUtils;
import com.hjq.permissions.OnPermission;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;

import java.io.File;
import java.io.IOException;
import java.util.List;

import cc.shinichi.library.ImagePreview;
import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.http.DownLoadManager;
import me.goldze.mvvmhabit.http.download.ProgressCallBack;
import me.goldze.mvvmhabit.utils.CommonUtil;
import me.goldze.mvvmhabit.utils.SPUtils;
import okhttp3.ResponseBody;

/**
 * 首页
 */
@Route(path = "/HomeActivity")
public class HomeActivity extends BaseActivity<ActivityHomeBinding, HomeViewModel> {

    String ACTION_USB_PERMISSION = "com.eloam.gaopaiyi.USB_PERMISSION";
    private UsbManager usbManager;
    boolean hasPermission = false;
    boolean hasCardModule = false;
    private String sys_remind = "";
    private ProgressDialog mProgressDialog;

    //ActivityLoginBinding类是databinding框架自定生成的,对应activity_login.xml
    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_home;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public HomeViewModel initViewModel() {
//        使用自定义的ViewModelFactory来创建ViewModel，如果不重写该方法，则默认会调用LoginViewModel(@NonNull Application application)构造方法
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(HomeViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        //设置状态栏深浅模式false为浅色
        changeStatusBarLight(true);
    }

    @Override
    public void initData() {
        super.initData();
        String version = CommonUtil.getVersion(this);
        if (!TextUtils.isEmpty(version)) {
            binding.tvVersion.setText("版本号:" + version);
        }

        ((TextView) findViewById(R.id.tv_home)).setVisibility(View.GONE);
        ((TextView) findViewById(R.id.tv_back)).setVisibility(View.GONE);
        Display display = this.getWindowManager().getDefaultDisplay();    //获得默认（本地）的显示设备
        if (SPUtils.getInstance().getString("changeLanguage", "zh").equals("bo")) {
            binding.tvBo.setBackgroundResource(R.drawable.shape_corner5_3c6af4);
            binding.tvBo.setTextColor(getColor(R.color.white));
            binding.tvZh.setBackgroundResource(R.drawable.shape_corner5_white);
            binding.tvZh.setTextColor(Color.parseColor("#333333"));
        } else {
            binding.tvZh.setBackgroundResource(R.drawable.shape_corner5_3c6af4);
            binding.tvZh.setTextColor(getColor(R.color.white));
            binding.tvBo.setBackgroundResource(R.drawable.shape_corner5_white);
            binding.tvBo.setTextColor(Color.parseColor("#333333"));
        }

        binding.tvZh.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!"zh".equals(SPUtils.getInstance().getString("changeLanguage", "zh"))) {
                    recreate();
                }
                SPUtils.getInstance().put("changeLanguage", "zh");
            }
        });

        binding.tvBo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!"bo".equals(SPUtils.getInstance().getString("changeLanguage", "zh"))) {
                    recreate();
                }
                SPUtils.getInstance().put("changeLanguage", "bo");
            }
        });

        initUSB();
        checkEnv();
    }

    @Override
    protected void onResume() {
        super.onResume();
        String macAddress = MacUtils.getMacAddress(HomeActivity.this);
        //检测app升级
        viewModel.upgradeApp(macAddress, com.gc.notarizationpc.util.CommonUtil.getVersionName(HomeActivity.this));
        Log.i("macAddress", "macAddress:" + macAddress);
        if (!TextUtils.isEmpty(macAddress)) {
            viewModel.observableList.clear();
            viewModel.loginByMac(macAddress, HomeActivity.this);
            viewModel.getCfg(macAddress);
        }

    }

    public void initUSB() {
        usbManager = (UsbManager) getSystemService(Context.USB_SERVICE);
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_USB_PERMISSION);
        filter.addAction(UsbManager.ACTION_USB_ACCESSORY_ATTACHED);
        registerReceiver(mUsbReceiver, filter);
        for (UsbDevice device : usbManager.getDeviceList().values()) {
            if (device.getVendorId() == 1024 && device.getProductId() == 50010) {    //身份证模块
                hasCardModule = true;
                if (!usbManager.hasPermission(device)) {
                    Intent intent = new Intent(ACTION_USB_PERMISSION);
                    PendingIntent pendingIntent = PendingIntent.getBroadcast(this, 0, intent, 0);
                    usbManager.requestPermission(device, pendingIntent);
                } else {
                    getPermission();
                }
            }
        }
        if (!hasCardModule) { //没有身份证模块
            getPermission();
            if (sys_remind == null || sys_remind.isEmpty()) {
                sys_remind = getString(R.string.idcard_con_excp);
            } else {
                sys_remind = sys_remind + "\n" + getString(R.string.idcard_con_excp);
            }
        }
    }

    private final BroadcastReceiver mUsbReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (ACTION_USB_PERMISSION.equals(action)) {
                synchronized (this) {
                    UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        Log.e("zzkong", "USB授权成功");
                        getPermission();
                    } else {
                        Log.e("zzkong", "USB授权失败");
                        finish();
                    }
                }
            }
        }
    };

    //环境检查
    private void checkEnv() {
        AppConfig.HIGHPID = "";
        UVCCameraUtil.INSTANCE.initUSBMonitorForPid(this);
        if (AppConfig.RGBPID.equalsIgnoreCase("c013")) {
        } else {
            if (TextUtils.isEmpty(sys_remind)) {
                sys_remind = "当事人摄像头连接异常";
            } else {
                sys_remind = sys_remind + "\n当事人摄像头连接异常";
            }
        }
        initUSB();
        if (!AppConfig.HIGHPID.equals("1054")) {
            if (TextUtils.isEmpty(sys_remind)) {
                sys_remind = "高拍仪连接异常";
            } else {
                sys_remind = sys_remind + "\n高拍仪连接异常";
            }
        }
        String usbname = SPUtils.getInstance().getString(AppConfig.LOCAL_USB_NAME, "");
//        if (!TextUtils.isEmpty(usbname)) {
//            //启用的是usb摄像头
//            if (AppConfig.USBPNAME != usbname) {
//                //环境摄像头连接异常
//                if (TextUtils.isEmpty(sys_remind)) {
//                    sys_remind = "环境摄像头连接异常";
//                } else {
//                    sys_remind = sys_remind + "\n环境摄像头连接异常";
//                }
//            }
////             cameraErroDialog(sys_remind);
//        } else {
        //启用的是萤石云摄像头  调接口查询是否绑定了萤石云摄像头
//            MainPresenter(mActivity).queryYinshiyunByMac(MacUtils.getMacAddress(this), new CommonView() {
//                @Override
//                public void commonSuccess(String msg) {
//                    Log.d(TAG, "mac binded yingshiyun");
//                    if (msg.equals("200"))
//                        cameraErroDialog(sys_remind);
//                    else {
//                        if (TextUtils.isEmpty(sys_remind)) {
//                            sys_remind = "未绑定环境摄像头";
//                        } else {
//                            sys_remind = sys_remind + "\n未绑定环境摄像头";
//                        }
//                        cameraErroDialog(sys_remind);
//                    }
//                }
//
//                @Override
//                public void commonFail() {
//                    cameraErroDialog(sys_remind);
//                }
//        });
//        }
        if (!TextUtils.isEmpty(sys_remind)) {
            toastError(sys_remind);
        }
    }


    public void getPermission() {
        XXPermissions.with(this)
                .permission(Permission.CAMERA) //不指定权限则自动获取清单中的危险权限
                .request(new OnPermission() {
                    @Override
                    public void hasPermission(List<String> granted, boolean isAll) {
                        hasPermission = true;
                    }

                    @Override
                    public void noPermission(List<String> denied, boolean quick) {
                    }
                });
    }


    @Override
    public void initViewObservable() {
        viewModel.getPublicKeySuccess.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean result) {
                if (result) {
                    String macAddress = MacUtils.getMacAddress(HomeActivity.this);
                    Log.i("macAddress", "macAddress:" + macAddress);
                    if (!TextUtils.isEmpty(macAddress)) {
                        viewModel.loginByMac(macAddress, HomeActivity.this);
                        viewModel.getCfg(macAddress);
                    }
                    //检测app升级
                    viewModel.upgradeApp(macAddress, com.gc.notarizationpc.util.CommonUtil.getVersionName(HomeActivity.this));

                } else {
                    toastError("获取公钥失败！");
                }
            }
        });
        viewModel.macAddressRegisterFail.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean result) {
                startActivity(new Intent(HomeActivity.this, DeviceRegisterActivity.class));
            }
        });

        //获取配置报错
        viewModel.getCfgFail.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String response) {
                toastError(response);
            }
        });

        viewModel.linkToWebView.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String url) {
                Intent intent = new Intent(HomeActivity.this, WebViewActivity.class);
                intent.putExtra("linkUrl", url);//http://debugtbs.qq.com
                startActivity(intent);
            }
        });

        viewModel.upgradeResponse.observe(this, new Observer<UpgradeResponse>() {
            @Override
            public void onChanged(UpgradeResponse upgradeResponse) {
                //展示升级提示框
                if (upgradeResponse != null) {
                    showDialogUpdate(upgradeResponse);
                }
            }
        });

    }

    AlertDialog alertDialog = null;

    public void showDialogUpdate(UpgradeResponse upgradeResponse) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setCancelable(false); //开启强制更新，无法关闭
        builder.setTitle(getString(R.string.version_update)).
                // 设置提示框的图标
//                        setIcon(R.mipmap.ic_launcher).
                // 设置要显示的信息
                        setMessage(upgradeResponse.getRenewExplain()).
                // 设置确定按钮
                        setPositiveButton(getString(R.string.update), (dialog, which) -> {
                    if (alertDialog != null && alertDialog.isShowing()) {
                        alertDialog.dismiss();
                        if (!TextUtils.isEmpty(upgradeResponse.getApkPackageUrl())) {
                            downloadApk(upgradeResponse.getApkPackageUrl());
                        }

                    }
                });
        // 生产对话框
        alertDialog = builder.create();
        // 显示对话框
        if (!alertDialog.isShowing()) {
            alertDialog.show();
        }

        initProgress();
    }

    private void initProgress() {
        mProgressDialog = new ProgressDialog(this);
        mProgressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        mProgressDialog.setProgressDrawable(this.getDrawable(R.drawable.common_progressdialog_progressbar_background));
        mProgressDialog.setCancelable(false);
    }

    String path;

    private void downloadApk(String url) {
        path = getFilesDir() + "";
        try {
            path = isExistDir(path);
        } catch (Exception ex) {
            toastError(getString(R.string.save_path_error));
            return;
        }
        String filePath = System.currentTimeMillis() + ".apk";
//        try {
//            if (!new File(filePath).exists()) {
//                new File(filePath).createNewFile();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        if (mProgressDialog != null) {
            mProgressDialog.show();
        }
        DownLoadManager.getInstance().load(url, new ProgressCallBack<ResponseBody>(path, filePath) {
            @Override
            public void onStart() {
                super.onStart();
            }

            @Override
            public void onCompleted() {
                mProgressDialog.dismiss();
            }

            @Override
            public void onSuccess(ResponseBody responseBody) {
                toastSuccess(getString(R.string.apk_download_complete));
                mProgressDialog.dismiss();
                installApk(path + "/" + filePath);
                Log.e("Test", path + "/" + filePath);
            }

            @Override
            public void progress(final long progress, final long total) {
                double percentage = ((double) progress / total) * 100;
                mProgressDialog.setProgress((int) percentage);
                mProgressDialog.setSecondaryProgress((int) percentage);
            }

            @Override
            public void onError(Throwable e) {
                e.printStackTrace();
                toastError(getString(R.string.upgrade_error));
                mProgressDialog.dismiss();
            }
        });
    }

    /**
     * 安装Apk
     */
    public void installApk(String filePath) {
        try {
            File apkFile = new File(filePath);
            Uri data = null;
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                data = FileProvider.getUriForFile(this, getPackageName() + ".fileprovider", apkFile);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            } else {
                data = Uri.fromFile(apkFile);
            }
            intent.setDataAndType(data, "application/vnd.android.package-archive");
            startActivity(intent);
        } catch (Exception ex) {
            ex.printStackTrace();
//            Toasty.error(mContext, ex.getMessage());
        }
    }

    private String isExistDir(String saveDir) throws IOException, IOException {
        File downloadFile = new File(saveDir);
        if (!downloadFile.mkdirs()) {
            downloadFile.createNewFile();
        }
        String savePath = downloadFile.getAbsolutePath();
        return savePath;
    }


    public void getVersion(View view) {

    }

    /**
     * 自助办证
     *
     * @param view
     */
    public void goToBZCX(View view) {
//        startActivity(SelfServiceCertificateHomeActivity.class);
        startActivity(CompariseAcitivity.class);
    }

    public void goToSPBZ(View view) {
        Intent intent = new Intent(this, IDCardCompariseMobileActivity.class);
        intent.putExtra("from", com.gc.notarizationpc.util.CommonUtil.SOURCE_FROM_SPBZ);
        startActivity(intent);
    }

    private ImagePreview.LoadStrategy loadStrategy = ImagePreview.LoadStrategy.Default;

    public void goToYYDJ(View view) {
//        toastInfo(getString(R.string.stay_tuned));
        // 普通图片1：
        Intent intent = new Intent(this, IDCardCompariseMobileActivity.class);
        intent.putExtra("from", com.gc.notarizationpc.util.CommonUtil.SOURCE_FROM_YYDJ);
        startActivity(intent);
    }

    public void goToZZBZ(View view) {
        startActivity(InquiryTypeHomeActivity.class);
//        startActivity(SelfServiceCertificateHomeActivity.class);
    }

    public void goToSPLX(View view) {
        Intent intent = new Intent(this, VideoConnectionActivity.class);
        intent.putExtra("from", com.gc.notarizationpc.util.CommonUtil.SOURCE_FROM_SPLX);
        startActivity(intent);
    }

}
