package com.gc.notarizationpc.ui.fragment

import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.example.framwork.ricyclerview.GridSpacingItemDecoration
import com.example.framwork.utils.DensityUtil
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.AppConfig
import com.gc.notarizationpc.common.BaseFragment
import com.gc.notarizationpc.model.ImageBean
import com.gc.notarizationpc.model.MaterialInfo
import com.gc.notarizationpc.ui.TakePhotoActivity
import com.gc.notarizationpc.ui.adapter.VideoImageAdapter
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter
import kotlinx.android.synthetic.main.fragment_video_step_two.*

class VideoStepTwoFragment(updateCallBack: IMaterialUpdateCallBack?) : BaseFragment(), NotarizationPresenter.IUpdateImageView {
    private var adapter: VideoImageAdapter? = null
    private var updateP: NotarizationPresenter? = null
    private var updateCallBack: IMaterialUpdateCallBack? = updateCallBack
    private var TAG = "VideoStepTwoFragment"
    private var lastClickTime = 0L
    // 两次点击间隔不能少于1000ms
    private val FAST_CLICK_DELAY_TIME = 1000
    constructor(): this(null)
    override fun lazyInit(view: View, savedInstanceState: Bundle) {}
    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {
        Log.i(TAG, "initViewsAndEvents")
        updateP = NotarizationPresenter(mActivity, this)
        takeBtn.setOnClickListener {
            if (System.currentTimeMillis() - lastClickTime >= FAST_CLICK_DELAY_TIME) {
                Log.i(TAG, "takePhotoClick")
                readyGo(TakePhotoActivity::class.java)
                lastClickTime = System.currentTimeMillis()
            } else {
                Log.i(TAG, "double takePhotoClick")
            }

        }
        adapter = VideoImageAdapter()
        rvPhoto.layoutManager = GridLayoutManager(mActivity, 4)
        rvPhoto.addItemDecoration(GridSpacingItemDecoration(4, DensityUtil.getInstance().dip2px(context, 15F), false))
        rvPhoto.adapter = adapter
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.fragment_video_step_two
    }


    fun showPhoto(path: Bitmap?) {
        Log.i(TAG, "showPhoto$path")
        adapter?.addData(path)
        updateP?.updateBitmap(path)
    }

    fun removePhoto(index: Int) {
        Log.i(TAG, "removePhoto$index")
        if (index >= 0)
            adapter?.remove(index)
    }

    override fun updateImageSuccess(bean: ImageBean?) {
        var imageInfo = MaterialInfo(bean?.unitGuid, bean?.filePath)
        updateCallBack?.updateMaterialSuccess(imageInfo)
    }

    override fun updateImageFail() {
        adapter?.itemCount?.minus(1)?.let { adapter?.remove(it) }
    }

    interface IMaterialUpdateCallBack {
        fun updateMaterialSuccess(bean: MaterialInfo?)
    }

}