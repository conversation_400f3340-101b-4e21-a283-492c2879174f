package com.gc.notarizationpc.ui.view;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.gc.mininotarization.R;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.utils.RegexUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;

public class InputPhoneNumberAlert extends Dialog {
    private Context mContext;
    private EventsListener resultListener;

    public InputPhoneNumberAlert(@NonNull Context context, EventsListener eventsListener) {
        super(context);
        mContext = context;
        resultListener = eventsListener;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.dialog_input_phone);
        initView();

    }

    private void initView() {
        EditText phoneTextView = findViewById(R.id.dialog_input_phone_edit_text);
        EditText verifyCodeTextView = findViewById(R.id.dialog_input_phone_verifycode_edit_text);
        Button cancelBtn = findViewById(R.id.dialog_input_phone_cancelButton);
        Button confirmBtn = findViewById(R.id.dialog_input_phone_confirmButton);
        Button verifyCodeBtn = findViewById(R.id.dialog_input_phone_verifyButton);
        TextView agreeTextView = findViewById(R.id.dialog_input_phone_agreement_text);
        CheckBox agreeCheckBox = findViewById(R.id.dialog_input_phone_checkBox);
        phoneTextView.setFocusable(true);
        phoneTextView.setFocusableInTouchMode(true);
        phoneTextView.requestFocus();
        verifyCodeTextView.setFocusable(true);
        verifyCodeTextView.setFocusableInTouchMode(true);
        verifyCodeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(phoneTextView.getText().toString()) && phoneTextView.getText().toString().length() != 11) {
                    ToastUtils.showLong(Utils.getContext().getString(R.string.pleaseInputCorrectPhoneNumber));
                    return;
                }
                BlockPuzzleDialog blockPuzzleDialog = new BlockPuzzleDialog(mContext);
                blockPuzzleDialog.show();
                blockPuzzleDialog.setOnResultsListener(new BlockPuzzleDialog.OnResultsListener() {
                    @Override
                    public void onResultsClick(String result) {
                        verifyCodeBtn.setEnabled(false);
                        verifyCodeBtn.setText("60s");
                        verifyCodeBtn.setBackground(mContext.getResources().getDrawable(R.drawable.shape_corner5_e8e8e8));
                        CountDownTimer timer = new CountDownTimer(60000, 1000) {
                            @SuppressLint("SetTextI18n")
                            @Override
                            public void onTick(long millisUntilFinished) {
                                verifyCodeBtn.setText(millisUntilFinished / 1000 + "s");
                            }

                            @Override
                            public void onFinish() {
                                verifyCodeBtn.setEnabled(true);
                                verifyCodeBtn.setText(mContext.getResources().getString(R.string.getVerfyCode));
                                verifyCodeBtn.setBackground(mContext.getResources().getDrawable(R.drawable.shape_corner5_3c6af4));
                            }
                        };
                        timer.start();
                        resultListener.verifyCodeEvent(phoneTextView.getText().toString());
                    }
                });
//                }
                //获取验证码
            }
        });
        agreeTextView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //查看协议
                resultListener.readAgreeEvent(agreeCheckBox.isChecked());
            }
        });
        agreeCheckBox.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (agreeCheckBox.isChecked()) {
                    agreeCheckBox.setBackground(mContext.getResources().getDrawable(R.mipmap.cb_press));
                } else {
                    agreeCheckBox.setBackground(mContext.getResources().getDrawable(R.mipmap.cb_normal));
                }
            }
        });
        cancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                resultListener.cancelEvent();
            }
        });
        confirmBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Map<String, String> map = new HashMap<>();
                map.put("phone", phoneTextView.getText() == null ? "" : phoneTextView.getText().toString());
                map.put("verifyCode", verifyCodeTextView.getText() == null ? "" : verifyCodeTextView.getText().toString());
                map.put("agree", agreeCheckBox.isChecked() ? "1" : "0");
                resultListener.decideEvent(map);
            }
        });
    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            //do something.
            return true;
        } else {
            return super.dispatchKeyEvent(event);
        }
    }

    public interface EventsListener<T> {
        void verifyCodeEvent(T value);

        void readAgreeEvent(T value);

        void cancelEvent();

        void decideEvent(T value);
    }
}
