package com.gc.notarizationpc.ui.viewmodel;

import android.graphics.drawable.Drawable;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.databinding.ObservableField;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.MenuInfo;
import com.gc.notarizationpc.data.model.response.CfgResponse;

import me.goldze.mvvmhabit.base.AppManager;
import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.greendao.model.LoginInfo;

/**
 * 首页 快捷入口 列表item viewmodel
 */

public class HomeQuickListItemViewModel extends ItemViewModel<HomeViewModel> {
    public ObservableField<CfgResponse.FunctionVoListDTO> entity = new ObservableField<>();
    public Drawable drawableImg;

    public HomeQuickListItemViewModel(@NonNull HomeViewModel viewModel, CfgResponse.FunctionVoListDTO entity) {
        super(viewModel);
        this.entity.set(entity);
    }

    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
    public int getPosition() {
        return viewModel.getItemPosition(this);
    }

    //条目的点击事件
    public BindingCommand itemClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            for (int i = 0; i < viewModel.observableList.size(); i++) {
                HomeQuickListItemViewModel netWorkItemViewModel = viewModel.observableList.get(i);
                if (netWorkItemViewModel.entity.get().getId() == entity.get().getId()) {
                    netWorkItemViewModel.entity.get().setChoose(true);
                    viewModel.observableList.set(i,netWorkItemViewModel);
                } else {
                    netWorkItemViewModel.entity.get().setChoose(false);
                    viewModel.observableList.set(i,netWorkItemViewModel);
                }
            }

            viewModel.goToWebview(entity.get().getLinkUrl());
        }
    });


}
