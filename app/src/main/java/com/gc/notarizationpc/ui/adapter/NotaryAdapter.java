package com.gc.notarizationpc.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.MediaController;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.gc.notarizationpc.R;
import com.gc.notarizationpc.model.NotaryInfo;
import com.gc.notarizationpc.ui.SelectNotary;

import java.util.ArrayList;

import es.dmoral.toasty.Toasty;

public class NotaryAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private Context mContext;
    private ArrayList<NotaryInfo> infoList = new ArrayList<>();

    public NotaryAdapter(Context context, ArrayList<NotaryInfo> infoList) {
        mContext = context;
        this.infoList = infoList;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(mContext).inflate(R.layout.notary_status_item_layout, parent, false);
        return new NormalHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        NormalHolder normalHolder = (NormalHolder) holder;
        int pos = normalHolder.getAdapterPosition();
        if (infoList != null && infoList.size() != 0 && infoList.get(pos) != null) {
            //
//            TextView notary_name;//公证员姓名
//            TextView notary_status;//公证员忙碌状态 0 在线 1 忙碌 2 离开 3 离线
//            ImageView notary_status_image;//公证员在线/忙碌图片
//            Button notary_start_notarization;//发起公证
            normalHolder.notary_name.setText(infoList.get(pos).getUserName());
            if (infoList.get(pos).getCurrentState() == 0) {
                normalHolder.notary_status.setText("状态：空闲");
                normalHolder.notary_status_image.setBackgroundResource(R.mipmap.notaryonline);
//                normalHolder.notary_start_notarization.setBackgroundResource(R.drawable.btn_login_bg);
                normalHolder.notary_start_notarization.setEnabled(true);
                normalHolder.notary_start_notarization.setOnClickListener(v -> {
                    //发起公证
                    if (mContext instanceof SelectNotary)
                        ((SelectNotary) mContext).sendNotarization(infoList.get(pos));
                });
            } else {
                normalHolder.notary_status.setText("状态：忙碌");
                normalHolder.notary_status_image.setBackgroundResource(R.mipmap.notaryoffline);
                normalHolder.notary_start_notarization.setEnabled(false);
            }


        }

    }

    @Override
    public int getItemCount() {
        return infoList.size();
    }

    public class NormalHolder extends RecyclerView.ViewHolder {

        TextView notary_name;//公证员姓名
        TextView notary_status;//公证员忙碌状态
        ImageView notary_status_image;//公证员在线/忙碌图片
        Button notary_start_notarization;//发起公证

        @SuppressLint("StringFormatMatches")
        public NormalHolder(View itemView) {
            super(itemView);
            setIsRecyclable(false);//资源允许复用
            notary_name = itemView.findViewById(R.id.notary_name);
            notary_status = itemView.findViewById(R.id.notary_status);
            notary_status_image = itemView.findViewById(R.id.notary_status_image);
            notary_start_notarization = itemView.findViewById(R.id.notary_start_notarization);

        }
    }

    public void setNewData(ArrayList<NotaryInfo> infoList) {
        this.infoList = infoList;
        notifyDataSetChanged();
    }

}
