package com.gc.notarizationpc.ui.adapter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.response.FeeInfoResponse;
import com.tencent.liteav.base.Log;

import java.math.BigDecimal;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseAdapter;
import me.goldze.mvvmhabit.base.BaseViewHolder;

public class FeeInfoAdapter extends BaseAdapter<FeeInfoResponse> {

    private List<FeeInfoResponse> data;

    public void setData(List<FeeInfoResponse> data) {
        this.data = data;
    }

    public FeeInfoAdapter(Context context, int itemLayoutRes, @Nullable Object type) {
        super(context, itemLayoutRes, type);
        mContext = context;
    }

    @Override
    public void bind(@NonNull BaseViewHolder viewHolder, FeeInfoResponse itemData, int position) {
        if (itemData == null) {
            return;
        }
        itemData = data.get(position);
        viewHolder.setText(R.id.tv_notary_item, itemData.getMattersName());
        viewHolder.setText(R.id.tv_item, itemData.getBillingItemName());
        Log.e("Test",new BigDecimal(itemData.getAmountReceivable()).divide(new BigDecimal("100")).toString()+"-------"+new BigDecimal((itemData.getAmountReceivable())).toString());
        viewHolder.setText(R.id.tv_price, mContext.getString(R.string.chinese_unit_with_value, new BigDecimal(itemData.getAmountReceivable()).divide(new BigDecimal("100")).toString()));//将分转换为元
    }

    @Override
    public int viewType(FeeInfoResponse itemData) {
        return 0;
    }

    public interface Onclick {
        void OnClicklistener(int position, int flag);
    }

    Onclick mOnclick;

    public void setOnClick(Onclick onClick) {
        this.mOnclick = onClick;
    }

}
