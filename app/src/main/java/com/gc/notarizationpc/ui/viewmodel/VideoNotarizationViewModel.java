package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;

import me.goldze.mvvmhabit.base.BaseViewModel;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui.viewmodel
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/1/12
 */
public class VideoNotarizationViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public VideoNotarizationViewModel.UIChangeObservable uc = new VideoNotarizationViewModel.UIChangeObservable();

    public class UIChangeObservable {

    }

    public VideoNotarizationViewModel(@NonNull Application application) {
        super(application);
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}

