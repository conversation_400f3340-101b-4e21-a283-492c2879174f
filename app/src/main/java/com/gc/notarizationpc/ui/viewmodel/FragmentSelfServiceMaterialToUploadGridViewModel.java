package com.gc.notarizationpc.ui.viewmodel;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableList;

import com.alibaba.android.arouter.utils.TextUtils;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.response.ListRequiredMaterialResponse;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * 这个是自助办证流程中上传材料fragment的viewModel
 */
public class FragmentSelfServiceMaterialToUploadGridViewModel extends ItemViewModel<FragmentSelfServiceUploadMaterialViewModel> {
    public ObservableField<ListRequiredMaterialResponse.MaterialTypeVoListDTO> entity = new ObservableField<>();

    //给RecyclerView添加ObservableList
    public ObservableList<FragmentSelfServiceMaterialToUploadGridItemViewModel> childObservableList = new ObservableArrayList<>();
    //给RecyclerView添加ItemBinding
    public ItemBinding<FragmentSelfServiceMaterialToUploadGridItemViewModel> childItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_self_service_material_to_upload);//这里指定了item的

    public FragmentSelfServiceMaterialToUploadGridViewModel(@NonNull FragmentSelfServiceUploadMaterialViewModel viewModel, ListRequiredMaterialResponse.MaterialTypeVoListDTO listRequiredMaterialResponse) {
        super(viewModel);
        this.entity.set(listRequiredMaterialResponse);
    }

    //点击具体材料
    public void childItemClick(ListRequiredMaterialResponse.MaterialVoListDTO materialVoListDTO) {
        if (TextUtils.isEmpty(materialVoListDTO.getFileUrl())) {
            //新增拍照
            Map<String, Object> param = new HashMap<>();
            param.put("flag", 1001);
            param.put("parent_pos", getItemPosition());
            param.put("value", materialVoListDTO);
            viewModel.childItemClickEvent.setValue(param);
        } else {
            //图片放大
            Map<String, Object> param = new HashMap<>();
            param.put("flag", 1002);
            param.put("parent_pos", getItemPosition());
            param.put("value", materialVoListDTO);
            viewModel.childItemClickEvent.setValue(param);
        }
    }

    //点击删除某个材料
    public void delItemClick(ListRequiredMaterialResponse.MaterialVoListDTO materialVoListDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put("flag", 1003);
        param.put("parent_pos", getItemPosition());
        param.put("value", materialVoListDTO);
        viewModel.childItemClickEvent.setValue(param);
    }


    /**
     * 获取条目下标
     *
     * @return
     */
    public int getItemPosition() {
        return viewModel.observableList.indexOf(this);
    }

    public int getItemCount() {
        return viewModel.observableList.size();
    }
}
