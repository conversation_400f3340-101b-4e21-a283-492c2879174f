package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.LoginResponse;
import com.gc.notarizationpc.data.model.response.UpgradeResponse;
import com.gc.notarizationpc.util.CommonUtil;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * mac地址注册 viewmodel
 */
public class DeviceRegisterViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<Boolean> macAddressRegisterFail = new SingleLiveEvent<Boolean>();
    public SingleLiveEvent<Boolean> macAddressRegisterSuccess = new SingleLiveEvent<Boolean>();
    public SingleLiveEvent<String> getCfgFail = new SingleLiveEvent<String>();
    public SingleLiveEvent<String> linkToWebView = new SingleLiveEvent<String>();
    public SingleLiveEvent<UpgradeResponse> upgradeResponse = new SingleLiveEvent<UpgradeResponse>();

    public class UIChangeObservable {

    }

    public DeviceRegisterViewModel(@NonNull Application application) {
        super(application);
    }

    //获取系统配置
    public void upgradeApp(String macAddress, String currentVersion) {
        showDialog();
        Map<String, String> param = new HashMap<>();
        param.put("macAddress", macAddress);
        param.put("uniqueIdentity", "14f5f9872e9f");
        RequestUtil.upgradeApp(param, new MyObserver<UpgradeResponse>() {
            @Override
            public void onSuccess(UpgradeResponse result) {
                dismissDialog();
                if (result != null) {
                    try {
                        String latestVersion = result.getThisTimeVersion();
                        if (CommonUtil.compareVersion(latestVersion, currentVersion) > 0) {
                            Log.d("Test", "更新检测：showDialogUpdate");
                            upgradeResponse.setValue(result);
                        }
                    } catch (Exception ex) {
                        toastError(Utils.getContext().getString(R.string.autoUpdate) + ":" + ex.getMessage());
                    }

                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                getCfgFail.setValue(errorMsg);
            }
        });

    }
    /**
     * 根据mac地址登录
     */
    public void loginByMac(String macAddress) {
        showDialog();
        RequestUtil.loginByMac(macAddress, new MyObserver<LoginResponse>() {
            @Override
            public void onSuccess(LoginResponse result) {
                dismissDialog();
                Log.i("loginByMac", "onSuccess: userId-----" + result.getUserId());
                if (result != null) {
                    SPUtils.getInstance().put("access_token", result.getAccessToken());
                    SPUtils.getInstance().put("token_type", result.getTokenType());
                    SPUtils.getInstance().put("userId", result.getUserId());
                    macAddressRegisterSuccess.setValue(true);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                macAddressRegisterFail.setValue(false);
            }
        });
    }




}
