package com.gc.notarizationpc.ui.viewmodel;

import android.graphics.drawable.Drawable;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.databinding.ObservableField;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.response.ListDocumentResponse;
import com.gc.notarizationpc.data.model.response.OrderAppointmentListModel;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 */
public class SelfServiceReadAndSignDocumentGridItemViewModel extends ItemViewModel<FragmentSelfServiceReadAndSignViewModel> {

    public ObservableField<String> entity = new ObservableField<>();
    public Drawable drawableImg;

    public SelfServiceReadAndSignDocumentGridItemViewModel(@NonNull FragmentSelfServiceReadAndSignViewModel viewModel, String listDocumentResponse) {
        super(viewModel);
        Log.e("FragmentGridItemViewModel", "FragmentGridItemViewModel");
        this.entity.set(listDocumentResponse);
        drawableImg = ContextCompat.getDrawable(viewModel.getApplication(), R.mipmap.erroimage);

    }

    /**
     * // 放大
     */
    public BindingCommand amplifyDoc = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.amplifyDoc(entity.get());
        }
    });
}
