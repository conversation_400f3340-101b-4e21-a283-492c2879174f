package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.SelfServiceOrderListModel;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * Created by goldze on 2017/7/17.
 */

public class SelfServiceOrderGridItemViewModel extends ItemViewModel<SelfServiceOrderGridViewModel> {


    public ObservableField<SelfServiceOrderListModel.DataListDTO> entity = new ObservableField<>();

    // 公证类型
   public String itemGridNotaryOrderNotaryTypeString ;

    // 订单状态
    public String itemGridNotaryOrderNotaryStatusString;

    // 订单状态颜色
    public Integer itemGridNotaryOrderNotaryStatusColor;

    // 订单状态字体颜色
    public Integer itemGridNotaryOrderNotaryStatusTextColor;

    // 删除按钮状态
    public Integer itemGridNotaryDelete;

    // 补充文书状态
    public Integer itemGridNotarySupplyMaterialsStatus;

    //继续填写
    public Integer itemGridNotaryContinueWrite;

    //阅读文书
    public Integer itemGridNotaryReadDocument;




    public SelfServiceOrderGridItemViewModel(@NonNull SelfServiceOrderGridViewModel viewModel, SelfServiceOrderListModel.DataListDTO entity) {
        super(viewModel);
        this.entity.set(entity);

        //ImageView的占位图片，可以解决RecyclerView中图片错误问题
//        drawableImg = ContextCompat.getDrawable(viewModel.getApplication(), R.mipmap.ic_launcher);
    }



    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
    public int getPosition() {
        return viewModel.getItemPosition(this);
    }

    public  int getCount(){
        return viewModel.getItemCount();
    }

    /**
     * 进入详情
     */
    public BindingCommand itemClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            Log.i("FragmentGridItemViewModel","FragmentGridItemViewModel itemClick");
            viewModel.itemClick(entity.get());
        }
    });

    /**
     * 继续填写
     */
    public BindingCommand continueWrite = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.continueWrite(entity.get());
        }
    });

    /**
     * 补充材料
     */
    public BindingCommand supplementaryMaterialsClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.supplementaryMaterials(entity.get());
        }
    });

    /**
     * 删除
     */
    public BindingCommand delete = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.delete(entity.get());
        }
    });

    /**
     * 阅读文书
     */
    public BindingCommand readDocument = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.readDocument(entity.get());
        }
        });

}
