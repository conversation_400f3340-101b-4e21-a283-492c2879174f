package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.gc.notarizationpc.ui.HomeActivity;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;

/**
 * 首页快捷入口 viewmodel
 */

public class ElectronicRulesViewModel extends BaseViewModel {

    public SingleLiveEvent<Boolean> singleDialogLiveEvent = new SingleLiveEvent<Boolean>();

    public ElectronicRulesViewModel(@NonNull Application application) {
        super(application);
    }

    //给RecyclerView添加ObservableList
    public ObservableList<HomeQuickListItemViewModel> observableList = new ObservableArrayList<>();

    @Override
    public void onDestroy() {
        super.onDestroy();
    }


    /**
     *返回
     */
    public BindingCommand<Object> backEvent = new BindingCommand<Object>(new BindingAction() {
        @Override
        public void call() {
            finish();

        }
    });

    /**
     * 返回首页
     */
    public BindingCommand<Object> homeEvent = new BindingCommand<Object>(new BindingAction() {
        @Override
        public void call() {
            startActivity(HomeActivity.class);
        }
    });




}
