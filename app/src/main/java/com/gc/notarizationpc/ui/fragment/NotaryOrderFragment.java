package com.gc.notarizationpc.ui.fragment;

import static me.goldze.mvvmhabit.utils.ToastUtils.toastError;
import static me.goldze.mvvmhabit.utils.ToastUtils.toastInfo;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentGridviewBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.common.CommonRequest;
import com.gc.notarizationpc.data.model.request.SignDocRequest;
import com.gc.notarizationpc.data.model.response.DocInfoResponse;
import com.gc.notarizationpc.data.model.response.MaterialListResponse;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.data.model.response.VideoOrderListModel;
import com.gc.notarizationpc.ui.video.CounselingRoomActivity;
import com.gc.notarizationpc.ui.view.CustomerFooter;
import com.gc.notarizationpc.ui.view.SignDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationApplyInfoDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationLawDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationMaterialDialog;
import com.gc.notarizationpc.ui.viewmodel.FragmentGridViewModel;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.websocket.MyMqttService;

import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseFragment;
import me.goldze.mvvmhabit.utils.ConvertUtils;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.BR;

public class NotaryOrderFragment extends BaseFragment<FragmentGridviewBinding, FragmentGridViewModel> {

    private VideoNotarizationApplyInfoDialog dialog;

    private VideoNotarizationLawDialog lawDialog;

    private MyMqttService myMqttService;

    private VideoOrderListModel.DataListDTO mDataListDTO;
    private SignDialog signDialog;

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_gridview;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public void initParam() {
        super.initParam();

    }

    @Override
    public FragmentGridViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getActivity().getApplication());

        return ViewModelProviders.of(this, factory).get(FragmentGridViewModel.class);
    }

    @Override
    public void initData() {
        super.initData();
        getArguments().getInt("type");
        viewModel.mPhoneNumber.set((String) getArguments().get("phoneNumber"));
        viewModel.mIdCardNumber.set((String) getArguments().get("idCard"));
        binding.twinklingRefreshLayout.setBottomView(new CustomerFooter(getContext(), false));
    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();

        viewModel.refreshEvent.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer aBoolean) {
                if (aBoolean == 0) {
                    binding.twinklingRefreshLayout.finishRefreshing();
                }
                if (aBoolean == 1) {
                    binding.twinklingRefreshLayout.finishLoadmore();
                }
                if (aBoolean == 2) {
                    binding.twinklingRefreshLayout.setBottomView(new CustomerFooter(getContext(), true));
                }
            }
        });
//        viewModel.comeIntoOrderDetail.observe(this, new Observer<Boolean>() {
//            @Override
//            public void onChanged(Boolean aBoolean) {
//                if (aBoolean){
//                    startActivity(OrderDetailActivity.class);
//                }
//            }
//    });
        viewModel.applyMaterialEvent.observe(this, new Observer<VideoOrderListModel.DataListDTO>() {
            @Override
            public void onChanged(VideoOrderListModel.DataListDTO dataListDTO) {
                mDataListDTO = dataListDTO;
                showProgress(false, getString(R.string.loading) + "...");
                CommonRequest.listMaterialByCaseInfoId(dataListDTO.getId(), new PopwindowUtil.ResultSecondListener() {
                    @Override
                    public void result(Object value) {
                        hideProgress();
                        if (value != null) {
                            List<MaterialListResponse> materialListResponses = (List<MaterialListResponse>) value;
                            hideProgress();
                            VideoNotarizationMaterialDialog materialDialog = new VideoNotarizationMaterialDialog(getContext(), dataListDTO.getId(), "03", materialListResponses, CommonUtil.SOURCE_FROM_SPBZ, new PopwindowUtil.ResultThirdListener() {
                                @Override
                                public void result(Object value) {

                                }

                                @Override
                                public void secondResult(Object value) {

                                }

                                @Override
                                public void thirdResult(Object value) {

                                }
                            });
                            if (!materialDialog.isShowing()) {
                                materialDialog.show();
                                CommonRequest.settingDialog(materialDialog, getContext());
                            }
                        } else {
                            toastInfo(Utils.getContext().getString(R.string.theMaterialToBeUploadedIsNotFound));
                        }


                    }

                    @Override
                    public void secondResult(Object value) {
                        hideProgress();
                        ToastUtils.showShort(value == null ? Utils.getContext().getString(R.string.failedToLoadTheMaterialInformation) : value.toString());
                    }
                });
            }

        });

        viewModel.readDocumentsEvent.observe(this, new Observer<VideoOrderListModel.DataListDTO>() {
            @Override
            public void onChanged(VideoOrderListModel.DataListDTO dataListDTO) {
                mDataListDTO = dataListDTO;
                readDocument(dataListDTO);
            }
        });
        viewModel.enterRoomEvent.observe(this, new Observer<VideoOrderListModel.DataListDTO>() {
            @Override
            public void onChanged(VideoOrderListModel.DataListDTO dataListDTO) {
//                showProgress(false,"连接中...");
                mDataListDTO = dataListDTO;
                if (!TextUtils.isEmpty(dataListDTO.getRoomId())) {
                    Intent intent = new Intent(getContext(), CounselingRoomActivity.class);
                    intent.putExtra("roomId", "notary2.0-" + dataListDTO.getRoomId());
                    intent.putExtra("orderId", dataListDTO.getId());
                    intent.putExtra("mechanismName", dataListDTO.getMechanismManageName());
                    intent.putExtra("from", CommonUtil.SOURCE_FROM_ORDER);
                    intent.putExtra("notaryName", dataListDTO.getNotaryName());
                    intent.putExtra("caseInfoId", mDataListDTO.getId());
                    intent.putExtra("notaryId", mDataListDTO.getNotaryId());
                    startActivity(intent);
                } else {
                    toastError(getString(R.string.notReceivedRoomNumber));
                }

            }
        });

        viewModel.networkErrorEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                binding.fragmentGridviewEmpty.setVisibility(View.VISIBLE);
                binding.twinklingRefreshLayout.setVisibility(View.GONE);
            } else {
                binding.fragmentGridviewEmpty.setVisibility(View.GONE);
                binding.twinklingRefreshLayout.setVisibility(View.VISIBLE);
            }
        });

        viewModel.notaryOrderListEvent.observe(this, new Observer<List<VideoOrderListModel.DataListDTO>>() {
            @Override
            public void onChanged(List<VideoOrderListModel.DataListDTO> modelList) {
                viewModel.dealNetWorkData(modelList, getContext());
            }
        });

    }

    /**
     * 阅读文书
     */
    private void readDocument(VideoOrderListModel.DataListDTO dataListDTO) {
        showProgress(false, getString(R.string.loading) + "...");
        CommonRequest.readDocument(dataListDTO.getId(), new PopwindowUtil.ResultSecondListener<List<DocInfoResponse>>() {
            @Override
            public void result(List<DocInfoResponse> value) {
                hideProgress();
                if (value != null && value.size() > 0) {
                    if (lawDialog == null || !lawDialog.isShowing()) {
                        VideoNotarizationLawDialog lawDialog = new VideoNotarizationLawDialog(getContext(), new Handler(), value, dataListDTO.getId(), "", true, new PopwindowUtil.ResultListener<DocInfoResponse>() {
                            @Override
                            public void result(DocInfoResponse value) {
                                showSignDialog(value, dataListDTO);
                            }
                        });
                        if (!lawDialog.isShowing()) {
                            lawDialog.show();
                            Window dialogWindow = lawDialog.getWindow();
                            dialogWindow.getDecorView().setBackgroundColor(getResources().getColor(android.R.color.white));
                            dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
                            // dialogWindow.setWindowAnimations(R.style.mystyle);
                            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
                            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
                            lp.height = WindowManager.LayoutParams.MATCH_PARENT;
                            lp.alpha = 1.0f;
                            dialogWindow.setAttributes(lp);
                            dialogWindow.setGravity(Gravity.RIGHT);
                            lawDialog.setCanceledOnTouchOutside(false);
                        }
                    }
                }
            }

            @Override
            public void secondResult(List<DocInfoResponse> value) {
                hideProgress();
            }
        });
    }

    /**
     * 展示签字
     */
    public void showSignDialog(DocInfoResponse docInfoResponse, VideoOrderListModel.DataListDTO dataListDTO) {
        signDialog = new SignDialog(getContext(), docInfoResponse.getApplicantName(), new PopwindowUtil.ResultSecondListener() {
            @Override
            public void result(Object value) {
                if (value != null) {
                    Bitmap bitmap = (Bitmap) value;
                    showProgress(false, Utils.getContext().getString(R.string.uploadingSignatureFile) + "...");
                    CommonRequest.uploadSignPic(bitmap, new PopwindowUtil.ResultSecondListener() {

                        @Override
                        public void result(Object value) {
                            hideProgress();
                            UploadFileBean uploadFileBean = (UploadFileBean) value;
                            if (value != null) {
                                SignDocRequest signDocRequest = new SignDocRequest();
                                IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(getContext(), "idcardinfo");
                                if (idCardUserInfo != null) {
                                    signDocRequest.setUserId(idCardUserInfo.getUserId());
                                }

                                signDocRequest.setPicFileId(uploadFileBean.getId());
                                signDocRequest.setCaseInfoId(dataListDTO.getId());
                                signDocRequest.setPicFileUrl(uploadFileBean.getSharedUrl());
                                signDocRequest.setIdentityCards(docInfoResponse.getCredentialNum());
                                signDocRequest.setRepresentativeName(docInfoResponse.getApplicantName());
                                List<String> tempList = new ArrayList<>();
                                for (int i = 0; i < docInfoResponse.getDocTypeList().size(); i++) {
                                    if (docInfoResponse.getDocTypeList().get(i) != null) {
                                        for (int j = 0; j < docInfoResponse.getDocTypeList().get(i).getDocumentVOList().size(); j++) {
                                            if (docInfoResponse.getDocTypeList().get(i).getDocumentVOList().get(j).getId() != null)
                                                tempList.add(docInfoResponse.getDocTypeList().get(i).getDocumentVOList().get(j).getId());
                                        }
                                    }
                                }
                                signDocRequest.setIdList(tempList);
                                showProgress(false, Utils.getContext().getString(R.string.uploadingSignatureFile) + "...");
                                CommonRequest.signMultiDocument(signDocRequest, new PopwindowUtil.ResultSecondListener() {
                                    @Override
                                    public void result(Object value) {
                                        hideProgress();
                                        if (signDialog != null && signDialog.isShowing()) {
                                            signDialog.dismiss();
                                        }
                                        readDocument(mDataListDTO);
                                        viewModel.requestNetWork();
                                        ToastUtils.showShort(Utils.getContext().getString(R.string.signSuccess));
                                    }

                                    @Override
                                    public void secondResult(Object value) {
                                        hideProgress();
                                        ToastUtils.showShort(value == null ? Utils.getContext().getString(R.string.signFail) : value.toString());
                                    }
                                });

                            }
                        }

                        @Override
                        public void secondResult(Object value) {
                            hideProgress();
                            ToastUtils.showLong(value == null ? Utils.getContext().getString(R.string.failedToUploadTheSignedFile) : value.toString());
                        }
                    });
                }
            }

            @Override
            public void secondResult(Object value) {

            }
        });
        if (!signDialog.isShowing()) {
            signDialog.show();
            Window dialogWindow = signDialog.getWindow();
            dialogWindow.getDecorView().setBackgroundColor(this.getResources().getColor(android.R.color.white));
            dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
            // dialogWindow.setWindowAnimations(R.style.mystyle);
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = ConvertUtils.dp2px(1000f);
            lp.height = ConvertUtils.dp2px(700f);
            lp.alpha = 1.0f;
            dialogWindow.setAttributes(lp);
            dialogWindow.setGravity(Gravity.CENTER);
            signDialog.setCanceledOnTouchOutside(false);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        viewModel.requestNetWork();
    }
}
