package com.gc.notarizationpc.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentOrderDetailApplyInformationBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.ui.viewmodel.FragmentOrderDetailApplyInformationViewModel;

import me.goldze.mvvmhabit.base.BaseFragment;
import me.tatarka.bindingcollectionadapter2.BR;

public class OrderDetailApplyInformationFragment extends BaseFragment <FragmentOrderDetailApplyInformationBinding, FragmentOrderDetailApplyInformationViewModel>{
    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_order_detail_apply_information;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public FragmentOrderDetailApplyInformationViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getActivity().getApplication());

        return ViewModelProviders.of(this, factory).get(FragmentOrderDetailApplyInformationViewModel.class);
    }

    @Override
    public void initData() {
        super.initData();

    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();

    }
}
