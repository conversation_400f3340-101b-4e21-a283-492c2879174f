package com.gc.notarizationpc.ui.view;

import static me.goldze.mvvmhabit.utils.ToastUtils.toastInfo;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.DocInfoResponse;
import com.gc.notarizationpc.data.model.response.MaterialListResponse;
import com.gc.notarizationpc.ui.adapter.PictureOperationAdapter;
import com.gc.notarizationpc.ui.inquirycertification.InquiryOrderListHomeActivity;
import com.gc.notarizationpc.ui.inquirycertification.OrderDetailActivity;
import com.gc.notarizationpc.ui.selfservicecertificate.CompariseAcitivity;
import com.gc.notarizationpc.ui.video.CounselingRoomActivity;
import com.gc.notarizationpc.ui.video.TakePhotoActivity;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.util.ResultListener;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseAdapter;
import me.goldze.mvvmhabit.base.BaseViewHolder;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.GlideRoundedCornersTransform;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * 受理室 材料上传
 */
public class VideoNotarizationMaterialDialog extends Dialog implements ResultListener {
    private Context mContext;
    private TextView tvScanUpload;
    private RecyclerView rvBooklet, rvContent, rvDegree, rvIdCard, rvParent;
    private ImageView ivDetail;
    private int currentClickIndex;//点击了哪个事项
    private List<MaterialListResponse> materialListResponses;//接口返回的最原始的包括未上传的材料
    private ParentAdatper parentAdatper;
    private String caseInfoId;
    private RelativeLayout rnlPreview;

    private PictureOperationAdapter bookletAdapter, contentAdapter, idcardAdapter;
    private static final int MAX_SELECT_NUMBER = 30;

    // 来源渠道  sourceWay	来源方式 01-调查取得,02-办证过程形成,03-当事人提交原件,04-当事人提交复印件,99-其他
    private String mSourceWay;

    // 当前材料的类型id
//    private List<String> materialDataTypeIdList = new ArrayList<>();

    // 保存一个idlist 方便后续清空时使用
    private List<String> materialIdList = new ArrayList<>();

    private PopwindowUtil.ResultThirdListener mResultListener;

    private int mSourceFrom;

    public static ResultListener takePhotoListener;


    public VideoNotarizationMaterialDialog(Context context, String caseInfoId, String sourceWay, List<MaterialListResponse> materialListResponseList, int from, PopwindowUtil.ResultThirdListener resultListener) {
        super(context);
        mContext = context;
        materialListResponses = materialListResponseList;
        this.caseInfoId = caseInfoId;
        mSourceWay = sourceWay;
        mResultListener = resultListener;
        mSourceFrom = from;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_video_notarization_material);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();

    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        takePhotoListener = this;
        rnlPreview = findViewById(R.id.rnl_preview);
        rvParent = findViewById(R.id.rv_parent);
        tvScanUpload = findViewById(R.id.tv_scan_upload);
        ivDetail = findViewById(R.id.iv_detail);
        findViewById(R.id.iv_close_img).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                rnlPreview.setVisibility(View.GONE);
            }
        });
//        rvContent = findViewById(R.id.rv_gzcontent);
//        rvIdCard = findViewById(R.id.rv_idcard);
//        rvDegree = findViewById(R.id.rv_degree);

        parentAdatper = new ParentAdatper(mContext, R.layout.item_list_law_material_parent, 0);
        rvParent.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false));
        rvParent.setAdapter(parentAdatper);


        childPhotoList.addAll(materialListResponses);
//        for (int i = 0; i < childPhotoList.size(); i++) {
////            materialIdList.add(childPhotoList.get(i).getMaterialVoList().get(0).getMaterialId());
//            materialDataTypeIdList.add(childPhotoList.get(i).getMaterialVoList().get(0).getMaterialTypeId());
//        }
        List<MaterialListResponse> afterMaterialListResponses = new ArrayList<>();

//        //过滤掉未上传的内容
        for (int i = 0; i < materialListResponses.size(); i++) {
            MaterialListResponse materialListResponse = new MaterialListResponse();
            materialListResponse.setMaterialTypeName(materialListResponses.get(i).getMaterialTypeName());
            if (materialListResponses.get(i).getMaterialVoList() != null) {
                List<MaterialListResponse.MaterialVoListDTO> tmpvoList = new ArrayList<>();

                for (int j = 0; j < materialListResponses.get(i).getMaterialVoList().size(); j++) {

                    if (materialListResponses.get(i).getMaterialVoList().get(j) != null && materialListResponses.get(i).getMaterialVoList().get(j).getStatus() != null && materialListResponses.get(i).getMaterialVoList().get(j).getStatus() != -1) {
                        tmpvoList.add(materialListResponses.get(i).getMaterialVoList().get(j));
                    }
                }
                materialListResponse.setMaterialVoList(tmpvoList);
            }
            afterMaterialListResponses.add(materialListResponse);
        }

        parentAdatper.refreshAdapter(afterMaterialListResponses);
        parentAdatper.setOnItemClickListener(new BaseViewHolder.OnItemClickListener<MaterialListResponse>() {
            @Override
            public void onItemClick(BaseViewHolder viewHolder, int position, MaterialListResponse itemData, Object type) {
                currentClickIndex = position;
            }

            @Override
            public void onItemLongClick(BaseViewHolder viewHolder, int position, MaterialListResponse itemData, Object type) {

            }
        });


        findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowing()) {
                    mResultListener.result("");
                    dismiss();
                }
            }
        });

        findViewById(R.id.iv_refresh).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //刷新
                getMaterial();
            }
        });


    }

    List<MaterialListResponse> childPhotoList = new ArrayList<>();
    PictureOperationAdapter currentChildAdapter;


    private class ParentAdatper extends BaseAdapter<MaterialListResponse> {
        public ParentAdatper(Context context, int itemLayoutRes, @Nullable Object type) {
            super(context, itemLayoutRes, type);
            mContext = context;
        }

        @Override
        public void bind(@NonNull BaseViewHolder viewHolder, MaterialListResponse materialListResponse, int position) {
            if (materialListResponse == null) {
                return;
            }
            viewHolder.setText(R.id.tv_name, materialListResponse.getMaterialTypeName());
            RecyclerView rvChild = viewHolder.getView(R.id.rv_child);
            rvChild.setLayoutManager(new GridLayoutManager(mContext, 3));
            PictureOperationAdapter contentAdapter = new PictureOperationAdapter(mContext, R.layout.list_item_operation_picture, "0");
            List<MaterialListResponse.MaterialVoListDTO> childs = new ArrayList<>();
            try {
                for (MaterialListResponse.MaterialVoListDTO materialVoListDTO : childPhotoList.get(position).getMaterialVoList()) {
                    if (materialVoListDTO != null && materialVoListDTO.getStatus() != null && materialVoListDTO.getStatus() != -1) {
                        childs.add(materialVoListDTO);
                    }
                }


                MaterialListResponse.MaterialVoListDTO createDTO = new MaterialListResponse.MaterialVoListDTO("CREATE", "");
                createDTO.setMaterialId(materialListResponses.get(currentClickIndex).getMaterialVoList().get(0).getMaterialId());
                createDTO.setMaterialTypeId(materialListResponses.get(currentClickIndex).getMaterialVoList().get(0).getMaterialTypeId());
                createDTO.setMaterialTypeName(materialListResponse.getMaterialTypeName());
                if (childs != null && childs.size() > 0) {
                    if ((childs.size() > 1 && childs.size() < MAX_SELECT_NUMBER) && "PICTURE".equals(childs.get(childs.size() - 1).getType())) {
                        Log.e("Test", "add  CREATE");
                        childs.add(createDTO);
                    } else if (childs.size() == 1) {
                        if (!"CREATE".equals(childs.get(0).getType())) {
                            childs.add(createDTO);
                        }

                    }
                } else {
                    childs.add(createDTO);
                }
            } catch (Exception e) {
                e.printStackTrace();
                MobclickAgent.reportError(mContext, e);
                Log.e("Test", e.getMessage());
            }
            contentAdapter.refreshAdapter(childs);
            rvChild.setAdapter(contentAdapter);
            contentAdapter.notifyDataSetChanged();
            contentAdapter.setOnItemViewClickListener(new BaseViewHolder.OnItemViewClickListener<MaterialListResponse.MaterialVoListDTO>() {
                @Override
                public void onItemViewClick(BaseViewHolder viewHolder, View view, int pos, MaterialListResponse.MaterialVoListDTO itemData, Object type) {
                    currentClickIndex = position;
                    if (view.getId() == R.id.work_iv_wip_delete) {
                        try {
                            if ("PICTURE".equals(childPhotoList.get(currentClickIndex).getMaterialVoList().get(pos).getType())) {
                                //获取文书
                                if (childPhotoList.get(currentClickIndex).getMaterialVoList().get(pos) == null) {
                                    return;
                                }
                                Map<String, Object> param = new HashMap<>();
                                param.put("caseInfoId", caseInfoId);
                                param.put("materialId", childPhotoList.get(currentClickIndex).getMaterialVoList().get(pos).getMaterialId());
                                param.put("materialTypeId", childPhotoList.get(currentClickIndex).getMaterialVoList().get(pos).getMaterialTypeId());
                                IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(getContext(), "idcardinfo");
                                if (idCardUserInfo != null) {
                                    param.put("userId", idCardUserInfo.getUserId());
                                }
                                RequestUtil.clearMaterialAndReUploadByUser(param, new MyObserver<Object>() {
                                    @Override
                                    public void onSuccess(Object result) {
                                        try {
//                                        childPhotoList.get(currentClickIndex).getMaterialVoList().remove(pos);
//                                        if (pos <= MAX_SELECT_NUMBER - 1 && !"CREATE".equals(childPhotoList.get(currentClickIndex).getMaterialVoList().get(childPhotoList.get(currentClickIndex).getMaterialVoList().size() - 1).getType())) {
//                                            childPhotoList.get(currentClickIndex).getMaterialVoList().add(new MaterialListResponse.MaterialVoListDTO("CREATE", ""));
//                                        }
//                                        contentAdapter.refreshAdapter(childPhotoList.get(currentClickIndex).getMaterialVoList());
                                            getMaterial();

                                            mResultListener.thirdResult("");
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            MobclickAgent.reportError(mContext, e);
                                        }
                                    }

                                    @Override
                                    public void onFailure(Throwable e, String errorMsg) {
                                        ToastUtils.toastError(Utils.getContext().getString(R.string.deleteFailed));
                                    }
                                });
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            MobclickAgent.reportError(mContext, e);
                        }
                    }
                    if (view.getId() == R.id.work_iv_wip_create) {
                        try {
                            List<MaterialListResponse.MaterialVoListDTO> childs = new ArrayList<>();
                            for (MaterialListResponse.MaterialVoListDTO materialVoListDTO : childPhotoList.get(currentClickIndex).getMaterialVoList()) {
                                if (materialVoListDTO != null && materialVoListDTO.getStatus() != null && materialVoListDTO.getStatus() != -1) {
                                    childs.add(materialVoListDTO);
                                }
                            }
                            currentClickIndex = position;
                            currentChildAdapter = contentAdapter;
                            Intent intent = new Intent(mContext, TakePhotoActivity.class);
                            intent.putExtra("caseInforId", caseInfoId);
                            intent.putExtra("sourceWay", mSourceWay);
                            intent.putExtra("materialTypeId", materialListResponses.get(position).getMaterialVoList().get(0).getMaterialTypeId());
                            intent.putExtra("materialId", materialListResponses.get(position).getMaterialVoList().get(0).getMaterialId());
                            intent.putExtra("materialListCount", childs.size());
                            intent.putExtra("from", mSourceFrom);
                            mContext.startActivity(intent);
                        } catch (Exception e) {
                            e.printStackTrace();
                            MobclickAgent.reportError(mContext, e);
                        }

                    } else if (view.getId() == R.id.work_iv_wip_picture) {
//                        if (CommonUtil.fileCanNotPreview(childPhotoList.get(currentClickIndex).getMaterialVoList().get(pos).getSuffixName())) {
//                            ToastUtils.toastError(Utils.getContext().getString(R.string.file_cant_preview));
//                            return;
//                        }
                        rnlPreview.setVisibility(View.VISIBLE);
//                    //查看大图
                        RequestOptions options = new RequestOptions();
                        //圆角
                        options = options.transform(new
                                GlideRoundedCornersTransform(mContext, 6f, GlideRoundedCornersTransform.CornerType.ALL));
//            if (!TextUtils.isEmpty(itemData.getName())) {
                        try {
                            Glide.with(viewHolder.itemView.getContext())
                                    .load(childPhotoList.get(currentClickIndex).getMaterialVoList().get(pos).getFileUrl())
                                    .apply(options).placeholder(R.mipmap.erroimage)
                                    .into(ivDetail);
                        } catch (Exception e) {
                            e.printStackTrace();
                            MobclickAgent.reportError(mContext, e);
                        }

                    }
                }

                @Override
                public void onItemViewLongClick(BaseViewHolder viewHolder, View view,
                                                int position, MaterialListResponse.MaterialVoListDTO itemData, Object type) {

                }


            });
        }

        @Override
        public int viewType(MaterialListResponse itemData) {
            return 0;
        }

    }

    private void getMaterial() {
        showProgress();
        RequestUtil.listMaterialByCaseInfoId(caseInfoId, new MyObserver<List<MaterialListResponse>>() {
            @Override
            public void onSuccess(List<MaterialListResponse> materialVoListDTO) {
                hideProgress();
                if (materialVoListDTO != null) {
                    childPhotoList = materialVoListDTO;
                    materialListResponses = childPhotoList;
                    parentAdatper.refreshAdapter(childPhotoList);
                    parentAdatper.notifyDataSetChanged();

                } else {
                    toastInfo(WorkstatusEnum.PLEASE_UPLOAD_HINT.msg);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                hideProgress();
                toastInfo(WorkstatusEnum.PLEASE_UPLOAD_HINT.msg);
            }
        });
    }

    @Override
    public void result(List<MaterialListResponse.MaterialVoListDTO> pictureInfoBeans) {
        if (pictureInfoBeans != null && pictureInfoBeans.size() > 0) {
            Log.e("Test", "currentClickIndex==" + currentClickIndex);
            try {
                getMaterial();

//                if (childPhotoList.get(currentClickIndex).getMaterialVoList().size() == 1) {
//                    //上一个是status为-1 未上传的占位材料,删除
//                    childPhotoList.get(currentClickIndex).setMaterialVoList(new ArrayList<>());
//                }
//                if (childPhotoList.get(currentClickIndex).getMaterialVoList().size() > 0) {
//                    childPhotoList.get(currentClickIndex).getMaterialVoList().remove(childPhotoList.get(currentClickIndex).getMaterialVoList().size() - 1);
//                }
////                if (childPhotoList.get(currentClickIndex).getMaterialVoList().size() > 0) {
////                    for (int i = 0; i < childPhotoList.get(currentClickIndex).getMaterialVoList().size(); i++) {
////                        for (int j = 0; j < pictureInfoBeans.size(); j++) {
////                            if (pictureInfoBeans.get(j).getMaterialId().equals(childPhotoList.get(currentClickIndex).getMaterialVoList().get(i).getMaterialId())) {
////                                childPhotoList.get(currentClickIndex).getMaterialVoList().remove(i);
////                            }
////                        }
////                    }
////                }
//                childPhotoList.get(currentClickIndex).getMaterialVoList().addAll(pictureInfoBeans);
//                List<MaterialListResponse.MaterialVoListDTO> listDTOS = childPhotoList.get(currentClickIndex).getMaterialVoList();
//                if (childPhotoList.get(currentClickIndex).getMaterialVoList().size() < MAX_SELECT_NUMBER && !childPhotoList.get(currentClickIndex).getMaterialVoList().get(childPhotoList.get(currentClickIndex).getMaterialVoList().size() - 1).getType().equals("CREATE")) {
////                    listDTOS.add(new MaterialListResponse.MaterialVoListDTO("CREATE", ""));
//                    MaterialListResponse.MaterialVoListDTO createDTO = new MaterialListResponse.MaterialVoListDTO("CREATE", "");
//                    createDTO.setStatus(0);
//                    childPhotoList.get(currentClickIndex).getMaterialVoList().add(createDTO);
//                }
//
//                List<MaterialListResponse.MaterialVoListDTO> childs = new ArrayList<>();
//                for (MaterialListResponse.MaterialVoListDTO materialVoListDTO : childPhotoList.get(currentClickIndex).getMaterialVoList()) {
//                    if (materialVoListDTO != null && materialVoListDTO.getStatus() != null && materialVoListDTO.getStatus() != -1) {
//                        childs.add(materialVoListDTO);
//                    }
//                }
//                currentChildAdapter.refreshAdapter(childs);
            } catch (Exception e) {
                e.printStackTrace();
                MobclickAgent.reportError(mContext, e);
            }
            mResultListener.secondResult(materialListResponses.get(currentClickIndex).getMaterialTypeName());
        }
    }

    private void showProgress() {
        if (mContext instanceof CounselingRoomActivity) {
            ((CounselingRoomActivity) mContext).showProgress();
        } else if (mContext instanceof OrderDetailActivity) {
            ((OrderDetailActivity) mContext).showProgress();
        } else if (mContext instanceof InquiryOrderListHomeActivity) {
            ((InquiryOrderListHomeActivity) mContext).showProgress();
        }

    }

    private void hideProgress() {
        if (mContext instanceof CounselingRoomActivity) {
            ((CounselingRoomActivity) mContext).hideProgress();
        } else if (mContext instanceof OrderDetailActivity) {
            ((OrderDetailActivity) mContext).hideProgress();
        } else if (mContext instanceof InquiryOrderListHomeActivity) {
            ((InquiryOrderListHomeActivity) mContext).hideProgress();
        }
    }


    @Override
    public void show() {
        super.show();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }
}
