package com.gc.notarizationpc.ui.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.gc.mininotarization.R;

import java.util.HashMap;
import java.util.Map;

public class InputIdCardNumberAlert extends Dialog {
    private Context mContext;
    private ButtonClickListenerWithBackValue resultListener;

    public InputIdCardNumberAlert(@NonNull Context context,ButtonClickListenerWithBackValue buttonClickListener) {
        super(context);
        mContext = context;
        resultListener = buttonClickListener;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.dialog_input_idcard_information);
        initView();

    }

    private void initView(){
        TextView idCardNumberTextView = findViewById(R.id.dialog_input_id_card_information_id_card);
        EditText idCardText = findViewById(R.id.dialog_input_id_card_information_id_card_edit_text);
        CheckBox checkBox = findViewById(R.id.dialog_input_id_card_checkBox);
        TextView onLineRules = findViewById(R.id.dialog_input_id_card_agreement_text);
        Button cancelBtn = findViewById(R.id.dialog_input_id_card_information_back);
        Button confirmBtn = findViewById(R.id.dialog_input_id_card_information_confirm);
        idCardText.setFocusable(true);
        idCardText.setFocusableInTouchMode(true);
        idCardText.requestFocus();
        onLineRules.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                resultListener.otherEvent(checkBox.isChecked());
            }
        });
        checkBox.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (checkBox.isChecked()) {
                    checkBox.setBackground(mContext.getResources().getDrawable(R.mipmap.cb_press));
                } else {
                    checkBox.setBackground(mContext.getResources().getDrawable(R.mipmap.cb_normal));
                }

            }
        });
        cancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                resultListener.cancel();
            }
        });
        confirmBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Map<String, String> map = new HashMap<>();
                map.put("idCard", idCardText.getText() == null ? "" : idCardText.getText().toString());
                map.put("agree", checkBox.isChecked() ? "1" : "0");
                resultListener.decide(map);
            }
        });

    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_BACK ) {
            //do something.
            return true;
        } else {
            return super.dispatchKeyEvent(event);
        }
    }

    public interface ButtonClickListenerWithBackValue<T> {
        void cancel();

        void decide(T value);

        void otherEvent(T value);
    }
}
