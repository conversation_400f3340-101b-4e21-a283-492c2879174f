package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.NotaryMatterItem;

import me.goldze.mvvmhabit.base.ItemViewModel;

/**
 * Created by goldze on 2017/7/17.
 */

public class SelfServiceSelfFeeItemViewModel extends ItemViewModel<FragmentSelfServiceHasBeenFinishedViewModel> {

    public ObservableField<NotaryMatterItem.MattersInfosDTO> entity = new ObservableField<>();

    public SelfServiceSelfFeeItemViewModel(@NonNull FragmentSelfServiceHasBeenFinishedViewModel viewModel, NotaryMatterItem.MattersInfosDTO entity) {
        super(viewModel);
        Log.e("FragmentGridItemViewModel", "FragmentGridItemViewModel");
        this.entity.set(entity);
    }



}
