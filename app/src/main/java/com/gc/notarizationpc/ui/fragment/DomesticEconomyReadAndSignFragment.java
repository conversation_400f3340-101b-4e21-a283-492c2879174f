package com.gc.notarizationpc.ui.fragment;


import static me.goldze.mvvmhabit.utils.ToastUtils.toastError;
import static me.goldze.mvvmhabit.utils.ToastUtils.toastSuccess;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.utils.TextUtils;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentDomesticEconomyReadAndSignBinding;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.selfservicecertificate.DomesticEconomyNotarizationActivity;
import com.gc.notarizationpc.ui.viewmodel.DomesticEconomyReadAndSignDocumentGridItemViewModel;
import com.gc.notarizationpc.ui.viewmodel.FragmentDomesticEconomyReadAndSignViewModel;
import com.gc.notarizationpc.widget.DomesticEconomyStepBar;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.List;

import cc.shinichi.library.ImagePreview;
import cc.shinichi.library.bean.ImageInfo;
import me.goldze.mvvmhabit.base.BaseFragment;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.BR;

/**
 * 这个是自助办证流程中上传材料的fragment
 */
public class DomesticEconomyReadAndSignFragment extends BaseFragment<FragmentDomesticEconomyReadAndSignBinding, FragmentDomesticEconomyReadAndSignViewModel> {

    private DomesticEconomyStepBar domesticEconomyStepBar;

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_domestic_economy_read_and_sign;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public void initData() {
        super.initData();
        initSelfServiceStepBar();
    }

    public void setLastStepGone() {
        binding.tvFirstText.setVisibility(View.GONE);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && viewModel != null) {
            String recordId = SPUtils.getInstance().getString("self_recordId");
            IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
            if (!TextUtils.isEmpty(recordId) && idCardUserInfo != null && !TextUtils.isEmpty(idCardUserInfo.getUserId())) {
                viewModel.listDocument(recordId, idCardUserInfo.getUserId());
            }
        }
    }

    private void initSelfServiceStepBar() {
        domesticEconomyStepBar = (DomesticEconomyStepBar) getView().findViewById(R.id.domestic_economy_step_bar);
        TextView tvFirstNumView = domesticEconomyStepBar.getTv_first_num();
        tvFirstNumView.setBackground(ResourcesCompat.getDrawable(getResources(), R.mipmap.icon_work_online, null));
        tvFirstNumView.setText("");
        TextView tvFirstView = domesticEconomyStepBar.getTv_first();
        tvFirstView.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color3333));
        domesticEconomyStepBar.setTv_first_num(tvFirstNumView);
        domesticEconomyStepBar.setTv_first(tvFirstView);

        TextView tvSecondNum = domesticEconomyStepBar.getTv_second_num();
        tvSecondNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.mipmap.icon_work_online, null));
        tvSecondNum.setText("");
        TextView tvSecond = domesticEconomyStepBar.getTv_second();
        tvSecond.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color3333));
        domesticEconomyStepBar.setTv_second_num(tvSecondNum);
        domesticEconomyStepBar.setTv_second(tvSecond);

        TextView tvThreeNum = domesticEconomyStepBar.getTv_three_num();
        tvThreeNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.shape_circle_3c6af4, null));
        tvThreeNum.setText("3");
        TextView tvThree = domesticEconomyStepBar.getTv_three();
        tvThree.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color_A5A5A5));
        domesticEconomyStepBar.setTv_three_num(tvThreeNum);
        domesticEconomyStepBar.setTv_three(tvThree);

    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();

//        单个签字图片文件上传成功
        viewModel.uploadFileSingleLiveEvent.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean response) {
                if (response != null && response) {
                    binding.tvSecond.setText(R.string.complete_read);
                    String recordId = SPUtils.getInstance().getString("self_recordId");
                    IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
                    if (!TextUtils.isEmpty(recordId) && idCardUserInfo != null && !TextUtils.isEmpty(idCardUserInfo.getUserId())) {
                        viewModel.listDocument(recordId, idCardUserInfo.getUserId());
                    }
                }
            }
        });


        viewModel.getInfoErrorEvent.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer response) {
                if (WorkstatusEnum.APPLICATION_INFO_FAIL.code == response) {
                    toastError(WorkstatusEnum.APPLICATION_INFO_FAIL.msg);
                } else if (WorkstatusEnum.FEE_INFO_FAIL.code == response) {
                    toastError(WorkstatusEnum.FEE_INFO_FAIL.msg);
                } else if (WorkstatusEnum.DOC_INFO_FAIL.code == response) {
                    toastError(WorkstatusEnum.DOC_INFO_FAIL.msg);
                } else if (WorkstatusEnum.UPLOAD_SIGN_FAIL.code == response) {
                    toastError(WorkstatusEnum.UPLOAD_SIGN_FAIL.msg);
                } else if (WorkstatusEnum.SIGN_DOC_FAIL.code == response) {
                    toastError(WorkstatusEnum.SIGN_DOC_FAIL.msg);
                } else if (WorkstatusEnum.LIST_MATERIAL_FAIL.code == response) {
                    toastError(WorkstatusEnum.LIST_MATERIAL_FAIL.msg);
                }
            }
        });

        //点击放大
        viewModel.amplifyDocEvent.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String o) {
                try {
                    if (viewModel.tempDoc != null && viewModel.tempDoc.size() > 0) {
                        List<ImageInfo> imgs = new ArrayList<>();
                        for (String filePath : viewModel.tempDoc) {
                            ImageInfo imageInfo = new ImageInfo();
                            imageInfo.setThumbnailUrl(filePath);
                            imageInfo.setOriginUrl(filePath);
                            imgs.add(imageInfo);
                        }
                        Integer index = viewModel.tempDoc.indexOf(o);
                        ImagePreview.getInstance().setContext(getActivity()).setShowCloseButton(false).setShowDownButton(false).setImageInfoList(imgs).setTransitionShareElementName("shared_element_container").setIndex(index).start();
                    }
                } catch (Exception e) {
                    Log.e("ERROR", e.getMessage());
                    MobclickAgent.reportError(getActivity(), e);
                }
            }
        });
        //点击文书进行预览
        viewModel.previewDocEvent.observe(this, new Observer<List<String>>() {
            @Override
            public void onChanged(List<String> o) {
                binding.viewPager.setVisibility(View.VISIBLE);
                if (o != null && o.size() > 0) {
                    viewModel.documentPreViewList.clear();
                    for (String filePath : o) {
                        viewModel.documentPreViewList.add(new DomesticEconomyReadAndSignDocumentGridItemViewModel(viewModel, filePath));
                    }
                }

            }
        });

        viewModel.currentDocumentIndex.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (integer != null) {
                    binding.viewPager.scrollToPosition(integer);
                }
            }
        });

        viewModel.firstButtonClickEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object o) {
                ((DomesticEconomyNotarizationActivity) getActivity()).viewPager.setCurrentItem(1, false);
            }
        });

        viewModel.secondButtonClickEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object o) {
//                String recordId = SPUtils.getInstance().getString("self_recordId");
//                viewModel.submit(recordId);
                toastSuccess(Utils.getContext().getString(R.string.orderCreateSuccess));
                startActivity(HomeActivity.class);
            }
        });
    }

}
