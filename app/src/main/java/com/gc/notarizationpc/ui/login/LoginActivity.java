package com.gc.notarizationpc.ui.login;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.activity.result.ActivityResult;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.gc.mininotarization.databinding.ActivityLoginBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.SerachBean;
import com.gc.notarizationpc.myview.SearchDialog;
import com.gc.notarizationpc.util.PopwindowUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import es.dmoral.toasty.Toasty;
import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.greendao.model.LoginInfo;
import me.goldze.mvvmhabit.http.BaseResponse;
import okhttp3.MultipartBody;

/**
 * 一个MVVM模式的登陆界面
 */
public class LoginActivity extends BaseActivity<ActivityLoginBinding, LoginViewModel> {

    //ActivityLoginBinding类是databinding框架自定生成的,对应activity_login.xml
    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_login;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;

    }

    @Override
    public LoginViewModel initViewModel() {
//        使用自定义的ViewModelFactory来创建ViewModel，如果不重写该方法，则默认会调用LoginViewModel(@NonNull Application application)构造方法
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        //View持有ViewModel的引用，如果没有特殊业务处理，这个方法可以不重写
        return ViewModelProviders.of(this, factory).get(LoginViewModel.class);

    }


    @Override
    public void initViewObservable() {
        //监听ViewModel中pSwitchObservable的变化, 当ViewModel中执行【uc.pSwitchObservable.set(!uc.pSwitchObservable.get());】时会回调该方法
        viewModel.getUC().observe(this, (Observer<ActivityResult>) result -> {
            if (result.getResultCode() == Activity.RESULT_OK) {
                Toast.makeText(LoginActivity.this, "得到demoactivity给的回调值:" + result.getData().getStringExtra("key"), Toast.LENGTH_SHORT).show();
            } else {
                // 处理失败逻辑
            }
        });

        viewModel.loginInfoSingleLiveEvent.observe(this, new Observer<BaseResponse>() {
            @Override
            public void onChanged(BaseResponse response) {
                if (response.isSuccess) {
                    Toast.makeText(LoginActivity.this, "登录成功" + ((LoginInfo) response.getResult()).getEmployeeName(), Toast.LENGTH_SHORT).show();
                } else {
                    Toasty.error(LoginActivity.this, "登陆失败").show();
                }

            }
        });

        viewModel.errorStr.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                Toast.makeText(LoginActivity.this, "登录失败" + s, Toast.LENGTH_SHORT).show();
            }
        });


    }

    //    public class ProxyClick {
    public void showTimePickDialog() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");

        //时间选择器
        TimePickerView timePicker = new TimePickerBuilder(LoginActivity.this, (date, v1) -> {

            String dateString = formatter.format(date);
            Log.d("timePicker", dateString);
            binding.txTime.setText(dateString);
        })//年月日时分秒 的显示与否，不设置则默认全部显示
                .setType(new boolean[]{true, true, true, true, true, false})
                .setLabel("年", "月", "日", "时", "分", "秒")
                .setContentTextSize(16)//字体大小
                .setDate(Calendar.getInstance())//设置参数
                .build();
        timePicker.show();
    }

    //测试 弹出签名
    public void showSignDialog(View view) {
        PopwindowUtil.showSignature(LoginActivity.this, new PopwindowUtil.SignatureListener() {
            @Override
            public void result(MultipartBody.Part body) {
                if (body != null) {
                    viewModel.uploadSign(body);
                }
            }

            @Override
            public void ondissmiss() {

            }
        });
    }
//    }

    /**
     * 网络模拟一个登陆操作
     **/
    public void showD(View view) {
        List<SerachBean> serachBeans = new ArrayList<>();
        String data = "{\"code\":200,\"message\":\"\",\"success\":\"true\",\n" +
                "\"data\":{\"#\":[{\"name\":\"666\",\"id\":\"1\"}, {\"name\":\"777\",\"id\":\"2\"}],\n" +
                "\"A\":[{\"name\":\"阿拉伯\",\"id\":\"3\"},{\"name\":\"澳大利亚\",\"id\":\"10\"}],\n" +
                "\"B\":[{\"name\":\"保加德\",\"id\":\"4\"}],\n" +
                "\"D\":[{\"name\":\"德国\",\"id\":\"6\"}],\n" +
                "\"F\":[{\"name\":\"法国\",\"id\":\"8\"}],\n" +
                "\"X\":[{\"name\":\"匈牙利\",\"id\":\"9\"}]\n" +
                "       }     \n" +
                "}";
        //Feature.OrderedField 防止array顺序错乱
        JSONObject jsonObject = JSON.parseObject(data, Feature.OrderedField);
        JSONObject jsonObject1 = jsonObject.getJSONObject("data");

        for (String key : jsonObject1.keySet()) {
            SerachBean beanList = new SerachBean();
            Log.d("zpzp", key);
            beanList.setKey(key);
            JSONArray array = jsonObject1.getJSONArray(key);
            List<SerachBean.Bean> bean = JSONObject.parseArray(array.toJSONString(), SerachBean.Bean.class);
            beanList.setBean(bean);
            serachBeans.add(beanList);
        }
        //展示搜索dialog
        SearchDialog dialog = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            //type 表示可选个数
            dialog = new SearchDialog(LoginActivity.this, serachBeans, true, "", 3);
        }
        dialog.setInterface(o -> {
            String text = "";
            for (int i = 0; i < o.size(); i++) {
                if (i == o.size() - 1) {
                    text += o.get(i);
                } else
                    text += o.get(i) + ",";
            }
            Toasty.info(LoginActivity.this, "当前选择的是" + text, 3000).show();
        });
        dialog.show();


    }

    public void showListDialog(View view) {
        ArrayList<String> faqs = new ArrayList<>();
        faqs.add("问题1");
        faqs.add("问题2");
        faqs.add("问题3");
        faqs.add("问题4");
//        PopwindowUtil.showFaqRecyclePop(this,faqs);
    }




}
