package com.gc.notarizationpc.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.MenuInfo;
import com.gc.notarizationpc.data.model.response.ApplyInfoResponse;

import java.util.List;

import me.goldze.mvvmhabit.base.BaseAdapter;
import me.goldze.mvvmhabit.base.BaseViewHolder;

//受理室基础申请信息
public class ApplyInfoAdapter extends BaseAdapter<MenuInfo> {

    private List<MenuInfo> data;

    public void setData(List<MenuInfo> data) {
        this.data = data;
    }

    public ApplyInfoAdapter(Context context, int itemLayoutRes, @Nullable Object type) {
        super(context, itemLayoutRes, type);
        mContext = context;
    }

    @Override
    public void bind(@NonNull BaseViewHolder viewHolder, MenuInfo itemData, int position) {
        if (itemData == null) {
            return;
        }
        viewHolder.setText(R.id.tv_key, itemData.getKey());
        viewHolder.setText(R.id.tv_value, itemData.getName());
        if (!TextUtils.isEmpty(itemData.getKey()) && itemData.getKey().contains("divider")) {
            //为了增加间隔
            viewHolder.getView(R.id.divider).setVisibility(View.VISIBLE);
            viewHolder.getView(R.id.tv_key).setVisibility(View.GONE);//用来当分隔符的divider不显示
            viewHolder.getView(R.id.tv_value).setVisibility(View.GONE);
        } else {
            viewHolder.getView(R.id.divider).setVisibility(View.GONE);
            viewHolder.getView(R.id.tv_key).setVisibility(View.VISIBLE);//用来当分隔符的divider不显示
            viewHolder.getView(R.id.tv_value).setVisibility(View.VISIBLE);
        }
    }

    @Override
    public int viewType(MenuInfo itemData) {
        return 0;
    }

    public interface Onclick {
        void OnClicklistener(int position, int flag);
    }

    Onclick mOnclick;

    public void setOnClick(Onclick onClick) {
        this.mOnclick = onClick;
    }
}
