package com.gc.notarizationpc.ui.inquirycertification;

import android.graphics.Bitmap;
import android.graphics.Typeface;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.example.scarx.idcardreader.utils.IdCardRenderUtils;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityInquiryOrderListHomeBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.fragment.MyAppointmentOrderGridFragment;
import com.gc.notarizationpc.ui.fragment.NotaryOrderFragment;
import com.gc.notarizationpc.ui.fragment.SelfServiceOrderFragment;
import com.gc.notarizationpc.ui.viewmodel.InquiryOrderListHomeViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo;

import me.goldze.mvvmhabit.base.BaseActivity;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class InquiryOrderListHomeActivity extends BaseActivity<ActivityInquiryOrderListHomeBinding, InquiryOrderListHomeViewModel> {
    private String TAG = "Test";
    private IdCardRenderUtils idCardReaderUtils = new IdCardRenderUtils();
    private SoundPool soundPool = new SoundPool(100, AudioManager.STREAM_MUSIC, 0);
    private boolean isActivity = true;
    private boolean isNoCard = true;
    private IDCardInfo idcardinfo = null;
    private Bitmap idCardBmp = null;
    private Integer readSuccess = null;
    private Integer readTips = null;
    private Integer validateSuccess = null;
    private Integer validateFail = null;
    private Integer retry = null;
    private Integer faceCamera = null;
    private boolean hasCardInfo = false;

    private FragmentManager fragmentManager;

    private Fragment fragment1;

    private Fragment fragment2;

    private Fragment fragment3;

    private Handler mHandler;

    private void switchFragment(int page) {
        Fragment fragment;
        switch (page) {
            case 1:
                fragmentManager.beginTransaction().replace(R.id.inquiry_order_list_home_fragment, fragment1).commit();
                break;
            case 2:
                fragmentManager.beginTransaction().replace(R.id.inquiry_order_list_home_fragment, fragment2).commit();
                break;
            case 3:
                fragmentManager.beginTransaction().replace(R.id.inquiry_order_list_home_fragment, fragment3).commit();
                break;
        }

    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            startActivity(HomeActivity.class);
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_inquiry_order_list_home;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public InquiryOrderListHomeViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(InquiryOrderListHomeViewModel.class);
    }


    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }


    @Override
    protected void onStop() {
        super.onStop();

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }


    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });

        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });
        Bundle bundle = this.getIntent().getExtras(); //读取intent的数据给bundle对象
        String phoneNumber = bundle.getString("phoneNumber"); //通过key得到value
        String idCard = bundle.getString("idCard");
        String verifyCode = bundle.getString("verifyCode");
        viewModel.mPhoneNumber.set(phoneNumber);
        viewModel.mIdCardNumber.set(idCard);
        viewModel.mValidNum.set(verifyCode);
        mHandler = new MyHandler(this);
        Bundle mBundle = new Bundle();
        mBundle.putString("phoneNumber", phoneNumber);
        mBundle.putString("idCard", idCard);
        mBundle.putString("verifyCode", verifyCode);
        fragmentManager = getSupportFragmentManager();
        fragment1 = new NotaryOrderFragment();
        fragment2 = new SelfServiceOrderFragment();
        fragment3 = new MyAppointmentOrderGridFragment();
        fragment1.setArguments(mBundle);
        fragment2.setArguments(mBundle);
        fragment3.setArguments(mBundle);
        switchFragment(1);
    }

    @Override
    public void initViewObservable() {
        viewModel.tabClickEvent.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (integer == 1) {
                    binding.inquiryOrderListNotaryOrderTab.setTextColor(getResources().getColor(R.color.colorPrimary));
                    binding.inquiryOrderListSelfServiceTab.setTextColor(getResources().getColor(R.color.date_picker_text_dark));
                    binding.inquiryOrderListMyAppointmentTab.setTextColor(getResources().getColor(R.color.date_picker_text_dark));
                    binding.inquiryOrderListNotaryOrderLine.setVisibility(View.VISIBLE);
                    binding.inquiryOrderListSelfServiceLine.setVisibility(View.GONE);
                    binding.inquiryOrderListMyAppointmentLine.setVisibility(View.GONE);
                    binding.inquiryOrderListNotaryOrderTab.setTypeface(Typeface.DEFAULT_BOLD);
                    binding.inquiryOrderListSelfServiceTab.setTypeface(Typeface.DEFAULT);
                    binding.inquiryOrderListMyAppointmentTab.setTypeface(Typeface.DEFAULT);
                } else if (integer == 2) {
                    binding.inquiryOrderListNotaryOrderTab.setTextColor(getResources().getColor(R.color.date_picker_text_dark));
                    binding.inquiryOrderListSelfServiceTab.setTextColor(getResources().getColor(R.color.colorPrimary));
                    binding.inquiryOrderListMyAppointmentTab.setTextColor(getResources().getColor(R.color.date_picker_text_dark));
                    binding.inquiryOrderListNotaryOrderLine.setVisibility(View.GONE);
                    binding.inquiryOrderListSelfServiceLine.setVisibility(View.VISIBLE);
                    binding.inquiryOrderListMyAppointmentLine.setVisibility(View.GONE);
                    binding.inquiryOrderListNotaryOrderTab.setTypeface(Typeface.DEFAULT);
                    binding.inquiryOrderListSelfServiceTab.setTypeface(Typeface.DEFAULT_BOLD);
                    binding.inquiryOrderListMyAppointmentTab.setTypeface(Typeface.DEFAULT);
                } else if (integer == 3) {
                    binding.inquiryOrderListNotaryOrderTab.setTextColor(getResources().getColor(R.color.date_picker_text_dark));
                    binding.inquiryOrderListSelfServiceTab.setTextColor(getResources().getColor(R.color.date_picker_text_dark));
                    binding.inquiryOrderListMyAppointmentTab.setTextColor(getResources().getColor(R.color.colorPrimary));
                    binding.inquiryOrderListNotaryOrderLine.setVisibility(View.GONE);
                    binding.inquiryOrderListSelfServiceLine.setVisibility(View.GONE);
                    binding.inquiryOrderListMyAppointmentLine.setVisibility(View.VISIBLE);
                    binding.inquiryOrderListNotaryOrderTab.setTypeface(Typeface.DEFAULT);
                    binding.inquiryOrderListSelfServiceTab.setTypeface(Typeface.DEFAULT);
                    binding.inquiryOrderListMyAppointmentTab.setTypeface(Typeface.DEFAULT_BOLD);
                }
                switchFragment(integer);
            }
        });

    }


}
