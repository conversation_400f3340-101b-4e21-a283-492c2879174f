package com.gc.notarizationpc.ui.fragment;

import static me.goldze.mvvmhabit.utils.ToastUtils.toastError;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentMyAppointmentGridviewBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.data.model.response.OrderAppointmentListModel;
import com.gc.notarizationpc.ui.inquirycertification.OrderDetailActivity;
import com.gc.notarizationpc.ui.video.CounselingRoomActivity;
import com.gc.notarizationpc.ui.view.CustomerFooter;
import com.gc.notarizationpc.ui.viewmodel.FragmentMyAppointmentGridViewModel;
import com.gc.notarizationpc.util.CommonUtil;

import me.goldze.mvvmhabit.base.BaseFragment;
import me.tatarka.bindingcollectionadapter2.BR;

public class MyAppointmentOrderGridFragment extends BaseFragment <FragmentMyAppointmentGridviewBinding, FragmentMyAppointmentGridViewModel>{
    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_my_appointment_gridview;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public FragmentMyAppointmentGridViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getActivity().getApplication());

        return ViewModelProviders.of(this, factory).get(FragmentMyAppointmentGridViewModel.class);
    }

    @Override
    public void initData() {
        super.initData();
        getArguments().getInt("type");
        viewModel.mPhoneNumber.set((String) getArguments().get("phoneNumber"));
        viewModel.mIdCardNumber.set((String) getArguments().get("idCard"));
        viewModel.requestNetWork();
        binding.twinklingRefreshLayout.setBottomView(new CustomerFooter(getContext(), false));

    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();
        viewModel.refreshEvent.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer aBoolean) {
                if (aBoolean == 0) {
                    binding.twinklingRefreshLayout.finishRefreshing();
                }
                if (aBoolean == 1) {
                    binding.twinklingRefreshLayout.finishLoadmore();
                }
                if (aBoolean == 2) {
                    binding.twinklingRefreshLayout.setBottomView(new CustomerFooter(getContext(), true));
                }
            }
        });
        viewModel.comeIntoOrderDetail.observe(this, aBoolean -> {
            if (aBoolean){
                startActivity(OrderDetailActivity.class);
            }
        });
        viewModel.pageEmptyEvent.observe(this, aBoolean -> {
            if (aBoolean){
               binding.twinklingRefreshLayout.setVisibility(View.GONE);
               binding.fragmentMyAppointmentGridviewEmpty.setVisibility(View.VISIBLE);
            }
        });

        viewModel.enterRoomEvent.observe(this, new Observer<OrderAppointmentListModel>() {
            @Override
            public void onChanged(OrderAppointmentListModel dataListDTO) {
//                showProgress(false,"连接中...");
                if (!TextUtils.isEmpty(dataListDTO.getRoomId())) {
                    Intent intent = new Intent(getContext(), CounselingRoomActivity.class);
                    intent.putExtra("roomId", "notary2.0-" + dataListDTO.getRoomId());
                    intent.putExtra("orderId", dataListDTO.getCaseInfoId());
                    intent.putExtra("mechanismName", dataListDTO.getNotarialName());
                    intent.putExtra("from", CommonUtil.SOURCE_FROM_ORDER);
                    intent.putExtra("notaryName", dataListDTO.getOfficeName());
                    intent.putExtra("caseInfoId", dataListDTO.getCaseInfoId());
                    intent.putExtra("notaryId", dataListDTO.getOfficeId());
                    startActivity(intent);
                } else {
                    toastError(getString(R.string.notReceivedRoomNumber));
                }

            }
        });

    }
}
