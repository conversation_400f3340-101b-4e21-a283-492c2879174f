package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.NotaryMatterItem;

import java.util.List;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * 这个是自助办证流程中上传材料fragment中材料上传部分的viewModel
 */
public class FragmentSelfServiceHasBeenFinishedViewModel extends BaseViewModel {
    public FragmentSelfServiceHasBeenFinishedViewModel(@NonNull Application application) {
        super(application);
    }

    //右侧文书展示的ItemBinding
    public ItemBinding<SelfFeeItemViewModel> feeInformationItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_self_service_has_finished_fee_information);
    // 右侧文文书展示的数据源
    public ObservableList<SelfFeeItemViewModel> feeInformationList = new ObservableArrayList();

    public SingleLiveEvent finishEvent = new SingleLiveEvent();

    /**
     * 获取公证事项费用
     */
    public void getSelfMatters() {
        showDialog();
        RequestUtil.getSelfMatters(new MyObserver<List<NotaryMatterItem>>() {
            @Override
            public void onSuccess(List<NotaryMatterItem> response) {
                dismissDialog();
//                if (response != null && response.size() > 0) {
//                    notarialMatterItems = response;
//                    notaryItemLiveEvent.setValue(notarialMatterItems);
//                } else {
//                    toastError(Utils.getContext().getString(R.string.getSelfNotaryFeeFail));
//                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.getSelfNotaryFeeFail) : errorMsg);
            }
        });
    }

    // 完成
    public BindingCommand finish = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            finishEvent.setValue("");
        }
    });
}
