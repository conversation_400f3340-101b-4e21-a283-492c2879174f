package com.gc.notarizationpc.ui.video;

import static com.tencent.trtc.TRTCCloudDef.TRTCRoleAnchor;
import static com.tencent.trtc.TRTCCloudDef.TRTC_APP_SCENE_LIVE;
import static com.tencent.trtc.TRTCCloudDef.TRTC_APP_SCENE_VIDEOCALL;
import static com.tencent.trtc.TRTCCloudDef.TRTC_QUALITY_Bad;
import static com.tencent.trtc.TRTCCloudDef.TRTC_QUALITY_Down;
import static com.tencent.trtc.TRTCCloudDef.TRTC_QUALITY_Vbad;
import static com.tencent.trtc.TRTCCloudDef.TRTC_VIDEO_BUFFER_TYPE_BYTE_ARRAY;
import static com.tencent.trtc.TRTCCloudDef.TRTC_VIDEO_BUFFER_TYPE_TEXTURE;
import static com.tencent.trtc.TRTCCloudDef.TRTC_VIDEO_PIXEL_FORMAT_I420;
import static com.tencent.trtc.TRTCCloudDef.TRTC_VIDEO_PIXEL_FORMAT_Texture_2D;
import static com.tencent.trtc.TRTCCloudDef.TRTC_VIDEO_RESOLUTION_1280_720;
import static com.tencent.trtc.TRTCCloudDef.TRTC_VIDEO_RESOLUTION_MODE_LANDSCAPE;
import static com.tencent.trtc.TRTCCloudDef.TRTC_VIDEO_RESOLUTION_MODE_PORTRAIT;
import static com.tencent.trtc.TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_BIG;
import static com.tencent.trtc.TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_SUB;

import android.app.Dialog;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.SurfaceTexture;
import android.graphics.drawable.ColorDrawable;
import android.hardware.usb.UsbDevice;
import android.media.MediaPlayer;
import android.net.Uri;
import android.opengl.EGLContext;
import android.opengl.EGLSurface;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.TextureView;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.alibaba.fastjson.JSON;
import com.amap.api.location.AMapLocation;
import com.downloader.PRDownloader;
import com.eloam.gaopaiyi.util.UVCCameraUtil;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.CounselingRoomBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.RoomUserInfo;
import com.gc.notarizationpc.bean.SocketMessageInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.data.model.request.SignDocRequest;
import com.gc.notarizationpc.data.model.response.ApplyInfoResponse;
import com.gc.notarizationpc.data.model.response.DocInfoResponse;
import com.gc.notarizationpc.data.model.response.FeeInfoResponse;
import com.gc.notarizationpc.data.model.response.MaterialListResponse;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.view.SignDialog;
import com.gc.notarizationpc.ui.view.VideoExitTimeDecreaseAlert;
import com.gc.notarizationpc.ui.view.VideoLineMaterialDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationApplyInfoDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationFeeDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationLawDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationMaterialDialog;
import com.gc.notarizationpc.ui.viewmodel.CounselingRoomViewModel;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.CustomCameraCapture1;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopIdCardComparison;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.util.helper.CustomCameraCapture;
import com.gc.notarizationpc.util.helper.CustomFrameRender;
import com.gc.notarizationpc.util.helper.render.EglCore;
import com.gc.notarizationpc.websocket.MyMqttService;
import com.gc.notarizationpc.websocket.WebSocketCallBack;
import com.gc.notarizationpc.widget.AutoFitTextureView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.serenegiant.usb.USBMonitor;
import com.serenegiant.usb.UVCCamera;
import com.tencent.custom.customcapture.utils.GenerateTestUserSig;
import com.tencent.liteav.TXLiteAVCode;
import com.tencent.liteav.trtc.TRTCCloudImpl;
import com.tencent.rtmp.ui.TXCloudVideoView;
import com.tencent.trtc.TRTCCloud;
import com.tencent.trtc.TRTCCloudDef;
import com.tencent.trtc.TRTCCloudListener;

import java.lang.ref.WeakReference;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.agora.rtc.RtcEngine;
import me.goldze.mvvmhabit.base.AppManager;
import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.utils.ConvertUtils;
import me.goldze.mvvmhabit.utils.SPUtils;

/**
 * 受理室
 */
public class CounselingRoomActivity extends BaseActivity<CounselingRoomBinding, CounselingRoomViewModel> implements WebSocketCallBack {
    private USBMonitor mUSBMonitor;
    String roomId;
    private static String TAG = "Test";
    private int OVERLAY_PERMISSION_REQ_CODE = 1234;
    private int OVERLAY_PERMISSION_SHARE_REQ_CODE = 1235;
    private int mPreviewTexture = 0;
    private SurfaceTexture mPreviewSurfaceTexture = null;
    private EglCore mEglCore = null;
    private EGLSurface mDummySurface = null;
    private EGLSurface mDrawSurface = null;
    private boolean mMVPMatrixInit = false;
    private UVCCamera mCamera = null;
    private boolean mPreviewing = false;
    private int mSurfaceWidth = 0;
    private int mSurfaceHeight = 0;
    private boolean mTextureDestroyed = false;
    private float[] mTransform = new float[16];
    private float[] mMVPMatrix = new float[16];
    private int DEFAULT_CAPTURE_WIDTH = 480;
    private int DEFAULT_CAPTURE_HEIGHT = 640;
    private ArrayList<RoomUserInfo> userList = null;
    private String localPid = "";
    private USBMonitor.UsbControlBlock localCtrlBlock = null;
    public MyMqttService myMqttService = null;
    private TXCloudVideoView mTXCloudPreviewView = null;
    private TextureView textureView = null;
    private List<TXCloudVideoView> mRemoteVideoList = null;
    private VideoNotarizationApplyInfoDialog dialog = null;
    private Handler mHandler;
    private String mechanismName;//公证员所属机构
    private String notaryName;//公证员名称
    private String caseInfoId;//案件id
    private VideoNotarizationLawDialog lawDialog;
    private SignDialog signDialog;
    private int sourceFrom;
    private LinearLayout lnlLineContent, lnlContent;
    private TextureView tvLineUploadMaterial;
    private String recordId;
    private String notaryId;

    // mqtt 邀请的当前用户
    private String currentIdCard;

    // 公证员是否加入受理室
    private boolean remoteIsAdd;

    // 当前是签字还是阅读文书
    private boolean isSign = false;

    private String applicantName;

    private boolean convertFlag = false;//是否从 咨询室转成 案件

    private String contactNum = "";

    private int mApplicantType;//3:企业签章
    private String companyName;//企业签章公司名称
    private String sealId;//签章图片id
    private String sealUrl;//签章图片url


    //ActivityLoginBinding类是databinding框架自定生成的,对应activity_login.xml
    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.counseling_room;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    private TRTCCloud mTRTCCloud = null;
    private CustomCameraCapture mCustomCameraCapture = null;
    private CustomFrameRender mCustomFrameRender = null;
    private ArrayList<String> mRemoteUserIdList = null;
    private boolean mStartPushFlag = false;
    private HashMap<String, CustomFrameRender> mCustomRemoteRenderMap = null;
    private String orderId;
    private TextView tvTitle;

    private VideoNotarizationMaterialDialog materialDialog;
    private VideoLineMaterialDialog materialLineDialog;
    private USBMonitor mUSBMonitor1 = null;
    private String localPid1 = "";
    private USBMonitor.UsbControlBlock localCtrlBlock1 = null;

    // 视频退出倒计时弹框
    private VideoExitTimeDecreaseAlert videoExitTimeDecreaseAlert;
    private CustomCameraCapture.VideoFrameReadListener mVideoFrameReadListener = new CustomCameraCapture.VideoFrameReadListener() {
        @Override
        public void onFrameAvailable(EGLContext eglContext, int textureId, int width, int height) {
            TRTCCloudDef.TRTCVideoFrame videoFrame = new TRTCCloudDef.TRTCVideoFrame();
            videoFrame.texture = new TRTCCloudDef.TRTCTexture();
            videoFrame.texture.textureId = textureId;
            videoFrame.texture.eglContext14 = eglContext;
            videoFrame.width = width;
            videoFrame.height = height;
            videoFrame.pixelFormat = TRTCCloudDef.TRTC_VIDEO_PIXEL_FORMAT_Texture_2D;
            videoFrame.bufferType = TRTCCloudDef.TRTC_VIDEO_BUFFER_TYPE_TEXTURE;

            // Setting encoder parameters for the main screen
            TRTCCloudDef.TRTCVideoEncParam encParam = new TRTCCloudDef.TRTCVideoEncParam();
            encParam.videoResolution = TRTCCloudDef.TRTC_VIDEO_RESOLUTION_640_360;
            encParam.videoFps = 15;
            encParam.videoBitrate = 550;
            encParam.videoResolutionMode = TRTCCloudDef.TRTC_VIDEO_RESOLUTION_MODE_LANDSCAPE;

            mTRTCCloud.setVideoEncoderParam(encParam);
//            mTRTCCloud.startLocalAudio();
//            mTRTCCloud.startLocalAudio(TRTCCloudDef.TRTC_AUDIO_QUALITY_DEFAULT);
            mTRTCCloud.setLocalViewFillMode(TRTCCloudDef.TRTC_VIDEO_RENDER_MODE_FIT);
            mTRTCCloud.sendCustomVideoData(TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_BIG, videoFrame);
        }

    };

    @Override
    public CounselingRoomViewModel initViewModel() {
//        使用自定义的ViewModelFactory来创建ViewModel，如果不重写该方法，则默认会调用LoginViewModel(@NonNull Application application)构造方法
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(CounselingRoomViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        //设置状态栏深浅模式false为浅色
        changeStatusBarLight(false);
    }

    @Override
    public void initData() {
        super.initData();
        remoteIsAdd = false;
        tvTitle = findViewById(R.id.tv_sub_title);
        sourceFrom = getIntent().getIntExtra("from", 0);
        notaryId = getIntent().getStringExtra("notaryId");
        usbName = SPUtils.getInstance().getString(AppConfig.LOCAL_USB_NAME, AppConfig.USBPNAME_DEFAULT);
        mRemoteVideoList = new ArrayList<>();
        ((ArrayList<TXCloudVideoView>) mRemoteVideoList).add((TXCloudVideoView) findViewById(R.id.trtcRemote));
        mRemoteUserIdList = new ArrayList();
//        //这里初始化摄像头
//        AppConfig.HIGHPID = "";
//        UVCCameraUtil.INSTANCE.initUSBMonitorForPid(this);
        mHandler = new MyHandler(this);
        Intent intent = getIntent();
        roomId = intent.getStringExtra("roomId");//从上个页面传递过来的
        orderId = intent.getStringExtra("orderId");
        notaryName = intent.getStringExtra("notaryName");
        mechanismName = intent.getStringExtra("mechanismName");
        caseInfoId = intent.getStringExtra("caseInfoId");
        recordId = intent.getStringExtra("recordId");
        ((TextView) findViewById(R.id.notary_office_text_id)).setText(mechanismName + getString(R.string.video_notarization_title) + notaryName);
        try {
            if (sourceFrom == CommonUtil.SOURCE_FROM_SPBZ) {
                myMqttService = new MyMqttService(this, "/videoConnectingRecords/" + orderId,
                        "/videoConnectingRecords/" + orderId, AppConfig.HOST, this);
            } else if (sourceFrom == CommonUtil.SOURCE_FROM_SPLX) {
                myMqttService = new MyMqttService(this, "/consultingRecords/" + orderId,
                        "/consultingRecords/" + orderId, AppConfig.HOST, this);
            } else if (sourceFrom == CommonUtil.SOURCE_FROM_ORDER) {
                myMqttService = new MyMqttService(this, "/case/" + orderId,
                        "/case/" + orderId, AppConfig.HOST, this);
                myMqttService.start();
            }
        } catch (Exception e) {
            Log.e("Test", e.getMessage());
        }

        myMqttService.start();
        mTXCloudPreviewView = findViewById(R.id.trtcMain);
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });
        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });
        lnlLineContent = findViewById(R.id.lnl_line_content);
        lnlContent = findViewById(R.id.lnl_content);
        if (sourceFrom == CommonUtil.SOURCE_FROM_SPLX) {
            lnlLineContent.setVisibility(View.VISIBLE);
            lnlContent.setVisibility(View.GONE);
        } else if (sourceFrom == CommonUtil.SOURCE_FROM_SPBZ) {
            lnlLineContent.setVisibility(View.GONE);
            lnlContent.setVisibility(View.VISIBLE);
        }


        //设置媒体音量百分之50
        com.gc.notarizationpc.util.CommonUtil.setSystemVoice(this);
        //start 编号：GCXMJC4757
        SPUtils.getInstance().put(AppConfig.LOCAL_USB_NAME, AppConfig.USBPNAME_DEFAULT);
        initUsbMonitor();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                UVCCameraUtil.INSTANCE.initUSBMonitorForPid(CounselingRoomActivity.this);

                if (!TextUtils.isEmpty(usbName)) {
//                    // If a USB camera is enabled
//                    if (AppConfig.USBPNAME.equals(usbName)) {
                    if (UVCCameraUtil.INSTANCE.checkUsbDevice(CounselingRoomActivity.this, usbName)) {
                        initUsbMonitor1();
                    } else {
                        toastInfo("未检测到本地环境摄像头设备，请检查确认后重试");
                    }

//                    }
                }
            }
        }, 3000);
        if (sourceFrom == CommonUtil.SOURCE_FROM_SPLX) {
            Map<String, String> param = new HashMap<>();
            param.put("recordId", recordId);
            IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
            if (idCardUserInfo != null && !TextUtils.isEmpty(idCardUserInfo.getSysUserId())) {
                param.put("userId", idCardUserInfo.getSysUserId());
            }
            //视频咨询视频连线成功之后更新咨询记录状态
            viewModel.justState(param);
        }


    }

    private DocInfoResponse currentDSR;//当前选中签字阅读文书的当事人

    private void showExitDialog() {
        PopwindowUtil.videoExitAlert(this, new PopwindowUtil.ResultListener() {
            @Override
            public void result(Object value) {
                showFiveSencondExitDialog();
            }
        });
    }

    /**
     * 五秒倒计时退出
     */
    private void showFiveSencondExitDialog() {
        if (videoExitTimeDecreaseAlert == null || !videoExitTimeDecreaseAlert.isShowing()) {
            videoExitTimeDecreaseAlert = new VideoExitTimeDecreaseAlert(CounselingRoomActivity.this, new PopwindowUtil.ResultSecondListener() {
                @Override
                public void result(Object value) {
                    finish();
                }

                @Override
                public void secondResult(Object value) {
                    finish();
                }
            });
            videoExitTimeDecreaseAlert.show();
            Window window = videoExitTimeDecreaseAlert.getWindow();
            window.getDecorView().setBackgroundColor(getResources().getColor(R.color.white));
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setPadding(0, 0, 0, 0);
            window.setLayout(
                    window.getContext().getResources().getDisplayMetrics().widthPixels - 100,
                    window.getContext().getResources().getDisplayMetrics().heightPixels - 120);
            window.setGravity(Gravity.CENTER);
            videoExitTimeDecreaseAlert.setCanceledOnTouchOutside(false);
        }
    }

    @Override
    public void initViewObservable() {
        PRDownloader.initialize(this);
//        viewModel.convertSuccess.observe(this, new Observer<String>() {
//            @Override
//            public void onChanged(String s) {
//                if (!TextUtils.isEmpty(s)) {
//                    //视频咨询转成案件后，将用户信息保存到缓存
//                    IdCardUserInfo userInfo= (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
//                    userInfo.setUserId(s);
//                    SPUtils.getInstance().saveObject(CounselingRoomActivity.this, "idcardinfo", userInfo);
//                }
//
//            }
//        });
        viewModel.closeVideoEvent.observe(this, aBoolean -> {
            showExitDialog();
        });

        viewModel.getInfoErrorEvent.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer response) {
                if (WorkstatusEnum.APPLICATION_INFO_FAIL.code == response) {
                    toastError(WorkstatusEnum.APPLICATION_INFO_FAIL.msg);
                } else if (WorkstatusEnum.FEE_INFO_FAIL.code == response) {
                    toastError(WorkstatusEnum.FEE_INFO_FAIL.msg);
                } else if (WorkstatusEnum.DOC_INFO_FAIL.code == response) {
                    toastError(WorkstatusEnum.DOC_INFO_FAIL.msg);
                } else if (WorkstatusEnum.UPLOAD_SIGN_FAIL.code == response) {
                    toastError(WorkstatusEnum.UPLOAD_SIGN_FAIL.msg);
                } else if (WorkstatusEnum.SIGN_DOC_FAIL.code == response) {
                    toastError(WorkstatusEnum.SIGN_DOC_FAIL.msg);
                    isSign = false;
                } else if (WorkstatusEnum.LIST_MATERIAL_FAIL.code == response) {
                    toastError(WorkstatusEnum.LIST_MATERIAL_FAIL.msg);
                }
            }
        });

        viewModel.applyInfoResponseSingleLiveEvent.observe(this, new Observer<ApplyInfoResponse>() {
            @Override
            public void onChanged(ApplyInfoResponse applyInfoResponse) {
                //申请信息
                if (dialog == null || !dialog.isShowing()) {
                    dialog = new VideoNotarizationApplyInfoDialog(CounselingRoomActivity.this, caseInfoId, applyInfoResponse);
                    dialog.show();
                    settingDialog(dialog);
                }
            }
        });

        viewModel.feeInfoResponseSingleLiveEvent.observe(this, new Observer<List<FeeInfoResponse>>() {
            @Override
            public void onChanged(List<FeeInfoResponse> feeInfoResponse) {
                //缴费信息
                VideoNotarizationFeeDialog feeDialog = new VideoNotarizationFeeDialog(CounselingRoomActivity.this, caseInfoId, feeInfoResponse);
                if (!feeDialog.isShowing()) {
                    feeDialog.show();
                    settingDialog(feeDialog);
                }
            }
        });

        viewModel.docInfoResponseSingleLiveEvent.observe(this, new Observer<List<DocInfoResponse>>() {
            @Override
            public void onChanged(List<DocInfoResponse> docInfoResponseList) {
                //收到了签字邀请
                if (isSign) {
                    if (mApplicantType == 3) {
                        if (currentIdCard.equals("")) {
                            readDocuments(docInfoResponseList);
                        } else {
                            for (DocInfoResponse docInfoResponse : docInfoResponseList) {
                                if (docInfoResponse.getCredentialNum().equals(currentIdCard)) {
                                    currentDSR = docInfoResponse;
                                }
                            }

                            //弹出企业签章弹框
                            PopwindowUtil.showCompanySign(CounselingRoomActivity.this, sealUrl, new PopwindowUtil.ResultListener() {
                                @Override
                                public void result(Object value) {
//                                    isSign = false;

                                    //调用企业签章
                                    SignDocRequest signDocRequest = new SignDocRequest();
                                    IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
                                    if (idCardUserInfo != null) {
                                        signDocRequest.setUserId(idCardUserInfo.getUserId());
                                    }
                                    signDocRequest.setPicFileId(sealId);
                                    signDocRequest.setPicFileUrl(sealUrl);
                                    signDocRequest.setCaseInfoId(caseInfoId);
                                    signDocRequest.setIdentityCards(currentIdCard);
                                    signDocRequest.setRepresentativeName(companyName);

                                    List<String> ids = new ArrayList<>();
                                    if (currentDSR != null && currentDSR.getDocTypeList() != null) {
                                        for (DocInfoResponse.DocTypeListDTO docTypeListDTO : currentDSR.getDocTypeList()) {
                                            if (docTypeListDTO.getDocumentVOList() != null) {
                                                for (DocInfoResponse.DocTypeListDTO.DocumentVOListDTO documentVOListDTO : docTypeListDTO.getDocumentVOList()) {
                                                    if (documentVOListDTO != null && !TextUtils.isEmpty(documentVOListDTO.getId())) {
                                                        ids.add(documentVOListDTO.getId());
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    signDocRequest.setIdList(ids);
                                    viewModel.signMultiDocument(signDocRequest);
                                    currentIdCard = "";
                                }

                            });
                        }
                    } else {
                        if (currentIdCard.equals("")) {
                            readDocuments(docInfoResponseList);
                        } else {
                            for (DocInfoResponse docInfoResponse : docInfoResponseList) {
                                if (docInfoResponse.getCredentialNum().equals(currentIdCard)) {
                                    showSignDialog(docInfoResponse);
                                    currentDSR = docInfoResponse;
                                }

                            }
                        }
                    }

                } else {
                    readDocuments(docInfoResponseList);
                }
            }
        });

        //单个签字图片文件上传成功
        viewModel.uploadFileSingleLiveEvent.observe(this, new Observer<UploadFileBean>() {
            @Override
            public void onChanged(UploadFileBean response) {
                if (response != null) {
                    try {
                        SignDocRequest signDocRequest = new SignDocRequest();
                        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
                        if (idCardUserInfo != null) {
                            signDocRequest.setUserId(idCardUserInfo.getUserId());
                        }
                        signDocRequest.setPicFileId(response.getId());
                        signDocRequest.setPicFileUrl(response.getSharedUrl());
                        signDocRequest.setCaseInfoId(caseInfoId);
                        signDocRequest.setIdentityCards(currentDSR.getCredentialNum());
                        signDocRequest.setRepresentativeName(currentDSR.getApplicantName());

                        List<String> ids = new ArrayList<>();
                        if (currentDSR != null && currentDSR.getDocTypeList() != null) {
                            for (DocInfoResponse.DocTypeListDTO docTypeListDTO : currentDSR.getDocTypeList()) {
                                if (docTypeListDTO.getDocumentVOList() != null) {
                                    for (DocInfoResponse.DocTypeListDTO.DocumentVOListDTO documentVOListDTO : docTypeListDTO.getDocumentVOList()) {
                                        if (documentVOListDTO != null && !TextUtils.isEmpty(documentVOListDTO.getId())) {
                                            ids.add(documentVOListDTO.getId());
                                        }
                                    }
                                }
                            }
                        }
                        signDocRequest.setIdList(ids);
                        viewModel.signMultiDocument(signDocRequest);
                    } catch (Exception e) {
                        Log.e("Test", e.getMessage());
                        e.printStackTrace();
                    }
                }

            }
        });


        //上传材料  查询所需材料
        viewModel.materialListSingleLiveEvent.observe(this, new Observer<List<MaterialListResponse>>() {
            @Override
            public void onChanged(List<MaterialListResponse> materialListResponses) {
                if (materialDialog != null && materialDialog.isShowing()) {
                    materialDialog.dismiss();
                }

                VideoNotarizationMaterialDialog materialDialog = new VideoNotarizationMaterialDialog(CounselingRoomActivity.this, caseInfoId, "02", materialListResponses, CommonUtil.SOURCE_FROM_SPBZ, new PopwindowUtil.ResultThirdListener() {
                    @Override
                    public void result(Object value) {

                    }

                    @Override
                    public void secondResult(Object value) {

                        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
                        if (idCardUserInfo != null) {
                            if (value != null) {
                                try {
                                    String currentMaterialTypeName = (String) value;
                                    Map<String, Object> data = new HashMap<>();
                                    data.put("code", "upload_material_success");
                                    data.put("content", idCardUserInfo.getName() + "上传《" + currentMaterialTypeName + "》材料");
                                    data.put("_origin", "small machine");
                                    String json = JSON.toJSONString(data);
                                    myMqttService.publish(json);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    Log.e("Test", e.getMessage());
                                }
                            }
                        }

                    }

                    @Override
                    public void thirdResult(Object value) {
                        Map<String, Object> data = new HashMap<>();
                        data.put("code", "delete_materials");
                        data.put("_origin", "small machine");
                        myMqttService.publish(JSON.toJSONString(data));
                    }
                });
                if (!materialDialog.isShowing()) {
                    materialDialog.show();
                    Window dialogWindow = materialDialog.getWindow();
                    dialogWindow.getDecorView().setBackgroundColor(getResources().getColor(android.R.color.white));
                    dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
                    // dialogWindow.setWindowAnimations(R.style.mystyle);
                    WindowManager.LayoutParams lp = dialogWindow.getAttributes();
                    lp.width = WindowManager.LayoutParams.MATCH_PARENT;
                    lp.height = WindowManager.LayoutParams.MATCH_PARENT;
                    lp.alpha = 1.0f;
                    dialogWindow.setAttributes(lp);
                    dialogWindow.setGravity(Gravity.RIGHT);
                    materialDialog.setCanceledOnTouchOutside(false);
                }
            }
        });

        //视频连线上传材料  查询所需材料
        viewModel.materialLineListSingleLiveEvent.observe(this, new Observer<List<MaterialListResponse.MaterialVoListDTO>>() {
            @Override
            public void onChanged(List<MaterialListResponse.MaterialVoListDTO> materialVoListDTO) {
                List<MaterialListResponse> listResponses = new ArrayList<>();
                MaterialListResponse materialListResponse = new MaterialListResponse();
                //这是为了保持和视频办证上传材料 接口保持格式一致
                materialListResponse.setMaterialTypeName(getString(R.string.other_material));
                if (materialVoListDTO == null || materialVoListDTO.size() == 0) {
                    materialVoListDTO = new ArrayList<>();
                    MaterialListResponse.MaterialVoListDTO materialVoListDTO1 = new MaterialListResponse.MaterialVoListDTO("CREATE", "");
                    materialVoListDTO.add(materialVoListDTO1);
                } else if (materialVoListDTO != null && (materialVoListDTO.size() < 30 && materialVoListDTO.size() > 0)) {
                    MaterialListResponse.MaterialVoListDTO materialVoListDTO1 = new MaterialListResponse.MaterialVoListDTO("CREATE", "");
                    materialVoListDTO.add(materialVoListDTO1);
                }
                materialListResponse.setMaterialVoList(materialVoListDTO);
                listResponses.add(materialListResponse);

                materialLineDialog = new VideoLineMaterialDialog(CounselingRoomActivity.this, recordId, "02", listResponses, CommonUtil.SOURCE_FROM_SPLX, new PopwindowUtil.ResultSecondListener() {
                    @Override
                    public void result(Object value) {
                        try {
                            List fileIdList = new ArrayList();
                            if (value != null) {
                                List fileUrlList = (List<MaterialListResponse.MaterialVoListDTO>) value;
                                for (int i = 0; i < fileUrlList.size(); i++) {
                                    fileIdList.add(((MaterialListResponse.MaterialVoListDTO) fileUrlList.get(i)).getFileId());
                                }
                                Map<String, Object> data = new HashMap<>();
                                data.put("code", "update_consult_materials");
                                data.put("content", fileIdList);
                                data.put("name", "小一体机");
                                String json = JSON.toJSONString(data);
                                myMqttService.publish(json);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            Log.e("Error", e.getMessage());
                        }
                    }

                    @Override
                    public void secondResult(Object value) {
                        try {
                            List fileIdList = new ArrayList();
                            if (value != null) {
                                List fileUrlList = (List<MaterialListResponse.MaterialVoListDTO>) value;
                                for (int i = 0; i < fileUrlList.size(); i++) {
                                    //未上传的过滤掉
                                    fileIdList.add(((MaterialListResponse.MaterialVoListDTO) fileUrlList.get(i)).getFileId());

                                }
                                Map<String, Object> data = new HashMap<>();
                                data.put("code", "delete_consult_materials");
                                data.put("content", fileIdList);
                                data.put("name", "小一体机");
                                String json = JSON.toJSONString(data);
                                myMqttService.publish(json);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            Log.e("Error", e.getMessage());
                        }

                    }
                });
                if (!materialLineDialog.isShowing()) {
                    materialLineDialog.show();
                    Window dialogWindow = materialLineDialog.getWindow();
                    dialogWindow.getDecorView().setBackgroundColor(getResources().getColor(android.R.color.white));
                    dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
                    // dialogWindow.setWindowAnimations(R.style.mystyle);
                    WindowManager.LayoutParams lp = dialogWindow.getAttributes();
                    lp.width = WindowManager.LayoutParams.MATCH_PARENT;
                    lp.height = WindowManager.LayoutParams.MATCH_PARENT;
                    lp.alpha = 1.0f;
                    dialogWindow.setAttributes(lp);
                    dialogWindow.setGravity(Gravity.RIGHT);
                    materialLineDialog.setCanceledOnTouchOutside(false);
                }
            }
        });

        //单人签字所有文书成功
        viewModel.singleSignSucessLiveEvent.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String response) {
                if (!TextUtils.isEmpty(response)) {
                    try {
                        toastSuccess(getString(R.string.signSuccess));
                        if (signDialog != null && signDialog.isShowing()) {
                            signDialog.dismiss();
                        }
                        String contentString = currentDSR.getApplicantName() + "完成";
                        for (int i = 0; i < currentDSR.getDocTypeList().size(); i++) {
                            for (int j = 0; j < currentDSR.getDocTypeList().get(i).getDocumentVOList().size(); j++) {
                                contentString += currentDSR.getDocTypeList().get(i).getDocumentVOList().get(j).getDocumentName() + ",";
                            }
                        }
                        contentString = contentString.substring(0, contentString.length() - 1);
                        contentString += "签名";
                        Map<String, Object> data = new HashMap<>();
                        data.put("code", "finish_document_sign");
                        data.put("content", contentString);
                        data.put("idCard", currentDSR.getCredentialNum());
                        data.put("_origin", "small machine");
                        data.put("picFileUrl", response);
                        myMqttService.publish(JSON.toJSONString(data));
                        if (!isSign) {
                            viewModel.listApplicantsAndDocuments(caseInfoId);
                        }
                        isSign = false;
                    } catch (Exception e) {
                        e.printStackTrace();
                        Log.e("Test", e.getMessage());
                    }
                }

            }
        });


    }

    // 阅读文书
    private void readDocuments(List<DocInfoResponse> docInfoResponseList) {
        //文书信息
        if (lawDialog != null || lawDialog != null && lawDialog.isShowing()) {
            lawDialog.dismiss();
        }
        lawDialog = new VideoNotarizationLawDialog(CounselingRoomActivity.this, mHandler, docInfoResponseList, caseInfoId, currentIdCard, remoteIsAdd, new PopwindowUtil.ResultListener<DocInfoResponse>() {
            @Override
            public void result(DocInfoResponse value) {
                if (value == null) {
                    return;
                }
                showSignDialog(value);
                currentDSR = value;
            }
        });
        currentIdCard = "";
        if (!lawDialog.isShowing()) {
            lawDialog.show();
            Window dialogWindow = lawDialog.getWindow();
            dialogWindow.getDecorView().setBackgroundColor(getResources().getColor(android.R.color.white));
            dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
            // dialogWindow.setWindowAnimations(R.style.mystyle);
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.height = WindowManager.LayoutParams.MATCH_PARENT;
            lp.alpha = 1.0f;
            dialogWindow.setAttributes(lp);
            dialogWindow.setGravity(Gravity.RIGHT);
            lawDialog.setCanceledOnTouchOutside(false);
        }
    }

    public void getVersion(View view) {

    }

    /**
     * 展示申请信息
     */
    public void showApplyDialog(View view) {
        viewModel.getCaseInfo(caseInfoId);

    }


    private void settingDialog(Dialog dialog) {
        Window dialogWindow = dialog.getWindow();
        dialogWindow.getDecorView().setBackgroundColor(getResources().getColor(android.R.color.white));
        dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
        // dialogWindow.setWindowAnimations(R.style.mystyle);
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = ConvertUtils.dp2px(470f);
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        lp.alpha = 1.0f;
        dialogWindow.setAttributes(lp);
        dialogWindow.setGravity(Gravity.RIGHT);
        dialog.setCanceledOnTouchOutside(false);
    }

    /**
     * 展示缴费信息
     */
    public void showFeeDialog(View view) {
        viewModel.getPaymentList(caseInfoId);

    }

    /**
     * 展示法律文书
     */
    public void showLowDialog(View view) {
        viewModel.listApplicantsAndDocuments(caseInfoId);
    }


    /**
     * 展示材料上传
     */
    public void showMatterUploadDialog(View view) {
        if (sourceFrom == CommonUtil.SOURCE_FROM_SPBZ || sourceFrom == CommonUtil.SOURCE_FROM_ORDER || convertFlag == true) {
            viewModel.listMaterialByCaseInfoId(caseInfoId);
        } else if (sourceFrom == CommonUtil.SOURCE_FROM_SPLX) {
            viewModel.listOtherMaterial(recordId);
        }
    }

    /**
     * 展示签字
     */
    public void showSignDialog(DocInfoResponse docInfoResponse) {
        signDialog = new SignDialog(this, docInfoResponse.getApplicantName(), new PopwindowUtil.ResultSecondListener() {
            @Override
            public void result(Object value) {
                if (value != null) {
                    Bitmap bitmap = (Bitmap) value;
                    viewModel.uploadSignPic(bitmap);
                }
            }

            @Override
            public void secondResult(Object value) {
                isSign = false;
            }
        });
        currentIdCard = "";
        if (!signDialog.isShowing()) {
            signDialog.show();
            Window dialogWindow = signDialog.getWindow();
            dialogWindow.getDecorView().setBackgroundColor(this.getResources().getColor(android.R.color.white));
            dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
            // dialogWindow.setWindowAnimations(R.style.mystyle);
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = ConvertUtils.dp2px(1000f);
            lp.height = ConvertUtils.dp2px(700f);
            lp.alpha = 1.0f;
            dialogWindow.setAttributes(lp);
            dialogWindow.setGravity(Gravity.CENTER);
            signDialog.setCanceledOnTouchOutside(false);
        }
    }

    /**
     * 初始化usb摄像头
     */
    private void initUsbMonitor() {
        Log.i("Test", "initUsbMonitor" + AppConfig.RGBPID);
//        if (AppConfig.RGBPID != "c013") {
//            UVCCameraUtil.INSTANCE.initUSBMonitorForPid(this);
//            toastError("当事人摄像头连接异常");
//            return;
//        }
        mUSBMonitor = UVCCameraUtil.INSTANCE.initUSBMonitor(this, new UVCCameraUtil.OnMyDevConnectListener() {
            @Override
            public void onConnectDev(UsbDevice device, USBMonitor.UsbControlBlock ctrlBlock) {
                String pid = String.format("%x", device.getProductId());
                localPid = pid;
                localCtrlBlock = ctrlBlock;
                Log.i(TAG, localPid);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        // 开始腾讯云直播和推流shang
                        if (!TextUtils.isEmpty(roomId)) {
                            IdCardUserInfo userInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
                            if (userInfo != null) {
                                if (sourceFrom == CommonUtil.SOURCE_FROM_SPLX) {
                                    //视频咨询连线的时候，和pc通信用的是sysuserid
                                    enterRoom(roomId, userInfo.getSysUserId());
                                } else {
                                    //视频咨询连线的时候，和pc通信用的是userid
                                    enterRoom(roomId, userInfo.getUserId());
                                }

                            }
//                            if (AppConfig.RGBPID.equals("c013")) {

//                            } else {
//                                enterRoom(roomId, "one-machine-portrait");
//                            }
                        }
                    }
                });
            }
        });
        mUSBMonitor.register();
        UVCCameraUtil.INSTANCE.requestPermission(this, AppConfig.RGBPID, 250, mUSBMonitor);
    }

    boolean portrait = false;
    private String usbName = null;
    private CustomCameraCapture1 mCustomCameraCapture1 = null;
    private TRTCCloud mTRTCCloud1 = null;
    private CustomCameraCapture1.VideoFrameReadListener mVideoFrameReadListener1 = new CustomCameraCapture1.VideoFrameReadListener() {
        @Override
        public void onFrameAvailable(EGLContext eglContext, int textureId, int width, int height) {
            TRTCCloudDef.TRTCVideoFrame videoFrame = new TRTCCloudDef.TRTCVideoFrame();
            videoFrame.texture = new TRTCCloudDef.TRTCTexture();
            videoFrame.texture.textureId = textureId;
            videoFrame.texture.eglContext14 = eglContext;
            videoFrame.width = 1080;
            videoFrame.height = 1920;
            videoFrame.pixelFormat = TRTCCloudDef.TRTC_VIDEO_PIXEL_FORMAT_Texture_2D;
            videoFrame.bufferType = TRTCCloudDef.TRTC_VIDEO_BUFFER_TYPE_TEXTURE;
            mTRTCCloud1.sendCustomVideoData(TRTC_VIDEO_STREAM_TYPE_SUB, videoFrame);
        }

    };

    /**
     * 初始化环境摄像头
     */
    private void initUsbMonitor1() {
        Log.i(TAG, "initUsbMonitor1");
        mUSBMonitor1 = UVCCameraUtil.INSTANCE.initUSBMonitor(this, new UVCCameraUtil.OnMyDevConnectListener() {
            @Override
            public void onConnectDev(UsbDevice device, USBMonitor.UsbControlBlock ctrlBlock) {
                Log.d(TAG, "pname == " + device.getProductName());
                localPid1 = device.getProductName().toString();
                localCtrlBlock1 = ctrlBlock;
//                if (device.getProductName().toString().equals(usbName) && !portrait) {
                if (UVCCameraUtil.INSTANCE.checkUsbDevice(CounselingRoomActivity.this, usbName)) {
                    enterRoom1(roomId, "environment");
                } else {
                    toastInfo("未检测到本地环境摄像头设备，请检查确认后重试");
                }

                portrait = true;
//                }
            }
        });
        mUSBMonitor1.register();
        UVCCameraUtil.INSTANCE.requestPermissionUSB(this, 250, mUSBMonitor1);
    }

    private void enterRoom1(String roomId, String userId) {
        Log.d(TAG, "enterRoom1==" + userId);
        mCustomCameraCapture1 = new CustomCameraCapture1(localPid1, localCtrlBlock1);
        mCustomCameraCapture1.startInternal(mVideoFrameReadListener1, usbName);
        mTRTCCloud1 = TRTCCloudImpl.createInstance(getApplicationContext());
        TRTCCloudDef.TRTCParams mTRTCParams = new TRTCCloudDef.TRTCParams();
        mTRTCParams.sdkAppId = GenerateTestUserSig.SDKAPPID;
        mTRTCParams.userId = userId;
        mTRTCParams.strRoomId = roomId;
        mTRTCParams.userSig = GenerateTestUserSig.genTestUserSig(mTRTCParams.userId);
        mTRTCParams.role = TRTCCloudDef.TRTCRoleAnchor;
        mTRTCCloud1.muteAllRemoteAudio(true);
        mTRTCCloud1.enterRoom(mTRTCParams, TRTC_APP_SCENE_VIDEOCALL);
        mTRTCCloud1.enableCustomVideoCapture(TRTC_VIDEO_STREAM_TYPE_SUB, true);
    }

    private void enterRoom(String roomId, String userId) {
        mCustomCameraCapture = new CustomCameraCapture(localPid, localCtrlBlock);
        mCustomCameraCapture.startInternal(mVideoFrameReadListener);
        mCustomFrameRender = new CustomFrameRender(userId, TRTC_VIDEO_STREAM_TYPE_BIG);
        mCustomRemoteRenderMap = new HashMap<>();
        mTRTCCloud = TRTCCloudImpl.createInstance(getApplicationContext());
        mTRTCCloud.setListener(new TRTCCloudImplListener(this));
        TRTCCloudDef.TRTCParams mTRTCParams = new TRTCCloudDef.TRTCParams();
        mTRTCParams.sdkAppId = GenerateTestUserSig.SDKAPPID;
        mTRTCParams.userId = userId;
        mTRTCParams.strRoomId = roomId;
        mTRTCParams.userSig = GenerateTestUserSig.genTestUserSig(mTRTCParams.userId);
        mTRTCParams.role = TRTCRoleAnchor;
        mTRTCCloud.enterRoom(mTRTCParams, TRTC_APP_SCENE_LIVE);
        mTRTCCloud.enableCustomVideoCapture(TRTC_APP_SCENE_LIVE, true);


        startScreenCapture();
    }

    @Override
    public void onSocketMessage(@Nullable String next) {
        Log.i(TAG, "Socket:" + next);
        if (next.startsWith("{") && next.endsWith("}")) {
            SocketMessageInfo info = JSON.parseObject(next, SocketMessageInfo.class);
            //查看申请信息
            if ("viewApplicationInfo".equals(info.getCode())) {
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
                viewModel.getCaseInfo(caseInfoId);

            } else if ("uploadFile".equals(info.getCode())) {
                //邀请上传材料


            } else if ("viewFeeDetail".equals(info.getCode())) {
                //查看缴费信息
                viewModel.getPaymentList(caseInfoId);

            } else if ("viewDocument".equals(info.getCode())) {
                if (lawDialog != null && lawDialog.isShowing()) {
                    lawDialog.dismiss();
                }
                //查看文书
                IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
                if (sourceFrom == CommonUtil.SOURCE_FROM_SPLX) {
                    if (idCardUserInfo != null && !TextUtils.isEmpty(idCardUserInfo.getSysUserId())) {
                        //查看文书todo
                        if (info.getInvitorList() != null && info.getInvitorList().contains(idCardUserInfo.getSysUserId())) {
                            currentIdCard = info.getObjectId() == null ? "" : info.getObjectId();
                            isSign = false;
                            viewModel.listApplicantsAndDocuments(caseInfoId);
                        }
                    }
                } else {
                    if (idCardUserInfo != null && !TextUtils.isEmpty(idCardUserInfo.getUserId())) {
                        //查看文书todo
                        if (info.getInvitorList() != null && info.getInvitorList().contains(idCardUserInfo.getUserId())) {
                            currentIdCard = info.getObjectId() == null ? "" : info.getObjectId();
                            isSign = false;
                            viewModel.listApplicantsAndDocuments(caseInfoId);
                        }
                    }
                }


            } else if ("closeChat".equals(info.getCode())) {
                //结束视频
                showFiveSencondExitDialog();

            } else if ("getPosition".equals(info.getCode())) {
                IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
                String tmpUserId = idCardUserInfo.getUserId();
                if (sourceFrom == CommonUtil.SOURCE_FROM_SPLX && idCardUserInfo != null) {
                    tmpUserId = idCardUserInfo.getSysUserId();
                }
                if (idCardUserInfo != null && !TextUtils.isEmpty(tmpUserId)) {
                    //获取位置信息
                    if (info.getInvitorList() != null && info.getInvitorList().contains(tmpUserId)) {
                        viewModel.getLocationInformation(CounselingRoomActivity.this, new PopwindowUtil.ResultListener<AMapLocation>() {
                            @Override
                            public void result(AMapLocation value) {
                                Map locationData = new HashMap<>();
                                locationData.put("address", value.getProvince() + value.getCity());
                                locationData.put("id", caseInfoId);
                                locationData.put("latitude", value.getLatitude());
                                locationData.put("longitude", value.getLongitude());
                                Log.i("Test", "locationData:" + JSON.toJSONString(locationData));
                                try {
                                    viewModel.uploadLocationInformation(locationData, new PopwindowUtil.ResultListener() {
                                        @Override
                                        public void result(Object value1) {
                                            Map data = new HashMap();
                                            data.put("code", "location_success");
                                            data.put("ipRegion", value.getProvince() + value.getCity());
                                            data.put("userId", sourceFrom == CommonUtil.SOURCE_FROM_SPLX ? idCardUserInfo.getSysUserId() : idCardUserInfo.getUserId());
                                            data.put("idCard: ", idCardUserInfo.getIdCard());
                                            data.put("content", idCardUserInfo.getName() + "的定位信息获取成功");
                                            myMqttService.publish(JSON.toJSONString(data));
                                        }
                                    });
                                } catch (Exception e) {
                                    Log.e("Test", e.getMessage());
                                }
                            }
                        });
                    }
                }
            } else if ("createCase".equals(info.getCode())) {
                myMqttService.unSubscribe();
                caseInfoId = info.getCaseId();
                applicantName = info.getApplicantName();
                myMqttService.setData(this, "/case/" + info.getCaseId(),
                        "/case/" + info.getCaseId(), AppConfig.HOST, this);
                myMqttService.start();
                if (sourceFrom == CommonUtil.SOURCE_FROM_SPLX) {
                    //如果是视频咨询连线，pc端点击 生成案件的时候触发，转换受理室来源
                    lnlLineContent.setVisibility(View.GONE);
                    lnlContent.setVisibility(View.VISIBLE);
//                    sourceFrom = CommonUtil.SOURCE_FROM_SPBZ;
                    convertFlag = true;
                    //视频咨询连线转换 案件，将pc端给的信息缓存到本地
                    IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
                    if (idCardUserInfo == null) {
                        idCardUserInfo = new IdCardUserInfo();
                    }
                    idCardUserInfo.setIdCard(info.getCredentialNum());
                    idCardUserInfo.setMobile(info.getContactNum());
                    idCardUserInfo.setNation("CHN");
                    idCardUserInfo.setName(info.getApplicantName());
                    idCardUserInfo.setUserId(info.getMiniUserId());
                    SPUtils.getInstance().saveObject(CounselingRoomActivity.this, "idcardinfo", idCardUserInfo);

                    if (materialLineDialog != null && materialLineDialog.isShowing()) {
                        materialLineDialog.dismiss();
                    }
                }
            } else if ("inviteSign".equals(info.getCode())) {
                if (signDialog != null && signDialog.isShowing()) {
                    signDialog.dismiss();
                }
                IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
                mApplicantType = info.getApplicantType();
                if (idCardUserInfo != null && !TextUtils.isEmpty(sourceFrom == CommonUtil.SOURCE_FROM_SPLX ? idCardUserInfo.getSysUserId() : idCardUserInfo.getUserId())) {
                    //邀请签字(个人)
                    if ((info.getApplicantType() != 3 && info.getInvitorList() != null && info.getInvitorList().contains(sourceFrom == CommonUtil.SOURCE_FROM_SPLX ? idCardUserInfo.getSysUserId() : idCardUserInfo.getUserId()))
                            || info.getApplicantType() == 3) {
                        isSign = true;

                        currentIdCard = info.getObjectId() == null ? "" : info.getObjectId();
                        viewModel.listApplicantsAndDocuments(caseInfoId);
                        if (info.getApplicantType() == 3) {
                            //企业签章
                            companyName = info.getName();
                            sealId = info.getSealId();
                            sealUrl = info.getSealUrl();
                        }
                    }
                }
            } else if ("openAudio".equals(info.getCode())) {
                IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
                if (idCardUserInfo != null && !TextUtils.isEmpty(sourceFrom == CommonUtil.SOURCE_FROM_SPLX ? idCardUserInfo.getSysUserId() : idCardUserInfo.getUserId())) {
                    if (info.getInvitorList() != null && info.getInvitorList().contains(sourceFrom == CommonUtil.SOURCE_FROM_SPLX ? idCardUserInfo.getSysUserId() : idCardUserInfo.getUserId())) {
//                        mTRTCCloud.muteLocalAudio(true);
                        mTRTCCloud.startLocalAudio(TRTCCloudDef.TRTC_AUDIO_QUALITY_DEFAULT);
                    }
                }

            } else if ("closeAudio".equals(info.getCode())) {
                IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(CounselingRoomActivity.this, "idcardinfo");
                if (idCardUserInfo != null && !TextUtils.isEmpty(sourceFrom == CommonUtil.SOURCE_FROM_SPLX ? idCardUserInfo.getSysUserId() : idCardUserInfo.getUserId())) {
                    if (info.getInvitorList() != null && info.getInvitorList().contains(sourceFrom == CommonUtil.SOURCE_FROM_SPLX ? idCardUserInfo.getSysUserId() : idCardUserInfo.getUserId())) {
//                        mTRTCCloud.muteLocalAudio(true);
                        mTRTCCloud.stopLocalAudio();
                    }
                }

            } else if ("userInputInfo".equals(info.getCode())) {
                String content = info.getContent();
                if (!TextUtils.isEmpty(content)) {
                    Map<String, String> receivedData = new HashMap<>();
                    Type mapType = new TypeToken<Map<String, Object>>() {
                    }.getType();
                    receivedData = new Gson().fromJson(content, mapType);
                    contactNum = receivedData.get("contactNum");
                }
                //视频咨询连线时候，pc端发起身份识别
                //先播放提示音
                MediaPlayer mediaPlayer = MediaPlayer.create(this, R.raw.idcardread);
                mediaPlayer.start();
                mediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                    @Override
                    public void onPrepared(MediaPlayer mp) {
                        mediaPlayer.seekTo(0);
                        mediaPlayer.start();
                    }
                });
                mediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {
                    @Override
                    public boolean onError(MediaPlayer mp, int what, int extra) {
                        return false;
                    }
                });
                new PopIdCardComparison(this).showIdCardComparison(this, new PopIdCardComparison.IdCardNotarizationiListener() {
                    @Override
                    public void getResult(IdCardUserInfo idCardInfo) {
                        if (idCardInfo == null) {
                            return;
                        }
                        Map data = new HashMap();
                        data.put("code", "user_input_info");
                        //获取到身份证识别结果
                        Map info = new HashMap();
                        info.put("applicantName", idCardInfo.getName());
                        info.put("credentialType", "1");
                        info.put("_credentialType", "身份证");
                        info.put("credentialNum", idCardInfo.getIdCard());
                        info.put("contactNum", TextUtils.isEmpty(contactNum) ? "" : contactNum);
                        data.put("content", JSON.toJSONString(info));
                        data.put("_origin", "small machine");
                        myMqttService.publish(JSON.toJSONString(data));
                    }

                    @Override
                    public void getError(String error) {

                    }
                });

            }
        }

    }

    private void startRemoteCustomRender(String userId, TXCloudVideoView renderView) {
        Log.e("Test", "startRemoteCustomRender");
        CustomFrameRender customRender = new CustomFrameRender(userId, TRTC_VIDEO_STREAM_TYPE_BIG);
        TextureView textureView = new TextureView(renderView.getContext());
        renderView.addVideoView(textureView);
        mTRTCCloud.setRemoteVideoRenderListener(userId, TRTC_VIDEO_PIXEL_FORMAT_I420, TRTC_VIDEO_BUFFER_TYPE_BYTE_ARRAY, customRender);
        customRender.start(textureView);
        mCustomRemoteRenderMap.put(userId, customRender);
//        mTRTCCloud.startLocalAudio();
        mTRTCCloud.startLocalAudio(TRTCCloudDef.TRTC_AUDIO_QUALITY_DEFAULT);
        mTRTCCloud.startRemoteView(userId, TRTC_VIDEO_STREAM_TYPE_BIG, null);
    }

    private void stopRemoteCustomRender(String userId) {
        CustomFrameRender render = mCustomRemoteRenderMap.remove(userId);
        if (render != null) {
            render.stop(false);
        }
        mTRTCCloud.stopRemoteView(userId);
    }


    class TRTCCloudImplListener extends TRTCCloudListener {
        private WeakReference<CounselingRoomActivity> mContext;

        public TRTCCloudImplListener(CounselingRoomActivity activity) {
            mContext = new WeakReference<>(activity);
        }

        @Override
        public void onUserVideoAvailable(String userId, boolean available) {
            Log.e("Test", userId + "===远端流");
            Integer index = mRemoteUserIdList.indexOf(userId);
            Log.e("Test", "userId" + userId + "===远端流\n" + "index====" + index + "\n" + "available===" + available);
            if (available) {
//                if (index != -1) {
//                    return;
//                }
                Log.e("Test", userId + "远端流");
                //这里需要过滤远端流，根据公证员id进行匹配,防止加载其他端的流
                if (!TextUtils.isEmpty(notaryId) && userId.equals(notaryId)) {
                    Log.e("Test", "远端户加入成功" + userId + "远端流");
                    remoteIsAdd = true;
                    mRemoteUserIdList.add(userId);
                    binding.tvNotaryStateOffVideo.setVisibility(View.GONE);
                    binding.trtcRemote.setVisibility(View.VISIBLE);
                }
            } else {
                if (index == -1) {
                    return;
                }
                remoteIsAdd = false;
//                stopRemoteCustomRender(userId);
//                remoteIsAdd = false;
//                mRemoteUserIdList.remove(index.intValue());
                binding.tvNotaryStateOffVideo.setVisibility(View.VISIBLE);
                binding.trtcRemote.setVisibility(View.GONE);
            }
        }


        @Override
        public void onError(int errCode, String errMsg, Bundle extraInfo) {
            if (errCode == TXLiteAVCode.ERR_ROOM_ENTER_FAIL) {
                toastError(getString(R.string.user_offline));
                AppManager.getAppManager().finishAllActivity(HomeActivity.class);
            }
        }

        @Override
        public void onNetworkQuality(TRTCCloudDef.TRTCQuality localQuality, ArrayList<TRTCCloudDef.TRTCQuality> remoteQuality) {
            super.onNetworkQuality(localQuality, remoteQuality);
            if (localQuality.quality == TRTC_QUALITY_Bad || localQuality.quality == TRTC_QUALITY_Vbad || localQuality.quality == TRTC_QUALITY_Down) {
                toastError(getString(R.string.network_not_stable));
            }
        }

        @Override
        public void onStopPublishing(int err, String errMsg) {
            super.onStopPublishing(err, errMsg);
            Log.d("onStopPublishing", errMsg);
        }

        @Override
        public void onRemoteUserEnterRoom(String userId) {
            Log.d("onRemoteUserEnterRoom的用户userId:", userId);
            super.onRemoteUserEnterRoom(userId);
            if (!TextUtils.isEmpty(notaryId) && userId.equals(notaryId)) {
                remoteIsAdd = true;
                startRemoteCustomRender(userId, (TXCloudVideoView) findViewById(R.id.trtcRemote));
            }
        }
    }

    private RtcEngine mRtcEngine = null;
    private TextureView surfaceView = null;

    private void startScreenCapture() {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N && !Settings.canDrawOverlays(this)) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + this.getPackageName()));
            startActivityForResult(intent, OVERLAY_PERMISSION_REQ_CODE);
        } else {
            screenCapture();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Log.d("zpzp", "code == " + requestCode);
        if (requestCode == OVERLAY_PERMISSION_REQ_CODE) {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N && !Settings.canDrawOverlays(this)) {
                Toast.makeText(getApplicationContext(), getString(R.string.open_float_win), Toast.LENGTH_SHORT).show();
            } else {
                screenCapture();
            }

        } else if (resultCode == OVERLAY_PERMISSION_SHARE_REQ_CODE) {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N && !Settings.canDrawOverlays(this)) {
                Toast.makeText(getApplicationContext(), getString(R.string.open_float_win), Toast.LENGTH_SHORT).show();
            } else {
                screenCapture();
            }
        }
    }

    private void screenCapture() {
        //end 编号：GCXMJC4757
        Log.d(TAG, "screenCapture");
        TRTCCloudDef.TRTCVideoEncParam encParams = new TRTCCloudDef.TRTCVideoEncParam();
        if (AppConfig.RGBPID.equals("c013")) {
            Log.d(TAG, "横屏");
            encParams.videoResolution = TRTC_VIDEO_RESOLUTION_1280_720;
            encParams.videoResolutionMode = TRTC_VIDEO_RESOLUTION_MODE_LANDSCAPE;
        } else {
            Log.d(TAG, "竖屏");
            encParams.videoResolution = TRTC_VIDEO_RESOLUTION_1280_720;
            encParams.videoResolutionMode = TRTC_VIDEO_RESOLUTION_MODE_PORTRAIT;
        }
        encParams.enableAdjustRes = true;
        encParams.videoFps = 10;
        encParams.videoBitrate = 1200;

        TRTCCloudDef.TRTCScreenShareParams params = new TRTCCloudDef.TRTCScreenShareParams();
        mTRTCCloud.startScreenCapture(TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_SUB, encParams, params);
        mTRTCCloud.enableCustomVideoCapture(TRTC_VIDEO_STREAM_TYPE_BIG, true);
        mTRTCCloud.setLocalVideoRenderListener(TRTC_VIDEO_PIXEL_FORMAT_Texture_2D, TRTC_VIDEO_BUFFER_TYPE_TEXTURE, mCustomFrameRender);
        textureView = new AutoFitTextureView(this);
        ((AutoFitTextureView) textureView).setAspectRatio(8, 5);
        mTXCloudPreviewView.addVideoView(textureView);
        mCustomFrameRender.start(textureView);
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy");
        try {

            if (myMqttService != null) {
                myMqttService.unSubscribe();
            }
            //leaveChannel(true);
            exitRoom(true);
            if (mUSBMonitor != null) {
                mUSBMonitor.unregister();
            }
            if (mUSBMonitor1 != null && mUSBMonitor1.isRegistered() == true) {
                mUSBMonitor1.unregister();
            }

            UVCCameraUtil.INSTANCE.releaseRGBCamera();
            UVCCameraUtil.INSTANCE.releaseIRCamera();
            //增加关闭高拍仪
            if (mUSBMonitor1 != null)
                UVCCameraUtil.INSTANCE.releaseHighCamera();
            //增加关闭usb外接摄像头
            UVCCameraUtil.INSTANCE.releaseUsbCamera();

            if (mUSBMonitor != null) {
                mUSBMonitor.destroy();
                mUSBMonitor = null;
            }
            if (mUSBMonitor1 != null) {
                mUSBMonitor1.destroy();
                mUSBMonitor1 = null;
            }

            PopwindowUtil.dismissMatchDialog();
        } catch (Exception e) {
            Log.d(TAG, e.getMessage());
        }
        super.onDestroy();
    }

    private long shutdown_time = 0;

    private void exitRoom(boolean isNormalExit) {
        if (System.currentTimeMillis() - shutdown_time < 5000)
            return;
        shutdown_time = System.currentTimeMillis();
        if (mCustomRemoteRenderMap != null) {
            for (CustomFrameRender render : mCustomRemoteRenderMap.values()) {
                render.stop(true);
            }
            mCustomRemoteRenderMap.clear();
        }
        if (mCustomCameraCapture != null) {
            mCustomCameraCapture.stop();
        }
        if (mCustomCameraCapture1 != null) {
            mCustomCameraCapture1.stop();
        }
        if (mCustomFrameRender != null) {
            mCustomFrameRender.stop(true);
        }
        hideRemoteView();
        if (mTRTCCloud != null) {
            mTRTCCloud.stopAllRemoteView();
            mTRTCCloud.stopScreenCapture();
            mTRTCCloud.exitRoom();
            mTRTCCloud.setListener(null);
        }

        TRTCCloudImpl.destroyInstance(mTRTCCloud);
        mTRTCCloud = null;

        if (mTRTCCloud1 != null) {
            mTRTCCloud1.stopAllRemoteView();
            mTRTCCloud1.stopScreenCapture();
            mTRTCCloud1.exitRoom();
            mTRTCCloud1.setListener(null);
        }
        TRTCCloudImpl.destroyInstance(mTRTCCloud1);
        mTRTCCloud1 = null;
        Log.i(TAG, "exitRoom");
        if (isNormalExit) {
            toastSuccess(getString(R.string.notarizaiton_consult_over));
        } else {
            toastError(getString(R.string.contact_admin));
        }

    }

    private void hideRemoteView() {
        if (mRemoteUserIdList != null) {
            mRemoteUserIdList.clear();
        }
        if (mRemoteVideoList != null) {
            for (View videoView : mRemoteVideoList) {
                videoView.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            showExitDialog();
            return false;
        }
        return super.onKeyDown(keyCode, event);
    }


}
