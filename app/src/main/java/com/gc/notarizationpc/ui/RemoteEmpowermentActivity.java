package com.gc.notarizationpc.ui;

import android.content.Intent;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.gc.notarizationpc.R;
import com.gc.notarizationpc.common.AccountManger;
import com.gc.notarizationpc.common.BaseActivity;
import com.gc.notarizationpc.common.RemoteBean;
import com.gc.notarizationpc.common.RemotesBean;
import com.gc.notarizationpc.model.RemoteInfo;
import com.gc.notarizationpc.ui.presenter.RemoteEmpowermentPresenter;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.Objects;

import es.dmoral.toasty.Toasty;

public class RemoteEmpowermentActivity extends BaseActivity implements View.OnClickListener, RemoteEmpowermentPresenter.IMobileView {

    private String TAG = "RemoteEmpowermentActivity";
    private ImageView backClicked;
    private EditText editText;
    private Button send_button;
    private RecyclerView recyclerView;
    private SwipeRefreshLayout refreshLayout;

    protected boolean isRefreshing = false;
    protected boolean isLoadMore = false;

    protected int limit = 20;       //条数
    protected int page = 1;         //页码

    private String id;
    private String idcard;
    private String name;
    private int lastVisibleItem;
    private RemoteInfo remoteInfo;

    private BaseQuickAdapter<RemotesBean.ItemsDTO, BaseViewHolder> quickAdapter;
    private List<RemotesBean.ItemsDTO> bean;

    @Override
    protected void getIntentData(Intent intent) {
        id = intent.getStringExtra("id");
        name = intent.getStringExtra("name");
        idcard = intent.getStringExtra("idcard");
    }

    @Override
    protected int getContentViewLayoutID() {
        return R.layout.activity_remote_empowerment;
    }

    @Override
    protected void initViewsAndEvents() {
        Log.i(TAG, "initViewsAndEvents");
        remoteInfo = new RemoteInfo();

        initView();
        initRefreshLayout();
        resetRecycler();
    }

    @Override
    protected void onResume() {
        Log.i(TAG, "onResume");
        super.onResume();
        initDate("1", limit + "");
    }

    private void initDate(String s, String s1) {
        Log.i(TAG, "initDate");
        remoteInfo.setCurrentPage(s);
        remoteInfo.setPageSize(s1);
        new RemoteEmpowermentPresenter(mActivity,this).getNotary();
    }

    @Override
    public void getSuccess(String notaryId) {
        try {
            JSONObject jsonObject = new JSONObject(notaryId);
            String notaryIdt = jsonObject.getString("notaryId");
            String notaryPublicId;
            if (jsonObject.has("notaryPublicId")) {
                notaryPublicId = jsonObject.getString("notaryPublicId");
            } else {
                notaryPublicId = "";
            }
            Objects.requireNonNull(AccountManger.getInstance(mActivity)).updateNotaryId(notaryIdt, notaryPublicId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        remoteInfo.setLoanOfficerId(name);
        remoteInfo.setIdCard(idcard);
        new RemoteEmpowermentPresenter(mActivity,this).getRemote(remoteInfo);
    }

    private void initView() {
        Log.i(TAG, "initView");
        backClicked = findViewById(R.id.backClicked);
        //editText = findViewById(R.id.editText);
        send_button = findViewById(R.id.send_button);
        recyclerView = findViewById(R.id.recyclerView);
        refreshLayout = findViewById(R.id.refreshLayout);

        backClicked.setOnClickListener(this);
        //send_button.setOnClickListener(this);
    }

    //初始化刷新控件
    private void initRefreshLayout() {
        Log.i(TAG, "initRefreshLayout");
        refreshLayout.setColorSchemeColors(Color.parseColor("#f39800"));
        refreshLayout.setOnRefreshListener(() -> {
            Log.i(TAG, "onRefresh");
            isRefreshing = true;
            page = 1;
            initDate(page + "", limit + "");
        });
    }

    @Override
    public void onClick(View v) {
        Log.i(TAG, "onClick");
        switch (v.getId()) {
            case R.id.backClicked:
                finish();
                break;
            case R.id.send_button:
                page = 1;
                remoteInfo.setCurrentPage(page + "");
                remoteInfo.setPageSize(limit + "");
                remoteInfo.setLoanOfficerId(name);
                remoteInfo.setIdCard(idcard);
                new RemoteEmpowermentPresenter(mActivity, this).reRemote(remoteInfo);
                break;
        }
    }

    private RemoteInfo getMobile() {
        Log.i(TAG, "getMobile");
        String trim = editText.getText().toString().trim();
        if (TextUtils.isEmpty(trim) || trim.length() < 18) {
            Toasty.error(this, "请输入正确的身份证号", android.widget.Toast.LENGTH_SHORT).show();
            remoteInfo.setLoanOfficerId("");
            return remoteInfo;
        }
        remoteInfo.setLoanOfficerId(trim);
        return remoteInfo;
    }

    private void resetRecycler() {
        Log.i(TAG, "resetRecycler");
        recyclerView.setNestedScrollingEnabled(false);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(quickAdapter = new BaseQuickAdapter<RemotesBean.ItemsDTO, BaseViewHolder>(R.layout.item_remote) {
            @Override
            protected void convert(@NotNull BaseViewHolder baseViewHolder, RemotesBean.ItemsDTO remoteBean) {
                baseViewHolder.setText(R.id.tv_remote, remoteBean.getBorrower());
                baseViewHolder.setText(R.id.tv_id, remoteBean.getIdCard());
            }
        });

        quickAdapter.setOnItemClickListener((adapter, view, position) -> {
            Log.i(TAG, "onItemClick" + position);
            page = 1;
            Intent intent = new Intent(RemoteEmpowermentActivity.this, RemoteDetailsActivity.class);
            intent.putExtra("bean", bean.get(position));
            intent.putExtra("position", position);
            startActivity(intent);
        });

        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE && lastVisibleItem + 1 == quickAdapter.getItemCount()) {
                    isLoadMore = true;
                    initDate(page + "", limit + "");
                }
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                lastVisibleItem = layoutManager.findLastVisibleItemPosition();
            }
        });
    }

    private void initContent(List<RemotesBean.ItemsDTO> remoteBean) {
        Log.i(TAG, "initContent");
        if (isLoadMore) {
            isLoadMore = false;
            quickAdapter.getLoadMoreModule().loadMoreComplete();
            if (remoteBean.size() < limit) {
                quickAdapter.getLoadMoreModule().loadMoreEnd();
            }
            quickAdapter.addData(remoteBean);
        } else {
            refreshLayout.setRefreshing(false);
            isRefreshing = false;
            quickAdapter.setNewData(remoteBean);
        }
        page++;
    }

    @Override
    public void sendSuccess() {
        Log.i(TAG, "sendSuccess");
    }

    @Override
    public void goModelSelectActivity(RemoteBean userBean) {
        Log.i(TAG, "goModelSelectActivity");
    }

    @Override
    public void getModelSelectActivity(RemotesBean remotesBean) {
        Log.i(TAG, "getModelSelectActivity");
        if (isLoadMore) {
            bean.addAll(remotesBean.getItems());
        } else {
            bean = remotesBean.getItems();
        }
        initContent(remotesBean.getItems());
    }
}