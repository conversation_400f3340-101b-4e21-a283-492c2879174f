package com.gc.notarizationpc.ui;

import android.os.Bundle;

import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityVideoNotarizationBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.ui.viewmodel.VideoNotarizationViewModel;

import me.goldze.mvvmhabit.base.BaseActivity;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class VideoNotarizationActivity extends BaseActivity<ActivityVideoNotarizationBinding, VideoNotarizationViewModel> {

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_video_notarization;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public VideoNotarizationViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(VideoNotarizationViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }

    @Override
    public void initData() {
        super.initData();
    }

    @Override
    public void initViewObservable() {

    }
}
