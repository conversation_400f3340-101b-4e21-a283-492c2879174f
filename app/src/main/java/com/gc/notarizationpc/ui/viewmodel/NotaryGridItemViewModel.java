package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.NotaryItemModel;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * Created by goldze on 2017/7/17.
 */

public class NotaryGridItemViewModel extends ItemViewModel<SelfServiceCertificateHomeViewModel> {


    public ObservableField<NotaryItemModel> entity = new ObservableField<>();

    public int background;

    public NotaryGridItemViewModel(@NonNull SelfServiceCertificateHomeViewModel viewModel, NotaryItemModel entity) {
        super(viewModel);
        this.entity.set(entity);

        //ImageView的占位图片，可以解决RecyclerView中图片错误问题
//        drawableImg = ContextCompat.getDrawable(viewModel.getApplication(), R.mipmap.ic_launcher);
    }






    /**
     * 选中item
     */
    public BindingCommand itemClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {

            Log.i("FragmentGridItemViewModel","FragmentGridItemViewModel itemClick");
            viewModel.itemClick(entity.get());
        }
    });


}
