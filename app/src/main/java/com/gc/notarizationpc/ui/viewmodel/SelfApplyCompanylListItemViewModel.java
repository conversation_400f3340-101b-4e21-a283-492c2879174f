package com.gc.notarizationpc.ui.viewmodel;

import android.graphics.drawable.Drawable;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.request.AddSelfServiceRequest;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * 自助申办信息，单位列表
 */

public class SelfApplyCompanylListItemViewModel extends ItemViewModel<SelfApplyInfoViewModel> {
    public ObservableField<AddSelfServiceRequest.RecordCorpApplicantsDTO> entity = new ObservableField<>();
    public Drawable drawableImg;

    public SelfApplyCompanylListItemViewModel(@NonNull SelfApplyInfoViewModel viewModel, AddSelfServiceRequest.RecordCorpApplicantsDTO entity) {
        super(viewModel);
        this.entity.set(entity);
    }

    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
    public int getPosition() {
        return viewModel.getCompanyItemPosition(this);
    }

    //编辑
    public BindingCommand editClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.companyClickHandler("edit", entity.get(), getPosition());
        }
    });

    //删除
    public BindingCommand delClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.companyClickHandler("delete", entity.get(), getPosition());
        }
    });


}
