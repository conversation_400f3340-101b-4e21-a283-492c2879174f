package com.gc.notarizationpc.ui

import android.content.Intent
import android.view.View
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseActivity

class ModelSelectActivity : BaseActivity() {

    private var mobilePhoneCard:String? = "";

    override fun getIntentData(intent: Intent?) {
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_model_select
    }

    override fun initViewsAndEvents() {
    }

    fun videoClick(view: View?) {
        val intent = Intent(this, PersonSelectActivity::class.java)
        intent?.putExtra("toWhere","videoClick");
        startActivity(intent)
        finish()
    }

    fun selfClick(view: View?) {
        val intent = Intent(this, PersonSelectActivity::class.java)
        intent?.putExtra("toWhere","selfClick");
        startActivity(intent)
        finish()
    }

    fun contractClick(view: View?) {
        toastInfo("功能升级中")
    }
}