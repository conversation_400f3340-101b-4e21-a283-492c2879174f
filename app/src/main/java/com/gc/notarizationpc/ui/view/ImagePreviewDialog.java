package com.gc.notarizationpc.ui.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.util.RecyclerViewPageChangeListenerHelper;

import java.util.ArrayList;
import java.util.List;

import cc.shinichi.library.ImagePreview;
import cc.shinichi.library.bean.ImageInfo;
import me.goldze.mvvmhabit.base.BaseAdapter;
import me.goldze.mvvmhabit.base.BaseViewHolder;
import me.goldze.mvvmhabit.utils.GlideRoundedCornersTransform;

public class ImagePreviewDialog extends Dialog {

    private Context mContext;

    private List<String> mImageList;

    private RecyclerView mRecyclerView;

    private TextView mTvCurrentPosition;

    private int mCurrentPosition = 0;

    public ImagePreviewDialog(@NonNull Context context, List<String> imageList) {
        super(context);
        mContext = context;
        mImageList = imageList;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_image_preview);
        initViews();
        setCanceledOnTouchOutside(true);
    }

    private void initViews() {
        mRecyclerView = findViewById(R.id.dialog_image_preview_recycleview);
        mTvCurrentPosition = findViewById(R.id.image_number_text);
        String count = (mCurrentPosition + 1) + "/" + mImageList.size();
        mTvCurrentPosition.setText(count);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(mContext);
        linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mRecyclerView.setLayoutManager(linearLayoutManager);
        PagerSnapHelper snapHelper = new PagerSnapHelper();
        snapHelper.attachToRecyclerView(mRecyclerView);
        ImagePreviewAdapter imagePreviewAdapter = new ImagePreviewAdapter(mContext, R.layout.item_image_perview, false);
        imagePreviewAdapter.refreshAdapter(mImageList);
        mRecyclerView.setAdapter(imagePreviewAdapter);
        mRecyclerView.addOnScrollListener(new RecyclerViewPageChangeListenerHelper(snapHelper, new RecyclerViewPageChangeListenerHelper.OnPageChangeListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {

            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {

            }

            @Override
            public void onPageSelected(int position) {
                //do something
                mCurrentPosition = position;
                String count = (mCurrentPosition + 1) + "/" + mImageList.size();
                mTvCurrentPosition.setText(count);
            }
        }));

    }

    public class ImagePreviewAdapter extends BaseAdapter<String> {
        private Context mContext;

        public ImagePreviewAdapter(Context context, int itemLayoutRes, @Nullable Object type) {
            super(context, itemLayoutRes, type);
            mContext = context;
        }

        @Override
        public void bind(@NonNull BaseViewHolder viewHolder, String itemData, int position) {
            if (viewHolder != null) {
                RequestOptions requestOptions = new RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .override(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL)
                        .format(DecodeFormat.PREFER_RGB_565)
                        .transform(new GlideRoundedCornersTransform(mContext, 8f, GlideRoundedCornersTransform.CornerType.ALL));
                Glide.with(mContext)
                        .load(itemData)
                        .apply(requestOptions).error(R.mipmap.erroimage).placeholder(R.mipmap.erroimage)
                        .into((ImageView) viewHolder.getView(R.id.pv));

                //点击图片放大
                viewHolder.getView(R.id.pv).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (TextUtils.isEmpty(itemData)) {
                            return;
                        }
                        List<ImageInfo> imgs = new ArrayList<>();
                        ImageInfo imageInfo = new ImageInfo();
                        imageInfo.setThumbnailUrl(itemData);
                        imageInfo.setOriginUrl(itemData);
                        imgs.add(imageInfo);
                        ImagePreview.getInstance().setContext(mContext).setShowCloseButton(false).setShowDownButton(false).setImageInfoList(imgs).setTransitionShareElementName("shared_element_container").start();

                    }
                });
            }
        }

        @Override
        public int viewType(String itemData) {
            return 0;
        }
    }
}
