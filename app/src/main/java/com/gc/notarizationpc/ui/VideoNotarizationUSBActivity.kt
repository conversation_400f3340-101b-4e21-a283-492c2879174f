package com.gc.notarizationpc.ui


import android.annotation.SuppressLint
import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.graphics.SurfaceTexture
import android.hardware.usb.UsbDevice
import android.net.Uri
import android.opengl.EGLSurface
import android.opengl.GLES11Ext
import android.opengl.GLES20
import android.opengl.Matrix
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.provider.Settings
import android.text.TextUtils
import android.util.DisplayMetrics
import android.util.Log
import android.view.TextureView
import android.view.View
import android.view.View.GONE
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.alibaba.fastjson.JSON
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.eloam.gaopaiyi.util.UVCCameraUtil
import com.example.framwork.baseapp.AppManager
import com.example.framwork.noHttp.Bean.AppIdAndTokenEntity
import com.example.framwork.utils.BitmapTool
import com.example.framwork.utils.DLog
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.AppConfig
import com.gc.notarizationpc.common.BaseActivity
import com.gc.notarizationpc.model.*
import com.gc.notarizationpc.ui.adapter.CustomBannerAdapter
import com.gc.notarizationpc.ui.fragment.*
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter
import com.gc.notarizationpc.utils.CommonUtil
import com.gc.notarizationpc.utils.DrawView
import com.gc.notarizationpc.utils.GenerateTestUserSig
import com.gc.notarizationpc.utils.SPMgr
import com.gc.notarizationpc.utils.core.EglCore
import com.gc.notarizationpc.utils.core.GlUtil
import com.gc.notarizationpc.utils.core.ProgramTextureOES
import com.gc.notarizationpc.utils.helper.CustomCameraCapture
import com.gc.notarizationpc.utils.helper.CustomCameraCapture1
import com.gc.notarizationpc.utils.helper.CustomFrameRender
import com.gc.notarizationpc.websocket.MyMqttService
import com.gc.notarizationpc.websocket.WebSocketCallBack
import com.gc.notarizationpc.widget.AutoFitTextureView
import com.serenegiant.usb.IFrameCallback
import com.serenegiant.usb.USBMonitor
import com.serenegiant.usb.UVCCamera
import com.tencent.liteav.TXLiteAVCode
import com.tencent.liteav.trtc.TRTCCloudImpl
import com.tencent.rtmp.ui.TXCloudVideoView
import com.tencent.trtc.TRTCCloud
import com.tencent.trtc.TRTCCloudDef
import com.tencent.trtc.TRTCCloudDef.*
import com.tencent.trtc.TRTCCloudListener
import io.agora.rtc.Constants
import io.agora.rtc.IRtcEngineEventHandler
import io.agora.rtc.RtcEngine
import io.agora.rtc.models.ChannelMediaOptions
import io.agora.rtc.ss.ScreenSharingClient
import io.agora.rtc.video.AgoraVideoFrame
import io.agora.rtc.video.VideoCanvas
import io.agora.rtc.video.VideoEncoderConfiguration
import kotlinx.android.synthetic.main.activity_notarization_video.*
import kotlinx.android.synthetic.main.layout_banner.*
import kotlinx.android.synthetic.main.layout_video_room.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.json.JSONObject
import java.io.IOException
import java.lang.ref.WeakReference
import java.nio.ByteBuffer

/**
 * 视频公证 本地摄像头调用界面
 */
class VideoNotarizationUSBActivity : BaseActivity(), WebSocketCallBack, NotarizationPresenter.IVideoView, VideoStepTwoFragment.IMaterialUpdateCallBack,
        VideoStepFiveFragment.IPayCallBack, NotarizationPresenter.IUpdateImageView, NotarizationPresenter.FacePreView,
        TextureView.SurfaceTextureListener, SurfaceTexture.OnFrameAvailableListener, NotarizationPresenter.saveSignatureView, View.OnClickListener {

    private var TAG = "VideoNotarizationUSBActivity"
    private var roomId: String? = null
    private var usbName: String? = null
    private var greffierName: String? = null
    private var oneFragment: VideoStepOneFragment? = null
    private var twoFragment: VideoStepTwoFragment? = null
    private var threeFragment: VideoStepThreeFragment? = null
    private var fourFragment: VideoStepFourFragment? = null
    private var fiveFragment: VideoStepFiveFragment? = null
    private val fragmentList: ArrayList<Fragment>? = ArrayList()
    private var offSet = 1
    private var offSet1 = 1
    private var socketSendCode: String? = null
    private var updateSignatureP: NotarizationPresenter? = null
    private var facePreViewP: NotarizationPresenter? = null
    private var notarizationInfo: NotarizationInfo? = null

    //公证事项  缴费需要
    private var remarksInfo: RoomRemarksInfo? = null
    private var updateP: NotarizationPresenter? = null

    private var appIdAndTokenEntity: AppIdAndTokenEntity? = null
    private var mPreviewTexture = 0
    private var mPreviewSurfaceTexture: SurfaceTexture? = null
    private var mEglCore: EglCore? = null
    private var mDummySurface: EGLSurface? = null
    private var mDrawSurface: EGLSurface? = null
    private var mProgram: ProgramTextureOES? = null
    private var mMVPMatrixInit = false
    private var mCamera: UVCCamera? = null
    private var mPreviewing = false
    private var mSurfaceWidth = 0
    private var mSurfaceHeight = 0
    private var mTextureDestroyed = false
    private val mTransform = FloatArray(16)
    private val mMVPMatrix = FloatArray(16)
    private val DEFAULT_CAPTURE_WIDTH = 480
    private val DEFAULT_CAPTURE_HEIGHT = 640
    private var userList: ArrayList<RoomUserInfo>? = null
    private var mUSBMonitor: USBMonitor? = null
    private var mUSBMonitor1: USBMonitor? = null
    private var localPid: String = ""
    private var localCtrlBlock: USBMonitor.UsbControlBlock? = null
    private var localPid1: String = ""
    private var localCtrlBlock1: USBMonitor.UsbControlBlock? = null
    private var mSSClient: ScreenSharingClient? = null
    private var myMqttService: MyMqttService? = null
    private var mTXCloudPreviewView: TXCloudVideoView? = null
    private var textureView: TextureView? = null
    private var mRemoteVideoList: List<TXCloudVideoView>? = null

    private var longpressalarm: ImageView? = null

    //签名
    var drawView: DrawView? = null
    private var main_linlayout: LinearLayout? = null
    private var bt: Button? = null
    private var bt_clear: Button? = null
    private var name_pos: TextView? = null
    private var img: ImageView? = null
    private var user_name = ""
    private var positions = 0

    private var mTRTCCloud: TRTCCloud? = null
    private var mTRTCCloud1: TRTCCloud? = null
    private var mCustomCameraCapture: CustomCameraCapture? = null
    private var mCustomCameraCapture1: CustomCameraCapture1? = null
    private var mCustomFrameRender: CustomFrameRender? = null
    private val OVERLAY_PERMISSION_REQ_CODE = 1234
    private val OVERLAY_PERMISSION_SHARE_REQ_CODE = 1235
    private var mRemoteUserIdList: ArrayList<String>? = null
    private var mCustomRemoteRenderMap: HashMap<String, CustomFrameRender>? = null
    private val mVideoFrameReadListener: CustomCameraCapture.VideoFrameReadListener = CustomCameraCapture.VideoFrameReadListener { eglContext, textureId, width, height ->
        val videoFrame = TRTCVideoFrame()
        videoFrame.texture = TRTCTexture()
        videoFrame.texture.textureId = textureId
        videoFrame.texture.eglContext14 = eglContext
        videoFrame.width = width
        videoFrame.height = height
        videoFrame.pixelFormat = TRTCCloudDef.TRTC_VIDEO_PIXEL_FORMAT_Texture_2D
        videoFrame.bufferType = TRTCCloudDef.TRTC_VIDEO_BUFFER_TYPE_TEXTURE
        // 大画面的编码器参数设置
        var encParam = TRTCCloudDef.TRTCVideoEncParam()
        encParam.videoResolution = TRTCCloudDef.TRTC_VIDEO_RESOLUTION_1280_720
        encParam.videoFps = 15
        encParam.videoBitrate = 550
        // videoResolutionMode 设置为横屏
        encParam.videoResolutionMode = TRTCCloudDef.TRTC_VIDEO_RESOLUTION_MODE_LANDSCAPE
        mTRTCCloud?.setVideoEncoderParam(encParam)
        mTRTCCloud?.startLocalAudio()
        //开启本地音频的采集和发布
        mTRTCCloud?.startLocalAudio(TRTCCloudDef.TRTC_AUDIO_QUALITY_DEFAULT);
        mTRTCCloud?.setLocalViewFillMode(TRTCCloudDef.TRTC_VIDEO_RENDER_MODE_FIT)
        mTRTCCloud?.sendCustomVideoData(TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_BIG, videoFrame)
    }
    private val mVideoFrameReadListener1: CustomCameraCapture1.VideoFrameReadListener = CustomCameraCapture1.VideoFrameReadListener { eglContext, textureId, width, height ->
        val videoFrame = TRTCVideoFrame()
        videoFrame.texture = TRTCTexture()
        videoFrame.texture.textureId = textureId
        videoFrame.texture.eglContext14 = eglContext
        videoFrame.width = 1080
        videoFrame.height = 1920
        videoFrame.pixelFormat = TRTCCloudDef.TRTC_VIDEO_PIXEL_FORMAT_Texture_2D
        videoFrame.bufferType = TRTCCloudDef.TRTC_VIDEO_BUFFER_TYPE_TEXTURE
        mTRTCCloud1?.sendCustomVideoData(TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_SUB, videoFrame)
    }

    @SuppressLint("HandlerLeak")
    private val hander: Handler = object : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                999 -> {
                    if (oneFragment != null && applicationList != null) {
                        oneFragment?.refreshApplicationList(applicationList!!)
                    }
                    doSomething()
                }
            }
        }
    }

    fun doSomething() {
        hander.sendEmptyMessageDelayed(999, 800)
    }

    override fun getIntentData(intent: Intent?) {
        roomId = intent?.getStringExtra("roomId")
        usbName = SPMgr.getInstance(mActivity).getElement(AppConfig.LOCAL_USB_NAME, "")
        greffierName = intent?.getStringExtra("greffierName")
        notarizationInfo = intent?.getSerializableExtra("notarizationInfo") as NotarizationInfo?

    }

    override fun getContentViewLayoutID(): Int {
        return if (AppConfig.RGBPID == "c013") {
            R.layout.activity_notarization_video
        } else {
            R.layout.activity_notarization_video_portrait
        }
    }

    override fun initViewsAndEvents() {
        Log.i(TAG, "initViewsAndEvents")
        updateP = NotarizationPresenter(mActivity, this as NotarizationPresenter.IUpdateImageView)
        updateSignatureP = NotarizationPresenter(mActivity, this as NotarizationPresenter.IVideoView)
        facePreViewP = NotarizationPresenter(mActivity, this as NotarizationPresenter.FacePreView)
        myMqttService = MyMqttService(this, ".topic.shiping.collect." + userInfo?.idCard, ".topic.shiping." + userInfo?.idCard, AppConfig.HOST, this)
        myMqttService?.start()
        mTXCloudPreviewView = findViewById(R.id.trtcMain)
//        mTXCloudPreviewView1 = findViewById(R.id.trtcShares)
        mRemoteVideoList = ArrayList()
        (mRemoteVideoList as ArrayList<TXCloudVideoView>).add((findViewById<TXCloudVideoView>(R.id.trtcRemote)))
        mRemoteUserIdList = ArrayList()

        longpressalarm = findViewById(R.id.longpressalarm)
        longpressalarm?.setOnLongClickListener(View.OnLongClickListener {
            baojin()
            false
        })
        //设置媒体音量百分之50
        CommonUtil.setSystemVoice(this)
        //start 编号：GCXMJC4757
        initUsbMonitor()
        Handler().postDelayed(Runnable {
            UVCCameraUtil.initUSBMonitorForPid(mActivity)
            if (!TextUtils.isEmpty(usbName)) {
                //启用的是usb摄像头
                if (AppConfig.USBPNAME == usbName) {
                    initUsbMonitor1()
                }
            }
        }, 3000)

        init() //初始化组件
        getWH() //获取我们xml布局中view的宽高
        onNavigationItemSelected(0)
    }

    //获取我们xml布局中view的宽高
    private fun getWH() {
        // 获取创建的宽度和高度
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        // 创建一个DrawView，该DrawView的宽度、高度与该Activity保持相同
        main_linlayout = findViewById<View>(R.id.main_linlayout) as LinearLayout
    }

    private fun init() {
        bt = findViewById<View>(R.id.bt) as Button
        bt_clear = findViewById<View>(R.id.bt_clear) as Button
        name_pos = findViewById<View>(R.id.name_pos) as TextView
        bt?.setOnClickListener(this)
        bt_clear?.setOnClickListener(this)
        img = findViewById<View>(R.id.img) as ImageView
    }

    private fun enterRoom(roomId: String, userId: String) {
        Log.d(TAG, "enterRoom")
        mCustomCameraCapture = CustomCameraCapture(localPid, localCtrlBlock)
        mCustomCameraCapture?.startInternal(mVideoFrameReadListener)
        mCustomFrameRender = CustomFrameRender(userId, TRTC_VIDEO_STREAM_TYPE_BIG)
        mCustomRemoteRenderMap = HashMap()
        mTRTCCloud = TRTCCloudImpl.createInstance(applicationContext)

        mTRTCCloud?.setListener(TRTCCloudImplListener(mActivity as VideoNotarizationUSBActivity))
        val mTRTCParams = TRTCParams()
        mTRTCParams.sdkAppId = GenerateTestUserSig.SDKAPPID
        mTRTCParams.userId = userId
        mTRTCParams.strRoomId = roomId
        mTRTCParams.userSig = GenerateTestUserSig.genTestUserSig(mTRTCParams.userId)
        mTRTCParams.role = TRTCRoleAnchor
        mTRTCCloud?.enterRoom(mTRTCParams, TRTC_APP_SCENE_LIVE)


        mTRTCCloud?.enableCustomVideoCapture(TRTC_VIDEO_STREAM_TYPE_BIG, true)
        mTRTCCloud?.setLocalVideoRenderListener(TRTC_VIDEO_PIXEL_FORMAT_Texture_2D, TRTC_VIDEO_BUFFER_TYPE_TEXTURE, mCustomFrameRender)
        textureView = AutoFitTextureView(this)
        (textureView as AutoFitTextureView).setAspectRatio(8, 5)
        mTXCloudPreviewView!!.addVideoView(textureView)
        mCustomFrameRender?.start(textureView)


    }

    private fun enterRoom1(roomId: String, userId: String) {
        Log.d(TAG, "enterRoom1")
        mCustomCameraCapture1 = CustomCameraCapture1(localPid1, localCtrlBlock1)
        mCustomCameraCapture1?.startInternal(mVideoFrameReadListener1, usbName)
        mTRTCCloud1 = TRTCCloudImpl.createInstance(applicationContext)
        val mTRTCParams = TRTCParams()
        mTRTCParams.sdkAppId = GenerateTestUserSig.SDKAPPID
        mTRTCParams.userId = userId
        mTRTCParams.strRoomId = roomId
        mTRTCParams.userSig = GenerateTestUserSig.genTestUserSig(mTRTCParams.userId)
        mTRTCParams.role = TRTCRoleAnchor
        mTRTCCloud1?.muteAllRemoteAudio(true)
        mTRTCCloud1?.enterRoom(mTRTCParams, TRTC_APP_SCENE_VIDEOCALL)
        mTRTCCloud1?.enableCustomVideoCapture(TRTC_VIDEO_STREAM_TYPE_SUB, true)
    }

    private fun hideRemoteView() {
        mRemoteUserIdList?.clear()
        for (videoView in mRemoteVideoList!!) {
            videoView.visibility = View.GONE
        }
    }

    var shutdown_time: Long = 0;
    private fun exitRoom(isNormalExit: Boolean) {
        if (System.currentTimeMillis() - shutdown_time < 5000)
            return
        shutdown_time = System.currentTimeMillis()
        if (mCustomRemoteRenderMap != null) {
            for (render in mCustomRemoteRenderMap!!.values) {
                render.stop(true)
            }
            mCustomRemoteRenderMap!!.clear()
        }
        if (mCustomCameraCapture != null) {
            mCustomCameraCapture!!.stop()
        }
        if (mCustomCameraCapture1 != null) {
            mCustomCameraCapture1!!.stop()
        }
        if (mCustomFrameRender != null) {
            mCustomFrameRender!!.stop(true)
        }
        hideRemoteView()
        if (mTRTCCloud != null) {
            mTRTCCloud!!.stopAllRemoteView()
            mTRTCCloud!!.stopScreenCapture()
            mTRTCCloud!!.exitRoom()
            mTRTCCloud!!.setListener(null)
        }
        mTRTCCloud = null
        if (mTRTCCloud1 != null) {
            mTRTCCloud1!!.stopAllRemoteView()
            mTRTCCloud1!!.stopScreenCapture()
            mTRTCCloud1!!.exitRoom()
            mTRTCCloud1!!.setListener(null)
        }
        mTRTCCloud1 = null
//        TRTCCloud.destroySharedInstance()
        TRTCCloudImpl.destroyInstance(mTRTCCloud)
        TRTCCloudImpl.destroyInstance(mTRTCCloud1)
        Log.i(TAG, "exitRoom")
        var text: String
        if (isNormalExit) {
            toastSuccess(getString(R.string.notarizaiton_consult_over))
        } else {
            toastError(getString(R.string.contact_admin))
        }
        if (hander.hasMessages(999))
            hander.removeMessages(999)

    }

    private fun startRemoteCustomRender(userId: String, renderView: TXCloudVideoView) {
        val customRender = CustomFrameRender(userId, TRTC_VIDEO_STREAM_TYPE_BIG)
        val textureView = TextureView(renderView.context)
        renderView.addVideoView(textureView)
        mTRTCCloud!!.setRemoteVideoRenderListener(userId, TRTC_VIDEO_PIXEL_FORMAT_I420, TRTC_VIDEO_BUFFER_TYPE_BYTE_ARRAY, customRender)
        customRender.start(textureView)
        mCustomRemoteRenderMap!![userId] = customRender
        mTRTCCloud?.startLocalAudio()
        //开启本地音频的采集和发布
        mTRTCCloud?.startLocalAudio(TRTCCloudDef.TRTC_AUDIO_QUALITY_DEFAULT)
        mTRTCCloud!!.startRemoteView(userId, TRTC_VIDEO_STREAM_TYPE_BIG, null)
    }

    private fun stopRemoteCustomRender(userId: String) {
        val render = mCustomRemoteRenderMap!!.remove(userId)
        render?.stop(false)
        mTRTCCloud!!.stopRemoteView(userId)
    }


    inner class TRTCCloudImplListener(USBActivity: VideoNotarizationUSBActivity) : TRTCCloudListener() {
        private val mContext: WeakReference<VideoNotarizationUSBActivity> = WeakReference<VideoNotarizationUSBActivity>(USBActivity)
        override fun onUserVideoAvailable(userId: String, available: Boolean) {
            val index: Int? = <EMAIL>?.indexOf(userId)
            if (available) {
                if (index != -1) {
                    return
                }
                if (userId != "ScreenShare") {
                    mRemoteUserIdList?.add(userId)
                }
            } else {
                if (index == -1) {
                    return
                }
                stopRemoteCustomRender(userId)
                index?.let { mRemoteUserIdList?.removeAt(it) }
            }
            refreshRemoteVideo()
        }

        private fun refreshRemoteVideo() {
            Log.d(TAG, "refreshRemoteVideo")
            if (mRemoteUserIdList?.size!! > 0) {
                mRemoteVideoList?.get(0)?.visibility = View.VISIBLE
                mRemoteUserIdList?.get(0)?.let { mRemoteVideoList?.get(0)?.let { it1 -> startRemoteCustomRender(it, it1) } }
            } else {
                Log.i(TAG, "无远端用户")
            }
        }

        override fun onError(errCode: Int, errMsg: String, extraInfo: Bundle?) {
            val USBActivity: VideoNotarizationUSBActivity? = mContext.get()
            if (USBActivity != null) {
                if (errCode == TXLiteAVCode.ERR_ROOM_ENTER_FAIL) {
                    toastError(getString(R.string.user_offline))
                    AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
                }
            }
        }
    }

    /**
     * 初始化usb摄像头
     */
    private fun initUsbMonitor1() {
        Log.i(TAG, "initUsbMonitor1")
        mUSBMonitor1 =
                UVCCameraUtil.initUSBMonitor(mActivity, object : UVCCameraUtil.OnMyDevConnectListener {
                    override fun onConnectDev(
                            device: UsbDevice,
                            ctrlBlock: USBMonitor.UsbControlBlock
                    ) {
                        Log.d(TAG, "pname == " + device?.productName)
                        localPid1 = device?.productName.toString()
                        localCtrlBlock1 = ctrlBlock
                        if (device?.productName.toString() == usbName && !portrait) {
                            enterRoom1(roomId!!, "one-machine-usbshare")
                            portrait = true
                        }
                    }
                })
        mUSBMonitor1?.register()

        UVCCameraUtil.requestPermissionUSB(mActivity, 250, mUSBMonitor1!!)
    }

    var landscape = false
    var portrait = false

    /**
     * 初始化usb摄像头
     */
    private fun initUsbMonitor() {
        Log.i(TAG, "initUsbMonitor")
        mUSBMonitor =
                UVCCameraUtil.initUSBMonitor(mActivity, object : UVCCameraUtil.OnMyDevConnectListener {
                    override fun onConnectDev(
                            device: UsbDevice,
                            ctrlBlock: USBMonitor.UsbControlBlock
                    ) {
                        val pid = String.format("%x", device.productId)
                        Log.d(TAG, "pid == " + pid)
                        localPid = pid
                        localCtrlBlock = ctrlBlock
                        Log.i(TAG, localPid)
                        runOnUiThread {
                            if (AppConfig.RGBPID == "c013" && !landscape) {
                                enterRoom(roomId!!, "one-machine-landscape")
                                landscape = true
                            } else {
                                enterRoom(roomId!!, "one-machine-portrait")
                            }
                        }

                    }
                })
        mUSBMonitor?.register()

        UVCCameraUtil.requestPermission(mActivity, AppConfig.RGBPID, 250, mUSBMonitor!!)

    }


    private var mRtcEngine: RtcEngine? = null
    private var surfaceView: TextureView? = null


    private fun startScreenCapture() {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N && !Settings.canDrawOverlays(this)) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + this.packageName))
            startActivityForResult(intent, OVERLAY_PERMISSION_REQ_CODE)
        } else {
            screenCapture()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        Log.d("zpzp", "code == " + requestCode)
        if (requestCode == OVERLAY_PERMISSION_REQ_CODE) {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N && !Settings.canDrawOverlays(this)) {
                Toast.makeText(applicationContext, getString(R.string.open_float_win), Toast.LENGTH_SHORT).show()
            }
        } else if (resultCode == OVERLAY_PERMISSION_SHARE_REQ_CODE) {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N && !Settings.canDrawOverlays(this)) {
                Toast.makeText(applicationContext, getString(R.string.open_float_win), Toast.LENGTH_SHORT).show()
            } else {
                screenCapture()
            }
        }
    }

    private fun screenCapture() {
        //end 编号：GCXMJC4757
        Log.d(TAG, "screenCapture")
        val encParams = TRTCVideoEncParam()

        if (AppConfig.RGBPID == "c013") {
            Log.d(TAG, "横屏")
            encParams.videoResolution = TRTC_VIDEO_RESOLUTION_1280_720
            encParams.videoResolutionMode = TRTC_VIDEO_RESOLUTION_MODE_LANDSCAPE
        } else {
            Log.d(TAG, "竖屏")
            encParams.videoResolution = TRTC_VIDEO_RESOLUTION_1280_720
            encParams.videoResolutionMode = TRTC_VIDEO_RESOLUTION_MODE_PORTRAIT
        }
        encParams.enableAdjustRes = true
        encParams.videoFps = 10
        encParams.videoBitrate = 1200
        val params = TRTCScreenShareParams()
//        params?.floatingView=trtcShareTwo
        mTRTCCloud!!.startScreenCapture(TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_SUB, encParams, params)


        mTRTCCloud?.enableCustomVideoCapture(TRTC_VIDEO_STREAM_TYPE_BIG, true)
        mTRTCCloud?.setLocalVideoRenderListener(TRTC_VIDEO_PIXEL_FORMAT_Texture_2D, TRTC_VIDEO_BUFFER_TYPE_TEXTURE, mCustomFrameRender)
        textureView = AutoFitTextureView(this)
//        (textureView as AutoFitTextureView).setAspectRatio(8, 5)
        mTXCloudPreviewView!!.addVideoView(textureView)
        mCustomFrameRender?.start(textureView)

    }

    var applicationList: ArrayList<RoomUserInfo>? = null
    var companyList: List<RoomCompanyInfo>? = null

    /**
     * 接收到websocket消息
     */
    override fun onSocketMessage(next: String?) {
        DLog.d("Socket:$next")
        Log.i(TAG, "Socket:$next")
        if (next?.contains("{") == true) {
            lifecycleScope.launch(Dispatchers.Main) {
                val info: SocketMessageInfo = JSON.parseObject(next, SocketMessageInfo::class.java)
                signature.visibility = View.GONE
                if (info.code == "join") {
                    tvNotaryState.visibility = View.GONE
                } else if (info.code == "400") {
                    AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
//                    exitRoom(true)
                } else if (info.code == "502") {
                    onNavigationItemSelected(4)
                    fiveFragment?.initPayInfo(greffierName, remarksInfo, notarizationInfo)
                } else if (info.code == "511") {
                    onNavigationItemSelected(3)
                } else if (info.code == "505") {
                    onNavigationItemSelected(2)
                } else if (info.code == "504") {
                    onNavigationItemSelected(0)
                } else if (info.code == "500") {
                    onNavigationItemSelected(1)
                } else if (info.code == "00") {
                    applicationList = JSON.parseArray(info.info, RoomUserInfo::class.java) as ArrayList<RoomUserInfo>?
                    removeNull(applicationList as ArrayList<RoomUserInfo>)
//                    if (userList == null)

                    userList = applicationList
                    if (!hander.hasMessages(999))
                        hander.sendEmptyMessage(999)
//                    if (oneFragment != null) {
//                        oneFragment?.refreshApplicationList(applicationList!!)
//                    }
                } else if (info.code == "01") {
                    var proxyList = JSON.parseArray(info.info, RoomUserInfo::class.java) as ArrayList<RoomUserInfo>?
                    removeNull(proxyList as ArrayList<RoomUserInfo>)
                    if (oneFragment != null)
                        oneFragment?.refreshProxyList(proxyList!!)
                } else if (info.code == "02") {
                    companyList = JSON.parseArray(info.info, RoomCompanyInfo::class.java)
                    if (oneFragment != null)
                        oneFragment?.refreshCompanyList(companyList!!)
                } else if (info.code == "001") {
                    remarksInfo = JSON.parseObject(info.info, RoomRemarksInfo::class.java)
                    if (oneFragment != null)
                        oneFragment?.refreshRemarkList(remarksInfo!!)
                } else if (info.code == "101" || info.code == "110" ||  info.code == "106" || info.code == "107") {
                    val bannerList = JSON.parseArray(info.info, String::class.java)
                    showMatterView(bannerList)
                    if (info.resttingsignNum3) {
                        offSet = 1
                    }
                } else if (info.code == "next2"
                        || info.code == "201") {//被证明文件和谈话笔录关闭 onlyoffice
                    val bannerList = JSON.parseArray(info.info, String::class.java)
                    showMatterView(bannerList)
                    if (info.resttingsignNum3) {
                        offSet = 1
                    }
//                    if (stepView.currentStep == 2) {
//                        //被证明文件
//                        threeFragment?.invisiableWebView()
//                    } else if (stepView.currentStep == 3) {
//                        //谈话笔录
//                        fourFragment?.invisiableWebView()
//                    }
                } else if (info.code == "104") {
                    //{"userId":"321088199412103778","code":"104","info":["http://146.56.222.41:8080/group1/M00/01/02/Cs4ABWJdBdeAQ_73AACNRgVcb0Y992.png"]}
//                    val bitmap = BitmapTool.stringToBitmap(info.info)
                    layoutBanner.visibility = View.GONE
//                    notification_vedio?.setBackgroundColor(Color.TRANSPARENT)
                    signature.visibility = View.VISIBLE
//                    signature.setImageBitmap(bitmap)
                    var signGS = info.info?.substring(2, info.info?.length!! - 2).toString();
                    Log.d(TAG, "signGS == $signGS")
                    if (!signGS.isNullOrEmpty())
                        Glide.with(mActivity).load(signGS)
                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                .transition(DrawableTransitionOptions.withCrossFade()).into(signature)

                } else if (info.code == "next1") {
                    val bannerList = JSON.parseArray(info.info, String::class.java)
                    showMatterView(bannerList)
                } else if (info.code == "102") {
                    //签字
                    socketSendCode = "020"
                    signature(info.name, info.idCard, "shiping,手印", offSet1.toString())
                    offSet1++
                } else if (info.code == "103") {
                    //签字
                    socketSendCode = "220"
                    signature(info.info, info.curidCard, "shiping,被接谈人签名：", info.offset.toString())
                    offSet++
                } else if (info.code == "1002") {
                    socketSendCode = "2002"
                    val sUser: SignatureUserInfo = JSON.parseObject(info.info, SignatureUserInfo::class.java)
                    if (info.keyWordIndex == null) {
                        if (info.isUpload_pdf) {
                            signature(sUser.name, sUser.idCard, "shiping," + sUser.keyWord, "ZMsame_ispdf")
                        } else {
                            signature(sUser.name, sUser.idCard, "shiping," + sUser.keyWord, "ZMsame")
                        }
                    } else {
                        if (info.isUpload_pdf) {
                            signature(sUser.name, sUser.idCard, "shiping," + sUser.keyWord, "ZMsame_ispdf")
                        } else {
                            signature(sUser.name, sUser.idCard, "shiping," + sUser.keyWord, "ZMsame,${info.keyWordIndex}")
                        }
                    }
                } else if (info.code == "510") {
                    onNavigationItemSelected(2)
                    if (!TextUtils.isEmpty(info.info!!))
                        Handler().postDelayed({ threeFragment?.showHtml(info.info!!) }, 200)
//                    threeFragment?.showHtml(info.info!!)
                } else if (info.code == "view-onlyoffice") {
//                    var officeConfigResponse = JSON.parseObject(info.info, OfficeSocketBean::class.java)
//                    Log.e("Test", officeConfigResponse.onlyOfficeId + "," + officeConfigResponse.orderId)
//                    if (!TextUtils.isEmpty(officeConfigResponse.orderId) && !TextUtils.isEmpty(officeConfigResponse.onlyOfficeId)) {
//                        if (stepView.currentStep == 2) {
//                            //被证明文件
//                            threeFragment?.getOfficeConfig(officeConfigResponse.orderId, officeConfigResponse.onlyOfficeId)
//                        } else if (stepView.currentStep == 3) {
//                            //谈话笔录
//                            fourFragment?.getOfficeConfig(officeConfigResponse.orderId, officeConfigResponse.onlyOfficeId)
//                        }
//
//                    }
                } else if (info.code == "close-onlyoffice") {
//                    if (stepView.currentStep == 2) {
//                        //被证明文件
//                        threeFragment?.invisiableWebView()
//                    } else if (stepView.currentStep == 3) {
//                        //谈话笔录
//                        fourFragment?.invisiableWebView()
//                    }

                } else if (info.code == "40002") {
                    twoFragment?.removePhoto(info.index)
                } else if (info.code == "023") {
                    fiveFragment?.refreshAddress(info.info)
                } else if (info.code == "025") {
                    fiveFragment?.refreshGZF(info.info)
                } else if (info.code == "026") {
                    fiveFragment?.refreshFLFWF(info.info)
                } else if (info.code == "027") {
                    fiveFragment?.refreshSMFWF(info.info)
                } else if (info.code == "028") {
                    fiveFragment?.refreshPZF(info.info)
                } else if (info.code == "029") {
                    fiveFragment?.refreshQTFWF(info.info)
                } else if (info.code == "300") {
                    //TODO 收到线上支付的mqtt，展示支付dialog
                    fiveFragment?.showPayDialog(notarizationInfo?.orderNo!!, notarizationInfo?.unitGuid!!,roomId!!,userInfo?.idCard)
//                    fiveFragment?.getQrCode(notarizationInfo?.orderNo!!)
                } else if (info.code == "realSign") {
                    socketSendCode = "realSignCB"
                    if (!isForeground(mActivity, TakePhotoActivity::class.java.name)) {
                        toastInfo(getString(R.string.prepare_take_photo))
                        val intent = Intent(mActivity, TakePhotoActivity::class.java)
                        intent.putExtra("come", 1)
                        readyGo(intent)
                    }
                    snapshotVideo()
                } else if (info.code == "verfi") {
                    socketSendCode = "verfied"
                    snapshotVideo()
                } else if (info.code == "closeGaoP") {
                    EventBus.getDefault().post(TakePhotoInfo(1))
                }
            }
        }
    }

    fun baojin() {
        val json = JSONObject()
        json.put("code", "callPolice")
        json.put("userId", roomId)
        json.put("info", "")
        if (myMqttService != null)
            myMqttService?.publish(json.toString())
    }

    private fun setDrawView(length: Int?) {
        var with: Int = main_linlayout!!.width * length!!
        var dou: Double? = 1.0 + length.minus(1) * 0.5
        if (dou != null) {
            with = (dou * main_linlayout!!.width).toInt() + 1
        }
        drawView = DrawView(mActivity, main_linlayout!!.width, main_linlayout!!.height)
        main_linlayout!!.addView(drawView)
        drawView!!.requestFocus()
    }

    /**
     * 签字
     */
    private fun signature(name: String?, cardNo: String?, keyWord: String?, offset: String?) {
        Log.i(TAG, "signature")
        if (main_linlayout!!.width == 0) {
            val vto2 = main_linlayout!!.viewTreeObserver
            vto2.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    Log.d("zpzp", "画布初始化完成")
                    main_linlayout!!.viewTreeObserver.removeGlobalOnLayoutListener(this)
                    setDrawView(name!!.length)
                }
            })
        } else {
            if (drawView == null) {
                setDrawView(name!!.length)
            } else {
                main_linlayout!!.removeAllViews()
                setDrawView(name!!.length)
            }
        }

        positions = 0
        signatureView?.visibility = View.VISIBLE
        user_name = name.toString()
        if (!TextUtils.isEmpty(user_name)) {
//            if (user_name.length < 2) {
                bt?.text = getString(R.string.done)
//            } else {
//                bt?.text = getString(R.string.next_one)
//            }
        }
        name_pos?.text = user_name
    }

    /**
     * 显示文件
     */
    private fun showMatterView(bannerList: List<String>) {
        Log.i(TAG, "showMatterView")
        layoutBanner.visibility = View.VISIBLE
        val adapter = CustomBannerAdapter(bannerList)
        banner?.let {
            it.addBannerLifecycleObserver(this)
            it.setIntercept(false)
            it.adapter = adapter
        }
    }

    private fun removeNull(list: ArrayList<RoomUserInfo>) {
        Log.i(TAG, "removeNull")
        val value: MutableListIterator<RoomUserInfo> = list.listIterator()
        while (value.hasNext()) {
            val userInfo: RoomUserInfo = value.next()
            if (TextUtils.isEmpty(userInfo.address) &&
                    TextUtils.isEmpty(userInfo.name) &&
                    TextUtils.isEmpty(userInfo.idCard) &&
                    TextUtils.isEmpty(userInfo.mobile) &&
                    TextUtils.isEmpty(userInfo.principal)) {
                value.remove()
            }
        }
    }

    //--------------------点击事件
    fun lookClick(view: View) {
        Log.i(TAG, "lookClick")
        layoutBanner.visibility = View.GONE
        val json = JSONObject()
        json.put("code", "888")
        json.put("userId", roomId)
        myMqttService?.publish(json.toString())
    }


    override fun onStop() {
        Log.i(TAG, "onStop")
        super.onStop()
    }

    override fun onStart() {
        Log.i(TAG, "onStart")
        super.onStart()
    }

    override fun onDestroy() {
        Log.i(TAG, "onDestroy")
        try {
            if (banner != null) {
                banner.destroy()
            }
            if (hander.hasMessages(999))
                hander.removeMessages(999)
            myMqttService?.unSubscribe()
            //leaveChannel(true)
            exitRoom(true)
            mUSBMonitor?.unregister()
            if (mUSBMonitor1?.isRegistered == true)
                mUSBMonitor1?.unregister()
            UVCCameraUtil.releaseRGBCamera()
            UVCCameraUtil.releaseIRCamera()
            //增加关闭高拍仪
            if (mUSBMonitor1 != null)
                UVCCameraUtil.releaseHighCamera()
            //增加关闭usb外接摄像头
            UVCCameraUtil.releaseUsbCamera()
            mUSBMonitor?.destroy()
            mUSBMonitor = null
            mUSBMonitor1?.destroy()
            mUSBMonitor1 = null
        } catch (e: Exception) {
            Log.d(TAG, e.message)
        }
        super.onDestroy()
    }

    /*添加fragment*/
    private fun addFragment(fragment: Fragment) {
        Log.i(TAG, "addFragment")
        /*判断该fragment是否已经被添加过  如果没有被添加  则添加*/
        if (!fragment.isAdded) {
            supportFragmentManager.beginTransaction().add(R.id.frameStep, fragment).commitAllowingStateLoss()
            supportFragmentManager.executePendingTransactions()
            fragmentList?.add(fragment)
        }
    }

    /*替换fragment*/
    private fun replaceFragment(fragment: Fragment) {
        /*判断该fragment是否已经被添加过*/
        if (!fragment.isAdded) {
            supportFragmentManager.beginTransaction().replace(R.id.frameStep, fragment).commitAllowingStateLoss()
//            fragmentList?.add(fragment)
        } else {
            showFragment(fragment)
        }
    }

    /**
     * 显示fragment
     */
    private fun showFragment(fragment: Fragment) {
        Log.i(TAG, "showFragment")
        if (fragmentList != null) {
            for (frag in fragmentList) {
                if (frag !== fragment) {
                    /*先隐藏其他fragment*/
                    supportFragmentManager.beginTransaction().hide(frag).commitAllowingStateLoss()
                }
            }
        }
        supportFragmentManager.beginTransaction().show(fragment).commitAllowingStateLoss()
    }

    private fun onNavigationItemSelected(pos: Int): Boolean {
        Log.i(TAG, "onNavigationItemSelected$pos")
        stepView.go(pos, true)
        when (pos) {
            0 -> {
                if (oneFragment == null) {
                    oneFragment = VideoStepOneFragment()
                    addFragment(oneFragment!!)
                }
                replaceFragment(oneFragment!!)
            }
            1 -> {
                hander.removeMessages(999)
                if (twoFragment == null) {
                    twoFragment = VideoStepTwoFragment(this)
                    addFragment(twoFragment!!)
                }
                replaceFragment(twoFragment!!)
            }
            2 -> {
                hander.removeMessages(999)
                if (threeFragment == null) {
                    threeFragment = VideoStepThreeFragment()
                    addFragment(threeFragment!!)
                }
                replaceFragment(threeFragment!!)
            }
            3 -> {
                hander.removeMessages(999)
                if (fourFragment == null) {
                    fourFragment = VideoStepFourFragment()
                    addFragment(fourFragment!!)
                }
                replaceFragment(fourFragment!!)
            }
            4 -> {
                hander.removeMessages(999)
                if (fiveFragment == null) {
                    fiveFragment = VideoStepFiveFragment(this)
                    fiveFragment?.refreshApplicationList(userList)
                    addFragment(fiveFragment!!)
                }
                replaceFragment(fiveFragment!!)
            }
        }
        return true
    }

    /**
     * 上传签名
     */
    override fun updateSignatureSuccess(bean: ImageBean?, encData: String?) {
        Log.i(TAG, "updateSignatureSuccess 上传签名")
        val map: MutableMap<String, String> = HashMap()
        map["code"] = socketSendCode!!
        map["encDataFilePath"] = encData!!
        map["info"] = bean?.filePath!!
        map["userId"] = roomId!!
        val signature: String? = JSON.toJSONString(map)
        DLog.d(signature)
        myMqttService?.publish(signature)
    }

    override fun saveSignatureSuccess(issuccess: Boolean) {
        if (!issuccess) {
            toastError(getString(R.string.sign_fail_hint))
            bt!!.text = getString(R.string.done)
            name_pos?.text = user_name
            reSign()
        } else {
            signatureView.visibility = GONE
        }

    }

    override fun updateSignatureFailed(msg: String?) {
        toastError(getString(R.string.sign_fail_hint))
        bt!!.text = getString(R.string.done)
        reSign()
    }

    override fun isUseEventBus(): Boolean {
        return true
    }

    @Subscribe
    fun onEventMainThread(eb: EBPhotoInfo) {
        Log.i(TAG, "onEventMainThread")
        if (socketSendCode == "realSignCB") {
            val base64: String = BitmapTool.bitmapToBase64(eb.path)
            val map: MutableMap<String, String> = HashMap()
            map["code"] = socketSendCode!!
            map["info"] = "data:image/png;base64,$base64"
            map["userId"] = roomId!!
            val img: String? = JSON.toJSONString(map).replace("\\n", "")
            myMqttService?.publish(img)
            socketSendCode = "over"
        } else {
            twoFragment?.showPhoto(eb.path)
        }
    }


    override fun updateMaterialSuccess(bean: MaterialInfo?) {
        Log.i(TAG, "updateMaterialSuccess")
        val list: List<*> = listOf(bean)
        myMqttService?.publish(JSON.toJSONString(SocketSendInfo("010", list, roomId)))
    }

    override fun payFail(payType: Int) {
        Log.i(TAG, "payFail")
        if (payType == 1) {
            val json = com.alibaba.fastjson.JSONObject()
            json.put("code", "3333")
            json.put("info", "微信支付失败")
            json.put("userId", roomId)
            myMqttService!!.publish(json.toJSONString())
        } else {
            val json = com.alibaba.fastjson.JSONObject()
            json.put("code", "3333")
            json.put("info", "支付宝支付失败")
            json.put("userId", roomId)
            myMqttService!!.publish(json.toJSONString())
        }
    }

    override fun paySuccess(payType: Int) {
        Log.i(TAG, "paySuccess")
        if (payType == 1) {
            val json = com.alibaba.fastjson.JSONObject()
            json.put("code", "301")
            json.put("info", "微信支付成功")
            json.put("userId", roomId)
            myMqttService!!.publish(json.toJSONString())
        } else {
            val json = com.alibaba.fastjson.JSONObject()
            json.put("code", "301")
            json.put("info", "支付宝支付成功")
            json.put("userId", roomId)
            myMqttService!!.publish(json.toJSONString())
        }

    }

    private fun snapshotVideo() {
        Log.i(TAG, "snapshotVideo")
        if (socketSendCode == "realSignCB") {
            EventBus.getDefault().post(TakePhotoInfo(0))
        } else {
//                updateP?.updateBitmap(surfaceView!!.bitmap)
            updateP?.updateBitmap(textureView!!.bitmap)
        }

    }

    override fun updateImageSuccess(bean: ImageBean?) {
        Log.i(TAG, "updateImageSuccess")
        val map: MutableMap<String, String> = HashMap()
        map["code"] = socketSendCode!!
        map["info"] = bean?.filePath!!
        map["userId"] = roomId!!
        val img: String? = JSON.toJSONString(map)
        myMqttService?.publish(img)
        signatureView.visibility = GONE
        bt!!.text = "完成"
        if (!TextUtils.isEmpty(user_name))
            name_pos?.text = user_name
    }

    override fun updateImageFail() {
        Log.i(TAG, "updateImageFail")
        toastError(getString(R.string.sign_fail_hint))
        bt!!.text = getString(R.string.done)
        reSign()
    }

    override fun getAppIdAndTokenError() {
        Log.i(TAG, "getAppIdAndToken failed 连接失败")
    }

    override fun getAppIdAndTokenSuccess(appIdAndTokenEntity: AppIdAndTokenEntity?) {
        Log.i(TAG, "getAppIdAndTokenSuccess")
        this.appIdAndTokenEntity = appIdAndTokenEntity
        //initAgoraEngineAndJoinChannel()
        initAgoraEngineAndJoinChannel()
    }

    /**
     * 初始化
     */
    private fun initAgoraEngineAndJoinChannel() {
        Log.i(TAG, "initAgoraEngineAndJoinChannel")
        initializeAgoraEngine()
        setupLocalVideo()
    }

    private val mRtcEventHandler = object : IRtcEngineEventHandler() {

        // 注册 onUserJoined 回调。
        // 远端用户成功加入频道时，会触发该回调。
        // 可以在该回调用调用 setupRemoteVideo 方法设置远端视图。
        override fun onUserJoined(uid: Int, elapsed: Int) {
            Log.i(TAG, "onUserJoined + " + uid)
            if (uid == 888) {
                runOnUiThread { setupRemoteVideo(uid) }
            }
        }

        // 注册 onUserOffline 回调。远端用户离开频道后，会触发该回调。
        override fun onUserOffline(uid: Int, reason: Int) {
            Log.i(TAG, "onUserOffline" + uid)
        }

    }

    /**
     * 设置远端视频
     */
    private fun setupRemoteVideo(uid: Int) {
        Log.i(TAG, "setupRemoteVideo")
        val container = findViewById<FrameLayout>(R.id.trtcRemote)

        // 创建一个 SurfaceView 对象。
        val surfaceView = RtcEngine.CreateRendererView(baseContext)
        container.addView(surfaceView, FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT))
        // 设置远端视图。
        mRtcEngine!!.setupRemoteVideo(VideoCanvas(surfaceView, VideoCanvas.RENDER_MODE_HIDDEN, uid))
    }

    /**
     * 离开房间
     */
    private fun leaveChannel(isNormalExit: Boolean) {
        Log.i(TAG, "leaveChannel")
        if (isNormalExit) {
            toastSuccess(getString(R.string.video_notaization_over))
        } else {
            toastError(getString(R.string.contact_admin))
        }
        mRtcEngine!!.leaveChannel()
        AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
    }

    /**
     * 调用 Agora SDK 的方法初始化 RtcEngine。
     */
    private fun initializeAgoraEngine() {
        Log.i(TAG, "initializeAgoraEngine")
        try {
            mRtcEngine = RtcEngine.create(baseContext, "2137777e43314068bd9b3106643e84e2", mRtcEventHandler)
            // Initialize Screen Share Client
            mSSClient = ScreenSharingClient.getInstance()
            mSSClient!!.setListener(mListener)
        } catch (e: Exception) {
            Log.e(TAG, Log.getStackTraceString(e))

            throw RuntimeException("NEED TO check rtc sdk init fatal error\n" + Log.getStackTraceString(e))
        }
    }

    private val mListener: ScreenSharingClient.IStateListener = object : ScreenSharingClient.IStateListener {
        override fun onError(error: Int) {
            Log.e(TAG, "Screen share service error happened: $error")
        }

        override fun onTokenWillExpire() {
            Log.d(TAG, "Screen share service token will expire")
            mSSClient!!.renewToken(null) // Replace the token with your valid token
        }
    }

    /**
     * 设置本地视频
     */
    private fun setupLocalVideo() {
        Log.e(TAG, "setupLocalVideo")
        surfaceView = TextureView(baseContext)
        surfaceView!!.surfaceTextureListener = this


        val container = findViewById<FrameLayout>(R.id.trtcMain)
        container.addView(surfaceView, FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT))
        /**Set up to play remote sound with receiver */
        mRtcEngine!!.setDefaultAudioRoutetoSpeakerphone(false)
        mRtcEngine!!.setEnableSpeakerphone(false)
        mRtcEngine!!.setChannelProfile(Constants.CHANNEL_PROFILE_LIVE_BROADCASTING)
        /**In the demo, the default is to enter as the anchor.*/
        mRtcEngine!!.setClientRole(IRtcEngineEventHandler.ClientRole.CLIENT_ROLE_BROADCASTER)
        // 启用视频模块。
        mRtcEngine!!.enableVideo()
        // Setup video encoding configs
        val globalSettings = GlobalSettings()
        mRtcEngine!!.setVideoEncoderConfiguration(VideoEncoderConfiguration(
                globalSettings.videoEncodingDimensionObject,
                VideoEncoderConfiguration.FRAME_RATE.valueOf(globalSettings.videoEncodingFrameRate),
                VideoEncoderConfiguration.STANDARD_BITRATE,
                VideoEncoderConfiguration.ORIENTATION_MODE.valueOf(globalSettings.videoEncodingOrientation)
        ))
        mRtcEngine!!.setExternalVideoSource(true, true, true)
        // 设置本地视图。
        mRtcEngine!!.setupLocalVideo(VideoCanvas(surfaceView, VideoCanvas.RENDER_MODE_HIDDEN, 1))

        /** Allows a user to join a channel.
         * if you do not specify the uid, we will generate the uid for you */
        val option = ChannelMediaOptions()
        option.autoSubscribeAudio = true
        option.autoSubscribeVideo = true

        // 调用 joinChannel 方法加入频道。
        mRtcEngine!!.joinChannel(appIdAndTokenEntity?.token, roomId, "Extra Optional Data", 1, option)
        // 给pc端推送本地屏幕流
        mSSClient!!.start(
                this,
                "2137777e43314068bd9b3106643e84e2",
                appIdAndTokenEntity?.token,
                roomId,
                2,
                VideoEncoderConfiguration(
                        VideoEncoderConfiguration.VideoDimensions(1000, 1000),
                        VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_30,
                        VideoEncoderConfiguration.STANDARD_BITRATE,
                        VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE
                ),
                AppConfig.RGBPID
        )
    }

    /**
     * 是否是可见页面
     */
    private fun isForeground(context: Context?, className: String): Boolean {
        if (context == null || TextUtils.isEmpty(className)) return false
        val am = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val list = am.getRunningTasks(1)
        if (list != null && list.size > 0) {
            val cpn = list[0].topActivity
            if (className == cpn!!.className) return true
        }
        return false
    }

    override fun onSurfaceTextureAvailable(surface: SurfaceTexture?, width: Int, height: Int) {
        Log.i(TAG, "onSurfaceTextureAvailable")
        mTextureDestroyed = false
        mSurfaceWidth = width
        mSurfaceHeight = height
        mEglCore = EglCore()
        mDummySurface = mEglCore!!.createOffscreenSurface(1, 1)
        mEglCore!!.makeCurrent(mDummySurface)
        mPreviewTexture = GlUtil.createTextureObject(GLES11Ext.GL_TEXTURE_EXTERNAL_OES)
        // 创建新的 SurfaceTexture 对象，用于摄像头预览
        mPreviewSurfaceTexture = SurfaceTexture(mPreviewTexture)
        // 通过 Android 原生方法 setOnFrameAvailableListener 创建 OnFrameAvailableListener，监听是否有新的视频帧可用于 SurfaceTexture。如果有则触发 onFrameAvailable 回调
        mPreviewSurfaceTexture!!.setOnFrameAvailableListener(this)
        mDrawSurface = mEglCore!!.createWindowSurface(surface)
        mProgram = ProgramTextureOES()
        if (mCamera != null || mPreviewing) {
            Log.e(TAG, "Camera preview has been started")
            return
        }
        try {
            if (localPid == AppConfig.RGBPID) { //打开彩色摄像头
                Log.i(TAG, "onSurfaceTextureAvailable22 openRGBCamera")
                UVCCameraUtil.openRGBCamera(mPreviewSurfaceTexture!!, localCtrlBlock!!, object : IFrameCallback {
                    override fun onFrame(frame: ByteBuffer?) {
                        Log.i(TAG, "onSurfaceTextureAvailable22 openRGBCamera success")
                    }

                    override fun onFail() {
                        Log.i(TAG, "onSurfaceTextureAvailable22 openRGBCamera fail")
                    }
                })
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture?, width: Int, height: Int) {
    }

    override fun onSurfaceTextureDestroyed(surface: SurfaceTexture?): Boolean {
        Log.i(TAG, "onSurfaceTextureDestroyed")
        mTextureDestroyed = true
        if (mCamera != null && mPreviewing) {
            mCamera!!.stopPreview()
            mPreviewing = false
            mCamera!!.destroy()
            mCamera = null
        }
        mProgram!!.release()
        mEglCore!!.releaseSurface(mDummySurface)
        mEglCore!!.releaseSurface(mDrawSurface)
        mEglCore!!.release()
        return true
    }

    override fun onSurfaceTextureUpdated(surface: SurfaceTexture?) {
    }

    override fun onFrameAvailable(surfaceTexture: SurfaceTexture?) {
        Log.i(TAG, "onFrameAvailable")
        if (mTextureDestroyed) {
            return
        }
        if (!mEglCore!!.isCurrent(mDrawSurface)) {
            mEglCore!!.makeCurrent(mDrawSurface)
        }
        // 调用 updateTexImage() 将数据更新到 OpenGL ES 纹理对象
        // 调用 getTransformMatrix() 转换纹理坐标
        try {
            mPreviewSurfaceTexture!!.updateTexImage()
            mPreviewSurfaceTexture!!.getTransformMatrix(mTransform)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        // 设置 MVP 矩阵
        if (!mMVPMatrixInit) {
            // 本示例指定 activity 为竖屏模式。采集的图像会旋转 90 度，因此宽高数据在计算 frame ratio 时需要互换。
            val frameRatio: Float = DEFAULT_CAPTURE_HEIGHT / DEFAULT_CAPTURE_WIDTH.toFloat()
            val surfaceRatio: Float = mSurfaceWidth / mSurfaceHeight.toFloat()
            Matrix.setIdentityM(mMVPMatrix, 0)
            if (frameRatio >= surfaceRatio) {
                val w: Float = DEFAULT_CAPTURE_WIDTH * surfaceRatio
                val scaleW: Float = DEFAULT_CAPTURE_HEIGHT / w
                Matrix.scaleM(mMVPMatrix, 0, scaleW, 1F, 1F)
            } else {
                val h: Float = DEFAULT_CAPTURE_HEIGHT / surfaceRatio
                val scaleH: Float = DEFAULT_CAPTURE_WIDTH / h
                Matrix.scaleM(mMVPMatrix, 0, 1F, scaleH, 1F)
            }
            mMVPMatrixInit = true
        }
        // 设置视口大小
        GLES20.glViewport(0, 0, mSurfaceWidth, mSurfaceHeight)
        // 绘制视频帧
        mProgram!!.drawFrame(mPreviewTexture, mTransform, mMVPMatrix)
        // 将 EGL 图像 buffer 传递到 EGL Surface 用于播放，实现本地预览。mDrawSurface 是 EGLSurface 类的对象。
        mEglCore!!.swapBuffers(mDrawSurface)

        // 设置外部视频帧
        val frame = AgoraVideoFrame()
        frame.textureID = mPreviewTexture
        frame.format = AgoraVideoFrame.FORMAT_TEXTURE_OES
        frame.transform = mTransform
        if (AppConfig.RGBPID == "c013") {
            frame.stride = DEFAULT_CAPTURE_HEIGHT
            frame.height = DEFAULT_CAPTURE_WIDTH
        } else {
            frame.stride = 640
            frame.height = 650
        }

        frame.eglContext14 = mEglCore!!.eglContext
        frame.timeStamp = System.currentTimeMillis()
        // 向 SDK 推送外部视频帧
        val a: Boolean = mRtcEngine!!.pushExternalVideoFrame(frame)
        Log.e(TAG, "pushExternalVideoFrame:$a")
    }

    @SuppressLint("ResourceAsColor")
    override fun onClick(v: View?) {
        //end 编号：GCXMJC4756
        when (v!!.id) {
            R.id.bt -> {
//                val bit = drawView!!.paintBitmap
//                if (getString(R.string.next_one) == bt!!.text) {
////                    bitmapList.add(bit)
//                    positions++
//                    if (positions == user_name.length - 1) {
//                        bt!!.text = getString(R.string.done)
//                    }
//                    //                    drawView.clear();
//                    if (positions != 0) {
//                        drawView!!.scrollTo(main_linlayout!!.width / 2 * positions, 0)
//                        drawView!!.setwith(main_linlayout!!.width / 2 * positions)
//                    }
//                    name_pos?.text = user_name.substring(positions, positions + 1)
//                } else {
                    //上传图片到服务器 fastupload接口
                if (drawView?.isCanvasEmpty() == true) {
                    toastError("请先签字")
                } else {
                    val bit = drawView!!.getPaintBitmap(user_name.length)
                    updateP?.updateBitmap(bit)
                    drawView!!.clear()
//                    bitmapList.clear()
                    if (positions != 0)
                        drawView!!.scrollTo(0, 0)
                    positions = 0
                }
            }
            R.id.bt_clear -> {
                reSign()
//                drawView!!.clear()
//                if (positions != 0) {
//                    drawView!!.scrollTo(0, 0)
//                    drawView!!.setwith(0)
//                    positions = 0
//                    if (user_name.length < 2) {
//                        bt?.text = "完成"
//                    } else {
//                        bt?.text = "下一个"
//                    }
//                    name_pos?.text = user_name.substring(0, 1)
//                }
            }
        }
    }

    fun reSign() {
        try {
            if (drawView == null) {
                setDrawView(user_name!!.length)
            } else {
                drawView!!.clear()
                main_linlayout!!.removeAllViews()
                setDrawView(user_name!!.length)
            }
            positions = 0
            signatureView?.visibility = View.VISIBLE

            if (!TextUtils.isEmpty(user_name)) {
//                if (user_name.length < 2) {
                    bt?.text = getString(R.string.done)
//                } else {
//                    bt?.text = getString(R.string.next_one)
//                }
            }
            name_pos?.text = user_name
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}

