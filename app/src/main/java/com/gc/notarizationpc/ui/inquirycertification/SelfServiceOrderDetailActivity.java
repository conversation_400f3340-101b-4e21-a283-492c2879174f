package com.gc.notarizationpc.ui.inquirycertification;

import android.content.Intent;
import android.graphics.Bitmap;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Bundle;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.example.scarx.idcardreader.utils.IdCardRenderUtils;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivitySelfServiceOrderDetailBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.data.model.response.SelfServiceOrderDetailModel;
import com.gc.notarizationpc.data.model.response.SelfServiceOrderListModel;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.selfservicecertificate.SelfNotarizationActivity;
import com.gc.notarizationpc.ui.view.ImagePreviewDialog;
import com.gc.notarizationpc.ui.view.SignDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationApplyInfoDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationLawDialog;
import com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderDetailViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.websocket.MyMqttService;
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo;

import java.util.ArrayList;
import java.util.List;

import cc.shinichi.library.ImagePreview;
import cc.shinichi.library.bean.ImageInfo;
import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.utils.SPUtils;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class SelfServiceOrderDetailActivity extends BaseActivity<ActivitySelfServiceOrderDetailBinding, SelfServiceOrderDetailViewModel> {
    private String TAG = "Test";
    private IdCardRenderUtils idCardReaderUtils = new IdCardRenderUtils();
    private SoundPool soundPool = new SoundPool(100, AudioManager.STREAM_MUSIC, 0);
    private boolean isActivity = true;
    private boolean isNoCard = true;
    private IDCardInfo idcardinfo = null;
    private Bitmap idCardBmp = null;
    private Integer readSuccess = null;
    private Integer readTips = null;
    private Integer validateSuccess = null;
    private Integer validateFail = null;
    private Integer retry = null;
    private Integer faceCamera = null;
    private boolean hasCardInfo = false;
    private SignDialog signDialog;

    private MyMqttService myMqttService;

    private FragmentManager fragmentManager = getSupportFragmentManager();

    private Fragment fragment;

    private VideoNotarizationApplyInfoDialog dialog;

    private VideoNotarizationLawDialog lawDialog;

    private Handler mHandler;

    private SelfServiceOrderListModel.DataListDTO model;

    // 图片预览
    private ImagePreviewDialog imagePreviewDialog;


    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_self_service_order_detail;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public SelfServiceOrderDetailViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(SelfServiceOrderDetailViewModel.class);
    }


    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }


    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }


    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });
        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });
//        viewModel.getNotaryPurposeData();
        model = (SelfServiceOrderListModel.DataListDTO) getIntent().getSerializableExtra("orderData");
        /**
         * 1-草稿 2-待确认 3-待补充材料 4-待阅读文书 5-待支付 6-审查中 7-待出征 8-已出证 9-终止
         */
        // 待补充材料
        if (model.getProcessState() == 3) {
            viewModel.orderStatus.set(getString(R.string.waitToSupplementaryMaterials));
            binding.readDocument.setVisibility(View.GONE);
            binding.applyMaterial.setVisibility(View.VISIBLE);
        } else if (model.getProcessState() == 4) {
            // 待阅读文书
            viewModel.orderStatus.set(getString(R.string.waitingToReadInstrument));
            binding.readDocument.setVisibility(View.VISIBLE);
            binding.applyMaterial.setVisibility(View.VISIBLE);
        } else if (model.getProcessState() == 5) {
            // 待支付
            viewModel.orderStatus.set(getString(R.string.waitToPay));
            binding.readDocument.setVisibility(View.GONE);
            binding.applyMaterial.setVisibility(View.VISIBLE);
        } else if (model.getProcessState() == 9) {
            // 已终止
            viewModel.orderStatus.set(getString(R.string.terminated));
        } else if (model.getProcessState() == 8) {
            // 已归档 (已出证)
            viewModel.orderStatus.set(getString(R.string.hasBeenTestified));
        } else if (model.getProcessState() == 7) {
            // 待发证
            viewModel.orderStatus.set(getString(R.string.toBeIssued));
            binding.readDocument.setVisibility(View.GONE);
            binding.applyMaterial.setVisibility(View.GONE);
        } else if (model.getProcessState() == 6) {
            // 受理中
            viewModel.orderStatus.set(getString(R.string.underConsideration));
        }
        if (model.getProcessState() == 9 ||  model.getProcessState() == 8 || model.getProcessState() == 6) {
            viewModel.bottomStatusVisibility = false;
            LinearLayout layout = findViewById(R.id.activity_order_detail_main_layout);
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) layout.getLayoutParams();
            layoutParams.bottomMargin = getResources().getDimensionPixelSize(R.dimen.space_30);
            layout.setLayoutParams(layoutParams);
        } else {
            viewModel.bottomStatusVisibility = true;
        }
        viewModel.requestNetWork(model.getId());
        mHandler = new MyHandler(this);

//        fragment = new  OrderDetailApplyInformationFragment();
//        fragmentManager.beginTransaction().replace(R.id.order_detail_apply_information, fragment).commit();

    }

    @Override
    public void initViewObservable() {
        viewModel.unitInformationVisibilityEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                binding.unitInformationList.setVisibility(View.VISIBLE);
            } else {
                binding.unitInformationList.setVisibility(View.GONE);
            }
        });
        viewModel.feeVisibilityEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                binding.feeInformationList.setVisibility(View.VISIBLE);
            } else {
                binding.feeInformationList.setVisibility(View.GONE);
            }
        });
        viewModel.applyMaterialEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                Intent intent = new Intent(SelfServiceOrderDetailActivity.this, SelfNotarizationActivity.class);
                intent.putExtra("fragment_id", "fragment_1"); // 传递要显示的 Fragment 的标识符
                intent.putExtra("useType", model.getUsedPurpose());
                intent.putExtra("applicationItem",viewModel.selfServiceOrderDetailModel.get().getApplicationItem());
                SPUtils.getInstance().put("self_recordId", model.getId());
                startActivityForResult(intent,1000);
            }
        });

        viewModel.readDocumentEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                Intent intent = new Intent(SelfServiceOrderDetailActivity.this, SelfNotarizationActivity.class);
                intent.putExtra("fragment_id", "fragment_2"); // 传递要显示的 Fragment 的标识符
                intent.putExtra("useType", model.getUsedPurpose());
                intent.putExtra("applicationItem",viewModel.selfServiceOrderDetailModel.get().getApplicationItem());
                SPUtils.getInstance().put("self_recordId", model.getId());
                startActivityForResult(intent,1000);
            }
        });


        viewModel.stopCaseEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                PopwindowUtil.showNormalDialog(this, getString(R.string.whetherToTerminate), getString(R.string.theOrdeStatusChangesToTerminatedAfterTermination), getString(R.string.confirm), getString(R.string.cancel), new PopwindowUtil.ButtonClickListener() {
                    @Override
                    public void cancel() {

                    }

                    @Override
                    public void decide() {
                        viewModel.stopCase(model.getId());
                    }
                });
            }
        });

        viewModel.getStopCaseEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                setResult(1, new Intent());
                finish();
                toastSuccess(getString(R.string.successToTerminateCase));
            }
        });

        viewModel.documentPreViewEvent.observe(this, new Observer<List<String>>() {
            @Override
            public void onChanged(List<String> strings) {
                if (strings == null || strings.isEmpty()) {
                    return;
                }
//                if (imagePreviewDialog == null || !imagePreviewDialog.isShowing()) {
//                    imagePreviewDialog = new ImagePreviewDialog(SelfServiceOrderDetailActivity.this, strings);
//                    imagePreviewDialog.show();
//                }
                for (String imageString:strings) {
                    List<ImageInfo> imgs = new ArrayList<>();
                    ImageInfo imageInfo = new ImageInfo();
                    imageInfo.setThumbnailUrl(imageString);
                    imageInfo.setOriginUrl(imageString);
                    imgs.add(imageInfo);
                    ImagePreview.getInstance().setContext(SelfServiceOrderDetailActivity.this).setShowCloseButton(false).setShowDownButton(false).setImageInfoList(imgs).setTransitionShareElementName("shared_element_container").start();
                }
            }
        });

        viewModel.networkResult.observe(this, new Observer<SelfServiceOrderDetailModel>() {
            @Override
            public void onChanged(SelfServiceOrderDetailModel selfServiceOrderDetailModel) {
                if (selfServiceOrderDetailModel != null) {
                    if (selfServiceOrderDetailModel.getProcessState() == 3) {
                        viewModel.orderStatus.set(getString(R.string.waitToSupplementaryMaterials));
                        binding.readDocument.setVisibility(View.GONE);
                        binding.applyMaterial.setVisibility(View.VISIBLE);
                    } else if (selfServiceOrderDetailModel.getProcessState() == 4) {
                        // 待阅读文书
                        viewModel.orderStatus.set(getString(R.string.waitingToReadInstrument));
                        binding.readDocument.setVisibility(View.VISIBLE);
                        binding.applyMaterial.setVisibility(View.VISIBLE);
                    } else if (selfServiceOrderDetailModel.getProcessState() == 5) {
                        // 待支付
                        viewModel.orderStatus.set(getString(R.string.waitToPay));
                        binding.readDocument.setVisibility(View.GONE);
                        binding.applyMaterial.setVisibility(View.VISIBLE);
                    } else if (selfServiceOrderDetailModel.getProcessState() == 9) {
                        // 已终止
                        viewModel.orderStatus.set(getString(R.string.terminated));
                    } else if (selfServiceOrderDetailModel.getProcessState() == 8) {
                        // 已归档 (已出证)
                        viewModel.orderStatus.set(getString(R.string.hasBeenTestified));
                    } else if (selfServiceOrderDetailModel.getProcessState() == 7) {
                        // 待发证
                        viewModel.orderStatus.set(getString(R.string.toBeIssued));
                        binding.readDocument.setVisibility(View.GONE);
                        binding.applyMaterial.setVisibility(View.GONE);
                    } else if (selfServiceOrderDetailModel.getProcessState() == 6) {
                        // 受理中
                        viewModel.orderStatus.set(getString(R.string.underConsideration));
                    }
                    if (selfServiceOrderDetailModel.getProcessState() == 9 ||  selfServiceOrderDetailModel.getProcessState() == 8 || selfServiceOrderDetailModel.getProcessState() == 6) {
                        viewModel.bottomStatusVisibility = false;
                        LinearLayout layout = findViewById(R.id.activity_order_detail_main_layout);
                        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) layout.getLayoutParams();
                        layoutParams.bottomMargin = getResources().getDimensionPixelSize(R.dimen.space_30);
                        layout.setLayoutParams(layoutParams);
                    } else {
                        viewModel.bottomStatusVisibility = true;
                    }
                }
            }
        });

    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return false;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1000 && resultCode == RESULT_OK){
            viewModel.requestNetWork(model.getId());
        }
    }
}
