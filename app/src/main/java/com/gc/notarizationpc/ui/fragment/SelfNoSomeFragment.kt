package com.gc.notarizationpc.ui.fragment

import android.os.Bundle
import android.view.View
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseFragment
import com.gc.notarizationpc.ui.NotarizationSelfActivity
import kotlinx.android.synthetic.main.fragment_self_step_six.*


class SelfNoSomeFragment : BaseFragment() {


    private var currentActivity: NotarizationSelfActivity? = null

    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {

        currentActivity = mActivity as NotarizationSelfActivity?;

        tvNextStep?.setOnClickListener {
            currentActivity?.onNavigationItemSelected(5)
        };

    }

    override fun lazyInit(view: View?, savedInstanceState: Bundle?) {


    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.fragment_no_something
    }

}