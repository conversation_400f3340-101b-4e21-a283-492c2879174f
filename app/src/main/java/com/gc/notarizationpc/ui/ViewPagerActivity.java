package com.gc.notarizationpc.ui;

import android.os.Bundle;
import android.util.TypedValue;
import android.view.Gravity;
import android.widget.TextView;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentViewpagerBinding;
import com.gc.notarizationpc.ui.adapter.DataViewPagerAdapter;
import com.gc.notarizationpc.ui.fragment.FragmentList;
import com.gc.notarizationpc.util.CommonUtil;
import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.base.BaseFragment;
import me.goldze.mvvmhabit.base.BaseViewModel;

/**
 * ViewPager绑定的例子, 更多绑定方式，请参考 https://github.com/evant/binding-collection-adapter
 * 所有例子仅做参考,千万不要把它当成一种标准,毕竟主打的不是例子,业务场景繁多,理解如何使用才最重要。
 * Created by goldze on 2018/7/18.
 */

public class ViewPagerActivity extends BaseActivity<FragmentViewpagerBinding, BaseViewModel> {
    public List<BaseFragment> fragmentList;
    public List<String> titles;

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.fragment_viewpager;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }


    @Override
    public void initData() {
        fragmentList = new ArrayList<>();
        fragmentList.add(new FragmentList());
        fragmentList.add(new FragmentList());
        titles = new ArrayList<>();
        titles.add("办证记录");
        titles.add("预约记录");
        // 使用 TabLayout 和 ViewPager 相关联
        binding.tabs.setupWithViewPager(binding.viewPager);
        binding.viewPager.setAdapter(new DataViewPagerAdapter(getSupportFragmentManager(), fragmentList, titles));
        //给ViewPager设置adapter


        for (int i = 0; i < titles.size(); i++) {
            TabLayout.Tab tab = binding.tabs.getTabAt(i);
            //注意！！！这里就是添加我们自定义的布局
            TextView textView = new TextView(this);
            //调整了字大小
            float selectedSize = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_PX, 16, getResources().getDisplayMetrics());
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, selectedSize);
            textView.setTextColor(getResources().getColor(R.color.white));
            textView.setText(tab.getText());
            textView.setGravity(Gravity.CENTER);
            tab.setCustomView(textView);
            //这里是初始化时，默认item0被选中，setSelected（true）是为了给图片和文字设置选中效果，代码在文章最后贴出
            if (i == 0) {
                CommonUtil.setDrawableBottom(getBaseContext(), ((TextView) tab.getCustomView()), R.mipmap.line_blue_width4);
            }
        }

        binding.tabs.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                CommonUtil.setDrawableBottom(getBaseContext(), ((TextView) tab.getCustomView()), R.mipmap.line_blue_width4);
                binding.viewPager.setCurrentItem(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                CommonUtil.setDrawableBottom(getBaseContext(), ((TextView) tab.getCustomView()), null);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
            }
        });
    }

//    @Override
//    public void initViewObservable() {
//        viewModel.itemClickEvent.observe(this, new Observer<String>() {
//            @Override
//            public void onChanged(@Nullable String text) {
//                ToastUtils.showShort("position：" + text);
//            }
//        });
//    }
}
