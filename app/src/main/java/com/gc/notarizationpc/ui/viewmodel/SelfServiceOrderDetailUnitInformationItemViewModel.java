package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.SelfServiceOrderDetailModel;

import me.goldze.mvvmhabit.base.ItemViewModel;

/**
 * Created by goldze on 2017/7/17.
 */

public class SelfServiceOrderDetailUnitInformationItemViewModel extends ItemViewModel<SelfServiceOrderDetailViewModel> {


    public ObservableField<SelfServiceOrderDetailModel.RecordCorporatonApplicantVosDTO> entity = new ObservableField<>();

    public SelfServiceOrderDetailUnitInformationItemViewModel(@NonNull SelfServiceOrderDetailViewModel viewModel, SelfServiceOrderDetailModel.RecordCorporatonApplicantVosDTO entity) {
        super(viewModel);
        Log.e("FragmentGridItemViewModel", "FragmentGridItemViewModel");
        this.entity.set(entity);

        //ImageView的占位图片，可以解决RecyclerView中图片错误问题
//        drawableImg = ContextCompat.getDrawable(viewModel.getApplication(), R.mipmap.ic_launcher);
    }


}
