package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.SelfServiceOrderDetailModel;
import com.gc.notarizationpc.data.model.response.VideoOrderDetailModel;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * Created by goldze on 2017/7/17.
 */

public class SelfServiceOrderDetailDocumentItemViewModel extends ItemViewModel<SelfServiceOrderDetailViewModel> {


    public ObservableField<SelfServiceOrderDetailModel.DocTypeListDTO> entity = new ObservableField<>();

    public String documentPath = "";

    public SelfServiceOrderDetailDocumentItemViewModel(@NonNull SelfServiceOrderDetailViewModel viewModel, SelfServiceOrderDetailModel.DocTypeListDTO entity) {
        super(viewModel);
        Log.e("FragmentGridItemViewModel", "FragmentGridItemViewModel");
        this.entity.set(entity);
    }

    // 图片预览
    public BindingCommand documentPreView = new BindingCommand(() -> {
        viewModel.documentPreView(this.entity.get());

    });


}
