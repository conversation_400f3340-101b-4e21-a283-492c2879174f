package com.gc.notarizationpc.ui.viewmodel;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.databinding.ObservableField;

import com.gc.mininotarization.R;
//import com.goldze.mvvmhabit.ui.network.detail.DetailFragment;

import me.goldze.mvvmhabit.base.AppManager;
import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.greendao.model.LoginInfo;

/**
 * Created by goldze on 2017/7/17.
 */

public class FragmentListItemViewModel extends ItemViewModel<FragmentListModel> {
    public ObservableField<LoginInfo> entity = new ObservableField<>();
    public Drawable drawableImg;

    public FragmentListItemViewModel(@NonNull FragmentListModel viewModel, LoginInfo entity) {
        super(viewModel);
        this.entity.set(entity);
        //ImageView的占位图片，可以解决RecyclerView中图片错误问题
        drawableImg = ContextCompat.getDrawable(viewModel.getApplication(), R.mipmap.ic_launcher);
    }

    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
    public int getPosition() {
        return viewModel.getItemPosition(this);
    }

    //条目的点击事件
    public BindingCommand itemClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            //这里可以通过一个标识,做出判断，已达到跳入不同界面的逻辑
//            if (entity.get().getId() == -1) {
//                viewModel.deleteItemLiveData.setValue(FragmentListItemViewModel.this);
//            } else {
//                //跳转到详情界面,传入条目的实体对象
//                Bundle mBundle = new Bundle();
//                mBundle.putParcelable("entity", entity.get());
////                viewModel.startContainerActivity(DetailFragment.class.getCanonicalName(), mBundle);
//            }
        }
    });

    //item中某个控件的点击事件
    public BindingCommand stateClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            Toast.makeText(AppManager.getAppManager().currentActivity(), entity.get().getEmployeeName(), Toast.LENGTH_SHORT).show();
            //以前是使用Messenger发送事件，在NetWorkViewModel中完成删除逻辑
//            Messenger.getDefault().send(NetWorkItemViewModel.this, NetWorkViewModel.TOKEN_NETWORKVIEWMODEL_DELTE_ITEM);
            //现在ItemViewModel中存在ViewModel引用，可以直接拿到LiveData去做删除
        }
    });

}
