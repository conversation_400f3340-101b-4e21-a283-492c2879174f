package com.gc.notarizationpc.ui.adapter;

import static com.gc.notarizationpc.util.CommonUtil.FILE_TYPE_JEPG;
import static com.gc.notarizationpc.util.CommonUtil.FILE_TYPE_JPG;
import static com.gc.notarizationpc.util.CommonUtil.FILE_TYPE_PNG;

import android.content.Context;
import android.text.TextUtils;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.response.MaterialListResponse;
import com.gc.notarizationpc.util.CommonUtil;

import org.w3c.dom.Text;

import java.util.Locale;

import me.goldze.mvvmhabit.base.BaseAdapter;
import me.goldze.mvvmhabit.base.BaseViewHolder;

public class PictureOperationAdapter extends BaseAdapter<MaterialListResponse.MaterialVoListDTO> {
    private Context mContext;

    public PictureOperationAdapter(Context context, int itemLayoutRes, @Nullable Object type) {
        super(context, itemLayoutRes, type);
        mContext = context;
    }

    @Override
    public void bind(@NonNull BaseViewHolder viewHolder, MaterialListResponse.MaterialVoListDTO itemData, int position) {
        if (itemData != null && (itemData.getStatus() == null || itemData.getStatus() != -1)) {
            if ("CREATE".equals(itemData.getType())) {
                viewHolder.setVisibility(R.id.work_iv_wip_create, true);
                viewHolder.setVisibility(R.id.work_iv_wip_delete, false);
                viewHolder.setVisibility(R.id.work_iv_wip_picture, false);
            } else if (itemData.getFileUrl() == null || itemData.getFileUrl().isEmpty()) {
                viewHolder.setVisibility(R.id.work_iv_wip_create, false);
                viewHolder.setVisibility(R.id.work_iv_wip_delete, false);
                viewHolder.setVisibility(R.id.work_iv_wip_picture, false);
            } else {
                viewHolder.setVisibility(R.id.work_iv_wip_create, false);
                viewHolder.setVisibility(R.id.work_iv_wip_delete, true);
                viewHolder.setVisibility(R.id.work_iv_wip_picture, true);
//                if (!TextUtils.isEmpty(itemData.getSuffixName()) && (itemData.getSuffixName().contains(FILE_TYPE_PNG.toLowerCase(Locale.ROOT)) || itemData.getSuffixName().contains(FILE_TYPE_JPG.toLowerCase(Locale.ROOT)) ||
//                        itemData.getSuffixName().contains(FILE_TYPE_JEPG.toLowerCase(Locale.ROOT)))) {
                    CommonUtil.displayImage(((ImageView) viewHolder.getView(R.id.work_iv_wip_picture)), itemData.getFileUrl());
//                } else {
//                    CommonUtil.showFileType(mContext, ((ImageView) viewHolder.getView(R.id.work_iv_wip_picture)), CommonUtil.getDrawableByFileType(itemData.getSuffixName()));
//                }

            }
        }
        setOnItemViewClickListener(viewHolder, viewHolder.getView(R.id.work_iv_wip_delete), itemData, position);
        setOnItemViewClickListener(viewHolder, viewHolder.getView(R.id.work_iv_wip_create), itemData, position);
        setOnItemViewClickListener(viewHolder, viewHolder.getView(R.id.work_iv_wip_picture), itemData, position);
    }

    @Override
    public int viewType(MaterialListResponse.MaterialVoListDTO itemData) {
        return 0;
    }
}
