package com.gc.notarizationpc.ui.viewmodel;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.data.model.response.NotarialOfficeListBean;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * 匹配公证员，公证员列表
 */
public class NortaryListItemViewModel extends ItemViewModel<SelectNotaryViewModel> {
    public ObservableField<NotarialOfficeListBean.GroupVoDTO.OfficerListDTO> entity = new ObservableField<>();
    public int drawableImg;
    public ObservableField<String> notaryName = new ObservableField<>("");
    public int backgroundDraw;

    public int stateDraw;

    public int dotDraw;
    public ObservableField<String> stateStr = new ObservableField<>();

    public int stateColor;

    public NortaryListItemViewModel(@NonNull SelectNotaryViewModel viewModel, NotarialOfficeListBean.GroupVoDTO.OfficerListDTO entity) {
        super(viewModel);
        this.entity.set(entity);
    }

    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
    public int getPosition() {
        return viewModel.getNotaryItemPosition(this);
    }

    //条目的点击事件
    public BindingCommand itemClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            if (entity.get().getState() == WorkstatusEnum.DUTYONLINE.code || entity.get().getState() == WorkstatusEnum.ONLINE.code) {
                viewModel.refreshOfficeStateByOfficeId(entity.get().getOfficeId(), entity.get().getSourceFrom());
            }

            //这里可以通过一个标识,做出判断，已达到跳入不同界面的逻辑
//            if (entity.get().getId() == -1) {
//                viewModel.deleteItemLiveData.setValue(FragmentListItemViewModel.this);
//            } else {
//                //跳转到详情界面,传入条目的实体对象
//                Bundle mBundle = new Bundle();
//                mBundle.putParcelable("entity", entity.get());
////                viewModel.startContainerActivity(DetailFragment.class.getCanonicalName(), mBundle);
//            }
        }
    });


}
