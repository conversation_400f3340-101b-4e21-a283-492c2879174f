package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.SelfServiceOrderDetailModel;

import me.goldze.mvvmhabit.base.ItemViewModel;

/**
 * Created by goldze on 2017/7/17.
 */

public class SelfServiceOrderDetailFeeInformationItemViewModel extends ItemViewModel<SelfServiceOrderDetailViewModel> {


    public ObservableField<SelfServiceOrderDetailModel.PaymentListVODTO.MattersFeeListDTO> entity = new ObservableField<>();

    public SelfServiceOrderDetailFeeInformationItemViewModel(@NonNull SelfServiceOrderDetailViewModel viewModel, SelfServiceOrderDetailModel.PaymentListVODTO.MattersFeeListDTO entity) {
        super(viewModel);
        Log.e("FragmentGridItemViewModel", "FragmentGridItemViewModel");
        this.entity.set(entity);
    }

}
