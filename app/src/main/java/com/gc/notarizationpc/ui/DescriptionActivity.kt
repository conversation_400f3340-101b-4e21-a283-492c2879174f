package com.gc.notarizationpc.ui

import android.app.Dialog
import android.content.Intent
import android.text.Html
import android.view.View
import android.widget.Button
import android.widget.TextView
import com.example.framwork.utils.DialogUtils
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseActivity
import kotlinx.android.synthetic.main.activity_description.*
import java.io.*
import kotlin.jvm.Throws


class DescriptionActivity : BaseActivity() {

    private var protocolDialog: Dialog? = null
    private var toWhere: String? = "videoClick"


    override fun getIntentData(intent: Intent?) {
        toWhere = intent?.getStringExtra("toWhere")
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_description
    }

    override fun initViewsAndEvents() {
        tvProtocol.text = Html.fromHtml(getString(R.string.description_cb))
        initDialog()
    }

    fun nextClick(view: View?) {
        if (!cbProtocol.isChecked) {
            toastError("请阅读并同意《在线受理服务使用规则》")
            return
        }
        when {
            toWhere.equals("videoClick") -> {
                readyGo(NotarizationConfirmActivity::class.java)
                finish()
            }
            toWhere.equals("selfClick") -> {
                readyGo(NotarizationSelfActivity::class.java)
                finish()
            }
        }
    }

    fun protocolClick(view: View?) {
        showDialog()
    }

    private fun initDialog() {
        if (protocolDialog == null) {
            protocolDialog = DialogUtils.getInstance().getCenterDialog(mActivity, false, R.layout.dialog_protocol)
            val tvContent = protocolDialog?.findViewById<TextView>(R.id.dialog_content)
            val btnOk = protocolDialog?.findViewById<Button>(R.id.btn_ok)
            readFromRaw(tvContent)
            btnOk?.setOnClickListener { dismissDialog() }
        }
    }

    private fun showDialog() {
        if (protocolDialog?.isShowing == false) {
            protocolDialog?.show()
        }
    }

    private fun dismissDialog() {
        if (protocolDialog?.isShowing == true) {
            protocolDialog?.dismiss()
        }
    }


    /**
     * 从raw中读取txt
     */
    private fun readFromRaw(textView: TextView?) {
        try {
            val `is` = resources.openRawResource(R.raw.video_protocol)
            val text: String? = readTextFromRaw(`is`)
            textView?.text = text
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 按行读取txt
     *
     * @param is
     * @return
     * @throws Exception
     */
    @Throws(Exception::class)
    private fun readTextFromRaw(`is`: InputStream): String? {
        val reader = InputStreamReader(`is`)
        val bufferedReader = BufferedReader(reader)
        val buffer = StringBuffer("")
        var str: String?
        while (bufferedReader.readLine().also { str = it } != null) {
            buffer.append(str)
            buffer.append("\n")
        }
        return buffer.toString()
    }
}