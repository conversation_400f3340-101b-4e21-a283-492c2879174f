package com.gc.notarizationpc.ui.fragment

import android.content.ComponentName
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.framwork.noHttp.Bean.BaseResponseBean
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.AppConfig
import com.gc.notarizationpc.common.BaseFragment
import com.gc.notarizationpc.model.*
import com.gc.notarizationpc.ui.NotarizationSelfActivity
import com.gc.notarizationpc.ui.adapter.NotaryPriceAdapter
import com.gc.notarizationpc.ui.adapter.ProvideInfosAdapter
import com.gc.notarizationpc.ui.myview.CustomDialog
import com.gc.notarizationpc.ui.presenter.NotarizaSelfPresenter
import com.gc.notarizationpc.utils.CommonUtil
import com.gc.notarizationpc.widget.CustomSelectPicker
import es.dmoral.toasty.Toasty
import kotlinx.android.synthetic.main.fragment_self_step_three.*


class SelfStepThreeFragment : BaseFragment(), NotarizaSelfPresenter.INotarizaSelfView, CustomSelectPicker.Callback {

    private var currentActivity: NotarizationSelfActivity? = null
    private var customSelectPicker: CustomSelectPicker? = null;
    private var notarizationP: NotarizaSelfPresenter? = null
    private var msqAdapter: ProvideInfosAdapter? = null
    private var mgzAdapter: NotaryPriceAdapter? = null

    private var indexSubPlus: Int? = 1;
    private var intGetType: String? = "1";
    private var applyUserCur: ApplyUserEntity? = null;
    private var applyUserList: ArrayList<ApplyUserEntity>? = ArrayList();
    private var notaryItemsList: ArrayList<NotaryItemsEntity>? = null;
    private var orderInfo: OrderInfoEntity? = null;
    private var TAG = "SelfStepThreeFragment"


    //    private var toatlPrice: Float? = 0.0f;
    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {
        Log.i(TAG, "initViewsAndEvents")
        currentActivity = mActivity as NotarizationSelfActivity?;
        customSelectPicker = CustomSelectPicker(mActivity, this);
        tvNextStep?.setOnClickListener {
            if (customDialog != null && customDialog!!.isShowing)
                customDialog!!.dismiss()
            if (youjiAdress.visibility == View.VISIBLE && TextUtils.isEmpty(youjiAdress.text.toString())) {
                activity?.let { it1 -> Toasty.error(it1, "请填写邮寄地址").show() }
            } else {
                notarizationP = NotarizaSelfPresenter(mActivity, this, "order");
                var orderId = (mActivity as NotarizationSelfActivity?)?.orderId;
                var mobileInput = etMobilePhone.text.toString();
                var addressInput = youjiAdress.text.toString();
                var takeType = intGetType;
                notarizationP?.addNotaryDoSetState(orderId, mobileInput, addressInput, takeType, editNumber.text.toString().toInt());
            }
        };
        tvUpStep?.setOnClickListener {
            if (customDialog != null && customDialog!!.isShowing)
                customDialog!!.dismiss()
            currentActivity?.onNavigationItemSelected(1)
        }
        quchuWay?.setOnClickListener {
            var list = ArrayList<String>();
            list.add("自取");
            list.add("邮寄");
            list.add("智能柜");
            customSelectPicker?.show(list);
        };

        subjian?.setOnClickListener {
            if (indexSubPlus!! > 1) {
                indexSubPlus = indexSubPlus?.minus(1);
                editNumber.text = indexSubPlus.toString();
                totalPrice.setText((indexSubPlus!! * 20).toString() + "元")
            }
        };

        addjia?.setOnClickListener {
            indexSubPlus = indexSubPlus?.plus(1);
            editNumber.text = indexSubPlus.toString();
            totalPrice.setText((indexSubPlus!! * 20).toString() + "元")
        };
//        youjiAdress?.setOnClickListener {
//
//        }
        notarizationP = NotarizaSelfPresenter(mActivity, this, 1);
        notarizationP?.addNotaryGetByOrderId((mActivity as NotarizationSelfActivity?)?.orderId);
        initListViews();
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            notarizationP?.addNotaryGetByOrderId((mActivity as NotarizationSelfActivity?)?.orderId);
        }
    }

    private fun initListViews() {
        Log.i(TAG, "initListViews")
        sqrecyclerView.layoutManager = LinearLayoutManager(mActivity);
        msqAdapter = ProvideInfosAdapter(mActivity);
        sqrecyclerView.adapter = msqAdapter;
        msqAdapter!!.mCallBack = object : ProvideInfosAdapter.CallBack {
            override fun callBack(entity: ProviceBean, position: Int) {

            }
        }


        var mobileStr = (mActivity as NotarizationSelfActivity?)?.userInfo?.mobile;
        etMobilePhone.setText(mobileStr);

        gzfyrecyclerView.layoutManager = LinearLayoutManager(mActivity);
        mgzAdapter = NotaryPriceAdapter(mActivity);
        gzfyrecyclerView.adapter = mgzAdapter;
        mgzAdapter!!.mCallBack = object : NotaryPriceAdapter.CallBack {
            override fun callBack(entity: NotaryBean, position: Int) {

            }
        }
        var listNotary = ArrayList<NotaryBean>();
        mgzAdapter?.itemsList = listNotary;
        mgzAdapter?.notifyDataSetChanged();
    }

    override fun lazyInit(view: View?, savedInstanceState: Bundle?) {

    }

    override fun getContentViewLayoutID(): Int {
        return if (AppConfig.RGBPID == "c013") {
            R.layout.fragment_self_step_three;
        } else {
            R.layout.fragment_self_step_three_portrait;
        }
    }


    override fun sendNotarizationSuccess(items: List<NotrayBean>?) {
    }

    override fun queryResultLangeuList(bean: BaseResponseBean?) {

    }

    var isFrist = true;

    override fun queryResultByOrderId(bean: OrdersNotaryEntity?) {
        Log.i(TAG, "queryResultByOrderId")
        applyUserList = bean?.applyuser as ArrayList<ApplyUserEntity>?;
        var list = ArrayList<ProviceBean>();
        var proviceBean1 = ProviceBean();
        proviceBean1.birthday = "出生年月";
        proviceBean1.cardName = "姓名";
        proviceBean1.cardId = "证件号码";
        proviceBean1.cardType = "证件类型";
        proviceBean1.sexs = "性别";
        list.add(proviceBean1)
        for (obj in applyUserList!!) {
            var proviceBean = ProviceBean();
            var birthdayTem = obj.birthday?.split(" ");
            if (birthdayTem?.size!! > 0) {
                proviceBean.birthday = birthdayTem?.get(0);
            } else {
                proviceBean.birthday = obj.birthday;
            }
            proviceBean.cardName = obj.name;
            proviceBean.cardId = obj.idCard;
            proviceBean.cardType = "身份证";
            proviceBean.sexs = "男";
            if (!((obj.gender).toString()).equals("1")) {
                proviceBean.sexs = "女";
            }
            if (AppConfig.IS_PROXY)//代办的逻辑
            {
                //这里过滤如果是多个相同的代理人，保留一个
                proviceBean.isDaiBan = obj.isDaiBan
                Log.e("Test", "obj.isDaiBan" + obj.isDaiBan);
                //表示是代理人
                if (obj.name.equals((mActivity as NotarizationSelfActivity?)?.realNameCard) && !TextUtils.isEmpty(obj.principal)) {
                    if (isFrist) {
                        list.add(proviceBean)
                        isFrist = false
                    }

                } else {
                    list.add(proviceBean)
                }
            } else {//非代办的逻辑
                list.add(proviceBean)
            }
        }
        msqAdapter?.itemsList = list;
        msqAdapter?.notifyDataSetChanged();

        notaryItemsList = bean?.notaryItems as ArrayList<NotaryItemsEntity>?;
        var list2 = ArrayList<NotaryBean>();
        for (obj in notaryItemsList!!) {

            var notaryBean = NotaryBean();
            notaryBean.realName = obj.notaryItemName;
            notaryBean.certNumber = obj.notaryNum;
            notaryBean.numberPrice = obj.price;
            list2.add(notaryBean)
        }
        orderInfo = bean?.order;
        /*var notaryBean1 = NotaryBean();
        notaryBean1.realName = "总金额";
        notaryBean1.certNumber = "";
        notaryBean1.numberPrice = orderInfo?.fee;
        list2.add(notaryBean1)*/
        mgzAdapter?.itemsList = list2;
        mgzAdapter?.notifyDataSetChanged();

    }

    override fun queryResultBySetState(bean: OrderInfoEntity?) {
        Log.i(TAG, "queryResultBySetState")
        currentActivity?.lastOrderInfo = bean;
        currentActivity?.onNavigationItemSelected(3)
    }

    override fun queryResultNotarList(bean: BaseResponseBean?) {

    }

    override fun addResultSuccess(orderId: String?) {

    }

    override fun queryResultCountyList(bean: BaseResponseBean?) {

    }

    var haschinese: Boolean? = false
    var customDialog: CustomDialog? = null
    override fun onSelectItems(values: String?, index: Int) {
        quchuWay.text = values;
        if ("邮寄" == values) {
            youjiAdressText.visibility = View.VISIBLE
            youjiAdress.visibility = View.VISIBLE
            var inputType = CommonUtil.getTypeWriting(activity);
            if ("zh_cn" == inputType)
                haschinese = true
            if (haschinese == false) {
                //只展示一次
                haschinese = true
                if ("en_us" == inputType) {
                    //展示用户提示框
                    if (customDialog == null)
                        customDialog = activity?.let { CustomDialog(it) }
                    customDialog?.setsTitle("")?.setsMessage("")?.setsCancel("取消", View.OnClickListener {

                        customDialog!!.dismiss()

                    })?.setsConfirm("前往设置", View.OnClickListener {
                        customDialog!!.dismiss()
                        val intent = Intent("/")
                        val cm = ComponentName("com.android.settings", "com.android.settings.Settings\$InputMethodAndLanguageSettingsActivity")
                        intent.component = cm
                        intent.action = "android.intent.action.VIEW"
                        activity?.startActivityForResult(intent, 0)

                    })?.show()
                }


            }
        } else {
            youjiAdressText.visibility = View.GONE
            youjiAdress.visibility = View.GONE
        }
        intGetType = (index + 1).toString();
    }

    override fun outCancel() {
    }

}