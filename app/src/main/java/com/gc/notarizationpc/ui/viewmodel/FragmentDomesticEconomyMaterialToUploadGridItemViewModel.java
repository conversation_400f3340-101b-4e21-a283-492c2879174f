package com.gc.notarizationpc.ui.viewmodel;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.response.ListRequiredMaterialResponse;

import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * Created by goldze on 2017/7/17.
 */

public class FragmentDomesticEconomyMaterialToUploadGridItemViewModel extends SubItemViewModel<FragmentDomesticEconomyMaterialToUploadGridViewModel> {


    public ObservableField<ListRequiredMaterialResponse.MaterialVoListDTO> entity = new ObservableField<>();

    public boolean hideClose;

    public int drawableImg;

    public FragmentDomesticEconomyMaterialToUploadGridItemViewModel(@NonNull FragmentDomesticEconomyMaterialToUploadGridViewModel viewModel, ListRequiredMaterialResponse.MaterialVoListDTO entity) {
        super(viewModel);
        this.entity.set(entity);

        //ImageView的占位图片，可以解决RecyclerView中图片错误问题
        drawableImg = R.mipmap.icon_add;
    }


    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
    public int getChildPos() {
        return viewModel.childObservableList.indexOf(this);
    }

    public  int getCount(){
        return viewModel.childObservableList.size();
    }

    /**
     * 上传材料
     */
    public BindingCommand uploadMaterial = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.childItemClick(entity.get());
        }
    });

    /**
     * 删除材料
     */
    public BindingCommand deleteMaterial = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.delItemClick(entity.get());
        }
    });


}
