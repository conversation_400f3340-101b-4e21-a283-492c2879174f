package com.gc.notarizationpc.ui.adapter

import android.view.View
import android.widget.LinearLayout
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.example.framwork.adapter.CommonQuickAdapter
import com.gc.notarizationpc.R
import com.gc.notarizationpc.model.RoomUserInfo

class VideoStepOneAdapter(type: Int) : CommonQuickAdapter<RoomUserInfo>(R.layout.item_video_step_one) {
    private var typeA: Int? = type

    override fun convert(holder: BaseViewHolder?, item: RoomUserInfo?) {
        if (typeA == 1) {
            holder?.setText(R.id.tvProxy, item?.principal)
            holder?.getView<LinearLayout>(R.id.llProxy)?.visibility = View.VISIBLE
            holder?.setText(R.id.addressOrSex, "性别")
            var sex: String = ""
            sex = if (item?.gender.equals("1")) {
                "男"
            } else {
                "女"
            }
            holder?.setText(R.id.tvAddress, sex)
        } else {
            holder?.getView<LinearLayout>(R.id.llProxy)?.visibility = View.GONE
            holder?.setText(R.id.addressOrSex, "地址")
            holder?.setText(R.id.tvAddress, item?.address)
        }
        holder?.setText(R.id.tvName, item?.name)
        holder?.setText(R.id.tvIdCard, item?.idCard)
        holder?.setText(R.id.tvMobile, item?.mobile)

    }
}