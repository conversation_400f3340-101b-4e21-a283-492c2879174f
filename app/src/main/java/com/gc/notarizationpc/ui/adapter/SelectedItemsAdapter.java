package com.gc.notarizationpc.ui.adapter;

import android.content.Context;
import android.graphics.Color;
import android.util.SparseBooleanArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.gc.notarizationpc.R;
import com.gc.notarizationpc.model.ItemsChoseEntity;
import com.gc.notarizationpc.model.SelectedItemsEntity;
import com.gc.notarizationpc.model.TagsEntity;
import com.gc.notarizationpc.widget.SelectedMoreDialog;

import java.util.ArrayList;


public class SelectedItemsAdapter extends SectionedRecyclerViewAdapter<HeaderHolder, ContentsHolder, RecyclerView.ViewHolder> {

    public ArrayList<TagsEntity> allTagList;
    private Context mContext;
    private LayoutInflater mInflater;
    private SparseBooleanArray mBooleanMap;

    public SelectedItemsAdapter(Context context) {
        mContext = context;
        mInflater = LayoutInflater.from(context);
        mBooleanMap = new SparseBooleanArray();
    }

    public void setData(ArrayList<TagsEntity> allTagList) {
        this.allTagList = allTagList;
        notifyDataSetChanged();
    }

    @Override
    protected int getSectionCount() {
        return SelectedItemsEntity.isEmpty(allTagList) ? 0 : allTagList.size();
    }

    @Override
    protected int getItemCountForSection(int section) {
        int count = allTagList.get(section).tagInfoList.size();
        if (count >= 8 && !mBooleanMap.get(section)) {
            count = 8;
        }
        return SelectedItemsEntity.isEmpty(allTagList.get(section).tagInfoList) ? 0 : count;
    }

    //是否有footer布局
    @Override
    protected boolean hasFooterInSection(int section) {
        return false;
    }

    @Override
    protected HeaderHolder onCreateSectionHeaderViewHolder(ViewGroup parent, int viewType) {
        return new HeaderHolder(mInflater.inflate(R.layout.head_title_item, parent, false));
    }


    @Override
    protected RecyclerView.ViewHolder onCreateSectionFooterViewHolder(ViewGroup parent, int viewType) {
        return null;
    }

    @Override
    protected ContentsHolder onCreateItemViewHolder(ViewGroup parent, int viewType) {
        return new ContentsHolder(mInflater.inflate(R.layout.content_item, parent, false));
    }


    @Override
    protected void onBindSectionHeaderViewHolder(final HeaderHolder holder, final int section) {
        holder.openView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean isOpen = mBooleanMap.get(section);
                String text = isOpen ? "展开" : "关闭";
                mBooleanMap.put(section, !isOpen);
                holder.openView.setText(text);
                notifyDataSetChanged();
            }
        });
        holder.titleView.setText(allTagList.get(section).tagsName);
        holder.openView.setText(mBooleanMap.get(section) ? "关闭" : "展开");
    }


    @Override
    protected void onBindSectionFooterViewHolder(RecyclerView.ViewHolder holder, int section) {

    }

    @Override
    protected void onBindItemViewHolder(ContentsHolder holder, int section, int position) {
        String isSelected = allTagList.get(section).tagInfoList.get(position).isSelected();
        if (isSelected != null){
            if (isSelected.equals("1")){
                holder.descView.setBackgroundColor(Color.parseColor("#B3E5FF"));
            }else{
                holder.descView.setBackgroundColor(Color.parseColor("#FFFFFF"));
            }
        }else{
            holder.descView.setBackgroundColor(Color.parseColor("#FFFFFF"));
        }
        holder.descView.setText(allTagList.get(section).tagInfoList.get(position).getNotaryItemName());
        holder.descView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (isSelected != null){
                    if (isSelected.equals("1")){
                        if (onClickContentListener != null){
                            onClickContentListener.onSureClick(allTagList.get(section).tagInfoList.get(position),false);
                        }
                        allTagList.get(section).tagInfoList.get(position).setSelected("");
                    }else{
                        allTagList.get(section).tagInfoList.get(position).setSelected("1");
                        if (onClickContentListener != null){
                            onClickContentListener.onSureClick(allTagList.get(section).tagInfoList.get(position),true);
                        }
                    }
                }else{
                    allTagList.get(section).tagInfoList.get(position).setSelected("1");
                    if (onClickContentListener != null){
                        onClickContentListener.onSureClick(allTagList.get(section).tagInfoList.get(position),true);
                    }
                }
                notifyDataSetChanged();
            }
        });
    }
    /**
     * 设置确定取消按钮的回调
     */
    private OnClickContentListener onClickContentListener;

    public SelectedItemsAdapter setOnClickContentListener(OnClickContentListener onClickBottomListener) {
        this.onClickContentListener = onClickBottomListener;
        return this;
    }

    public interface OnClickContentListener{
        /**
         * 点击确认按钮事件
         * **/
        public void onSureClick(ItemsChoseEntity entity,boolean isAdd);

    }
}
