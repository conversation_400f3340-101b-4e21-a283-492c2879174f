package com.gc.notarizationpc.ui;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.os.StrictMode;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.DownloadListener;
import android.webkit.GeolocationPermissions;
import android.webkit.SslErrorHandler;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.RelativeLayout;
import android.widget.ZoomButtonsController;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.gc.mininotarization.R;
import com.umeng.analytics.MobclickAgent;

import java.lang.reflect.Field;

public class WebViewActivity extends Activity {
    private WebView webView;
    private final String TAG = "WebViewActivity";
    private String loadUrl;
    private RelativeLayout rnlNoNet;//无网络时候展示
    private boolean isWebViewloadError = false;//记录webView是否已经加载出错

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_webview);
        rnlNoNet = findViewById(R.id.rnl_no_net);
        loadUrl = getIntent().getStringExtra("linkUrl");

        webView = findViewById(R.id.webview);
        WebSettings settings = webView.getSettings();
        settings.setJavaScriptEnabled(true);

        settings.setUseWideViewPort(true);//让webview读取网页设置的viewport，pc版网页
        settings.setLoadWithOverviewMode(true);
        settings.setAllowFileAccess(true);
        setZoomViewInvisible(settings);
        setZoomControlGone(webView);
        settings.setSaveFormData(true);// 保存表单数据
        settings.setJavaScriptEnabled(true); // 是否与JS交互

        settings.setDomStorageEnabled(true);
        settings.setJavaScriptCanOpenWindowsAutomatically(true);//允许JS Alert对话框打开
        settings.setSupportMultipleWindows(true);
        //是否支持播放音乐
        settings.setPluginState(WebSettings.PluginState.ON);
        //使用预览模式加载界面
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        settings.setLoadWithOverviewMode(true);
        //与h5页面交互
        settings.setJavaScriptEnabled(true);
        settings.setGeolocationEnabled(true);
        settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        //是否需要用户点击才播放
        settings.setMediaPlaybackRequiresUserGesture(true);
        //开启DomStorage缓存
        settings.setDomStorageEnabled(true);

        StrictMode.VmPolicy.Builder builder = new StrictMode.VmPolicy.Builder();
        StrictMode.setVmPolicy(builder.build());

        webView.setDownloadListener(new DownloadListener() {
            @Override
            public void onDownloadStart(String url, String s1, String s2, String s3, long l) {
                Uri uri = Uri.parse(url);
                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                startActivity(intent);
            }
        });


        webView.setWebViewClient(new WebViewClient() {
            /**
             * WebView 完成加载页面时回调，一次Frame加载对应一次回调
             *
             * @param view
             * @param url
             */
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "onPageFinished: " + "webView---100%");
                if (!isWebViewloadError && View.VISIBLE == rnlNoNet.getVisibility()) {
                    rnlNoNet.setVisibility(View.GONE);//重新加载按钮
                    webView.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                Log.d(TAG, "onReceivedError: " + "error"+error);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    int statusCode = error.getErrorCode();
                    if (statusCode != -6) {
                        isWebViewloadError = true;
                        webView.setVisibility(View.GONE);
                        rnlNoNet.setVisibility(View.VISIBLE);
                    }
                }

            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                Log.d(TAG, "onReceivedSslError: " + "error"+error);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    webView.getSettings()
                            .setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
                }
                handler.proceed();
                isWebViewloadError = true;
                webView.setVisibility(View.GONE);
                rnlNoNet.setVisibility(View.VISIBLE);
            }
        });

        webView.setWebChromeClient(new WebChromeClient() {

            @Override
            public void onGeolocationPermissionsShowPrompt(String origin, GeolocationPermissions.Callback callback) {
                Log.e("Test", "onGeolocationPermissionsShowPrompt");
                callback.invoke(origin, true, false);
                super.onGeolocationPermissionsShowPrompt(origin, callback);
            }

            //android 5.0及以上
            @Override
            public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
                return true;
            }

            // For Android 3.0+
            //只能单独传一个uri
            public void openFileChooser(ValueCallback uploadMsg) {
            }

            //3.0--版本
            public void openFileChooser(ValueCallback uploadMsg, String acceptType) {
                openFileChooser(uploadMsg);
            }

            // For Android 4.1
            public void openFileChooser(ValueCallback uploadMsg, String acceptType, String capture) {
                openFileChooser(uploadMsg);
            }

        });

        //刷新按钮重新加载
        findViewById(R.id.tv_refresh).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                webView.clearHistory();//清除访问历史记录，不然再次加载index页面时候，canGoBack会为true，在首页点击返回键就无法弹出退出app的弹框
                if (!TextUtils.isEmpty(loadUrl)) {
                    webView.loadUrl(loadUrl);
                }
                isWebViewloadError = false;
            }
        });

        if (!TextUtils.isEmpty(loadUrl)) {
            webView.loadUrl(loadUrl);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        locationManager.getmLocationClientContinue().stopAssistantLocation();//停止辅助定位
    }

    /**
     * 申请指定的权限.
     */
    public void requestPermission(int code, String... permissions) {
        if (!hasPermission(permissions)) {
            ActivityCompat.requestPermissions(this, permissions, code);
        } else {
            //todo permission granted

        }
    }

    /**
     * 判断是否有指定的权限
     */
    public boolean hasPermission(String... permissions) {
        for (String permisson : permissions) {
            if (ContextCompat.checkSelfPermission(this, permisson) != PackageManager.PERMISSION_GRANTED) {
//                Toast.makeText(this, "请到设置授予权限，否则影响部分功能使用", Toast.LENGTH_SHORT).show();
                return false;
            }
        }
        return true;
    }


    /**
     * 隐藏缩放按钮
     *
     * @param settings
     */
    @TargetApi(Build.VERSION_CODES.HONEYCOMB)
    private void setZoomViewInvisible(WebSettings settings) {
        //去掉滚动条
        webView.setVerticalScrollBarEnabled(false);
        webView.setHorizontalScrollBarEnabled(false);

        // 设置可缩放
        settings.setBuiltInZoomControls(true);
//        // 根据版本号设置缩放按钮不显示
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {//用于判断是否为Android 3.0系统, 然后隐藏缩放控件
        settings.setDisplayZoomControls(false);
//        } else {// Android 3.0(11) 以下使用以下方法
//            setZoomControlGone(webView);
//        }
    }

    //实现放大缩小控件隐藏
    public void setZoomControlGone(View view) {
        Class classType;
        Field field;
        try {
            classType = WebView.class;
            field = classType.getDeclaredField("mZoomButtonsController");
            field.setAccessible(true);
            ZoomButtonsController mZoomButtonsController = new ZoomButtonsController(view);
            mZoomButtonsController.getZoomControls().setVisibility(View.GONE);
            try {
                field.set(view, mZoomButtonsController);
            }  catch (Exception e) {
                e.printStackTrace();
                MobclickAgent.reportError(this, e);
            }
        } catch (Exception e) {
            e.printStackTrace();
            MobclickAgent.reportError(this, e);
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            finish();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        webView.saveState(outState);
        super.onSaveInstanceState(outState);
    }
}

