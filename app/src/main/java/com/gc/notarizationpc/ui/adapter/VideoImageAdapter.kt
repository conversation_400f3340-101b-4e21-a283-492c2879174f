package com.gc.notarizationpc.ui.adapter

import android.graphics.Bitmap
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.example.framwork.adapter.CommonQuickAdapter
import com.example.framwork.glide.ImageLoaderUtils
import com.gc.notarizationpc.R

class VideoImageAdapter(layoutResId: Int) : CommonQuickAdapter<Bitmap>(layoutResId) {
    constructor() : this(R.layout.item_video_image) {
        addChildClickViewIds(R.id.btnDelImage)
    }

    override fun convert(holder: BaseViewHolder?, item: Bitmap?) {
        holder?.setImageBitmap(R.id.ivMaterial, item)
    }
}