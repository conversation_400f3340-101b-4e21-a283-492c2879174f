package com.gc.notarizationpc.ui.view;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.example.scarx.idcardreader.utils.IdCardRenderUtils;
import com.example.scarx.idcardreader.utils.imp.MyCallBack;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.request.AddSelfServiceRequest;
import com.gc.notarizationpc.data.model.response.EnterpriseCertificateTypeResponse;
import com.gc.notarizationpc.data.model.response.PersonDocumentType;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.umeng.analytics.MobclickAgent;
import com.zkteco.android.biometric.core.device.ParameterHelper;
import com.zkteco.android.biometric.core.device.TransportType;
import com.zkteco.android.biometric.core.utils.ToolUtils;
import com.zkteco.android.biometric.module.idcard.IDCardReader;
import com.zkteco.android.biometric.module.idcard.IDCardReaderFactory;
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CountDownLatch;

import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;

/**
 * 自助办证增加 申请人（个人，企业）
 */
public class AddApplicantDialog extends Dialog {
    private Context mContext;
    private PopwindowUtil.ResultSecondListener signatureListener = null;
    private IdCardRenderUtils idCardReaderUtils = new IdCardRenderUtils();
    private SoundPool soundPool = new SoundPool(100, AudioManager.STREAM_MUSIC, 0);
    private AddSelfServiceRequest.RecordApplicantsDTO mRecordApplicantsDTO;//代理人，申请人（个人）
    private AddSelfServiceRequest.RecordCorpApplicantsDTO mRecordCorpApplicantsDTO;//代理人，申请人（企业）
    private RadioButton rb_applicant, rb_proxy, rb_male, rb_female, rb_applicant_c;
    private ImageView ivBirth;
    private Integer readSuccess = null;
    private Integer readTips = null;
    private Integer validateSuccess = null;
    private Integer validateFail = null;
    private Integer retry = null;
    private Integer faceCamera = null;
    private boolean isNoCard = true;
    private IDCardInfo idcardinfo = null;
    private String TAG = AddApplicantDialog.class.getSimpleName();
    private int currentIndex = 0;//0个人1：单位
    private PersonDocumentType mCurrentPersonType;
    private Handler mhandler;
    private List<EnterpriseCertificateTypeResponse> mEnterpriseTypeList;
    private EnterpriseCertificateTypeResponse mCurrentEnterpriseTypeResponse;//当前企业证件类型
    private EditText edt_username, edt_phone, edt_card, edt_birth, edt_address, edt_name_c, edt_card_c, edt_legal_card, edt_company, edt_phone_c;
    private TextView tv_idcard_identy, tv_cancel, tv_confirm, tv_idcard_identy_c, tvPersonal, tvCompany;
    private List<PersonDocumentType> mPersonDocumentTypeList;
    private Spinner sp_identy_type_c, sp_certificate_type_c;//企业证件类型
    private PersonDocumentType mLegalPersonType;//当前选中的企业法人的证件类型
    private RelativeLayout rnlSpPersonalCardType;
    private LinearLayout lnlPerson, lnlCompany;
    private Spinner sp_certificate_type_value;
    private boolean fromPersonalEdit = false;//从个人当事人 编辑过来 不支持变更为单位类型
    private boolean fromCompanyEdit = false;//从单位 编辑过来 不支持变更为个人当事人

    public AddApplicantDialog(Context context, Handler handler, AddSelfServiceRequest.RecordApplicantsDTO recordApplicantsDTO,
                              List<PersonDocumentType> personDocumentTypeList, List<EnterpriseCertificateTypeResponse> enterpriseTypeList, AddSelfServiceRequest.RecordCorpApplicantsDTO recordCorpApplicantsDTO, PopwindowUtil.ResultSecondListener resultListener) {
        super(context);
        mContext = context;
        signatureListener = resultListener;
        this.mhandler = handler;
        if (recordApplicantsDTO != null) {
            this.mRecordApplicantsDTO = recordApplicantsDTO;
            currentIndex = 0;
            fromPersonalEdit = true;
        } else {
            mRecordApplicantsDTO = new AddSelfServiceRequest.RecordApplicantsDTO();
        }
        this.mEnterpriseTypeList = enterpriseTypeList;
        this.mPersonDocumentTypeList = personDocumentTypeList;
        if (recordCorpApplicantsDTO != null) {
            currentIndex = 1;
            fromCompanyEdit = true;
            this.mRecordCorpApplicantsDTO = recordCorpApplicantsDTO;
        } else {
            mRecordCorpApplicantsDTO = new AddSelfServiceRequest.RecordCorpApplicantsDTO();
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_add_applicant);

        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();
        if (currentIndex == 0) {
            showPersonal();
        } else {
            showCompany();
        }
        showData();
    }

    private void showPersonal() {
        lnlPerson.setVisibility(View.VISIBLE);
        tvPersonal.setBackgroundResource(R.drawable.shape_3c6af4_top_left);
        tvPersonal.setTextColor(Color.parseColor("#ffffff"));

        lnlCompany.setVisibility(View.GONE);
        tvCompany.setBackgroundResource(R.drawable.shape_f5f5f7_top_right);
        tvCompany.setTextColor(Color.parseColor("#333333"));
    }

    private void showCompany() {
        lnlPerson.setVisibility(View.GONE);
        tvPersonal.setBackgroundResource(R.drawable.shape_f5f5f7_top_left);
        tvPersonal.setTextColor(Color.parseColor("#333333"));

        lnlCompany.setVisibility(View.VISIBLE);
        tvCompany.setBackgroundResource(R.drawable.shape_3c6af4_top_right);
        tvCompany.setTextColor(Color.parseColor("#ffffff"));
    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        rb_applicant_c = findViewById(R.id.rb_applicant_c);
        edt_name_c = findViewById(R.id.edt_name_c);
        sp_certificate_type_c = findViewById(R.id.sp_certificate_type_c);
        edt_card_c = findViewById(R.id.edt_card_c);
        edt_legal_card = findViewById(R.id.edt_legal_card);
        edt_company = findViewById(R.id.edt_company);
        edt_phone_c = findViewById(R.id.edt_phone_c);
        tv_idcard_identy_c = findViewById(R.id.tv_idcard_identy_c);
        sp_identy_type_c = findViewById(R.id.sp_identy_type_c);

        rb_applicant = findViewById(R.id.rb_applicant);
        rb_proxy = findViewById(R.id.rb_proxy);
        rb_male = findViewById(R.id.rb_male);
        rb_female = findViewById(R.id.rb_female);
        edt_username = findViewById(R.id.edt_username);
        edt_phone = findViewById(R.id.edt_phone);
        edt_card = findViewById(R.id.edt_card);
        edt_address = findViewById(R.id.edt_address);
        edt_birth = findViewById(R.id.edt_birth);
        sp_certificate_type_value = findViewById(R.id.sp_certificate_type_value);
        tv_idcard_identy = findViewById(R.id.tv_idcard_identy);
        tv_cancel = findViewById(R.id.tv_cancel);
        tv_confirm = findViewById(R.id.tv_confirm);
        ivBirth = findViewById(R.id.iv_birth);
//        rnlSpPersonalCardType = findViewById(R.id.rnl_personal_certificate_type_value);


        lnlPerson = findViewById(R.id.lnl_personal);
        lnlCompany = findViewById(R.id.lnl_company);
        tvPersonal = findViewById(R.id.tv_personal);
        tvCompany = findViewById(R.id.tv_company);

        InputFilter filter = new InputFilter() {
            public CharSequence filter(CharSequence source, int start, int end,
                                       Spanned dest, int dstart, int dend) {
                for (int i = start; i < end; i++) {
                    if (!Character.isLetterOrDigit(source.charAt(i))) {
                        return "";
                    }
                }
                return null;
            }
        };

        edt_card_c.setFilters(new InputFilter[]{filter});

        edt_card.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                String idcard = editable.toString();
                if ("0".equals(CommonUtil.getGenderFromIDCard(idcard))) {
                    rb_female.setChecked(true);
                } else {
                    rb_male.setChecked(true);
                }
                edt_birth.setText(CommonUtil.getBirthDateFromIDCard(idcard));
            }
        });

        initPersonCardType();
        initCompanyLegalType();

        edt_birth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showDate();
            }
        });

        ivBirth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showDate();
            }
        });


        tvPersonal.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (fromCompanyEdit) {
                    ToastUtils.toastError(mContext.getString(R.string.company_cannot_convert_personal));
                    return;
                }
                currentIndex = 0;
                showPersonal();
            }
        });

        tvCompany.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (fromPersonalEdit) {
                    ToastUtils.toastError(mContext.getString(R.string.personal_cannot_convert_company));
                    return;
                }
                currentIndex = 1;
                showCompany();
            }
        });


        tv_cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });

        tv_confirm.setOnClickListener(new View.OnClickListener() {
            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void onClick(View view) {
                //个人编辑，新增
                if (currentIndex == 0) {
                    if (!rb_applicant.isChecked() && !rb_proxy.isChecked()) {
                        ToastUtils.toastError(mContext.getString(R.string.select_role_hint));
                        return;
                    } else if (TextUtils.isEmpty(edt_username.getText().toString())) {
                        ToastUtils.toastError(mContext.getString(R.string.input_name_hint));
                        return;
                    } else if (TextUtils.isEmpty(edt_phone.getText().toString())) {
                        ToastUtils.toastError(mContext.getString(R.string.input_phone_hint));
                        return;
                    } else if (edt_phone.getText().toString().length() != 11) {
                        ToastUtils.toastError(mContext.getString(R.string.pleaseInputCorrectPhoneNumber));
                        return;
                    } else if (TextUtils.isEmpty(edt_card.getText().toString())) {
                        ToastUtils.toastError(mContext.getString(R.string.input_idcard_hint));
                        return;
                    } else if (edt_card.getText().toString().length() != 18) {
                        ToastUtils.toastError(mContext.getString(R.string.valid_idcard_num));
                        return;
                    } else if (TextUtils.isEmpty(edt_birth.getText().toString())) {
                        ToastUtils.toastError(mContext.getString(R.string.select_birth_hint));
                        return;
                    } else if (!rb_male.isChecked() && !rb_female.isChecked()) {
                        ToastUtils.toastError(mContext.getString(R.string.select_sex_hint));
                        return;
                    }
                    String dateString = edt_birth.getText().toString(); // 示例日期
                    if (!TextUtils.isEmpty(dateString)) {
                        try {
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            Date date01 = simpleDateFormat.parse(dateString);

                            // 精确到毫秒
                            long milliSecond01 = date01.getTime();
                            long today = System.currentTimeMillis();
                            if (milliSecond01 > today) {
                                ToastUtils.toastError(mContext.getString(R.string.select_today_before));
                                return;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            MobclickAgent.reportError(mContext, e);
                        }
                    }
                    AddSelfServiceRequest.RecordApplicantsDTO updateApplicantDto = mRecordApplicantsDTO;
                    updateApplicantDto.setApplicantName(edt_username.getText().toString());
                    updateApplicantDto.setAddress(edt_address.getText().toString());
                    updateApplicantDto.setGender(rb_male.isChecked() ? 1 : 2);//1:男 2：女
                    updateApplicantDto.setBirthday(edt_birth.getText().toString());
                    updateApplicantDto.setSubstitute(rb_proxy.isChecked() ? 1 : -1);//是否代办 1:是 -1:否
                    updateApplicantDto.setSubstituteStr(rb_proxy.isChecked() ? "代理人" : "申请人");
                    Log.e("Test", "设置的SubstituteStr==" + updateApplicantDto.getSubstituteStr());
                    updateApplicantDto.setContactNum(edt_phone.getText().toString());

                    if (mCurrentPersonType != null) {
                        updateApplicantDto.setCredentialType(Integer.parseInt(mCurrentPersonType.getValue()));
                    }
                    updateApplicantDto.setCredentialNum(edt_card.getText().toString());
                    signatureListener.result(updateApplicantDto);
                    dismiss();
                } else {
                    //企业
                    if (!rb_applicant_c.isChecked()) {
                        ToastUtils.toastError(mContext.getString(R.string.select_role_hint));
                        return;
                    } else if (TextUtils.isEmpty(edt_name_c.getText().toString())) {
                        ToastUtils.toastError(mContext.getString(R.string.input_company_name_hint));
                        return;
                    } else if (TextUtils.isEmpty(edt_phone_c.getText().toString())) {
                        ToastUtils.toastError(mContext.getString(R.string.input_phone_hint));
                        return;
                    } else if (edt_phone_c.getText().toString().length() != 11) {
                        ToastUtils.toastError(mContext.getString(R.string.pleaseInputCorrectPhoneNumber));
                        return;
                    } else if (TextUtils.isEmpty(edt_card_c.getText().toString())) {
                        ToastUtils.toastError(mContext.getString(R.string.input_idcard_hint));
                        return;
                    } else if (edt_card_c.getText().toString().length() > 100) {
                        ToastUtils.toastError(mContext.getString(R.string.no_more_100));
                        return;
                    } else if (TextUtils.isEmpty(edt_company.getText().toString())) {
                        ToastUtils.toastError(mContext.getString(R.string.input_name_hint));
                        return;
                    } else if (TextUtils.isEmpty(edt_legal_card.getText().toString())) {
                        ToastUtils.toastError(mContext.getString(R.string.input_idcard_hint));
                        return;
                    } else if (edt_legal_card.getText().toString().length() != 18) {
                        ToastUtils.toastError(mContext.getString(R.string.valid_idcard_num));
                        return;
                    }
                    AddSelfServiceRequest.RecordCorpApplicantsDTO corpApplicantsDTO = mRecordCorpApplicantsDTO;
                    corpApplicantsDTO.setCorporationCredentialNum(edt_card_c.getText().toString());//单位证件号码
                    corpApplicantsDTO.setCredentialType(Integer.parseInt(mLegalPersonType.getValue()));//法人证件类型
                    corpApplicantsDTO.setCorporationCredentialType(Integer.parseInt(mCurrentEnterpriseTypeResponse.getValue()));//单位证件类型
                    corpApplicantsDTO.setContactNum(edt_phone_c.getText().toString());
                    corpApplicantsDTO.setCorporationName(edt_name_c.getText().toString());
                    corpApplicantsDTO.setLegalRepresentative(edt_company.getText().toString());//法定代表人
                    corpApplicantsDTO.setCredentialNum(edt_legal_card.getText().toString());//	法定代表人证件号码
                    corpApplicantsDTO.setRole("申请人");
                    signatureListener.secondResult(corpApplicantsDTO);
                    dismiss();
                }

            }
        });

        //个人 证件识别
        tv_idcard_identy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        playSound(2);
                    }
                }, 400);
                // 开启身份证识别
                if (idCardReaderUtils.isbStoped()) {
                    Log.e("Test", "idCardReaderUtils.setbStoped(false)");
                    idCardReaderUtils.setbStoped(false);
                } else {
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            startIdCardRead(1);
                        }
                    }, 3500);
                }
            }
        });

        //企业法人 证件识别
        tv_idcard_identy_c.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        playSound(2);
                    }
                }, 400);
                // 开启身份证识别
                if (idCardReaderUtils.isbStoped()) {
                    idCardReaderUtils.setbStoped(false);
                } else {
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            startIdCardRead(2);
                        }
                    }, 3500);
                }
            }
        });

        loadSoundRes(mContext);

        setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialogInterface) {
                try {
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            Log.e("Test", "dialog关闭");
                            idCardReaderUtils.setbStoped(true);
                            soundPool.release();
                        }
                    }, 200);

                } catch (Exception e) {
                    MobclickAgent.reportError(mContext, e);
                    Log.e(TAG, e.getMessage());
                }

            }
        });

    }

    boolean isPersonalInitialized = false;
    boolean isCompanyTypeInitialized = false;

    //处理个人 证件类型
    private void initPersonCardType() {
        List<String> personDocumentTypeValues = new ArrayList<>();
        for (PersonDocumentType personDocumentType : mPersonDocumentTypeList) {
            personDocumentTypeValues.add(personDocumentType.getLabel());
        }
        ArrayAdapter<String> adapter = new ArrayAdapter<String>(mContext, R.layout.spinner_item, personDocumentTypeValues);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        //个人证件类型
        sp_certificate_type_value.setAdapter(adapter);

        sp_certificate_type_value.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int pos, long l) {
                if (isPersonalInitialized) {
                    //选择了个人证件类型
                    idcardShowIdentity(pos);
                    mRecordApplicantsDTO.setCredentialType(Integer.parseInt(mPersonDocumentTypeList.get(pos).getValue()));
                    mCurrentPersonType = mPersonDocumentTypeList.get(pos);
                } else {
                    idcardShowIdentity(0);
                    isPersonalInitialized = true;
                    mCurrentPersonType = mPersonDocumentTypeList.get(0);//首次进来，默认选中第一个
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        //企业法人证件类型
        sp_identy_type_c.setAdapter(adapter);
        sp_identy_type_c.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int pos, long l) {
                if (isPersonalInitialized) {
                    //选择了个人证件类型
                    if (!TextUtils.isEmpty(mPersonDocumentTypeList.get(pos).getLabel()) && "身份证".equals(mPersonDocumentTypeList.get(pos).getLabel().trim())) {
                        tv_idcard_identy_c.setVisibility(View.VISIBLE);
                        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                                0,
                                LinearLayout.LayoutParams.WRAP_CONTENT,
                                1.3f
                        );
                        sp_identy_type_c.setLayoutParams(params); // 将布局参数设置给Spinner
                    } else {
                        tv_idcard_identy_c.setVisibility(View.GONE);
                        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                                0,
                                LinearLayout.LayoutParams.WRAP_CONTENT,
                                2f
                        );
                        sp_identy_type_c.setLayoutParams(params); // 将布局参数设置给Spinner
                    }
                    mRecordCorpApplicantsDTO.setCredentialType(Integer.parseInt(mPersonDocumentTypeList.get(pos).getValue()));
                    mLegalPersonType = mPersonDocumentTypeList.get(pos);
                } else {
                    isPersonalInitialized = true;
                    mLegalPersonType = mPersonDocumentTypeList.get(0);
                    if (!TextUtils.isEmpty(mPersonDocumentTypeList.get(pos).getLabel()) && "身份证".equals(mPersonDocumentTypeList.get(pos).getLabel().trim())) {
                        tv_idcard_identy_c.setVisibility(View.VISIBLE);
                        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                                0,
                                LinearLayout.LayoutParams.WRAP_CONTENT,
                                1.3f
                        );
                        sp_identy_type_c.setLayoutParams(params); // 将布局参数设置给Spinner
                    } else {
                        tv_idcard_identy_c.setVisibility(View.GONE);
                        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                                0,
                                LinearLayout.LayoutParams.WRAP_CONTENT,
                                2f
                        );
                        sp_identy_type_c.setLayoutParams(params); // 将布局参数设置给Spinner
                    }
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });
    }

    private void idcardShowIdentity(int pos) {
        if (!TextUtils.isEmpty(mPersonDocumentTypeList.get(pos).getLabel()) && "身份证".equals(mPersonDocumentTypeList.get(pos).getLabel().trim())) {
            tv_idcard_identy.setVisibility(View.VISIBLE);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(new LinearLayout.LayoutParams(0,
                    RelativeLayout.LayoutParams.WRAP_CONTENT,
                    1.3f));
            sp_certificate_type_value.setLayoutParams(params); // 将布局参数设置给Spinner
        } else {
            tv_idcard_identy.setVisibility(View.GONE);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(new LinearLayout.LayoutParams(
                    0,
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    2f)

            );
            sp_certificate_type_value.setLayoutParams(params); // 将布局参数设置给Spinner
        }
    }

    //处理单位 证件类型
    private void initCompanyLegalType() {
        List<String> enterpriseTypeValues = new ArrayList<>();
        for (EnterpriseCertificateTypeResponse personDocumentType : mEnterpriseTypeList) {
            enterpriseTypeValues.add(personDocumentType.getLabel());
        }
        ArrayAdapter<String> adapter = new ArrayAdapter<String>(mContext, R.layout.spinner_item, enterpriseTypeValues);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        sp_certificate_type_c.setAdapter(adapter);
        sp_certificate_type_c.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int pos, long l) {
                if (isCompanyTypeInitialized) {
                    mRecordCorpApplicantsDTO.setCorporationCredentialType(Integer.parseInt(mEnterpriseTypeList.get(pos).getValue()));
                    mCurrentEnterpriseTypeResponse = mEnterpriseTypeList.get(pos);
                } else {
                    isCompanyTypeInitialized = true;
                    mCurrentEnterpriseTypeResponse = mEnterpriseTypeList.get(0);
                }

            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });
    }

    private void startIdCardRead(int flag) {
        try {
            idcardinfo = null;
            HashMap<String, Object> ideograms = new HashMap<>();
            ideograms.put(ParameterHelper.PARAM_KEY_VID, 1024);
            ideograms.put(ParameterHelper.PARAM_KEY_PID, 50010);
            IDCardReader idCardReader = IDCardReaderFactory.createIDCardReader(
                    ToolUtils.getApplicationContext(),
                    TransportType.USB,
                    ideograms
            );
            idCardReaderUtils.readerIdCard(idCardReader, new CountDownLatch(1), new MyCallBack() {
                @Override
                public void onSuccess(IDCardInfo idCardInfo) {
                    if (isNoCard && null == idcardinfo) {
                        idcardinfo = idCardInfo;
                        //个人证件识别
                        mhandler.post(new Runnable() {
                            @Override
                            public void run() {
                                if (flag == 1) {
                                    //个人身份识别
                                    edt_address.setText(idcardinfo.getAddress());
                                    edt_birth.setText(CommonUtil.dateStringToString(idcardinfo.getBirth(), "yyyy-MM-dd"));
                                    if ("男".equals(idcardinfo.getSex())) {
                                        rb_male.setChecked(true);
                                    } else {
                                        rb_female.setChecked(true);
                                    }
                                    edt_username.setText(idcardinfo.getName());
                                    edt_card.setText(idcardinfo.getId());
                                } else if (flag == 2) {
                                    //企业法人身份证识别
                                    edt_legal_card.setText(idcardinfo.getId());
                                    edt_company.setText(idcardinfo.getName());
                                }
                            }
                        });
                        playSound(1);
                        isNoCard = false;
                    }
                }

                @Override
                public void onFail(String error) {
                    Log.e(TAG, "身份证读卡器出现异常,请检查设备");
                    Log.i(TAG, "readerIdCard fail");
                }

                @Override
                public void onRequestDevicePermission() {
                }

                @Override
                public void onNoCards() {
                    Log.i(TAG, "onNoCards");
                    isNoCard = true;
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            MobclickAgent.reportError(mContext, e);
        }
    }

    private void loadSoundRes(Context mActivity) {
        new Handler(Looper.getMainLooper()).post(() -> {
            if (SPUtils.getInstance().getString("changeLanguage", "zh").equals("bo")) {
                //咚
                readSuccess = soundPool.load(mActivity, R.raw.readcard_success_z, 1);
                //请将身份证放置于阅读器上
                readTips = soundPool.load(mActivity, R.raw.idcardread_z, 1);
                //请正对摄像头
                faceCamera = soundPool.load(mActivity, R.raw.face_camera_z, 1);
                //请重试
                retry = soundPool.load(mActivity, R.raw.retry_z, 1);
                //比对成功
                validateSuccess = soundPool.load(mActivity, R.raw.validate_success_z, 1);
                //比对失败
                validateFail = soundPool.load(mActivity, R.raw.validate_fail_z, 1);
            } else {
                //咚
                readSuccess = soundPool.load(mActivity, R.raw.readcard_success, 1);
                //请将身份证放置于阅读器上
                readTips = soundPool.load(mActivity, R.raw.idcardread, 1);
                //请正对摄像头
                faceCamera = soundPool.load(mActivity, R.raw.face_camera, 1);
                //请重试
                retry = soundPool.load(mActivity, R.raw.retry, 1);
                //比对成功
                validateSuccess = soundPool.load(mActivity, R.raw.validate_success, 1);
                //比对失败
                validateFail = soundPool.load(mActivity, R.raw.validate_fail, 1);
            }
        });
    }

    /**
     * 1: 读卡成功   2：请将身份证放置到阅读器上  3：请正对摄像头   4：比对失败 5：比对成功  6：请重试
     */
    public void playSound(int index) {
        switch (index) {
            case 1:
                soundPool.play(readSuccess, 1f, 1f, 1, 0, 1f);
                break;
            case 2:
                soundPool.play(readTips, 1f, 1f, 1, 0, 1f);
                break;
            case 3:
                soundPool.play(faceCamera, 1f, 1f, 1, 0, 1f);
                break;
            case 4:
                soundPool.play(validateFail, 1f, 1f, 1, 0, 1f);
                break;
            case 5:
                soundPool.play(validateSuccess, 1f, 1f, 1, 0, 1f);
                break;
            case 6:
                soundPool.play(retry, 1f, 1f, 1, 0, 1f);
                break;
        }
    }


    //展示日期
    private void showDate() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

        //时间选择器
        TimePickerView timePicker = new TimePickerBuilder(mContext, (date, v1) -> {
            String dateString = formatter.format(date);
            edt_birth.setText(dateString);
        })//年月日时分秒 的显示与否，不设置则默认全部显示
                .isDialog(true)
                .setType(new boolean[]{true, true, true, false, false, false})
                .setLabel("年", "月", "日", "时", "分", "秒")
                .setContentTextSize(16)//字体大小
                .setDate(Calendar.getInstance())//设置参数
//                        .setGravity(Gravity.CENTER)

                .build();
        if (!timePicker.isShowing()) {
            timePicker.show();
        }
    }

    private void showData() {
        if (currentIndex == 0 && mRecordApplicantsDTO != null) {
            if (mRecordApplicantsDTO.getSubstitute() == 1) {//是否代办 1:是 -1:否
                rb_proxy.setChecked(true);
                rb_applicant.setChecked(false);
            }
            edt_username.setText(mRecordApplicantsDTO.getApplicantName());
            edt_phone.setText(mRecordApplicantsDTO.getContactNum());
            edt_card.setText(mRecordApplicantsDTO.getCredentialNum());
            edt_birth.setText(mRecordApplicantsDTO.getBirthday());
            edt_address.setText(mRecordApplicantsDTO.getAddress());
            for (int i = 0; i < mPersonDocumentTypeList.size(); i++) {
                if (Integer.parseInt(mPersonDocumentTypeList.get(i).getValue()) == (mRecordApplicantsDTO.getCredentialType())) {
                    sp_certificate_type_value.setSelection(i);
                }
            }
            if (mRecordApplicantsDTO.getCredentialType() == 1) {
                tv_idcard_identy.setVisibility(View.VISIBLE);
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                        0,
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        1.3f
                );
                sp_certificate_type_value.setLayoutParams(params); // 将布局参数设置给Spinner
            } else {
                tv_idcard_identy.setVisibility(View.GONE);
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                        0,
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        2f
                );
                sp_certificate_type_value.setLayoutParams(params); // 将布局参数设置给Spinner
            }
            if (mRecordApplicantsDTO.getGender() == 1)//1:男 2：女
            {
                rb_male.setChecked(true);
            } else {
                rb_female.setChecked(true);
            }
        } else if (currentIndex == 1 && mRecordCorpApplicantsDTO != null) {
            edt_company.setText(mRecordCorpApplicantsDTO.getLegalRepresentative());
            //法人证件类型
            for (int i = 0; i < mPersonDocumentTypeList.size(); i++) {
                if (Integer.parseInt(mPersonDocumentTypeList.get(i).getValue()) == (mRecordCorpApplicantsDTO.getCredentialType())) {
                    sp_identy_type_c.setSelection(i);
                    if (mRecordCorpApplicantsDTO.getCredentialType() == 1) {
                        tv_idcard_identy_c.setVisibility(View.VISIBLE);
                        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                                0,
                                LinearLayout.LayoutParams.WRAP_CONTENT,
                                1.3f
                        );
                        sp_identy_type_c.setLayoutParams(params);
                    } else {
                        tv_idcard_identy_c.setVisibility(View.GONE);
                        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                                0,
                                LinearLayout.LayoutParams.WRAP_CONTENT,
                                2f
                        );
                        sp_identy_type_c.setLayoutParams(params);
                    }
                }
            }
            //公司证件类型
            for (int i = 0; i < mEnterpriseTypeList.size(); i++) {
                if (Integer.parseInt(mEnterpriseTypeList.get(i).getValue()) == (mRecordCorpApplicantsDTO.getCorporationCredentialType())) {
                    sp_certificate_type_c.setSelection(i);
                }
            }
            edt_card_c.setText(mRecordCorpApplicantsDTO.getCorporationCredentialNum());
            edt_legal_card.setText(mRecordCorpApplicantsDTO.getCredentialNum());
            edt_name_c.setText(mRecordCorpApplicantsDTO.getCorporationName());
            edt_phone_c.setText(mRecordCorpApplicantsDTO.getContactNum());
        }
    }


    @Override
    public void show() {
        super.show();
    }

}
