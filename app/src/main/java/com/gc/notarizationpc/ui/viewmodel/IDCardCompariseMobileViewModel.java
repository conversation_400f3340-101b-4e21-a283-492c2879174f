package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.request.StartApplyRequest;
import com.gc.notarizationpc.ui.common.OnLineRulesActivity;

import java.util.HashMap;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 *
 */
public class IDCardCompariseMobileViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    public ObservableField<String> phone = new ObservableField<>("");
    public ObservableField<String> verifyCode = new ObservableField<>("");
    public ObservableField<Boolean> isCheck = new ObservableField<>(false);
    public SingleLiveEvent<String> applySuccess = new SingleLiveEvent<>();
    public SingleLiveEvent<String> getCodeTextView = new SingleLiveEvent<>();
    public SingleLiveEvent<Integer> getInfoErrorEvent = new SingleLiveEvent<>();
    public IdCardUserInfo userInfo;

    public class UIChangeObservable {

    }

    public IDCardCompariseMobileViewModel(@NonNull Application application) {
        super(application);
    }


    /**
     * 返回
     */
    public BindingCommand goBackEvent = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            finish();
        }
    });

    /**
     * 获取短信验证码
     */
    public BindingCommand getVerifyCode = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
//            if (!RegexUtils.isMobileExact(phone.get())) {
//                getInfoErrorEvent.setValue(WorkstatusEnum.PHONT_HINT.code);
//                return;
//            }

            getCodeTextView.setValue(phone.get());

        }
    });

    /**
     * 确定
     */
    public BindingCommand decideEvent = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
//            if (!RegexUtils.isMobileExact(phone.get())) {
//                getInfoErrorEvent.setValue(WorkstatusEnum.PHONT_HINT.code);
//                return;
//            }
            if (TextUtils.isEmpty(verifyCode.get())) {
                getInfoErrorEvent.setValue(WorkstatusEnum.VERIFY_CODE_HINT.code);
                return;
            }
            if (!isCheck.get()) {
                getInfoErrorEvent.setValue(WorkstatusEnum.READ_RULE_HINT.code);
                return;
            }
            StartApplyRequest request = new StartApplyRequest();
            request.setMobile("+86-" + phone.get());
//            IdCardUserInfo userInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");

            if (userInfo != null) {
                request.setCredentialNum(userInfo.getIdCard());
                request.setNationality("CHN");
                request.setBirthday(userInfo.getBirthday());
                request.setGender(userInfo.getGender());
                request.setUserName(userInfo.getName());
                request.setCredentialType(1);
                request.setVerifyCode(verifyCode.get());
                addVideoOrder(request);
            } else {
                getInfoErrorEvent.setValue(WorkstatusEnum.NOT_GET_IDCARD.code);
            }


        }
    });

    public BindingCommand showRule = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            startActivity(OnLineRulesActivity.class);
        }
    });

    /**
     * 获取短信验证码
     */
    public void getMessageCode(String mobile) {
        HashMap<String, String> param = new HashMap<>();
        param.put("mobile", "+86-" + mobile);
        RequestUtil.getMessageCode(param, new MyObserver() {
            @Override
            public void onSuccess(Object response) {
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                getInfoErrorEvent.setValue(WorkstatusEnum.GET_VERIFY_CODE_FAIL.code);
            }
        });
    }

    /**
     * 开始申办
     */
    public void addVideoOrder(StartApplyRequest request) {
        RequestUtil.addVideoOrder(request, new MyObserver<String>() {
            @Override
            public void onSuccess(String response) {
                if (!TextUtils.isEmpty(response))
                    //跳转到匹配，选择公证员页面
                    applySuccess.setValue(response);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(TextUtils.isEmpty(errorMsg) ? Utils.getContext().getString(R.string.bidFail) : errorMsg);
            }
        });
    }


}
