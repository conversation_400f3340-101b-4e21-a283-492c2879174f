package com.gc.notarizationpc.ui.fragment

import android.os.Bundle
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseFragment
import com.gc.notarizationpc.common.RemotesBean
import kotlinx.android.synthetic.main.fragment_remote.*
import java.io.Serializable

class RemoteFragment : BaseFragment() {

    private var quickAdapter: BaseQuickAdapter<RemotesBean.ItemsDTO.UserListDTO?, BaseViewHolder>? = null
    private val APP_TYPE = "type"
    private var list = arrayListOf<RemotesBean.ItemsDTO.UserListDTO>()
    fun newInstance(type: List<RemotesBean.ItemsDTO.UserListDTO>): RemoteFragment? {
        val args = Bundle()
        val fragment: RemoteFragment = RemoteFragment()
        args.putSerializable(APP_TYPE, type as Serializable)
        fragment.arguments = args
        return fragment
    }

    override fun lazyInit(view: View, savedInstanceState: Bundle?) {

    }
    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {
        list = requireArguments().getSerializable(APP_TYPE) as ArrayList<RemotesBean.ItemsDTO.UserListDTO>
        remoteRv?.isNestedScrollingEnabled = false
        remoteRv?.adapter = object : BaseQuickAdapter<RemotesBean.ItemsDTO.UserListDTO?, BaseViewHolder>(R.layout.item_remote_notari_list) {
            override fun convert(holder: BaseViewHolder, item: RemotesBean.ItemsDTO.UserListDTO?) {
                holder.setText(R.id.tv_remote, item?.userName)
                holder.setText(R.id.tv_id, item?.idCard)
                if (item?.gender == 1) {
                    holder.setText(R.id.tv_type, "男")
                } else if (item?.gender == 0){
                    holder.setText(R.id.tv_type, "女")
                } else {
                    holder.setText(R.id.tv_type, "")
                }
            }
        }.also { quickAdapter = it }
        quickAdapter!!.addData(list)
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.fragment_remote
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}