package com.gc.notarizationpc.ui.fragment

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.text.TextUtils
import android.util.Log
import android.view.Gravity
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.example.framwork.utils.DateUtil
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseFragment
import com.gc.notarizationpc.model.*
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter
import com.gc.notarizationpc.utils.BitErCodeUtils
import com.gc.notarizationpc.utils.CommonUtil
import com.gc.notarizationpc.widget.PayDialog
import kotlinx.android.synthetic.main.fragment_video_step_five.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.util.*

class VideoStepFiveFragment(payCallBack: IPayCallBack?) : BaseFragment(), NotarizationPresenter.IPayView, NotarizationPresenter.IWXPayView {
    private var payP: NotarizationPresenter? = null
    private var wxP: NotarizationPresenter? = null
    private var wxQueryP: NotarizationPresenter? = null
    private var total: String = "0"
    private var orderNo: String? = null
    private var payCallBack: IPayCallBack? = payCallBack
    private var payType: Int = 1//1-微信 2 支付宝
    private var TAG = "VideoStepFiveFragment"
    private var userList: ArrayList<RoomUserInfo>? = null
    var dialog: PayDialog? = null;

    constructor() : this(null)

    override fun lazyInit(view: View, savedInstanceState: Bundle) {}

    private val handler: Handler = object : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == 0x01) {//微信轮询
                wxQueryP?.payWxState(orderNo)
            } else if (msg.what == 0x02) {//支付宝轮询
                payP?.payAliState(orderNo)
            }
        }
    }

    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {
        Log.i(TAG, "initViewsAndEvents")
        wxP = NotarizationPresenter(mActivity, this as NotarizationPresenter.IWXPayView)
        wxQueryP = NotarizationPresenter(mActivity, WxPayInfo::class.java, this as NotarizationPresenter.IWXPayView)
        payP = NotarizationPresenter(mActivity, this as NotarizationPresenter.IPayView)


        wxPay.setOnClickListener {
            Log.i(TAG, "onclick wxPay")
            payType = 1
            wxPayClick()
        }
        aliPay.setOnClickListener {
            Log.i(TAG, "onclick aliPay")
            payType = 2
            aliPayClick()
        }
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.fragment_video_step_five
    }

    fun initPayInfo(name: String?, remarksInfo: RoomRemarksInfo?, notarizationInfo: NotarizationInfo?) {
        Log.i(TAG, "initPayInfo")
        lifecycleScope.launch(Dispatchers.IO) {
            delay(500L)
            withContext(Dispatchers.Main) {
                var username: String? = ""
                if (null != userList) {
                    for (i in userList!!.indices) {
                        username += userList!![i].name + " "
                    }
                }
                if (TextUtils.isEmpty(username)) {
                    username = notarizationInfo?.name
                }
                tvPayName?.text = username
                tvStaff?.text = name
                tvNo?.text = notarizationInfo?.orderNo
                tvMatter?.text = remarksInfo?.gongzhengValue
                tvDate?.text = DateUtil.getInstance().getCurDateStr(DateUtil.FORMAT_YMD)
                tvPayDate?.text = DateUtil.getInstance().getCurDateStr(DateUtil.FORMAT_YMD)
            }
        }
    }

    fun refreshApplicationList(applicationList: ArrayList<RoomUserInfo>?) {
        Log.i(TAG, "refreshApplicationList")
        userList = applicationList
    }

    fun refreshAddress(address: String?) {
        Log.i(TAG, "refreshAddress")
        tvAddress?.text = address
    }

    fun refreshGZF(pay: String?) {
        Log.i(TAG, "refreshGZF")
        tvGZF?.text = pay
        total()
    }

    fun refreshFLFWF(pay: String?) {
        Log.i(TAG, "refreshFLFWF")
        tvFLFWF?.text = pay
        total()
    }

    fun refreshSMFWF(pay: String?) {
        Log.i(TAG, "refreshSMFWF")
        tvSMFWF?.text = pay
        total()
    }

    fun refreshPZF(pay: String?) {
        Log.i(TAG, "refreshPZF")
        tvPZF?.text = pay
        total()
    }

    fun refreshQTFWF(pay: String?) {
        Log.i(TAG, "refreshQTFWF")
        tvQTFWF?.text = pay
        total()
    }

    private fun total() {
        Log.i(TAG, "total")
        val b1 = BigDecimal(if (TextUtils.isEmpty(tvGZF.text.toString())) "0" else tvGZF.text.toString())
        val b2 = BigDecimal(if (TextUtils.isEmpty(tvFLFWF.text.toString())) "0" else tvFLFWF.text.toString())
        val b3 = BigDecimal(if (TextUtils.isEmpty(tvSMFWF.text.toString())) "0" else tvSMFWF.text.toString())
        val b4 = BigDecimal(if (TextUtils.isEmpty(tvPZF.text.toString())) "0" else tvPZF.text.toString())
        val b5 = BigDecimal(if (TextUtils.isEmpty(tvQTFWF.text.toString())) "0" else tvQTFWF.text.toString())
        total = b1.add(b2).add(b3).add(b4).add(b5).setScale(2).toPlainString()
        tvTotal.text = "合计：${total}元"
        payTotal.text = total
    }

    override fun showAliPay(payInfo: AliPayInfo?) {
        Log.i(TAG, "showAliPay")
        payView.visibility = View.VISIBLE
        hintPay.text = "请打开支付宝扫一扫"
        var httpWx = payInfo?.msg
        payQrCode.setImageBitmap(BitErCodeUtils.createQRImage(httpWx));
        //开启微信支付状态轮询
        handler.sendEmptyMessageDelayed(0x02, 1000);
    }

    override fun showAliPayFailed(statu: Int) {
        if (statu == -1) {
            PayStatu?.setText("支付失败，请重新扫码")
        }
        handler.sendEmptyMessageDelayed(0x02, 1000);
    }

    override fun showWXPaySuccess() {
        Log.i(TAG, "showWXPaySuccess")
        payView.visibility = View.INVISIBLE
        PayStatu?.setText("支付成功！")
        payCallBack?.paySuccess(payType)
        toastSuccess("支付成功！")
    }

    override fun showWXPayFailed(statu: Int) {
        if (statu == -1) {
            PayStatu?.setText("支付失败，请重新扫码")
        } else {
            PayStatu?.setText("待支付！")
        }
        handler.sendEmptyMessageDelayed(0x01, 1000)
    }


    override fun showWXPay(payInfo: String?) {
        Log.i(TAG, "showWXPay")
        payView.visibility = View.VISIBLE
        hintPay.text = "请打开微信扫一扫"
        var httpWx = payInfo
        payQrCode.setImageBitmap(BitErCodeUtils.createQRImage(httpWx));
        //开启微信支付状态轮询
        handler.sendEmptyMessageDelayed(0x01, 1000);
    }

    override fun showAliPaySuccess() {
        Log.i(TAG, "showAliPaySuccess")
        payView.visibility = View.INVISIBLE
        PayStatu?.text = "支付成功！"
        payCallBack?.paySuccess(payType)
        toastSuccess("支付成功！")
    }

    fun showPayDialog(orderNo: String, unitGuid: String, roomId: String, idCard: String?) {
        Log.e("Test", orderNo + "===" + unitGuid);
        //1 自助公证  2 视频公证
//        dialog = PayDialog(mActivity, 2, orderNo, unitGuid, total,roomId,idCard)
        if (dialog == null || !dialog?.isShowing!!) {
            dialog = PayDialog(requireContext(), 2, orderNo, unitGuid, total, roomId, idCard)
            dialog!!.show()
            val dialogWindow = dialog!!.window
            dialogWindow!!.decorView.setBackgroundColor(mActivity.resources.getColor(android.R.color.white))
            dialogWindow.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT)) //设置Dialog背景透明

            //dialogWindow.setWindowAnimations(R.style.mystyle);
            val lp = dialogWindow!!.attributes
            lp.width = CommonUtil.dp2px(mActivity, 920f);
            lp.height = CommonUtil.dp2px(mActivity, 580f);
            lp.alpha = 1.0f
            dialogWindow.attributes = lp
            dialogWindow.setGravity(Gravity.CENTER)
            dialog!!.setCanceledOnTouchOutside(false)
        }
    }

    fun getQrCode(orderNo: String) {
        Log.i(TAG, "getQrCode")
        this.orderNo = orderNo
        payTypeView.visibility = View.VISIBLE
    }

    private fun wxPayClick() {
        Log.i(TAG, "wxPayClick")
        payTypeView.visibility = View.GONE
        wxP?.getWXPay(orderNo, total)
    }

    private fun aliPayClick() {
        Log.i(TAG, "aliPayClick")
        payTypeView.visibility = View.GONE
        payP?.getAliPay(orderNo, total)
    }

    interface IPayCallBack {
        fun payFail(payType: Int)
        fun paySuccess(payType: Int)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (handler != null) {
            if (handler.hasMessages(0x01))
                handler.removeMessages(0x01)
            if (handler.hasMessages(0x02))
                handler.removeMessages(0x02)
        }
    }
}