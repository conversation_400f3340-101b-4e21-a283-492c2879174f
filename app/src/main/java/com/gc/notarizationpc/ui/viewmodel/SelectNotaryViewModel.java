package com.gc.notarizationpc.ui.viewmodel;

import static com.gc.mininotarization.R.string.busy;
import static com.gc.mininotarization.R.string.home_online;
import static com.gc.mininotarization.R.string.offline;
import static com.gc.mininotarization.R.string.work_online;

import android.app.Application;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.request.CancelConnectOfficeRequest;
import com.gc.notarizationpc.data.model.request.ConnectNotaryRequest;
import com.gc.notarizationpc.data.model.request.MacAddressRequest;
import com.gc.notarizationpc.data.model.request.OfficeListRequest;
import com.gc.notarizationpc.data.model.request.VideoAddRequest;
import com.gc.notarizationpc.data.model.response.MachineBindAreaResponse;
import com.gc.notarizationpc.data.model.response.NotarialOfficeListBean;
import com.gc.notarizationpc.util.CommonUtil;

import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.http.BaseResponse;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui.viewmodel
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/12
 */
public class SelectNotaryViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<BaseResponse> notaryListSingleLiveEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<MachineBindAreaResponse> machineBindAreaResponseSingleLiveEvent = new SingleLiveEvent<>();
    public MachineBindAreaResponse.ProvinceDTO mSelectProvince;
    public MachineBindAreaResponse.ProvinceDTO.GroupDTO.CityListDTO mCity;
    IdCardUserInfo userInfo;
    public String recordId;//记录id
    public SingleLiveEvent<Boolean> lineNotarySuccess = new SingleLiveEvent<>();//连线成功
    private int mCurrentNotaryOffice;//当前左侧选中的公证处下标
    public String mCurrentNotaryOfficeId;//当前左侧选中的公证处id
    public SingleLiveEvent<String> showConnectingEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<String> dismissConnectingEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<Boolean> switchCityEvent = new SingleLiveEvent<>();

    public class UIChangeObservable {
        //下拉刷新完成
//        public SingleLiveEvent finishRefreshing = new SingleLiveEvent<>();
//        //上拉加载完成
//        public SingleLiveEvent finishLoadmore = new SingleLiveEvent<>();

        //显示公证处
        public ObservableBoolean showNotaryAddressDialog;

        public UIChangeObservable() {
            showNotaryAddressDialog = new ObservableBoolean(false);
        }
    }


    public SelectNotaryViewModel(@NonNull Application application) {
        super(application);
    }

    public ObservableList<NortaryOfficeListItemViewModel> notaryOfficeList = new ObservableArrayList<>();
    public ItemBinding<NortaryOfficeListItemViewModel> notaryOfficeItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_list_notary_office);//这里指定了item的布局

    public ObservableList<NortaryListItemViewModel> notaryList = new ObservableArrayList<>();
    public ItemBinding<NortaryListItemViewModel> notaryItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_list_notary);//这里指定了item的布局

    private List<NotarialOfficeListBean> notarialOfficeListBeans = new ArrayList<>();

    public ObservableField<String> searchEdit = new ObservableField<>("");
    //点击左上角地区切换
    public BindingCommand notaryAddressOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            switchCityEvent.setValue(true);
        }
    });

    @Override
    public void onCreate() {
        super.onCreate();
        userInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
    }

    //    //下拉刷新
//    public BindingCommand onRefreshCommand = new BindingCommand(new BindingAction() {
//        @Override
//        public void call() {
//            ToastUtils.showShort("下拉刷新");
//            requestNotaryOffice();
//        }
//    });
//    //上拉加载
//    public BindingCommand onLoadMoreCommand = new BindingCommand(new BindingAction() {
//        @Override
//        public void call() {
//            if (observableList.size() > 50) {
//                uc.finishLoadmore.call();
//                return;
//            }
//            //模拟网络上拉加载更多
//            model.loadMore()
//                    .compose(RxUtils.schedulersTransformer()) //线程调度
//                    .doOnSubscribe(NetWorkViewModel.this) //请求与ViewModel周期同步
//                    .doOnSubscribe(new Consumer<Disposable>() {
//                        @Override
//                        public void accept(Disposable disposable) throws Exception {
//                            ToastUtils.showShort("上拉加载");
//                        }
//                    })
//                    .subscribe(new Consumer<DemoEntity>() {
//                        @Override
//                        public void accept(DemoEntity entity) throws Exception {
//                            for (DemoEntity.ItemsEntity itemsEntity : entity.getItems()) {
//                                NetWorkItemViewModel itemViewModel = new NetWorkItemViewModel(NetWorkViewModel.this, itemsEntity);
//                                //双向绑定动态添加Item
//                                observableList.add(itemViewModel);
//                            }
//                            //刷新完成收回
//                            uc.finishLoadmore.call();
//                        }
//                    });
//        }
//    });

    //搜索
    public BindingCommand searchClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            if (!TextUtils.isEmpty(searchEdit.get().toString())){
                requestNotary(AppConfig.macAddress, 2, searchEdit.get().toString());
            }
        }
    });

    //刷新
    public BindingCommand refreshClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            searchEdit.set("");
            requestNotary(AppConfig.macAddress, 1, searchEdit.get().toString());
        }
    });
    //返回
    public BindingCommand backClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            finish();
        }
    });


    /**
     * 根据公证处获取公证员列表
     */
    public void requestNotaryOffice(int postion, List<NotarialOfficeListBean.GroupVoDTO.OfficerListDTO> allNotary, int sourceFrom) {
        notaryList.clear();
        userInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
        for (int i = 0; i < allNotary.size(); i++) {
            if (allNotary.get(i) != null) {
                NotarialOfficeListBean.GroupVoDTO.OfficerListDTO officerListDTO = allNotary.get(i);
                officerListDTO.setChoose(false);
                officerListDTO.setSourceFrom(sourceFrom);
                NortaryListItemViewModel itemViewModel = new NortaryListItemViewModel(SelectNotaryViewModel.this, officerListDTO);
                if (officerListDTO.getState() == WorkstatusEnum.DUTYONLINE.code) {
                    itemViewModel.stateStr = new ObservableField<>(Utils.getContext().getResources().getString(work_online));
                    itemViewModel.stateColor = Utils.getContext().getColor(R.color.color1CB456);
                    itemViewModel.stateDraw = R.drawable.shape_corner10_dbf0e2;
                    itemViewModel.dotDraw = R.drawable.shape_corner5_1cb456;
                    itemViewModel.backgroundDraw = R.drawable.shape_corner5_3c6af4;
                    itemViewModel.entity.get().setClickable(true);
                } else if (officerListDTO.getState() == WorkstatusEnum.BUSY.code) {
                    itemViewModel.stateStr = new ObservableField<>(Utils.getContext().getResources().getString(busy));
                    itemViewModel.stateColor = Utils.getContext().getColor(R.color.colorF28213);
                    itemViewModel.stateDraw = R.drawable.shape_corner10_ffe6c8;
                    itemViewModel.dotDraw = R.drawable.shape_corner5_f28213;
                    itemViewModel.backgroundDraw = R.drawable.shape_corner5_d0d2d6;
                    itemViewModel.entity.get().setClickable(false);
                } else if (officerListDTO.getState() == WorkstatusEnum.ONLINE.code) {
                    itemViewModel.stateStr = new ObservableField<>(Utils.getContext().getResources().getString(home_online));
                    itemViewModel.stateColor = Utils.getContext().getColor(R.color.color1694FF);
                    itemViewModel.stateDraw = R.drawable.shape_corner10_daeeff;
                    itemViewModel.dotDraw = R.drawable.shape_corner5_1694ff;
                    itemViewModel.backgroundDraw = R.drawable.shape_corner5_3c6af4;
                    itemViewModel.entity.get().setClickable(true);
                } else if (officerListDTO.getState() == WorkstatusEnum.DUTYONLINE_BUSY.code) {
                    itemViewModel.stateStr = new ObservableField<>(Utils.getContext().getResources().getString(busy));
                    itemViewModel.stateColor = Utils.getContext().getColor(R.color.colorF28213);
                    itemViewModel.stateDraw = R.drawable.shape_corner10_ffe6c8;
                    itemViewModel.dotDraw = R.drawable.shape_corner5_f28213;
                    itemViewModel.backgroundDraw = R.drawable.shape_corner5_d0d2d6;
                    itemViewModel.entity.get().setClickable(false);
                } else if (officerListDTO.getState() == WorkstatusEnum.OFFLINE.code) {
                    itemViewModel.stateStr = new ObservableField<>(Utils.getContext().getResources().getString(offline));
                    itemViewModel.stateColor = Utils.getContext().getColor(R.color.color9C9FA7);
                    itemViewModel.stateDraw = R.drawable.shape_corner10_e2e2e2;
                    itemViewModel.dotDraw = R.drawable.shape_corner5_9c9fa7;
                    itemViewModel.backgroundDraw = R.drawable.shape_corner5_d0d2d6;
                    itemViewModel.entity.get().setClickable(false);
                }
                //双向绑定动态添加Item
                notaryList.add(itemViewModel);
            }
            mCurrentNotaryOffice = postion;

        }

    }

    /**
     * 获取公证员列表
     */
    public void requestNotary(String mac, Integer type, String searchStr) {
        showDialog(Utils.getContext().getString(R.string.loading));
        OfficeListRequest request = new OfficeListRequest();
        request.setMacAddress(mac);
        request.setMechanismName(searchStr);
        if (mCity != null) {
            request.setCity(mCity.getCode());
        }
        if (mSelectProvince != null) {
            request.setProvince(mSelectProvince.getCode());
        }
        request.setSearchType(type);//,1-初始化列表，2-搜索

        RequestUtil.getAllOfficerListAPI(request, new MyObserver<List<NotarialOfficeListBean>>() {
            @Override
            public void onSuccess(List<NotarialOfficeListBean> result) {
                dismissDialog();
                BaseResponse<List<NotarialOfficeListBean>> response = new BaseResponse<>();
                response.setResult(result);
                response.setSuccess(true);
                notaryListSingleLiveEvent.setValue(response);
                if (result != null && result.size() > 0) {
                    notarialOfficeListBeans = result;
                    try {
                        mCurrentNotaryOfficeId = notarialOfficeListBeans.get(mCurrentNotaryOffice).getId();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                BaseResponse<String> response = new BaseResponse<>();
                response.setResult(errorMsg);
                response.setSuccess(false);
                notaryListSingleLiveEvent.setValue(response);
            }
        });
    }


    /**
     * 根据mac地址查询关联的所属区域
     */
    public void findIntegratedMachineBindArea() {
        showDialog(Utils.getContext().getString(R.string.loading));
        MacAddressRequest request = new MacAddressRequest();
        request.setMacAddress(AppConfig.macAddress);
        RequestUtil.findIntegratedMachineBindArea(request, new MyObserver<MachineBindAreaResponse>() {
            @Override
            public void onSuccess(MachineBindAreaResponse result) {
                dismissDialog();
                if (result != null) {
                    machineBindAreaResponseSingleLiveEvent.setValue(result);
                    if (result != null) {
                        if (result.getProvince() != null) {
                            mSelectProvince = result.getProvince();
                        }
                        if (result.getProvince().getGroup() != null && result.getProvince().getGroup().size() > 0) {
                            mCity = result.getProvince().getGroup().get(0).getCityList().get(0);
                        }
                    }


                }

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                BaseResponse<String> response = new BaseResponse<>();
                response.setResult(errorMsg);
                response.setSuccess(false);
                notaryListSingleLiveEvent.setValue(response);
            }
        });
    }

    /**
     * 获取连线公证员
     */
    public void refreshOfficeStateByOfficeId(String officeId, int sourceFrom) {
        showConnectingEvent.setValue(officeId);

        RequestUtil.refreshOfficeStateByOfficeId(mCurrentNotaryOfficeId, officeId, new MyObserver() {
            @Override
            public void onSuccess(Object result) {
                dismissDialog();
                if (result != null && Integer.parseInt(result.toString())== WorkstatusEnum.ONLINE.code || Integer.parseInt(result.toString()) == WorkstatusEnum.DUTYONLINE.code) {
                    if (sourceFrom == CommonUtil.SOURCE_FROM_SPBZ) {
                        //创建视频订单
                        addVideoOrder(officeId, sourceFrom);
                    } else if (sourceFrom == CommonUtil.SOURCE_FROM_SPLX) {
                        addVideoLineOrder(officeId, sourceFrom);
                    }
                }

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissConnectingEvent.setValue("");

                //当前公证员状态不适合连线，则需要刷新列表
                requestNotary(AppConfig.macAddress, 1, searchEdit.get().toString());
            }
        });
    }

    /**
     * 视频办证记录新增
     */
    public void addVideoOrder(String officeId, int sourceFrom) {
        VideoAddRequest request = new VideoAddRequest();
        request.setOfficeId(officeId);
        request.setDispatchMode(1);//记录分发模式(1-指定公证员，2-匹配公证处)
        request.setSourceType("4");//来源 1-青桐智盒app，2-小程序,4-小一体机
        request.setMobile(userInfo.getMobile());
        request.setUserId(userInfo.getUserId());
        request.setUserName(userInfo.getName());

        RequestUtil.addVideoOrder(request, new MyObserver<String>() {
            @Override
            public void onSuccess(String result) {
                //得到订单记录id
                if (!TextUtils.isEmpty(result)) {
                    recordId = result;
                }
                lineNotary(officeId, mCurrentNotaryOfficeId, sourceFrom);

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg);
                dismissConnectingEvent.setValue("");
            }
        });
    }

    /**
     * 视频连线咨询记录新增
     */
    public void addVideoLineOrder(String officeId, int sourceFrom) {
        VideoAddRequest request = new VideoAddRequest();
        request.setOfficeId(officeId);
        request.setDispatchMode(1);//记录分发模式(1-指定公证员，2-匹配公证处)
        request.setSourceType("4");
//        request.setMobile(userInfo.getMobile());
        String sysUserId = SPUtils.getInstance().getString("userId");
        request.setUserId(sysUserId);//视频咨询连线没有刷身份证认证信息，所以这里用注册mac地址时候返回的userid
//        request.setUserName(userInfo.getName());

        RequestUtil.addVideoLineOrder(request, new MyObserver<String>() {
            @Override
            public void onSuccess(String result) {
                //得到订单记录id
                if (!TextUtils.isEmpty(result)) {
                    recordId = result;
                }
                //调用视频连线咨询专用的连线公证员接口
                consultLineNotary(officeId, mCurrentNotaryOfficeId, sourceFrom);

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg);
                dismissConnectingEvent.setValue("");
            }
        });
    }

    /**
     * 取消连接公证员
     */
    public void cancelConnectOffice(String officeId,int sourceFrom) {
        CancelConnectOfficeRequest request = new CancelConnectOfficeRequest();
        request.setOfficeId(officeId);
        request.setUserId(userInfo.getUserId());
        request.setRecordId(recordId);
        request.setRecordType(sourceFrom);
        RequestUtil.cancelConnectOffice(request, new MyObserver<Boolean>() {
            @Override
            public void onSuccess(Boolean result) {
                if (result) {
                    Log.e("Test", "取消连接公证员成功");
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg);
                dismissConnectingEvent.setValue("");
            }
        });
    }

    /**
     * 获取连线公证员
     */
    public void lineNotary(String officeId, String mechanismId, int sourceFrom) {
        ConnectNotaryRequest request = new ConnectNotaryRequest();
        request.setType(sourceFrom);//，1-咨询，2-视频公证
        request.setMobile(userInfo.getMobile());
        request.setUserId(userInfo.getUserId());
        request.setUserName(userInfo.getName());
        request.setRecordId(recordId);
        request.setMechanismId(mechanismId);//机构
        request.setOfficeId(officeId);

        RequestUtil.lineNotary(request, new MyObserver() {
            @Override
            public void onSuccess(Object object) {
                //连线成功
                lineNotarySuccess.setValue(true);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissConnectingEvent.setValue("");
                lineNotarySuccess.setValue(false);
            }
        });
    }

    /**
     * 视频咨询连线公证员
     */
    public void consultLineNotary(String officeId, String mechanismId, int sourceFrom) {
        ConnectNotaryRequest request = new ConnectNotaryRequest();
        request.setType(sourceFrom);//，1-咨询，2-视频公证
        userInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
        String sysUserId = SPUtils.getInstance().getString("userId");
        request.setUserId(sysUserId);
        request.setRecordId(recordId);
        request.setMechanismId(mechanismId);//机构
        request.setOfficeId(officeId);

        RequestUtil.consultLineNotary(request, new MyObserver() {
            @Override
            public void onSuccess(Object object) {
                //连线成功
                lineNotarySuccess.setValue(true);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissConnectingEvent.setValue("");
                lineNotarySuccess.setValue(false);
            }
        });
    }

    public int getItemPosition(NortaryOfficeListItemViewModel nortaryOfficeListItemViewModel) {
        return notaryOfficeList.indexOf(nortaryOfficeListItemViewModel);
    }

    public int getNotaryItemPosition(NortaryListItemViewModel nortaryListItemViewModel) {
        return notaryList.indexOf(nortaryListItemViewModel);
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}

