package com.gc.notarizationpc.ui.adapter

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.gc.notarizationpc.R
import com.gc.notarizationpc.model.ItemsChoseEntity
import com.gc.notarizationpc.model.NotrayBean

/**
 * 自定义布局，网络图片
 */
class SelectedGridItemsAdapter(var bolShow: Boolean, val mContext: Context) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    public var itemsList: ArrayList<NotrayBean>? = null;
    public var callBack: CallBack? = null
    public var mActivity: Context? = null

    interface CallBack {
        fun callBack(entity: NotrayBean, position: Int);
    }

    inner class ItemsViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
        val contentText: TextView = view.findViewById(R.id.contentItemsText);
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val leftView =
            LayoutInflater.from(parent.context).inflate(R.layout.selected_grid_items, parent, false)
        return ItemsViewHolder(leftView)
    }

    override fun getItemCount(): Int {
        return itemsList?.size!!;
    }

    /**
     * GCSWRW5067
     * 迷你一体机修改自助公证页面
     */
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val textItem = itemsList?.get(position);
        when (holder) {
            is ItemsViewHolder -> {
                if (textItem != null) {
                    holder.contentText.text = textItem.name
                    if (textItem.isSelect) {
                        holder.contentText.background = mActivity!!.resources.getDrawable(R.drawable.shape_bule_round)
                        holder.contentText.setTextColor(mActivity!!.getColor(R.color.select))
                    } else {
                        holder.contentText.background = mActivity!!.resources.getDrawable(R.drawable.shape_green_round)
                        holder.contentText.setTextColor(Color.BLACK)
                    }
                    holder.contentText.setOnClickListener {
                        textItem.isSelect = true;
                        callBack?.callBack(textItem, position)
                    }
                };

            };
        }
    }
}
