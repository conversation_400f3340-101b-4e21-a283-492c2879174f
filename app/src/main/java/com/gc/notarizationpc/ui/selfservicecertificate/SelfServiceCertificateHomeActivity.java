package com.gc.notarizationpc.ui.selfservicecertificate;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivitySelfServiceCertificateHomeBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.viewmodel.SelfServiceCertificateHomeViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopwindowUtil;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.utils.SPUtils;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 * 废弃，在自助办证第一步增加用途选项
 */
public class SelfServiceCertificateHomeActivity extends BaseActivity<ActivitySelfServiceCertificateHomeBinding, SelfServiceCertificateHomeViewModel> {

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_self_service_certificate_home;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public SelfServiceCertificateHomeViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(SelfServiceCertificateHomeViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }

    @Override
    protected void onResume() {
        super.onResume();
        viewModel.requestNetwork();
    }

    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });

        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });
        findViewById(R.id.tv_shenban).setVisibility(View.VISIBLE);
        findViewById(R.id.tv_shenban).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                PopwindowUtil.showShenbanHintDialog(SelfServiceCertificateHomeActivity.this);
            }
        });
    }

    @Override
    public void initViewObservable() {
        viewModel.goToSelfNor.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                if (!TextUtils.isEmpty(s)) {
                    Intent intent;
                    if (s.equals("6")){
                        intent = new Intent(SelfServiceCertificateHomeActivity.this, DomesticEconomyNotarizationActivity.class);
                    } else {
                        intent = new Intent(SelfServiceCertificateHomeActivity.this, SelfNotarizationActivity.class);
                    }
                    intent.putExtra("useType", s);
                    //清除掉自主记录id
                    SPUtils.getInstance().put("self_recordId", "");
                    startActivity(intent);
                }
            }
        });

    }


}

