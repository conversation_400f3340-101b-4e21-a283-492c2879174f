package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.response.SelfServiceOrderDetailModel;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * Created by goldze on 2017/7/17.
 */

public class SelfServiceOrderDetailNaturalInformationItemViewModel extends ItemViewModel<SelfServiceOrderDetailViewModel> {


    public ObservableField<SelfServiceOrderDetailModel.RecordApplicantVosDTO> entity = new ObservableField<>();

    public String personType = "";

    public String sexString = "";

    public SelfServiceOrderDetailNaturalInformationItemViewModel(@NonNull SelfServiceOrderDetailViewModel viewModel, SelfServiceOrderDetailModel.RecordApplicantVosDTO entity) {
        super(viewModel);
        Log.e("FragmentGridItemViewModel", "FragmentGridItemViewModel");
        this.entity.set(entity);

        //ImageView的占位图片，可以解决RecyclerView中图片错误问题
//        drawableImg = ContextCompat.getDrawable(viewModel.getApplication(), R.mipmap.ic_launcher);
    }

    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
//    public int getPosition() {
//        return viewModel.getItemPosition(this);
//    }
//
//    public  int getCount(){
//        return viewModel.getItemCount();
//    }
//
//    /**
//     * 进入详情
//     */
//    public BindingCommand itemClick = new BindingCommand(new BindingAction() {
//        @Override
//        public void call() {
//            viewModel.itemClick();
//        }
//    });

    /**
     * 补充材料
     */
    public BindingCommand applyMaterialClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            ToastUtils.showLong(Utils.getContext().getString(R.string.supplementaryMaterials));
//            Toast.makeText(AppManager.getAppManager().currentActivity(), entity.get().getEmployeeName(), Toast.LENGTH_SHORT).show();
            //以前是使用Messenger发送事件，在NetWorkViewModel中完成删除逻辑
//            Messenger.getDefault().send(NetWorkItemViewModel.this, NetWorkViewModel.TOKEN_NETWORKVIEWMODEL_DELTE_ITEM);
            //现在ItemViewModel中存在ViewModel引用，可以直接拿到LiveData去做删除
        }
    });

    /**
     * 进入受理室
     */
    public BindingCommand enterRoomClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            ToastUtils.showLong(Utils.getContext().getString(R.string.enterTheReceptionRoom));
        }
    });

    /**
     * 阅读文书
     */
    public BindingCommand readDocumentClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            ToastUtils.showLong(Utils.getContext().getString(R.string.readingInstrument));
        }
    });

    /**
     * 删除
     */
    public BindingCommand delete = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            ToastUtils.showLong(Utils.getContext().getString(R.string.delete));
        }
    });

    /**
     * 继续填写
     */
    public  BindingCommand continueWrite = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            ToastUtils.showLong(Utils.getContext().getString(R.string.continueWrite));
        }
    });

}
