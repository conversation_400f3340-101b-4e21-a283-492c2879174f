package com.gc.notarizationpc.ui.video;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.alibaba.fastjson.JSON;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityVideoConnectionBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.bean.SocketMessageInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.SelectNotaryActivity;
import com.gc.notarizationpc.ui.VideoNotarizationActivity;
import com.gc.notarizationpc.ui.selfservicecertificate.CompariseAcitivity;
import com.gc.notarizationpc.ui.viewmodel.VideoConnectionViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.websocket.MyMqttService;
import com.gc.notarizationpc.websocket.WebSocketCallBack;
import com.umeng.analytics.MobclickAgent;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.utils.CommonUtil;
import me.goldze.mvvmhabit.utils.ConvertUtils;

/**
 * 视频办证首页
 */
public class VideoConnectionActivity extends BaseActivity<ActivityVideoConnectionBinding, VideoConnectionViewModel> implements WebSocketCallBack {

    //ActivityLoginBinding类是databinding框架自定生成的,对应activity_login.xml
    private MyMqttService myMqttService = null;
    private static final String TAG = VideoNotarizationActivity.class.getSimpleName();
    private String recordId;
    CountDownTimer timer;
    private AlertDialog matchDialog;
    private int sourceFrom;

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_video_connection;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public VideoConnectionViewModel initViewModel() {
//        使用自定义的ViewModelFactory来创建ViewModel，如果不重写该方法，则默认会调用LoginViewModel(@NonNull Application application)构造方法
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(VideoConnectionViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        //设置状态栏深浅模式false为浅色
        changeStatusBarLight(true);
    }

    @Override
    public void initData() {
        super.initData();
        String version = CommonUtil.getVersion(this);
        sourceFrom = getIntent().getIntExtra("from", 0);
//        if (!TextUtils.isEmpty(version)) {
//            binding.tvVersion.setText(version);
//        }
//        viewModel.requestNetWork();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });

        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });

    }

    @Override
    public void initViewObservable() {
        viewModel.matchCallbackEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object o) {
                String flag = (String) o;

                if (WorkstatusEnum.ADD_VIDEO_ORDER_FAIL.code.toString().equals(flag)) {
                    toastError(WorkstatusEnum.ADD_VIDEO_ORDER_FAIL.msg);
                    dismissMatchDialog();
                } else if (WorkstatusEnum.CANCEL_MATCH.code.toString().equals(flag)) {
                    Log.e("Test", "用户取消了匹配公证员");
                    dismissMatchDialog();
                } else if (WorkstatusEnum.CANCEL_MATCH_FAIL.code.toString().equals(flag)) {
                    toastError(WorkstatusEnum.CANCEL_MATCH_FAIL.msg);
                } else if (!TextUtils.isEmpty(flag)) {
                    Log.e("Test", "匹配成功");
//                    dismissMatchDialog();
                    recordId = flag;
                    try {
                        if (sourceFrom == com.gc.notarizationpc.util.CommonUtil.SOURCE_FROM_SPBZ) {
                            myMqttService = new MyMqttService(VideoConnectionActivity.this, "/videoConnectingRecords/" + recordId,
                                    "/videoConnectingRecords/" + recordId, AppConfig.HOST, VideoConnectionActivity.this);
                        } else if (sourceFrom == com.gc.notarizationpc.util.CommonUtil.SOURCE_FROM_SPLX) {
                            myMqttService = new MyMqttService(VideoConnectionActivity.this, "/consultingRecords/" + recordId,
                                    "/consultingRecords/" + recordId, AppConfig.HOST, VideoConnectionActivity.this);
                        }
                    } catch (Exception e) {
                        Log.e("Test", e.getMessage());
                        MobclickAgent.reportError(VideoConnectionActivity.this, e);
                    }

                    myMqttService.start();
                }

            }

        });

        viewModel.matchNotaryFailEvent.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean o) {
                //匹配失败
                toastError(getString(R.string.match_fail));
                dismissMatchDialog();
                //取消定时匹配的定时器
                viewModel.cancelTimer();
            }

        });
    }

    private int count = 0;
    long startTime;

    /**
     * 匹配公证员弹框
     */
    public void showMatchingVideoAlert(final Context context, final PopwindowUtil.ButtonClickListener signatureListener) {
        //获取自定义布局文件pop.xml的视图
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.connection_video_alert, null);
        TextView clockTextView = view.findViewById(R.id.clock_text_id);
        Button closeTransactionBtn = view.findViewById(R.id.close_transaction);
//        Button makeAppointmentBtn = view.findViewById(R.id.make_appointment);
        matchDialog = builder.create();
        startTime = SystemClock.elapsedRealtime();
        //匹配使用正计时，并且
        timer = new CountDownTimer(Long.MAX_VALUE, 1000) {
            @SuppressLint("SetTextI18n")
            @Override
            public void onTick(long millisUntilFinished) {
                long elapsedTime = SystemClock.elapsedRealtime() - startTime;
                int seconds = (int) (elapsedTime / 1000);
                if (seconds < 10)
                    clockTextView.setText("00:0" + seconds + "s");
                else
                    clockTextView.setText("00:" + seconds + "s");

                //每20秒请求一次匹配接口，但是页面计时不清空
                if (seconds != 0 && seconds % 20 == 0) {
                    //20s请求一次接口，但是页面上的计时不清空，一直积累
                    signatureListener.decide();
                }

            }

            @Override
            public void onFinish() {
//                matchDialog.dismiss();
                //正向计时的时候这里走不到
                signatureListener.decide();
                startTime = SystemClock.elapsedRealtime();
                timer.start();
                //匹配倒计时结束后，再重新循环20s接着匹配
            }
        };
        startTime = SystemClock.elapsedRealtime();
        timer.start();

        matchDialog.show();
        Window window = matchDialog.getWindow();
        window.getDecorView().setBackgroundColor(context.getResources().getColor(android.R.color.white));
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));//设置Dialog背景透明
        window.setLayout(
                ConvertUtils.dp2px(568),
                ConvertUtils.dp2px(450));
        window.setGravity(Gravity.CENTER);
        matchDialog.setContentView(view);
        matchDialog.setCanceledOnTouchOutside(false);

        closeTransactionBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (timer != null) {
                    timer.cancel();
                    timer = null;
                }
                matchDialog.dismiss();
                signatureListener.cancel();
            }
        });


    }

    public void dismissMatchDialog() {
        if (matchDialog != null && matchDialog.isShowing()) {
            if (timer != null) {
                timer.cancel();
                Log.e("Test", "取消了定时器");
                timer = null;
            }
            matchDialog.dismiss();

        }
    }

    /**
     * 选择公证员
     *
     * @param view
     */
    public void selectNotary(View view) {
        Intent intent = new Intent(this, SelectNotaryActivity.class);
        intent.putExtra("from", sourceFrom);
        startActivity(intent);
    }

    public void matchNotary(View view) {
        //视频咨询和视频公证的创建记录订单接口不一样
        if (sourceFrom == com.gc.notarizationpc.util.CommonUtil.SOURCE_FROM_SPBZ) {
            viewModel.addVideoOrder(sourceFrom);
            //点击匹配公证员后先创建订单，再匹配公证员，每20s调用一次匹配接口
            showMatchingVideoAlert(this, new PopwindowUtil.ButtonClickListener() {
                @Override
                public void decide() {
                    //倒计时结束
                    viewModel.matchNotary(sourceFrom);
                }

                @Override
                public void cancel() {
                    //用户手动取消匹配
                    viewModel.cancelTimer();
                    viewModel.cancelMatch(com.gc.notarizationpc.util.CommonUtil.SOURCE_FROM_SPBZ);
                }
            });
        } else {
            viewModel.addVideoLineOrder(sourceFrom);
            //点击匹配公证员后先创建订单，再匹配公证员，每20s调用一次匹配接口
            showMatchingVideoAlert(this, new PopwindowUtil.ButtonClickListener() {
                @Override
                public void decide() {
                    //20s计时结束
                    viewModel.matchNotary(sourceFrom);
                }

                @Override
                public void cancel() {
                    //用户手动取消匹配
                    viewModel.cancelTimer();
                    viewModel.cancelMatch(com.gc.notarizationpc.util.CommonUtil.SOURCE_FROM_SPLX);
                }
            });
        }

    }

    private String roomId, orderId, notaryName, mechanismName;


    @Override
    public void onSocketMessage(@Nullable String next) {
//        if (next.startsWith("{") && next.endsWith("}")) {
        SocketMessageInfo info = JSON.parseObject(next, SocketMessageInfo.class);
        //停止等待 进入受理室
        if ("stopWaiting".equals(info.getCode())) {
//                isWaiting = false;
            if (null != info.getRoomId()) {
                roomId = info.getRoomId();
                orderId = recordId;
                notaryName = info.getNotaryName();
                mechanismName = info.getMechanismName();
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        //只有进入受理室才会取消匹配
//                        viewModel.cancelMatch();
                        viewModel.cancelTimer();

                    }
                });
                myMqttService.unSubscribe();
                dismissMatchDialog();
                Intent intent = new Intent(this, CounselingRoomActivity.class);
                intent.putExtra("roomId", roomId);
                intent.putExtra("orderId", orderId);
                intent.putExtra("mechanismName", mechanismName);
                intent.putExtra("notaryName", notaryName);
                intent.putExtra("caseInfoId", "");
                intent.putExtra("from", sourceFrom);
                intent.putExtra("recordId", recordId);
                intent.putExtra("notaryId", info.getNotaryId());
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);
//                finish();
            } else {
                dismissMatchDialog();
                Log.i(TAG, "onSocketMessage ==,消息体错误，不包含 公证员名称或房间号 ," + next);
                toastError(getString(R.string.notReceivedRoomNumber));
            }
        } else if ("exit_room".equals(info.getCode())) {
            //pc点击暂不受理
//            toastError(getString(R.string.notary_is_busing));
//            dismissMatchDialog();
        }
    }


}
