package com.gc.notarizationpc.ui.viewmodel;

import android.app.Activity;
import android.app.Application;
import android.graphics.Bitmap;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.amap.api.location.AMapLocation;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.request.SignDocRequest;
import com.gc.notarizationpc.data.model.response.ApplyInfoResponse;
import com.gc.notarizationpc.data.model.response.DocInfoResponse;
import com.gc.notarizationpc.data.model.response.FeeInfoResponse;
import com.gc.notarizationpc.data.model.response.MaterialListResponse;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.GpsUtil;
import com.gc.notarizationpc.util.PopwindowUtil;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

/**
 * 受理室
 */
public class CounselingRoomViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<String> convertSuccess = new SingleLiveEvent<>();

    public SingleLiveEvent<Boolean> closeVideoEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<Integer> getInfoErrorEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<ApplyInfoResponse> applyInfoResponseSingleLiveEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<List<FeeInfoResponse>> feeInfoResponseSingleLiveEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<List<DocInfoResponse>> docInfoResponseSingleLiveEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<String> singleSignSucessLiveEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<List<MaterialListResponse>> materialListSingleLiveEvent = new SingleLiveEvent<>();//视频办证材料列表
    public SingleLiveEvent<List<MaterialListResponse.MaterialVoListDTO>> materialLineListSingleLiveEvent = new SingleLiveEvent<>();//视频连线咨询材料列表
    public SingleLiveEvent<UploadFileBean> uploadFileSingleLiveEvent = new SingleLiveEvent<>();

    public class UIChangeObservable {

    }

    public CounselingRoomViewModel(@NonNull Application application) {
        super(application);
    }

    //给RecyclerView添加ObservableList
    public ObservableList<HomeQuickListItemViewModel> observableList = new ObservableArrayList<>();
    //给RecyclerView添加ItemBinding
    public ItemBinding<HomeQuickListItemViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.item_home_quick_enter);//这里指定了item的布局

    /**
     * 网络请求方法，在ViewModel中调用Model层，通过Okhttp+Retrofit+RxJava发起请求
     */
    public void getCaseInfo(String caseInfoId) {
        showDialog();
        RequestUtil.getCaseInfo(caseInfoId, new MyObserver<ApplyInfoResponse>() {
            @Override
            public void onSuccess(ApplyInfoResponse result) {
                dismissDialog();
                if (result != null) {
                    applyInfoResponseSingleLiveEvent.setValue(result);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                getInfoErrorEvent.setValue(WorkstatusEnum.APPLICATION_INFO_FAIL.code);
            }
        });

    }

    /**
     * 网络请求方法，在ViewModel中调用Model层，通过Okhttp+Retrofit+RxJava发起请求
     */
    public void getPaymentList(String caseInfoId) {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("caseInfoId", caseInfoId);
        RequestUtil.payMentRecords(params, new MyObserver<List<FeeInfoResponse>>() {
            @Override
            public void onSuccess(List<FeeInfoResponse> result) {
                dismissDialog();
                feeInfoResponseSingleLiveEvent.setValue(result);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                getInfoErrorEvent.setValue(WorkstatusEnum.FEE_INFO_FAIL.code);
            }
        });

    }


    public void uploadSignPic(Bitmap bitmap) {
        File file = CommonUtil.bitmapToFile(bitmap);

        RequestBody requestFile = RequestBody.create(MediaType.parse("image/png"), file);
        MultipartBody.Part body = MultipartBody.Part.createFormData("file", file.getName(), requestFile);
        RequestUtil.uploadFile(body, new MyObserver<UploadFileBean>() {
            @Override
            public void onSuccess(UploadFileBean result) {
                dismissDialog();
                if (result != null) {
                    Log.e("Test", result.getId());
                    uploadFileSingleLiveEvent.setValue(result);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                Log.e("Test", "图片上传失败");
                getInfoErrorEvent.setValue(WorkstatusEnum.UPLOAD_SIGN_FAIL.code);
            }
        });
    }


    //获取文书
    public void listApplicantsAndDocuments(String caseInfoId) {
        showDialog();
        RequestUtil.listApplicantsAndDocuments(caseInfoId, new MyObserver<List<DocInfoResponse>>() {
            @Override
            public void onSuccess(List<DocInfoResponse> result) {
                dismissDialog();
                if (result != null) {
                    docInfoResponseSingleLiveEvent.setValue(result);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                getInfoErrorEvent.setValue(WorkstatusEnum.DOC_INFO_FAIL.code);
            }
        });

    }


    /**
     * 获取用户信息
     */
    public void getUserInfor(Map<String, Object> param, PopwindowUtil.ResultSecondListener resultListener) {
        RequestUtil.getUserInformation(param, new MyObserver() {
            @Override
            public void onSuccess(Object result) {
                resultListener.result(result);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                resultListener.secondResult("");
//                toastError(errorMsg == null ? "查询信息失败" : errorMsg);
            }
        });
    }

    //公证文书签名(批量)
    public void signMultiDocument(SignDocRequest request) {
        showDialog("提交中");
        RequestUtil.signMultiDocument(request, new MyObserver<Object>() {
            @Override
            public void onSuccess(Object result) {
                dismissDialog();
                //一个当事人签名针对的是当前所有的文书，一个人签完
                singleSignSucessLiveEvent.setValue(request.getPicFileUrl());
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                getInfoErrorEvent.setValue(WorkstatusEnum.SIGN_DOC_FAIL.code);
            }
        });

    }

    //视频咨询视频连线成功之后更新咨询记录状态
    public void justState(Map<String, String> param) {
        showDialog();
        RequestUtil.justState(param, new MyObserver<Object>() {
            @Override
            public void onSuccess(Object result) {
                dismissDialog();
                Log.e("Test", "视频咨询视频连线成功之后更新咨询记录状态成功！");
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                getInfoErrorEvent.setValue(WorkstatusEnum.JUST_STATE_FAIL.code);
            }
        });

    }

    //视频连线查询上传材料
    public void listOtherMaterial(String recordId) {
        showDialog();
        RequestUtil.listOtherMaterial(recordId, new MyObserver<List<MaterialListResponse.MaterialVoListDTO>>() {
            @Override
            public void onSuccess(List<MaterialListResponse.MaterialVoListDTO> result) {
                dismissDialog();
                if (result != null) {
                    materialLineListSingleLiveEvent.setValue(result);
                } else {
                    toastInfo(WorkstatusEnum.PLEASE_UPLOAD_HINT.msg);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                getInfoErrorEvent.setValue(WorkstatusEnum.LIST_MATERIAL_FAIL.code);
            }
        });

    }

    //查询所需材料
    public void listMaterialByCaseInfoId(String caseInfoId) {
        showDialog();
        RequestUtil.listMaterialByCaseInfoId(caseInfoId, new MyObserver<List<MaterialListResponse>>() {
            @Override
            public void onSuccess(List<MaterialListResponse> result) {
                dismissDialog();
                if (result != null) {
                    materialListSingleLiveEvent.setValue(result);
                } else {
                    toastInfo(WorkstatusEnum.PLEASE_UPLOAD_HINT.msg);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                getInfoErrorEvent.setValue(WorkstatusEnum.LIST_MATERIAL_FAIL.code);
            }
        });

    }

    //获取定位信息
    public void getLocationInformation(Activity activity, PopwindowUtil.ResultListener resultListener) {
//        XXPermissions.with(activity).permission(Permission.ACCESS_FINE_LOCATION).request(new OnPermission() {
//            @Override
//            public void hasPermission(List<String> granted, boolean all) {
//                try {
        GpsUtil.getlocation(activity, new GpsUtil.MapGPS() {
            @Override
            public void success(AMapLocation aMapLocation) {
                Log.i("Test", "获取定位信息成功：" + aMapLocation.getAddress());
                resultListener.result(aMapLocation);
            }

            @Override
            public void failed() {
                Log.i("Test", "获取定位信息失败");
                toastError(WorkstatusEnum.GET_LOCATION_FAIL.msg);
            }
        }, true);
//                }catch (Exception e){
//                    Log.i("Test", "获取定位信息时catch报错："+e.getMessage());
//                }
//
//            }

//            @Override
//            public void noPermission(List<String> denied, boolean quick) {
//                Log.i("Test","没有定位权限"+denied.toString());
//                if (quick) {
//                    // 如果是被永久拒绝就跳转到应用权限系统设置页面
//                    XXPermissions.gotoPermissionSettings(activity);
//                }else{
//                    // 如果是被拒绝就提示
//                   toastError("获取定位权限失败");
//                }
//            }
//        });
    }

    /**
     * 上传定位信息
     */
    public void uploadLocationInformation(Map<String, Object> param, PopwindowUtil.ResultListener resultListener) {
        showDialog("上传定位信息中");
        RequestUtil.getLocationReport(param, new MyObserver<Object>() {
            @Override
            public void onSuccess(Object result) {
                dismissDialog();
                resultListener.result(result);
                Log.i("Test", "上传定位信息成功");
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                Log.i("Test", "上传定位信息失败");
            }
        });
    }


    /**
     * 获取条目下标
     *
     * @param netWorkItemViewModel
     * @return
     */
    public int getItemPosition(HomeQuickListItemViewModel netWorkItemViewModel) {
        return observableList.indexOf(netWorkItemViewModel);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    public BindingCommand closeVideo = new BindingCommand(() -> {
        closeVideoEvent.call();
    });
}
