package com.gc.notarizationpc.ui.fragment

import android.os.Bundle
import android.util.Log
import android.view.View
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseFragment
import com.gc.notarizationpc.model.RoomCompanyInfo
import com.gc.notarizationpc.model.RoomRemarksInfo
import com.gc.notarizationpc.model.RoomUserInfo
import com.gc.notarizationpc.ui.adapter.VideoCompanyAdapter
import com.gc.notarizationpc.ui.adapter.VideoContentAdapter
import com.gc.notarizationpc.ui.adapter.VideoStepOneAdapter
import kotlinx.android.synthetic.main.fragment_video_step_one.*

class VideoStepOneFragment : BaseFragment() {
    private var applicantAdapter = VideoStepOneAdapter(2)
    private var proxyAdapter = VideoStepOneAdapter(1)
    private var companyAdapter = VideoCompanyAdapter()
    private var remarkAdapter = VideoContentAdapter()
    private var TAG = "VideoStepOneFragment"
    override fun lazyInit(view: View, savedInstanceState: Bundle) {}
    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {
        Log.i(TAG, "initViewsAndEvents")
        rvApplicant!!.adapter = applicantAdapter
        rvProxy!!.adapter = proxyAdapter
        rvCompany!!.adapter = companyAdapter
        rvContent!!.adapter = remarkAdapter
        hint2.visibility = View.GONE
        hint3.visibility = View.GONE
        applicantAdapter.addData(RoomUserInfo())
        remarkAdapter.addData(RoomRemarksInfo())
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.fragment_video_step_one
    }

    fun refreshApplicationList(applicationList: List<RoomUserInfo>) {
        Log.i(TAG, "refreshApplicationList")
        applicantAdapter.setNewDatas(applicationList)
    }

    fun refreshProxyList(proxyList: List<RoomUserInfo>) {
        Log.i(TAG, "refreshProxyList")
        hint2.visibility = View.VISIBLE
        proxyAdapter.addNewData(proxyList)
    }

    fun refreshCompanyList(proxyList: List<RoomCompanyInfo>) {
        Log.i(TAG, "refreshCompanyList")
        hint3.visibility = View.VISIBLE
        companyAdapter.addNewData(proxyList)
    }

    fun refreshRemarkList(proxyList: RoomRemarksInfo) {
        Log.i(TAG, "refreshRemarkList")
        hint4.visibility = View.VISIBLE
        remarkAdapter.data.clear()
        remarkAdapter.addData(proxyList)
    }
}