package com.gc.notarizationpc.ui.fragment;

import static me.goldze.mvvmhabit.utils.ToastUtils.toastError;

import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.amap.api.location.AMapLocation;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentDomesticEconomyApplyInfoBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.bean.SerachBean;
import com.gc.notarizationpc.bean.SpinnerItemData;
import com.gc.notarizationpc.data.model.request.AddSelfServiceRequest;
import com.gc.notarizationpc.data.model.response.AreaModel;
import com.gc.notarizationpc.data.model.response.BindingNotaryOfficeModel;
import com.gc.notarizationpc.data.model.response.EnterpriseCertificateTypeResponse;
import com.gc.notarizationpc.data.model.response.LanguageModel;
import com.gc.notarizationpc.data.model.response.MachineBindAreaResponse;
import com.gc.notarizationpc.data.model.response.NotaryMatterItem;
import com.gc.notarizationpc.data.model.response.PersonDocumentType;
import com.gc.notarizationpc.ui.selfservicecertificate.DomesticEconomyNotarizationActivity;
import com.gc.notarizationpc.ui.view.AddApplicantDialog;
import com.gc.notarizationpc.ui.view.NotaryListByRegionAlert;
import com.gc.notarizationpc.ui.viewmodel.DomesticEconomyApplyInfoViewModel;
import com.gc.notarizationpc.ui.viewmodel.DomesticEconomyCompanylListItemViewModel;
import com.gc.notarizationpc.ui.viewmodel.DomesticEconomyNorItemListItemViewModel;
import com.gc.notarizationpc.ui.viewmodel.DomesticEconomyPersonalListItemViewModel;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.GpsUtil;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import me.goldze.mvvmhabit.base.BaseFragment;
import me.goldze.mvvmhabit.binding.viewadapter.spinner.IKeyAndValue;
import me.goldze.mvvmhabit.utils.ConvertUtils;
import me.goldze.mvvmhabit.utils.MaterialDialogUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.tatarka.bindingcollectionadapter2.BR;

/**
 * 自助办证  申请信息
 */
public class DomesticEconomyApplyInfoFragment extends BaseFragment<FragmentDomesticEconomyApplyInfoBinding, DomesticEconomyApplyInfoViewModel> {

    private String useType;//出国留学--1->学习 定居移民--2->定居 探亲旅游--3->探亲 商务劳务--5-->劳务 境外其他--4-->其他
    private List<SerachBean> areaList;
    private List<SerachBean> lanList;
    private List<PersonDocumentType> mPersonDocumentTypeList;
    private List<EnterpriseCertificateTypeResponse> mEnterpriseCertificateTypeResponseList;
    private NotaryListByRegionAlert notaryListByRegionAlert;//选择公证处弹框
    private Handler mhandler;

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_domestic_economy_apply_info;

    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return super.onCreateView(inflater, container, savedInstanceState);

    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            useType = getArguments().getString("useType");
        }
        if (savedInstanceState != null) {
            useType = savedInstanceState.getString("useType");
        }
        mhandler = new Handler();


    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public void initParam() {
        super.initParam();

    }

    @Override
    public DomesticEconomyApplyInfoViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getActivity().getApplication());
        return ViewModelProviders.of(this, factory).get(DomesticEconomyApplyInfoViewModel.class);
    }

    @Override
    public void initData() {
        super.initData();
        areaList = new ArrayList<>();
        lanList = new ArrayList<>();
        viewModel.showData();
        viewModel.getNotaryAreaList();
        viewModel.getNotaryLanguageList();
        viewModel.getTypePerson();
        viewModel.getEnterpriseCertificateType();
        viewModel.findIntegratedMachineBindArea();
        viewModel.selectNotaryItemList.clear();

    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        Log.e("Test", "setUserVisibleHint");

        if (isVisibleToUser) {
            if (viewModel != null && (areaList.size() == 0 || lanList.size() == 0)) {
                viewModel.showData();
                viewModel.getNotaryAreaList();
                viewModel.getNotaryLanguageList();
                viewModel.getTypePerson();
                viewModel.getEnterpriseCertificateType();
                viewModel.findIntegratedMachineBindArea();
            }
        }
    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();

        /**
         * 拿到公证事项列表数据
         */
        viewModel.notaryItemLiveEvent.observe(this, new Observer<List<NotaryMatterItem>>() {
            @Override
            public void onChanged(List<NotaryMatterItem> strings) {
                try {
                    if (viewModel.selectNotaryItemList.size() >= 50) {
                        toastError(getString(R.string.no_more_50));
                    }
                    if (strings == null || strings.size() == 0) {
                        toastError(getString(R.string.notary_items_empty));
                        return;
                    }
                    List<SerachBean> serachBeans = new ArrayList<>();
                    Set<String> keySet = new HashSet<>();
                    Log.d("strings", strings + "");

                    for (int i = 0; i < strings.size(); i++) {
                        keySet.add(strings.get(i).getInitial());
                        SerachBean serachBean = new SerachBean();
                        serachBean.setKey(strings.get(i).getInitial());
                        List<SerachBean.Bean> beans = new ArrayList<>();
                        for (int j = 0; j < strings.get(i).getMattersInfos().size(); j++) {
                            SerachBean.Bean bean = new SerachBean.Bean();
                            bean.setName(strings.get(i).getMattersInfos().get(j).getMattersName());
                            bean.setId(strings.get(i).getMattersInfos().get(j).getMattersId());
                            bean.setCopyFee(strings.get(i).getMattersInfos().get(j).getCopyFee());
                            bean.setNotarizationFee(strings.get(i).getMattersInfos().get(j).getNotarizationFee());
                            bean.setHadProtectedContent(strings.get(i).getMattersInfos().get(j).getHadProtectedContent());
                            bean.setNewNotarial(strings.get(i).getMattersInfos().get(j).getNewNotarial());
                            bean.setProtectedContent(strings.get(i).getMattersInfos().get(j).getProtectedContent());
                            bean.setWrongRelatedMatters(strings.get(i).getMattersInfos().get(j).getWrongRelatedMatters());
                            beans.add(bean);
                            serachBean.setBean(beans);
                        }
                        serachBeans.add(serachBean);
                    }
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        serachBeans.sort((o1, o2) -> o1.getKey().compareTo(o2.getKey()));
                    }

                    PopwindowUtil.showGroup(serachBeans, getActivity(), 50, new PopwindowUtil.ResultListener<List<SerachBean.Bean>>() {
                        @Override
                        public void result(List<SerachBean.Bean> bean) {
                            List<NotaryMatterItem.MattersInfosDTO> allNotaryItemList = new ArrayList<>();
                            for (int i = 0; i < bean.size(); i++) {
                                NotaryMatterItem.MattersInfosDTO mattersInfosDTO = new NotaryMatterItem.MattersInfosDTO();
                                mattersInfosDTO.setMattersId(bean.get(i).getId());
                                mattersInfosDTO.setMattersName(bean.get(i).getName());
                                mattersInfosDTO.setNotarizationFee(bean.get(i).getNotarizationFee());
                                mattersInfosDTO.setCopyFee(bean.get(i).getCopyFee());
                                mattersInfosDTO.setProtectedContent(bean.get(i).getProtectedContent());
                                mattersInfosDTO.setWrongRelatedMatters(bean.get(i).getWrongRelatedMatters());
                                mattersInfosDTO.setNewNotarial(bean.get(i).getNewNotarial());
                                mattersInfosDTO.setHadProtectedContent(bean.get(i).getHadProtectedContent());
                                DomesticEconomyNorItemListItemViewModel itemNorItemViewModel = new DomesticEconomyNorItemListItemViewModel(viewModel, mattersInfosDTO);
                                if (viewModel.observableNorItemList.size() >= 50) {
                                    toastError(getString(R.string.no_more_50));
                                    return;
                                }
                                viewModel.observableNorItemList.add(itemNorItemViewModel);
                                allNotaryItemList.add(mattersInfosDTO);
                            }
                            if (viewModel.observableNorItemList.size() <= 50) {
                                viewModel.selectNotaryItemList.addAll(allNotaryItemList);
                            }

                        }
                    });

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        /**
         * 拿到公证处列表数据
         */
        viewModel.notaryListSingleLiveEvent.observe(this, new Observer<List<BindingNotaryOfficeModel>>() {
            @Override
            public void onChanged(List<BindingNotaryOfficeModel> response) {
                if (response != null && !response.isEmpty() && response.size() == 1) {
                    binding.edtNotarizationOffice.setText(response.get(0).getMechanismName());
                    viewModel.mCurrentNotaryOffice = response.get(0);
                    binding.edtNotarizationOffice.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                    binding.edtNotarizationOffice.setSingleLine(true);
                    binding.edtNotarizationOffice.setSelected(true);
                    binding.edtNotarizationOffice.setFocusable(true);
                    binding.edtNotarizationOffice.setFocusableInTouchMode(true);

                }

            }
        });
        //获取小一体机绑定区域
        viewModel.machineBindAreaResponseSingleLiveEvent.observe(this, new Observer<MachineBindAreaResponse>() {
            @Override
            public void onChanged(MachineBindAreaResponse result) {
                if (result.getProvince() == null) {
                    return;
                }
                try {
                    if (result.getProvince().getGroup() != null && result.getProvince().getGroup().size() > 0 && result.getProvince().getGroup().get(0).getCityList() != null
                            && result.getProvince().getGroup().get(0).getCityList().size() > 0) {
                        //至少有一个城市
                        if (result.getProvince().getGroup().get(0) != null && result.getProvince().getGroup().get(0).getCityList().size() == 1) {
                            viewModel.mSelectProvince = result.getProvince();
                            viewModel.mCity = result.getProvince().getGroup().get(0).getCityList().get(0);
                        } else {
                            //多个城市判断，当前机器所在地址的
                            GpsUtil.getlocation(getActivity(), new GpsUtil.MapGPS() {
                                @Override
                                public void success(AMapLocation aMapLocation) {
                                    if (aMapLocation != null) {
                                        //如果机器再所在城市里面，则展示aMapLocation.getCity()，反之则随便展示第一个绑定机器的城市;点击可以切换
                                        if (new Gson().toJson(result.getProvince().getGroup()).contains(aMapLocation.getCity())) {
                                            viewModel.mSelectProvince = result.getProvince();
                                            for (MachineBindAreaResponse.ProvinceDTO.GroupDTO groupDTO : result.getProvince().getGroup()) {
                                                if (groupDTO != null) {
                                                    for (MachineBindAreaResponse.ProvinceDTO.GroupDTO.CityListDTO cityListDTO : groupDTO.getCityList()) {
                                                        if (cityListDTO != null && !TextUtils.isEmpty(cityListDTO.getName()) && cityListDTO.getName().contains(aMapLocation.getCity())) {
                                                            viewModel.mCity = cityListDTO;
                                                        }
                                                    }
                                                }
                                            }
                                        } else {

                                        }
                                    }

                                }

                                @Override
                                public void failed() {
                                    Log.e("Test", "定位失败");
                                }
                            });
                        }
                        viewModel.getListNotaryByRegion();//获取公证处列表
                    } else {
                        //如果该一体机一个城市都没有绑定呢？
//                    if (result.getProvince().getGroup().get(0).getCityList() != null && result.getProvince().getGroup().get(0).getCityList().size() > 0) {
//                        binding.tvLocation.setText(result.getProvince().getGroup().get(0).getCityList().get(0).getName());
//                    }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        //个人证件类型
        viewModel.personDocumentTypeListSingleLiveEvent.observe(this, new Observer<List<PersonDocumentType>>() {
            @Override
            public void onChanged(List<PersonDocumentType> personDocumentTypeList) {
                mPersonDocumentTypeList = personDocumentTypeList;
            }

        });
        //企业证件类型
        viewModel.enterpriseCertificateListSingleLiveEvent.observe(this, new Observer<List<EnterpriseCertificateTypeResponse>>() {
            @Override
            public void onChanged(List<EnterpriseCertificateTypeResponse> personDocumentTypeList) {
                mEnterpriseCertificateTypeResponseList = personDocumentTypeList;
            }

        });
        //点击个人 申请人列表（编辑，删除）
        viewModel.applicantOperateEvent.observe(this, new Observer<Map<String, Object>>() {
            @Override
            public void onChanged(Map<String, Object> result) {
                if (result != null) {
                    String flag = (String) result.get("flag");
                    int pos = Integer.parseInt(result.get("pos").toString());
                    Log.e("Test", pos + "==pos");
                    if (!TextUtils.isEmpty(flag)) {
                        if ("edit".equals(flag)) {
                            AddApplicantDialog addApplicantDialog = new AddApplicantDialog(getActivity(), mhandler, (AddSelfServiceRequest.RecordApplicantsDTO) result.get("value"), mPersonDocumentTypeList, mEnterpriseCertificateTypeResponseList, null, new PopwindowUtil.ResultSecondListener() {
                                @Override
                                public void result(Object value) {
                                    if (value == null) {
                                        return;
                                    }
                                    //获取到编辑后的个人申请人
                                    viewModel.observablePersonalList.set(pos, new DomesticEconomyPersonalListItemViewModel(viewModel, (AddSelfServiceRequest.RecordApplicantsDTO) value));
                                }

                                @Override
                                public void secondResult(Object value) {

                                }
                            });
                            if (addApplicantDialog != null && !addApplicantDialog.isShowing()) {
                                addApplicantDialog.show();
                                settingDialog(addApplicantDialog);
                            }
                        } else if ("delete".equals(flag)) {
                            MaterialDialogUtils.showBasicDialog(getActivity(), getString(R.string.delete_hint))
                                    .onNegative(new MaterialDialog.SingleButtonCallback() {
                                        @Override
                                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                        }
                                    }).onPositive(new MaterialDialog.SingleButtonCallback() {
                                        @Override
                                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                            viewModel.observablePersonalList.remove(Integer.parseInt(result.get("pos").toString()));
                                        }
                                    }).show();
                        }
                    }
                }
            }

        });
        //点击单位 申请人列表（编辑，删除）
        viewModel.companyOperateEvent.observe(this, new Observer<Map<String, Object>>() {
            @Override
            public void onChanged(Map<String, Object> result) {
                if (result != null) {
                    String flag = (String) result.get("flag");
                    if (!TextUtils.isEmpty(flag)) {
                        if ("edit".equals(flag)) {
                            AddApplicantDialog addApplicantDialog = new AddApplicantDialog(getActivity(), mhandler, null, mPersonDocumentTypeList, mEnterpriseCertificateTypeResponseList, (AddSelfServiceRequest.RecordCorpApplicantsDTO) result.get("value"), new PopwindowUtil.ResultSecondListener() {
                                @Override
                                public void result(Object value) {
                                }

                                @Override
                                public void secondResult(Object value) {
                                    if (value == null) {
                                        return;
                                    }
                                    //获取到编辑后的单位申请人
                                    for (DomesticEconomyCompanylListItemViewModel tmp : viewModel.observableCompanyList) {
                                        if (tmp != null && tmp.entity != null && tmp.entity.get().getCredentialNum().equals(((AddSelfServiceRequest.RecordCorpApplicantsDTO) result.get("value")).getCredentialNum())) {
                                            viewModel.observableCompanyList.remove(tmp);
                                        }
                                    }
                                    viewModel.observableCompanyList.add(new DomesticEconomyCompanylListItemViewModel(viewModel, (AddSelfServiceRequest.RecordCorpApplicantsDTO) value));

                                }
                            });
                            if (addApplicantDialog != null && !addApplicantDialog.isShowing()) {
                                addApplicantDialog.show();
                                settingDialog(addApplicantDialog);
                            }
                        } else if ("delete".equals(flag)) {
                            MaterialDialogUtils.showBasicDialog(getActivity(), getString(R.string.delete_hint))
                                    .onNegative(new MaterialDialog.SingleButtonCallback() {
                                        @Override
                                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                        }
                                    }).onPositive(new MaterialDialog.SingleButtonCallback() {
                                        @Override
                                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                            viewModel.observableCompanyList.remove(Integer.parseInt(result.get("pos").toString()));
                                        }
                                    }).show();
                        }
                    }
                }
            }

        });
        //点击公证事项列表（编辑，删除）
        viewModel.notaryItemOperateEvent.observe(this, new Observer<Map<String, Object>>() {
            @Override
            public void onChanged(Map<String, Object> result) {
                if (result != null) {
                    String flag = (String) result.get("flag");
                    if (!TextUtils.isEmpty(flag)) {
                        if ("delete".equals(flag)) {
                            MaterialDialogUtils.showBasicDialog(getActivity(), getString(R.string.delete_hint))
                                    .onNegative(new MaterialDialog.SingleButtonCallback() {
                                        @Override
                                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                        }
                                    }).onPositive(new MaterialDialog.SingleButtonCallback() {
                                        @Override
                                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                            if (Integer.parseInt(result.get("pos").toString()) < viewModel.observableNorItemList.size()) {
                                                viewModel.observableNorItemList.remove(Integer.parseInt(result.get("pos").toString()));
                                            }
                                            if (Integer.parseInt(result.get("pos").toString()) < viewModel.selectNotaryItemList.size()) {
                                                viewModel.selectNotaryItemList.remove(Integer.parseInt(result.get("pos").toString()));
                                            }

                                        }
                                    }).show();
                        }
                    }
                }
            }

        });
        viewModel.areaClickSingleLiveEvent.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean areaModels) {
                PopwindowUtil.showGroup(areaList, getActivity(), 1, new PopwindowUtil.ResultListener<List<SerachBean.Bean>>() {
                    @Override
                    public void result(List<SerachBean.Bean> bean) {
//                        binding.edtNotarizationAddress.setText(value);
                        IKeyAndValue keyAndValue = new SpinnerItemData(bean.get(0).getId(), bean.get(0).getName());
                        viewModel.selectedNotaryArea.set(keyAndValue);
                    }
                });
            }

        });
        viewModel.lanClickSingleLiveEvent.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean areaModels) {
                PopwindowUtil.showGroup(lanList, getActivity(), 1, new PopwindowUtil.ResultListener<List<SerachBean.Bean>>() {
                    @Override
                    public void result(List<SerachBean.Bean> value) {
//                        binding.edtNotarizationLanguage.setText(value);
                        IKeyAndValue keyAndValue = new SpinnerItemData(value.get(0).getId(), value.get(0).getName());
                        viewModel.selectedNotaryLanguage.set(keyAndValue);
                    }
                });
            }

        });

        viewModel.areaListSingleLiveEvent.observe(this, new Observer<List<AreaModel>>() {
            @Override
            public void onChanged(List<AreaModel> areaModels) {
                if (areaModels == null || areaModels.size() == 0) {
                    ToastUtils.showShort(getString(R.string.getNotaryOfficeAreaFail));
                    return;
                }
                try {
                    List<SerachBean> serachBeans = new ArrayList<>();
                    Set<String> keySet = new HashSet<>();
                    Log.d("areaModels", areaModels + "");
                    for (int i = 0; i < areaModels.size(); i++) {
                        keySet.add(CommonUtil.getFirstLetter(areaModels.get(i).getZhName()));
                    }
                    for (String key : keySet) {
                        List<SerachBean.Bean> beans = new ArrayList<>();
                        SerachBean serachBean = new SerachBean();
                        serachBean.setKey(key);
                        for (int i = 0; i < areaModels.size(); i++) {
                            if (key.equals(CommonUtil.getFirstLetter(areaModels.get(i).getZhName()))) {
                                SerachBean.Bean bean = new SerachBean.Bean();
                                bean.setName(areaModels.get(i).getZhName());
                                bean.setId(areaModels.get(i).getId());
                                beans.add(bean);
                            }
                        }
                        serachBean.setBean(beans);
                        serachBeans.add(serachBean);
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            serachBeans.sort((o1, o2) -> o1.getKey().compareTo(o2.getKey()));
                        }
                    }
                    IKeyAndValue keyAndValue = new SpinnerItemData(areaModels.get(0).getId(), areaModels.get(0).getZhName());
                    viewModel.selectedNotaryArea.set(keyAndValue);
                    areaList = serachBeans;

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

        });


        viewModel.notaryLanguageOfficeListSingleLiveEvent.observe(this, new Observer<List<LanguageModel>>() {

            @Override
            public void onChanged(List<LanguageModel> languageModels) {
                if (languageModels == null || languageModels.size() == 0) {
                    ToastUtils.showShort(getString(R.string.getNotaryOfficeLanguageFail));
                    return;
                }
                try {
                    List<SerachBean> serachBeans = new ArrayList<>();
                    Set<String> keySet = new HashSet<>();
                    Log.d("languageModels", languageModels + "");
                    for (int i = 0; i < languageModels.size(); i++) {
                        keySet.add(CommonUtil.getFirstLetter(languageModels.get(i).getLabel()));
                    }
                    for (String key : keySet) {
                        List<SerachBean.Bean> beans = new ArrayList<>();
                        SerachBean serachBean = new SerachBean();
                        serachBean.setKey(key);
                        for (int i = 0; i < languageModels.size(); i++) {
                            if (key.equals(CommonUtil.getFirstLetter(languageModels.get(i).getLabel()))) {
                                SerachBean.Bean bean = new SerachBean.Bean();
                                bean.setName(languageModels.get(i).getLabel());
                                bean.setId(languageModels.get(i).getValue());
                                beans.add(bean);
                            }
                        }
                        serachBean.setBean(beans);
                        Log.d("languageModels" + key, beans.toString() + "");
                        serachBeans.add(serachBean);
                    }
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        serachBeans.sort((o1, o2) -> o1.getKey().compareTo(o2.getKey()));
                    }
                    IKeyAndValue keyAndValue = new SpinnerItemData(languageModels.get(0).getValue(), languageModels.get(0).getLabel());
                    viewModel.selectedNotaryLanguage.set(keyAndValue);
                    lanList = serachBeans;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });


        viewModel.clickEvent.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer response) {
                if (response != null) {
                    switch (response) {
                        //点击公证处弹框
                        case 1001:
                            chooseNotaryOfficeList(viewModel.notarialOfficeListBeans);
                            break;
                        //点击下一步
                        case 1002:
                            viewModel.addSelfOrder(useType);
                            break;
                        //公证事项份数输入为0
                        case 1003:
                            toastError(getString(R.string.nortary_item_num_error));
                            break;
                        //新增申请人(个人，单位)
                        case 1004:
                            if (viewModel.observablePersonalList.size() + viewModel.observableCompanyList.size() >= 20) {
                                toastError(getString(R.string.no_more_tweenty));
                                return;
                            }
                            AddApplicantDialog feeDialog = new AddApplicantDialog(getActivity(), mhandler, null, mPersonDocumentTypeList, mEnterpriseCertificateTypeResponseList, null, new PopwindowUtil.ResultSecondListener() {
                                @Override
                                public void result(Object value) {
                                    if (value == null) {
                                        return;
                                    }
                                    //当新增了代理人后，当前当事人必须为代理人
                                    AddSelfServiceRequest.RecordApplicantsDTO newApplicant = (AddSelfServiceRequest.RecordApplicantsDTO) value;
                                    if (newApplicant.getSubstitute() == 1) {
                                        viewModel.observablePersonalList.get(0).entity.get().setSubstitute(1);
                                        viewModel.observablePersonalList.get(0).entity.get().setSubstituteStr("代理人");
                                        DomesticEconomyPersonalListItemViewModel first = viewModel.observablePersonalList.get(0);
                                        viewModel.observablePersonalList.remove(0);
                                        viewModel.observablePersonalList.add(0, first);
                                    }
                                    viewModel.observablePersonalList.add(new DomesticEconomyPersonalListItemViewModel(viewModel, newApplicant));
                                }

                                @Override
                                public void secondResult(Object value) {
                                    if (value == null) {
                                        return;
                                    }
                                    viewModel.observableCompanyList.add(new DomesticEconomyCompanylListItemViewModel(viewModel, (AddSelfServiceRequest.RecordCorpApplicantsDTO) value));

                                }
                            });
                            if (!feeDialog.isShowing()) {
                                feeDialog.show();
                                settingDialog(feeDialog);
                            }
                            break;
                        case 1005:
                            //选择了公证处后，获取公证事项
                            viewModel.getMattersByUseType(viewModel.mCurrentNotaryOffice.getId());
                            break;
                    }
                }

            }
        });

        viewModel.nextStepEvent.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                //相应控件的监听里面实现跳转
                ((DomesticEconomyNotarizationActivity) getActivity()).viewPager.setCurrentItem(1, false);
//                SelfServiceHasBeenFinishedFragment lastFragment = (SelfServiceHasBeenFinishedFragment) ((SelfNotarizationActivity) getActivity()).getSupportFragmentManager().findFragmentByTag("android:switcher:" + R.id.viewpager + ":" + 3);
//                lastFragment.setNotarialMatterItems(viewModel.selectNotaryItemList);
            }
        });


    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);

    }

    /**
     * 选择公证处
     */
    private void chooseNotaryOfficeList(List<BindingNotaryOfficeModel> data) {
        if (data != null && data.size() > 1) {
            if (notaryListByRegionAlert == null || !notaryListByRegionAlert.isShowing()) {
                notaryListByRegionAlert = new NotaryListByRegionAlert(getActivity(), data, new PopwindowUtil.ResultListener<BindingNotaryOfficeModel>() {
                    @Override
                    public void result(BindingNotaryOfficeModel value) {
                        notaryListByRegionAlert.dismiss();
                        if (value != null && value.getMechanismName() != null) {
                            viewModel.mCurrentNotaryOffice = value;
                            binding.edtNotarizationOffice.setText(value.getMechanismName());
                            binding.edtNotarizationOffice.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                            binding.edtNotarizationOffice.setSingleLine(true);
                            binding.edtNotarizationOffice.setSelected(true);
                            binding.edtNotarizationOffice.setFocusable(true);
                            binding.edtNotarizationOffice.setFocusableInTouchMode(true);
                        } else {
                            toastError(getResources().getString(R.string.getNotaryOfficeFail));
                        }
                    }
                });
                notaryListByRegionAlert.show();
                Window window = notaryListByRegionAlert.getWindow();
                window.getDecorView().setBackgroundColor(getActivity().getResources().getColor(R.color.white));
                window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                window.getDecorView().setPadding(0, 0, 0, 0);
                window.setLayout(
                        window.getContext().getResources().getDisplayMetrics().widthPixels - 200,
                        window.getContext().getResources().getDisplayMetrics().heightPixels - 120);

                window.setGravity(Gravity.CENTER);
                notaryListByRegionAlert.setCanceledOnTouchOutside(false);
            }
        } else if (data.size() <= 0) {
            toastError(getString(R.string.no_find_notary_office_please_contact));
        }

    }

    private void settingDialog(Dialog dialog) {
        Window dialogWindow = dialog.getWindow();
        dialogWindow.getDecorView().setBackgroundColor(getResources().getColor(android.R.color.white));
        dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = ConvertUtils.dp2px(800f);
        lp.height = ConvertUtils.dp2px(550f);
        lp.alpha = 1.0f;
        dialogWindow.setAttributes(lp);
        dialogWindow.setGravity(Gravity.CENTER);
        dialog.setCanceledOnTouchOutside(false);
    }

    @Override
    public void onResume() {
        super.onResume();
    }
}
