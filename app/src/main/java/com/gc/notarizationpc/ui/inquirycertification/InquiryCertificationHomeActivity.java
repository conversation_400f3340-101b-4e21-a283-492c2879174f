package com.gc.notarizationpc.ui.inquirycertification;

import static com.gc.notarizationpc.util.CommonUtil.dateStringToString;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;

import androidx.lifecycle.ViewModelProviders;

import com.example.scarx.idcardreader.utils.IdCardRenderUtils;
import com.example.scarx.idcardreader.utils.imp.MyCallBack;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityInquiryCertificationHomeBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.viewmodel.InquiryCertificationHomeViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.umeng.analytics.MobclickAgent;
import com.zkteco.android.IDReader.IDPhotoHelper;
import com.zkteco.android.IDReader.WLTService;
import com.zkteco.android.biometric.core.device.ParameterHelper;
import com.zkteco.android.biometric.core.device.TransportType;
import com.zkteco.android.biometric.core.utils.ToolUtils;
import com.zkteco.android.biometric.module.idcard.IDCardReader;
import com.zkteco.android.biometric.module.idcard.IDCardReaderFactory;
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo;

import java.util.HashMap;
import java.util.concurrent.CountDownLatch;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.utils.SPUtils;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class InquiryCertificationHomeActivity extends BaseActivity<ActivityInquiryCertificationHomeBinding, InquiryCertificationHomeViewModel> {
    private String TAG = "Test";
    private IdCardRenderUtils idCardReaderUtils = new IdCardRenderUtils();
    private SoundPool soundPool = new SoundPool(100, AudioManager.STREAM_MUSIC, 0);
    private boolean isActivity = true;
    private boolean isNoCard = true;
    private IDCardInfo idcardinfo = null;
    private Bitmap idCardBmp = null;
    private Integer readSuccess = null;
    private Integer readTips = null;
    private Integer validateSuccess = null;
    private Integer validateFail = null;
    private Integer retry = null;
    private Integer faceCamera = null;
    private boolean hasCardInfo = false;

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_inquiry_certification_home;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public InquiryCertificationHomeViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(InquiryCertificationHomeViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }

    private void startIdCardRead() {
        try {
            Log.i("Test", "startIdCardRead");

//        LogHelper.setLevel(8); // 身份证打印等级
            HashMap<String, Object> ideograms = new HashMap<>();
            ideograms.put(ParameterHelper.PARAM_KEY_VID, 1024);
            ideograms.put(ParameterHelper.PARAM_KEY_PID, 50010);
            IDCardReader idCardReader = IDCardReaderFactory.createIDCardReader(
                    ToolUtils.getApplicationContext(),
                    TransportType.USB,
                    ideograms
            );
            idCardReaderUtils.readerIdCard(idCardReader, new CountDownLatch(1), new MyCallBack() {
                @Override
                public void onSuccess(IDCardInfo idCardInfo) {
                    Log.i(TAG, "readerIdCard success 读卡成功" + (null == idcardinfo));
                    if (isNoCard && null == idcardinfo) {
                        idcardinfo = idCardInfo;
                        Log.e(TAG, "播放身份识别成功的声音");
                        playSound(1);
                        isNoCard = false;

//                        CustomIdCardInfo customIdCardInfo = new CustomIdCardInfo();
//                        customIdCardInfo.setAddress(idcardinfo.getAddress());
//                        customIdCardInfo.setBirthday(idcardinfo.getBirth());
//                        customIdCardInfo.setIdCard(idcardinfo.getId());
//                        customIdCardInfo.setName(idcardinfo.getName());
//                        customIdCardInfo.setNation(idcardinfo.getNation());
//                        customIdCardInfo.setGender(idcardinfo.getSex());
                        showIdCardInfo(idcardinfo);


                    }
                }

                @Override
                public void onFail(String error) {
                    Log.e(TAG, "身份证读卡器出现异常,请检查设备");

                    Log.i(TAG, "readerIdCard fail");
                }

                @Override
                public void onRequestDevicePermission() {
                }

                @Override
                public void onNoCards() {
                    Log.i(TAG, "onNoCards");
                    isNoCard = true;
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            MobclickAgent.reportError(this, e);
        }
    }

    @SuppressLint("SetTextI18n")
    public void showIdCardInfo(IDCardInfo idcardinfo) {
        Log.i(TAG, "showIdCardInfo");
        runOnUiThread(() -> {
            binding.tvName.setText(idcardinfo.getName());
            binding.tvSex.setText(idcardinfo.getSex());
            binding.tvNation.setText(idcardinfo.getNation());
            binding.tvIdcard.setText(idcardinfo.getId());
            binding.tvAddress.setText(idcardinfo.getAddress());
            binding.tvDepart.setText(idcardinfo.getDepart());
            binding.tvExpire.setText(idcardinfo.getValidityTime());
            binding.tvBirthday.setText(dateStringToString(idcardinfo.getBirth(), "yyyy.MM.dd"));
            //todo xhf
//            userInfo.setBirthday(dateStringToString(idcardinfo.getBirth()));
//            if ("男".equals(idcardinfo.getSex())) {
//                userInfo.setGender(1);
//            } else {
//                userInfo.setGender(2);
//            }
//            userInfo.setIdCard(idcardinfo.getId());
//            userInfo.setNation(idcardinfo.getNation());
//            userInfo.setName(idcardinfo.getName());
//            userInfo.setAddress(idcardinfo.getAddress());
            byte[] idCardImgbuf = new byte[WLTService.imgLength];
            int resultTok = WLTService.wlt2Bmp(idcardinfo.getPhoto(), idCardImgbuf);
            if (resultTok == 1) {
                idCardBmp = IDPhotoHelper.Bgr2Bitmap(idCardImgbuf);
                binding.imgHead.setImageBitmap(idCardBmp);
            }
//            try {
//                Thread.sleep(500L);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            playSound(3);
//            try {
//                Thread.sleep(3000L);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
            hasCardInfo = true;
        });
    }

    @Override
    protected void onStop() {
        super.onStop();
        isActivity = false;
        idCardReaderUtils.setbStoped(true);
    }

    @Override
    protected void onDestroy() {
        isActivity = false;
        try {
            idCardReaderUtils.setbStoped(true);
            soundPool.release();
        } catch (Exception e) {
            MobclickAgent.reportError(this, e);
            Log.e(TAG, e.getMessage());
        }
        super.onDestroy();
    }


    private void loadSoundRes() {
        Log.i(TAG, "loadSoundRes");
        new Handler(Looper.getMainLooper()).post(() -> {
            if (SPUtils.getInstance().getString("changeLanguage", "zh").equals("bo")) {
                //咚
                readSuccess = soundPool.load(this, R.raw.readcard_success_z, 1);
                //请将身份证放置于阅读器上
                readTips = soundPool.load(this, R.raw.idcardread_z, 1);
                //请正对摄像头
                faceCamera = soundPool.load(this, R.raw.face_camera_z, 1);
                //请重试
                retry = soundPool.load(this, R.raw.retry_z, 1);
                //比对成功
                validateSuccess = soundPool.load(this, R.raw.validate_success_z, 1);
                //比对失败
                validateFail = soundPool.load(this, R.raw.validate_fail_z, 1);
            } else {
                //咚
                readSuccess = soundPool.load(this, R.raw.readcard_success, 1);
                //请将身份证放置于阅读器上
                readTips = soundPool.load(this, R.raw.idcardread, 1);
                //请正对摄像头
                faceCamera = soundPool.load(this, R.raw.face_camera, 1);
                //请重试
                retry = soundPool.load(this, R.raw.retry, 1);
                //比对成功
                validateSuccess = soundPool.load(this, R.raw.validate_success, 1);
                //比对失败
                validateFail = soundPool.load(this, R.raw.validate_fail, 1);
            }
        });
    }

    /**
     * 1: 读卡成功   2：请将身份证放置到阅读器上  3：请正对摄像头   4：比对失败 5：比对成功  6：请重试
     */
    public void playSound(int index) {
        Log.i(TAG, "playSound index" + index);
        switch (index) {
            case 1:
                soundPool.play(readSuccess, 1f, 1f, 1, 0, 1f);
                break;
            case 2:
                soundPool.play(readTips, 1f, 1f, 1, 0, 1f);
                break;
            case 3:
                soundPool.play(faceCamera, 1f, 1f, 1, 0, 1f);
                break;
            case 4:
                soundPool.play(validateFail, 1f, 1f, 1, 0, 1f);
                break;
            case 5:
                soundPool.play(validateSuccess, 1f, 1f, 1, 0, 1f);
                break;
            case 6:
                soundPool.play(retry, 1f, 1f, 1, 0, 1f);
                break;
        }
    }


    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });

        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });
        loadSoundRes();

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                playSound(2);
            }
        }, 400);
        // 开启身份证识别
        if (idCardReaderUtils.isbStoped()) {
            Log.e("Test", "idCardReaderUtils.setbStoped(false)");
            idCardReaderUtils.setbStoped(false);
        } else {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    startIdCardRead();
                }
            }, 3500);
        }
    }

    @Override
    public void initViewObservable() {

    }


}
