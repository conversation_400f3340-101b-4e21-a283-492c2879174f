package com.gc.notarizationpc.ui.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.response.BindingNotaryOfficeModel;
import com.gc.notarizationpc.ui.adapter.NotaryOfficeListAdapter;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.github.promeg.pinyinhelper.Pinyin;

import java.util.ArrayList;
import java.util.List;

public class NotaryListByRegionAlert extends Dialog {
    private Context mContext;

    private CountDownTimer timer;
    private PopwindowUtil.ResultListener resultListener;

    private List<BindingNotaryOfficeModel> dataList;

    private BindingNotaryOfficeModel model;

    private List<String> notaryOfficeName;

    private List<BindingNotaryOfficeModel> tempDataList;

    private NotaryOfficeListAdapter mNotaryOfficeListAdapter;

    private List<BindingNotaryOfficeModel> originalDatas = new ArrayList<>();

    public NotaryListByRegionAlert(@NonNull Context context, List<BindingNotaryOfficeModel> data, PopwindowUtil.ResultListener eventsListener) {
        super(context);
        mContext = context;
        resultListener = eventsListener;
        dataList = new ArrayList<>();
        originalDatas = data;
        tempDataList = new ArrayList<>();
        dataList.addAll(data);
        tempDataList.addAll(data);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_notary_list_by_origin);
        initView();
    }

    private void initView() {
        EditText edtSearch = findViewById(R.id.edt_search);
        TextView tvSearch = findViewById(R.id.tv_search);
        TextView tvCancel = findViewById(R.id.tv_cancel);
        TextView tvConfirm = findViewById(R.id.tv_confirm);
        ImageView ivClose = findViewById(R.id.iv_close);
//        RelativeLayout relativeLayout = findViewById(R.id.rnl_search_bar);
//        LinearLayout noDataLinearLayout = findViewById(R.id.no_data);
        RecyclerView rvNotarialOffice = findViewById(R.id.list_notarial_office);
        rvNotarialOffice.setLayoutManager(new GridLayoutManager(mContext, 3));
        if (null != tempDataList && tempDataList.size() > 0) {
            //每次进来，默认选中第一个公证处
            tempDataList.get(0).setSelected(true);
            model = tempDataList.get(0);
            for (int i = 1; i < tempDataList.size(); i++) {
                tempDataList.get(i).setSelected(false);
            }
            mNotaryOfficeListAdapter = new NotaryOfficeListAdapter(mContext, R.layout.item_list_notary_office, 0);
            rvNotarialOffice.setAdapter(mNotaryOfficeListAdapter);
            mNotaryOfficeListAdapter.refreshAdapter(tempDataList);

            mNotaryOfficeListAdapter.setOnClick(new NotaryOfficeListAdapter.Onclick() {
                @Override
                public void OnClicklistener(int position, int flag) {
                    model = dataList.get(position);
                    for (int i = 0; i < dataList.size(); i++) {
                        dataList.get(i).setSelected(false);
                    }
                    dataList.get(position).setSelected(true);
                    Log.e("OnClicklistener", model.getMechanismName());
                    //点击单选

                }
            });

        }
        //确认
        tvConfirm.setOnClickListener(v -> {
            resultListener.result(model);
        });

        edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (TextUtils.isEmpty(edtSearch.getText().toString())) {
                    dataList = originalDatas;
                }
            }
        });

        //搜索
        tvSearch.setOnClickListener(v -> {
            tempDataList.clear();
            if (originalDatas != null && originalDatas.size() > 0) {
                for (int i = 0; i < originalDatas.size(); i++) {
                    if (originalDatas.get(i).getMechanismName() != null) {
                        String firstString = Pinyin.toPinyin(originalDatas.get(i).getMechanismName(), "").toLowerCase().replaceAll(",", "");
                        String secondString = Pinyin.toPinyin(edtSearch.getText().toString(), ",").toLowerCase().replaceAll(",", "");
                        if (firstString.contains(secondString)) {
                            BindingNotaryOfficeModel bindingNotaryOfficeModel = new BindingNotaryOfficeModel();
                            bindingNotaryOfficeModel = originalDatas.get(i);
                            bindingNotaryOfficeModel.setSelected(false);
                            tempDataList.add(originalDatas.get(i));
                        }
                    }
                }
            }
            if (tempDataList != null && tempDataList.size() > 0) {
                tempDataList.get(0).setSelected(true);
                model = tempDataList.get(0);
                for (int i = 1; i < tempDataList.size(); i++) {
                    tempDataList.get(i).setSelected(false);
                }
                dataList = tempDataList;
                mNotaryOfficeListAdapter.refreshAdapter(dataList);
            }

        });

        //关闭
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (this != null && isShowing()) {
                    dismiss();
                }
            }
        });

        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (this != null && isShowing()) {
                    dismiss();
                }
            }
        });

    }


}
