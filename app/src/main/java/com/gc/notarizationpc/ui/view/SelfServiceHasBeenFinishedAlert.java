package com.gc.notarizationpc.ui.view;

import static com.gc.notarizationpc.common.AppConfig.SERVER_TYPE_OLINE;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.util.PopwindowUtil;

public class SelfServiceHasBeenFinishedAlert extends Dialog {
    private Context mContext;

    private CountDownTimer timer;
    private PopwindowUtil.ResultListener resultListener;

    public SelfServiceHasBeenFinishedAlert(@NonNull Context context, PopwindowUtil.ResultListener eventsListener) {
        super(context);
        mContext = context;
        resultListener = eventsListener;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.self_service_has_been_finish_alert);
        initView();

    }

    private void initView() {
        TextView timeDecreaseText = findViewById(R.id.time_decrease_text);
        ImageView imageView = findViewById(R.id.qr_code);
        if (AppConfig.SERVER_TYPE == SERVER_TYPE_OLINE) {
            imageView.setImageResource(R.mipmap.applet_qrcode_online);
        } else {
            imageView.setImageResource(R.mipmap.applet_qrcode_test);
        }
        Button confirmBtn = findViewById(R.id.back_to_home);
        confirmBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                timer.cancel();
                // 点击确定传1
                resultListener.result(1);
            }
        });
        timer = new CountDownTimer(60000, 1000) {
            @Override
            public void onTick(long l) {
                if ((l / 1000) < 10){
                    timeDecreaseText.setText("00:" + "0" + (l / 1000));
                }else {
                    timeDecreaseText.setText("00:" + (l / 1000));
                }

            }

            @Override
            public void onFinish() {
                // 倒计时结束传2
                resultListener.result(2);
            }
        };
        timer.start();
    }

}
