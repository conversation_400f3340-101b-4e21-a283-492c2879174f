package com.gc.notarizationpc.ui.fragment;


import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.utils.TextUtils;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentSelfServiceUploadMaterialBinding;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.data.model.request.UpdateSelfServiceMaterialRequest;
import com.gc.notarizationpc.data.model.response.BindingNotaryOfficeModel;
import com.gc.notarizationpc.data.model.response.ListRequiredMaterialResponse;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.ui.ReservationActivity;
import com.gc.notarizationpc.ui.selfservicecertificate.SelfNotarizationActivity;
import com.gc.notarizationpc.ui.selfservicecertificate.SelfTakePhotoActivity;
import com.gc.notarizationpc.ui.view.ImagePreviewDialog;
import com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceMaterialToUploadGridItemViewModel;
import com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceMaterialToUploadGridViewModel;
import com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceUploadMaterialViewModel;
import com.gc.notarizationpc.util.SelfTakePhotoResultListener;
import com.gc.notarizationpc.widget.SelfServiceStepBar;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseFragment;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.BR;

/**
 * 这个是自助办证流程中上传材料的fragment
 */

public class SelfServiceUploadMaterialFragment extends BaseFragment<FragmentSelfServiceUploadMaterialBinding, FragmentSelfServiceUploadMaterialViewModel>
        implements SelfTakePhotoResultListener, View.OnClickListener {

    private SelfServiceStepBar selfServiceStepBar;
    private BindingNotaryOfficeModel selectNotary;
    int currentParentPos = 0;
    int receiveWay = 1;//领取方式 1-自取，2-邮寄(到付)
    private static final int MAX_SELECT_NUMBER = 30;

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_self_service_upload_material;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public void initData() {
        Log.e("Test", "SelfServiceUploadMaterialFragment initData");
        super.initData();
        initSelfServiceStepBar();
        String fragment_id = getArguments().getString("fragment_id");
        if (fragment_id != null) {
            binding.tvLastStep.setVisibility(View.GONE);
        }
        String recordId = SPUtils.getInstance().getString("self_recordId");
        selectNotary = (BindingNotaryOfficeModel) SPUtils.getInstance().readObject(Utils.getContext(), "self_selectNotary");
        if (!TextUtils.isEmpty(recordId) && selectNotary != null) {
            if (fragment_id != null) {
                showData(recordId);
            }
        }

    }

    public void setLastStepGone() {
        binding.tvLastStep.setVisibility(View.GONE);
    }


    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);

        if (isVisibleToUser && viewModel != null) {
            String recordId = SPUtils.getInstance().getString("self_recordId");
            selectNotary = (BindingNotaryOfficeModel) SPUtils.getInstance().readObject(Utils.getContext(), "self_selectNotary");
            if (!TextUtils.isEmpty(recordId) && selectNotary != null) {
                showData(recordId);
            }
        }
    }

    private void showData(String recordId) {
        if (selectNotary != null) {
            binding.selfServicePickupTime.setText("周一至周五 " + selectNotary.getBusiTime());
            binding.selfServicePickUpAddress.setText(selectNotary.getMechanismName());
            binding.selfServiceAddress.setText(selectNotary.getAddress());
        }
        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
        if (idCardUserInfo != null) {
            binding.edtRecipient.setText(idCardUserInfo.getName());
            binding.edtPhone.setText(idCardUserInfo.getMobile());
            binding.edtPostAddress.setText(idCardUserInfo.getAddress());
        }
        if (binding.rbEmailText.isChecked()) {
            binding.lnlLingqu.setVisibility(View.GONE);
            binding.lnlLingquTime.setVisibility(View.GONE);
            binding.lnlNotrayAddr.setVisibility(View.GONE);
            binding.lnlAddress.setVisibility(View.VISIBLE);
            binding.lnlPhone.setVisibility(View.VISIBLE);
            binding.lnlRecipient.setVisibility(View.VISIBLE);
            //领取方式 1-自取，2-邮寄(到付)
            receiveWay = 2;

        } else if (binding.rbPickupWayText.isChecked()) {
            binding.lnlLingqu.setVisibility(View.VISIBLE);
            binding.lnlLingquTime.setVisibility(View.VISIBLE);
            binding.lnlNotrayAddr.setVisibility(View.VISIBLE);
            binding.lnlAddress.setVisibility(View.GONE);
            binding.lnlPhone.setVisibility(View.GONE);
            binding.lnlRecipient.setVisibility(View.GONE);
            receiveWay = 1;
        }
        binding.rbEmailText.setOnClickListener(this);
        binding.rbPickupWayText.setOnClickListener(this);
        viewModel.listRequiredMaterial(selectNotary, recordId);
        takePhotoListener = this;
    }


    private void initSelfServiceStepBar() {
        selfServiceStepBar = (SelfServiceStepBar) getView().findViewById(R.id.self_service_step_bar);
        TextView tvFirstNumView = selfServiceStepBar.getTv_first_num();
        tvFirstNumView.setBackground(ResourcesCompat.getDrawable(getResources(), R.mipmap.icon_work_online, null));
        tvFirstNumView.setText("");
        TextView tvFirstView = selfServiceStepBar.getTv_first();
        tvFirstView.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color3333));
        selfServiceStepBar.setTv_first_num(tvFirstNumView);
        selfServiceStepBar.setTv_first(tvFirstView);

        TextView tvSecondNum = selfServiceStepBar.getTv_second_num();
        tvSecondNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.shape_circle_3c6af4, null));
        tvSecondNum.setText("2");
        TextView tvSecond = selfServiceStepBar.getTv_second();
        tvSecond.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color3333));
        selfServiceStepBar.setTv_second_num(tvSecondNum);
        selfServiceStepBar.setTv_second(tvSecond);

        TextView tvThreeNum = selfServiceStepBar.getTv_three_num();
        tvThreeNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.shape_circle_cccccc, null));
        tvThreeNum.setText("3");
        TextView tvThree = selfServiceStepBar.getTv_three();
        tvThree.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color_A5A5A5));
        selfServiceStepBar.setTv_three_num(tvThreeNum);
        selfServiceStepBar.setTv_three(tvThree);

        TextView tvFourNum = selfServiceStepBar.getTv_four_num();
        tvFourNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.shape_circle_cccccc, null));
        tvFourNum.setText("4");
        TextView tvFour = selfServiceStepBar.getTv_four();
        tvFour.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color_A5A5A5));
        selfServiceStepBar.setTv_four_num(tvFourNum);
        selfServiceStepBar.setTv_four(tvFour);
    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();
        //查询接口获取到的邮寄地址等信息
        viewModel.postInfoEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object o) {
                if (o != null) {
                    ListRequiredMaterialResponse.PostInfoDTO postInfoDTO = (ListRequiredMaterialResponse.PostInfoDTO) o;
                    if (postInfoDTO != null) {
                        if (postInfoDTO.getReceiveWay() == 1) {
                            //领取方式 1-自取，2-邮寄(到付)
                            binding.rbPickupWayText.setChecked(true);
                            receiveWay=1;
                            binding.lnlLingqu.setVisibility(View.VISIBLE);
                            binding.lnlLingquTime.setVisibility(View.VISIBLE);
                            binding.lnlNotrayAddr.setVisibility(View.VISIBLE);
                            binding.lnlAddress.setVisibility(View.GONE);
                            binding.lnlPhone.setVisibility(View.GONE);
                            binding.lnlRecipient.setVisibility(View.GONE);
                            binding.selfServiceAddress.setText(postInfoDTO.getNotarialAddress());
                            binding.selfServicePickupTime.setText("周一至周五 " + postInfoDTO.getBusiTime());
                            binding.selfServicePickUpAddress.setText(postInfoDTO.getNotarialName());
                        } else {
                            binding.rbEmailText.setChecked(true);
                            receiveWay=2;
                            binding.lnlLingqu.setVisibility(View.GONE);
                            binding.lnlLingquTime.setVisibility(View.GONE);
                            binding.lnlNotrayAddr.setVisibility(View.GONE);
                            binding.lnlAddress.setVisibility(View.VISIBLE);
                            binding.lnlPhone.setVisibility(View.VISIBLE);
                            binding.lnlRecipient.setVisibility(View.VISIBLE);
                            binding.edtPhone.setText(postInfoDTO.getPhoneNum());
                            binding.edtPostAddress.setText(postInfoDTO.getPostAddress());
                            binding.edtRecipient.setText(postInfoDTO.getConsigneeName());
                        }

                    }
                }
            }
        });

        viewModel.lastStepEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object o) {
                ((SelfNotarizationActivity) getActivity()).viewPager.setCurrentItem(0, false);
            }
        });

        //点击下一步
        viewModel.nextStepEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object o) {
                List<UpdateSelfServiceMaterialRequest.MaterialInfoListDTO> materialInfoListDTOS = new ArrayList<>();
                for (FragmentSelfServiceMaterialToUploadGridViewModel tmpParent : viewModel.observableList) {
                    for (FragmentSelfServiceMaterialToUploadGridItemViewModel tmp : tmpParent.childObservableList) {
                        if (tmp != null && tmp.entity.get() != null && !TextUtils.isEmpty(tmp.entity.get().getFileId())) {
                            UpdateSelfServiceMaterialRequest.MaterialInfoListDTO materialInfoListDTO = new UpdateSelfServiceMaterialRequest.MaterialInfoListDTO();
                            materialInfoListDTO.setMaterialTypeId(tmp.entity.get().getMaterialTypeId());
                            materialInfoListDTO.setFileId(tmp.entity.get().getFileId());
                            materialInfoListDTOS.add(materialInfoListDTO);
                        }
                    }
                }
                viewModel.updateSelfServiceMaterial(selectNotary, receiveWay, materialInfoListDTOS);
            }
        });

        viewModel.childItemClickEvent.observe(this, new Observer<Map<String, Object>>() {
            @Override
            public void onChanged(Map<String, Object> map) {
                int flag = Integer.parseInt(map.get("flag").toString());
                currentParentPos = map.get("parent_pos") != null ? Integer.parseInt(map.get("parent_pos").toString()) : 0;
                ListRequiredMaterialResponse.MaterialVoListDTO materialVoListDTO = (ListRequiredMaterialResponse.MaterialVoListDTO) map.get("value");
                switch (flag) {
                    case 1001:
                        Intent intent = new Intent(getActivity(), SelfTakePhotoActivity.class);
                        intent.putExtra("materialListCount", ((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.size() - 1);
                        startActivity(intent);
                        break;
                    case 1002:
                        //图片放大
                        if (materialVoListDTO == null) {
                            return;
                        }
                        List<String> list = new ArrayList<>();
                        list.add(materialVoListDTO.getFileUrl());
                        ImagePreviewDialog imagePreviewDialog = new ImagePreviewDialog(getActivity(), list);
                        if (!imagePreviewDialog.isShowing()) {
                            imagePreviewDialog.show();
                        }
                        break;
                    case 1003:
                        //图片删除
                        if (materialVoListDTO == null) {
                            return;
                        }
                        for (FragmentSelfServiceMaterialToUploadGridItemViewModel tmp : ((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList) {
                            if (tmp != null && tmp.entity.get() != null && !TextUtils.isEmpty(tmp.entity.get().getFileId()) && tmp.entity.get().getFileId().equals(materialVoListDTO.getFileId())) {
                                ((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.remove(tmp);
                                break;
                            }
                        }
                        boolean isHasDelete = false;
                        for (FragmentSelfServiceMaterialToUploadGridItemViewModel itemViewModel : ((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList) {
                            if (!itemViewModel.entity.get().isShowDel()) {
                                isHasDelete = true;
                            }
                        }
                        if (!isHasDelete) {
                            ListRequiredMaterialResponse.MaterialVoListDTO add = new ListRequiredMaterialResponse.MaterialVoListDTO();
                            add.setShowDel(false);
                            add.setFileUrl("");
                            ((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.add(new FragmentSelfServiceMaterialToUploadGridItemViewModel(((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)), add));
                        }
                        break;
                    case 1004:
                        //自助更新材料成功后
                        ((SelfNotarizationActivity) getActivity()).viewPager.setCurrentItem(2, false);
                        break;
                }
            }
        });

    }

    public static SelfTakePhotoResultListener takePhotoListener;

    @Override
    public void result(List<UploadFileBean> pictureInfoBeans) {
        //拿到拍照后的地址回调
        try {
            for (UploadFileBean uploadFileBean : pictureInfoBeans) {
                ListRequiredMaterialResponse.MaterialVoListDTO materialVoListDTO = new ListRequiredMaterialResponse.MaterialVoListDTO();
                materialVoListDTO.setShowDel(true);
                materialVoListDTO.setFileUrl(uploadFileBean.getSharedUrl());
                materialVoListDTO.setFileId(uploadFileBean.getId());
                materialVoListDTO.setMaterialTypeId(viewModel.parentList.get(currentParentPos).getMaterialTypeId());
                FragmentSelfServiceMaterialToUploadGridItemViewModel childObservableList = new FragmentSelfServiceMaterialToUploadGridItemViewModel(((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)), materialVoListDTO);
//            if (TextUtils.isEmpty(((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.get(((FragmentSelfServiceMaterialToUploadGridViewModel)
//                    viewModel.observableList.get(currentParentPos)).childObservableList.size() - 1).entity.get().getFileUrl())) {
//                ((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.remove(((FragmentSelfServiceMaterialToUploadGridViewModel)
//                        viewModel.observableList.get(currentParentPos)).childObservableList.size() - 1);
//            }
//            ((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.add(childObservableList);
//            if (((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.size() < 30) {
//                ListRequiredMaterialResponse.MaterialVoListDTO add = new ListRequiredMaterialResponse.MaterialVoListDTO();
//                add.setShowDel(false);
//                add.setFileUrl("");
//                ((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.add(new FragmentSelfServiceMaterialToUploadGridItemViewModel(viewModel, add));
//            }

                ((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.add(0, childObservableList);
                if (((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.size() > MAX_SELECT_NUMBER) {
                    ((FragmentSelfServiceMaterialToUploadGridViewModel) viewModel.observableList.get(currentParentPos)).childObservableList.remove(((FragmentSelfServiceMaterialToUploadGridViewModel)
                            viewModel.observableList.get(currentParentPos)).childObservableList.size() - 1);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            MobclickAgent.reportError(getActivity(), e);
        }


    }

    @Override
    public void onClick(View view) {
        if (view.getId() == binding.rbPickupWayText.getId()) {
            binding.lnlLingqu.setVisibility(View.VISIBLE);
            binding.lnlLingquTime.setVisibility(View.VISIBLE);
            binding.lnlNotrayAddr.setVisibility(View.VISIBLE);
            binding.lnlAddress.setVisibility(View.GONE);
            binding.lnlPhone.setVisibility(View.GONE);
            binding.lnlRecipient.setVisibility(View.GONE);
            receiveWay = 1;
        } else if (view.getId() == binding.rbEmailText.getId()) {
            binding.lnlLingqu.setVisibility(View.GONE);
            binding.lnlLingquTime.setVisibility(View.GONE);
            binding.lnlNotrayAddr.setVisibility(View.GONE);
            binding.lnlAddress.setVisibility(View.VISIBLE);
            binding.lnlPhone.setVisibility(View.VISIBLE);
            binding.lnlRecipient.setVisibility(View.VISIBLE);
            receiveWay = 2;
        }
    }
}
