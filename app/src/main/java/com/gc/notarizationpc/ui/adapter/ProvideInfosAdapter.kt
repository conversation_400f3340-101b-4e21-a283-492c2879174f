package com.gc.notarizationpc.ui.adapter

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.gc.notarizationpc.R
import com.gc.notarizationpc.model.ProviceBean

/**
 * 自定义布局，网络图片
 */
class ProvideInfosAdapter(val mContext: Context) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    public var mCallBack: CallBack? = null;
    public var itemsList: List<ProviceBean>? = null;

    inner class ItemsViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
        val realName: TextView = view.findViewById(R.id.realName);
        val cardType: TextView = view.findViewById(R.id.cardType);
        val cardId: TextView = view.findViewById(R.id.cardNumber);
        val sexs: TextView = view.findViewById(R.id.sexs);
        val birthday: TextView = view.findViewById(R.id.birthday);
        val type: TextView = view.findViewById(R.id.tv_type)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val leftView = LayoutInflater.from(parent.context).inflate(R.layout.provide_infos_items, parent, false)
        return ItemsViewHolder(leftView)
    }

    override fun getItemCount(): Int {
        if (itemsList != null && itemsList?.size!! > 0) {
            return itemsList?.size!!
        } else {
            return 0
        }

    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val textItem = itemsList?.get(position);
        when (holder) {
            is ItemsViewHolder -> {
                holder.realName.text = textItem?.cardName;
                holder.cardType.text = textItem?.cardType;
                holder.cardId.text = textItem?.cardId;
                holder.sexs.text = textItem?.sexs;
                holder.birthday.text = textItem?.birthday;
                if (position == 0) {
                    //标题栏不显示这条
                    holder.type.visibility = View.INVISIBLE
                } else {
                    holder.type.visibility = View.VISIBLE
                    if (textItem?.isDaiBan == 0) {
                        holder.type.text = "办理人"
                    } else if (textItem?.isDaiBan == 1) {
                        holder.type.text = "代理人"
                    }
                }
                Log.e("Test", "申请信息执行了几遍" + itemsList?.size)
                Log.e("Test", "textItem?.isDaiBan" + textItem?.isDaiBan)

                holder.birthday.setOnClickListener {
                    if (textItem != null) {
                        mCallBack?.callBack(textItem, position)
                    };
                }
            };
        }
    }

    interface CallBack {
        fun callBack(entity: ProviceBean, position: Int);
    }

}