package com.gc.notarizationpc.ui.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.gc.mininotarization.R;
import com.lcodecore.tkrefreshlayout.IBottomView;

public class CustomerFooter extends FrameLayout implements IBottomView {

    private int rotationSrc = R.mipmap.loading;
    private TextView footerTitle;
    private ProgressBar footerProgressbar;
    private boolean isLoading;

    private boolean mIsNoMoreData = false;

    public CustomerFooter(Context context, boolean isNoMoreData) {
        this(context,null,0);
        mIsNoMoreData = isNoMoreData;
    }

    public CustomerFooter(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        View view = View.inflate(getContext(),R.layout.footer_customer,  null);
        footerTitle = view.findViewById(R.id.custom_footer_title);
        footerProgressbar = view.findViewById(R.id.custom_footer_progress);
        footerProgressbar.setIndeterminateDrawable(ContextCompat.getDrawable(getContext(), rotationSrc));
        addView(view);
    }


    @Override
    public View getView() {
        return this;
    }

    @Override
    public void onPullingUp(float fraction, float maxBottomHeight, float bottomHeight) {

        if (mIsNoMoreData) {
            footerTitle.setText(R.string.no_more_data);
            footerProgressbar.setVisibility(View.INVISIBLE);
        }else{
            footerTitle.setText(R.string.load_more_data);
            if(footerTitle.getVisibility() != VISIBLE){
                footerTitle.setVisibility(VISIBLE);
                footerProgressbar.setVisibility(View.INVISIBLE);
            }
        }
    }

    @Override
    public void startAnim(float maxBottomHeight, float bottomHeight) {
        footerTitle.setVisibility(View.INVISIBLE);
        footerProgressbar.setVisibility(View.VISIBLE);
        isLoading = true;
    }

    @Override
    public void onPullReleasing(float fraction, float maxBottomHeight, float bottomHeight) {

    }

    @Override
    public void onFinish() {
        footerTitle.setText(R.string.load_more_data);
        footerTitle.setVisibility(View.VISIBLE);
        footerProgressbar.setVisibility(View.INVISIBLE);
        isLoading =false;
    }

    @Override
    public void reset() {
        footerTitle.setText(R.string.load_more_data);
        footerTitle.setVisibility(View.VISIBLE);
        footerProgressbar.setVisibility(View.INVISIBLE);
        isLoading =false;
    }
}
