package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.CfgResponse;
import com.gc.notarizationpc.data.model.response.LoginResponse;
import com.gc.notarizationpc.data.model.response.UpgradeResponse;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.util.MacUtils;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.CommonUtil;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * 首页快捷入口 viewmodel
 */

public class HomeViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<Boolean> macAddressRegisterFail = new SingleLiveEvent<Boolean>();
    public SingleLiveEvent<String> getCfgFail = new SingleLiveEvent<String>();
    public SingleLiveEvent<String> linkToWebView = new SingleLiveEvent<String>();
    public SingleLiveEvent<UpgradeResponse> upgradeResponse = new SingleLiveEvent<UpgradeResponse>();
    public SingleLiveEvent<Boolean> getPublicKeySuccess = new SingleLiveEvent<Boolean>();

    public class UIChangeObservable {

    }

    public HomeViewModel(@NonNull Application application) {
        super(application);
    }

    //给RecyclerView添加ObservableList
    public ObservableList<HomeQuickListItemViewModel> observableList = new ObservableArrayList<>();
    //给RecyclerView添加ItemBinding
    public ItemBinding<HomeQuickListItemViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.item_home_quick_enter);//这里指定了item的布局

    /**
     * 根据mac地址登录
     */
    public void loginByMac(String macAddress, Context context) {
        RequestUtil.loginByMac(macAddress, new MyObserver<LoginResponse>() {
            @Override
            public void onSuccess(LoginResponse result) {
                Log.i("loginByMac", "onSuccess: userId-----" + result.getUserId());
                if (result != null) {
                    SPUtils.getInstance().put("access_token", result.getAccessToken());
                    SPUtils.getInstance().put("token_type", result.getTokenType());
                    SPUtils.getInstance().put("userId", result.getUserId());
                    IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(context, "idcardinfo");
                    if (idCardUserInfo != null) {
                        idCardUserInfo.setSysUserId(result.getUserId());
                        idCardUserInfo.setUserId(result.getUserId());
                    } else {
                        idCardUserInfo = new IdCardUserInfo();
                        idCardUserInfo.setSysUserId(result.getUserId());
                        idCardUserInfo.setUserId(result.getUserId());
                    }
                    SPUtils.getInstance().saveObject(context, "idcardinfo", idCardUserInfo);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                macAddressRegisterFail.setValue(false);
            }
        });
    }

    //获取系统配置
    public void getCfg(String macAddress) {
        showDialog();
        observableList.clear();
        Map<String, String> param = new HashMap<>();
        param.put("macAddress", macAddress);
        RequestUtil.getCfg(param, new MyObserver<CfgResponse>() {
            @Override
            public void onSuccess(CfgResponse result) {
                dismissDialog();
                if (result != null) {
                    for (CfgResponse.FunctionVoListDTO functionVoListDTO : result.getFunctionVoList()) {
                        HomeQuickListItemViewModel itemViewModel = new HomeQuickListItemViewModel(HomeViewModel.this, functionVoListDTO);
                        observableList.add(itemViewModel);
                    }

                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                getCfgFail.setValue(errorMsg);
            }
        });

    }

    //获取系统配置
    public void upgradeApp(String macAddress, String currentVersion) {
        showDialog();
        Map<String, String> param = new HashMap<>();
        param.put("macAddress", macAddress);
        param.put("uniqueIdentity", "QH_asd123");
        RequestUtil.upgradeApp(param, new MyObserver<UpgradeResponse>() {
            @Override
            public void onSuccess(UpgradeResponse result) {
                dismissDialog();
                if (result != null) {
                    try {
                        String latestVersion = result.getThisTimeVersion();
                        if (com.gc.notarizationpc.util.CommonUtil.compareVersion(latestVersion, currentVersion) > 0) {
                            Log.d("Test", "更新检测：showDialogUpdate");
                            upgradeResponse.setValue(result);
                        }
                    } catch (Exception ex) {
                        toastError(Utils.getContext().getString(R.string.autoUpdate) + ":" + ex.getMessage());
                    }

                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                getCfgFail.setValue(errorMsg);
            }
        });

    }




    public void goToWebview(String url) {
        linkToWebView.setValue(url);
    }


    /**
     * 获取条目下标
     *
     * @param netWorkItemViewModel
     * @return
     */
    public int getItemPosition(HomeQuickListItemViewModel netWorkItemViewModel) {
        return observableList.indexOf(netWorkItemViewModel);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
