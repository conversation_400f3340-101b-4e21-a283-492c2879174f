package com.gc.notarizationpc.ui

import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import com.example.framwork.utils.MyLogUtils
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseActivity
import com.gc.notarizationpc.model.UserBean
import com.gc.notarizationpc.ui.presenter.AccountPresenter
import kotlinx.android.synthetic.main.activity_mobile.*
import java.lang.reflect.Method

class RemoteModelActivity : BaseActivity(), AccountPresenter.IMobileView {

    private var TAG = "RemoteModelActivity"
    private var codeP: AccountPresenter? = null
    override fun getIntentData(intent: Intent?) {
        codeP = AccountPresenter(mActivity, this)
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_remote_model
    }

    override fun initViewsAndEvents() {
        Log.i(TAG, "initViewsAndEvents")
//        disableShowSoftInput()
        editMobile.setOnEditorActionListener { _, actionId, _ ->
//            if (actionId == EditorInfo.IME_ACTION_UNSPECIFIED) {
//                codeP?.sendCode()
//            }
            false
        }
        editCode.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_UNSPECIFIED) {
                codeP?.authCode(userInfo, false)
            }
            false
        }
    }

    fun sendCodeClick(view: View?) {
        Log.i(TAG, "sendCodeClick")
//        editCode.requestFocus()
        if (TextUtils.isEmpty(editMobile.text.toString())) {
            toastInfo("请先输入手机号")
        } else {
            codeP?.sendCaptchaCode(editMobile.text.toString())
        }
    }

    fun submitClick(view: View?) {
        Log.i(TAG, "submitClick")
        if (editCode.text.toString() == "000000") {
            userInfo?.mobile = editMobile.text.toString()
            codeP?.register(userInfo, false)
        } else {
            userInfo?.mobile = editMobile.text.toString()
            codeP?.authCode(userInfo, false)
        }

    }

    /**
     * 禁止Edittext弹出软件盘，光标依然正常显示。
     */
    private fun disableShowSoftInput() {
        Log.i(TAG, "disableShowSoftInput")
        val cls = EditText::class.java
        var method: Method
        try {
            method = cls.getMethod("setShowSoftInputOnFocus", Boolean::class.javaPrimitiveType)
            method.isAccessible = true
            method.invoke(editMobile, false)
            method.invoke(editCode, false)
        } catch (e: Exception) {
        }
        try {
            method = cls.getMethod("setSoftInputShownOnFocus", Boolean::class.javaPrimitiveType)
            method.isAccessible = true
            method.invoke(editMobile, false)
            method.invoke(editCode, false)
        } catch (e: Exception) {
        }
    }

    override fun onDestroy() {
        Log.i(TAG, "onDestroy")
        buttonCode.stop()
        super.onDestroy()
    }

    override fun getMobile(): String {
        Log.i(TAG, "getMobile")
        if (TextUtils.isEmpty(editMobile.text.trim()) || editMobile.text.length < 11) {
            toastError("请输入有效手机号")
            return ""
        }
        userInfo?.mobile = editMobile.text.trim().toString()
        return userInfo?.mobile.toString()
    }

    override fun getSmsCode(): String {
        Log.i(TAG, "getSmsCode")
        if (TextUtils.isEmpty(editCode.text.trim())) {
            toastError("请输入验证码")
            return ""
        }
        return editCode.text.trim().toString()
    }

    override fun sendSuccess() {
        Log.i(TAG, "sendSuccess")
        buttonCode.start()
        toastSuccess("验证码已发送，请注意查收")
    }

    override fun goModelSelectActivity(userBean: UserBean) {
        Log.i(TAG, "goModelSelectActivity")
        userInfo?.userId = userBean.userInfo.unitGuid
        mApplication?.userInfo = userInfo
        val intent = Intent(this, RemoteEmpowermentActivity::class.java)
        intent.putExtra("id", userBean.userInfo.unitGuid)
        intent.putExtra("name", userInfo?.name)
        intent.putExtra("idcard", userInfo?.idCard)
        startActivity(intent)
    }
}