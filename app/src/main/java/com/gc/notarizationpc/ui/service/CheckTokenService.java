package com.gc.notarizationpc.ui.service;

import android.app.ActivityManager;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;

import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.common.NotarizationBean;
import com.gc.notarizationpc.common.RemoteBean;
import com.gc.notarizationpc.common.RemotesBean;
import com.gc.notarizationpc.model.NotaryEntity;
import com.gc.notarizationpc.ui.presenter.MainPresenter;
import com.gc.notarizationpc.ui.presenter.NotarizaSelfPresenter;
import com.gc.notarizationpc.ui.presenter.RemoteEmpowermentPresenter;
import com.gc.notarizationpc.utils.MacUtils;

import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class CheckTokenService extends Service implements MainPresenter.IHomeView, NotarizaSelfPresenter.INotarizaSelf {
    private final String TAG = "CheckTokenService";
    private Timer checkTimer = null;
    private long checkTime = 30 * 60 * 1000;
    private MainPresenter queryP = null;
    private NotarizaSelfPresenter queryR = null;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Log.d(TAG, "onBind");
        return null;
    }

    @Override
    public void onCreate() {
        Log.d(TAG, "onCreate");
        super.onCreate();
        if (isForeground(CheckTokenService.this, "MainActivity")) {
            startchecktoken();
        } else {
            if (checkTimer != null) {
                checkTimer.cancel();
                checkTimer = null;
            }
            stopSelf();
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "onStartCommand");
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public boolean onUnbind(Intent intent) {
        Log.d(TAG, "onUnbind");
        return super.onUnbind(intent);
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "onDestroy");
        if (checkTimer != null) {
            checkTimer.cancel();
            checkTimer = null;
        }
        stopSelf();
        super.onDestroy();
    }

    public void startchecktoken() {
        String mac = MacUtils.getMacAddress(this);
        if (queryP == null)
            queryP = new MainPresenter(CheckTokenService.this, this);
        if (queryR == null)
            queryR = new NotarizaSelfPresenter(CheckTokenService.this, this);
        if (checkTimer == null) {
            checkTimer = new Timer();
            checkTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    //半小时刷新一次token
                    queryP.getToken(mac);
                    //注册token使用后半小时刷新一次
                    if (!TextUtils.isEmpty(AppConfig.REGISTERTOKEN)) {
                        queryR.refreshRegistToken();
                    }
                }
            }, checkTime, checkTime);
        }
    }

    @Override
    public String getMacAddress() {
        return null;
    }

    @Override
    public void showNotary(String notaryId, boolean success) {

    }

    @Override
    public void showNotaryList(NotarizationBean bean) {

    }

    @Override
    public void getAccessPublicKeySuccess() {

    }

    @Override
    public void getAccessPublicKeyFail(String error) {

    }

    @Override
    public void mechineAddSuccess() {

    }

    @Override
    public void mechineAddFail() {

    }

    /**
     * 判断某个界面是否在前台
     *
     * @param context   Context
     * @param className 界面的类名
     * @return 是否在前台显示
     */

    public static boolean isForeground(Context context, String className) {
        if (context == null || TextUtils.isEmpty(className))
            return false;
        try {
            ActivityManager am = (ActivityManager) context.getSystemService(ACTIVITY_SERVICE);
            List list = am.getRunningTasks(1);
            ActivityManager.RunningTaskInfo taskInfo = (ActivityManager.RunningTaskInfo) list.get(0);
            if (taskInfo.topActivity.getShortClassName().contains(className)) { // 说明它已经启动了
                return true;
            }
        } catch (Exception E) {
            Log.d("CheckTokenService", E.getMessage());
            return false;
        }
        return false;
    }

    @Override
    public void queryNotaryEntitySuccess(NotaryEntity bean) {

    }

    @Override
    public void getPayConfigViewSuccess(String msg) {

    }

    @Override
    public void getPayConfigViewFail(String msg) {

    }
}
