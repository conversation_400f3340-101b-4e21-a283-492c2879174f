package com.gc.notarizationpc.ui.adapter;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.MenuInfo;

import me.goldze.mvvmhabit.base.BaseAdapter;
import me.goldze.mvvmhabit.base.BaseViewHolder;

//受理室缴费信息父布局
public class FeeParentAdapter extends BaseAdapter<MenuInfo> {

    public FeeParentAdapter(Context context, int itemLayoutRes, @Nullable Object type) {
        super(context, itemLayoutRes, type);
        mContext = context;
    }

    @Override
    public void bind(@NonNull BaseViewHolder viewHolder, MenuInfo itemData, int position) {
        if (itemData == null) {
            return;
        }
        viewHolder.setText(R.id.tv_name, itemData.getKey());
        viewHolder.setText(R.id.tv_value, itemData.getName());
        if (!TextUtils.isEmpty(itemData.getKey()) && itemData.getKey().contains("减免")) {
            ((TextView) viewHolder.getView(R.id.tv_value)).setTextColor(Color.parseColor("#E1594D"));
        } else {
            ((TextView) viewHolder.getView(R.id.tv_value)).setTextColor(Color.parseColor("#333333"));
        }
        RecyclerView rvFeeChild = viewHolder.getView(R.id.rv_fee_child);
        rvFeeChild.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });

//        if (itemData.getChild() != null) {
            FeeChildAdapter feeChildAdapter = new FeeChildAdapter(mContext, R.layout.item_list_fee_child, 0);
            rvFeeChild.setAdapter(feeChildAdapter);
            feeChildAdapter.refreshAdapter(itemData.getChild());
//        }

    }

    @Override
    public int viewType(MenuInfo itemData) {
        return 0;
    }

    public interface Onclick {
        void OnClicklistener(int position, int flag);
    }

    Onclick mOnclick;

    public void setOnClick(Onclick onClick) {
        this.mOnclick = onClick;
    }

    public class FeeChildAdapter extends BaseAdapter<MenuInfo> {

        public FeeChildAdapter(Context context, int itemLayoutRes, @Nullable Object type) {
            super(context, itemLayoutRes, type);
            mContext = context;
        }

        @Override
        public void bind(@NonNull BaseViewHolder viewHolder, MenuInfo itemData, int position) {
            if (itemData == null) {
                return;
            }
            viewHolder.setText(R.id.tv_child_key, itemData.getKey());
            viewHolder.setText(R.id.tv_child_value, itemData.getName());
            if (position == mDataList.size() - 1) {
                viewHolder.getView(R.id.divider).setVisibility(View.GONE);
            } else {
                viewHolder.getView(R.id.divider).setVisibility(View.VISIBLE);
            }
        }

        @Override
        public int viewType(MenuInfo itemData) {
            return 0;
        }
    }
}
