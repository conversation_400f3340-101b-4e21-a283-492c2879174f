package com.gc.notarizationpc.ui;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Typeface;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.example.framwork.baseapp.AppManager;
import com.example.framwork.noHttp.CallServer;
import com.example.framwork.utils.ToastUtils;
import com.gc.notarizationpc.R;
import com.gc.notarizationpc.common.BaseActivity;
import com.gc.notarizationpc.model.DataDTO;
import com.gc.notarizationpc.model.PayBean;
import com.gc.notarizationpc.ui.presenter.NotarizaSelfPresenter;
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter;
import com.gc.notarizationpc.utils.BitErCodeUtils;
import com.gc.notarizationpc.utils.MyOnclickClickListener;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Timer;
import java.util.TimerTask;

import es.dmoral.toasty.Toasty;

public class PayActivity extends BaseActivity implements NotarizationPresenter.JHPayView, NotarizaSelfPresenter.OrderQueryCommonView {
    private boolean isCommonPay = true;
    private TextView payNumber;//支付数额
    private RadioGroup radioGroup;
    private RadioButton commonPayType;//传统支付
    private RadioButton numberPayType;//数字支付
    private TextView weixin;//微信支付
    private TextView zhifubao;//支付宝支付
    private TextView yinhangka;//银行卡支付
    private TextView tvOperate;//确认支付
    private TextView payCancel;//取消支付
    private LinearLayout commonPayLayout;//传统支付模块布局
    private LinearLayout numberPayLyaout;//数币支付模块布局
    private ImageView ivQrPay;
    private NotarizationPresenter presenter;
    private String unitGuid = "";
    private String money = "";
    private int intoType;//1自助公证进入 2视频公证进入  默认2
    private Timer mTimer = null;
    private boolean isPaySuccess = false;
    private String orderNo = "";//订单 编号
    private int polling_time = 2 * 60;//轮询时长
    private Handler handler = new Handler();
    private ImageView ivQrBg;
    private NotarizationPresenter notarizationPresenter;
    private NotarizaSelfPresenter commonQueryPresenter;

    @Override
    protected void getIntentData(Intent intent) {
        unitGuid = intent.getStringExtra("unitGuid");
        money = intent.getStringExtra("money");
        intoType = intent.getIntExtra("intoType", 2);
        orderNo = intent.getStringExtra("orderNo");
    }

    @Override
    protected int getContentViewLayoutID() {
        return R.layout.pay_layout;
    }

    @Override
    protected void initViewsAndEvents() {
        notarizationPresenter = new NotarizationPresenter(PayActivity.this);
        commonQueryPresenter =new NotarizaSelfPresenter(this,this);
        ivQrPay = findViewById(R.id.qr_pay);
        ivQrBg = findViewById(R.id.qr_bg);
        tvOperate = findViewById(R.id.tv_operate);
        commonPayType = findViewById(R.id.rb_common);
        numberPayType = findViewById(R.id.rb_number);

        payNumber = findViewById(R.id.payNumber);

        payCancel = findViewById(R.id.payCancel);
        presenter = new NotarizationPresenter(this, this);

        payNumber.setText("¥ " + money);
        commonPay();
        commonPayType.setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                if (!isCommonPay) {
                    if (timeoutPayTimer != null) {
                        Log.e("Test", "点击了普通支付，cancel定时器");
                        timeoutPayTimer.cancel();
                        timeoutPayTimer = null;
                    }
                    commonPayType.setChecked(true);
                    commonPay();
                    CallServer.getRequestInstance().cancelBySign(this);
                    //调用下单接口
                    isCommonPay = true;
                }
            }
        });
        numberPayType.setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                if (isCommonPay) {
                    if (timer != null) {
                        Log.e("Test", "点击了数币支付，cancel定时器");
                        timer.cancel();
                        timer = null;
                    }
                    numberPayType.setChecked(true);
                    CallServer.getRequestInstance().cancelBySign(this);
                    numberPay();
                    isCommonPay = false;
                }
            }
        });

        findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finishActivity();
            }
        });

        payCancel.setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                // 取消支付  ,返回到上一个页面，，1为自助公证
                finishActivity();
            }
        });


        //点击操作按钮
        tvOperate.setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                if (isCommonPay) {
                    commonPay();
                } else {
                    numberPay();
                }
            }
        });
    }

    String bnkPyOrdrNo = "";//普通支付银行支付订单号
    String cstmPyOrdrNo = "";//客户方支付订单号

    private void commonPay() {
        presenter.placeOrderComm(unitGuid);
    }

    private void finishActivity() {

        if (timeoutPayTimer != null) {
            timeoutPayTimer.cancel();
            timeoutPayTimer = null;
        }
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        finish();
    }

    private Timer timeoutPayTimer;//数币定时器

    private Timer timer = new Timer();//普通定时器

    private void startTimerCommonPay() {
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                commonQueryPresenter.queyCommonOrderInfo(bnkPyOrdrNo, cstmPyOrdrNo);
            }
        };
        if (timer == null) {
            timer = new Timer();
        }
        timer.schedule(timerTask, 3000, 3000);
    }


    /**
     * 开启轮询查询订单信息
     * 300s 内未查到支付结果，显示支付超时提示，点击重新支付按钮，重新生成支付二维码
     */
    private void startPayTimeOutCountDownTime() {
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                notarizationPresenter.queyCcbisOrderInfo(orderNo, new NotarizationPresenter.OrderCommonView() {
                    @Override
                    public void orderCommonSuccess(PayBean bean) {
                        if (bean == null || TextUtils.isEmpty(bean.getPayStatus())) {
//                            if (timeoutPayTimer != null) {
//                                timeoutPayTimer.cancel();
//                                timeoutPayTimer = null;
//                            }
                            return;
                        }
                        if (TextUtils.equals(bean.getPayStatus(), "1") || TextUtils.equals(bean.getPayStatus(), "10")) {
                            isPaySuccess = true;
                            afterPay(bean.getPayStatus(), bean.getPayMessage());

                        } else if (TextUtils.equals(bean.getPayStatus(), "3")) {
                            //	1、成功 2、未支付 3、支付超时 4、支付失败
                            isPaySuccess = false;
                            afterPay(bean.getPayStatus(), bean.getPayMessage());

                        } else if (TextUtils.equals(bean.getPayStatus(), "4")) {
                            //	1、成功 2、未支付 3、支付超时 4、支付失败
                            isPaySuccess = false;
                            afterPay(bean.getPayStatus(), bean.getPayMessage());

                        }

                    }

                    @Override
                    public void orderCommonFail(String msg) {
                        if (!TextUtils.isEmpty(msg))
                            Toasty.error(AppManager.getAppManager().currentActivity(), msg).show();
                    }
                });

            }
        };
        if (timeoutPayTimer == null) {
            timeoutPayTimer = new Timer();
        }
        timeoutPayTimer.schedule(timerTask, 3000, 3000);

    }


    String payUrl = "";

    /**
     * 数币支付 流程
     */
    private void numberPay() {
        presenter.ccBisPlaceOrder(unitGuid);
    }

    private void afterPay(String status, String msg) {
        if (timeoutPayTimer != null) {
            Log.e("Test", "timeoutPayTimer.cancel()");
            timeoutPayTimer.cancel();
            timeoutPayTimer = null;
        }
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        handler.post(new Runnable() {
            @Override
            public void run() {
                if (TextUtils.isEmpty(status)) {
                    return;
                }
                String hint = "";
                switch (status) {
                    case "1":
                        hint = "支付成功!";
                        tvOperate.setVisibility(View.GONE);
                        finishActivity();
                        readyGo(new Intent(PayActivity.this, OrderSucessActivity.class));
                        break;
                    case "3":
                        hint = "支付超时!";
                        tvOperate.setText("二维码已失效，请点击刷新");
                        break;
                    case "4":
                        hint = "支付失败!";
                        tvOperate.setText("支付异常，请点击刷新");
                        break;
                    case "10":
                        hint = msg;
                        finishActivity();
                        readyGo(new Intent(PayActivity.this, OrderSucessActivity.class));
                        break;
                }
                ToastUtils.getInstance(PayActivity.this).toastInfo(hint);
                ivQrPay.setVisibility(View.VISIBLE);
                ivQrBg.setVisibility(View.VISIBLE);
                tvOperate.setVisibility(View.VISIBLE);
            }
        });


    }

    /**
     * @param type 0传统支付 1数字支付
     */
    public void setPayDrawable(int type) {
        if (type == 0) {
            commonPayType.setCompoundDrawablesWithIntrinsicBounds(null, null, null, getResources().getDrawable(R.drawable.paybarbotomline));
            commonPayType.setTextColor(getResources().getColor(R.color.pay_type));
            commonPayType.setTypeface(null, Typeface.BOLD);

            numberPayType.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
            numberPayType.setTextColor(getResources().getColor(R.color.black));
            numberPayType.setTypeface(null, Typeface.NORMAL);
        } else {
            commonPayType.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
            commonPayType.setTextColor(getResources().getColor(R.color.black));
            commonPayType.setTypeface(null, Typeface.NORMAL);

            numberPayType.setCompoundDrawablesWithIntrinsicBounds(null, null, null, getResources().getDrawable(R.drawable.paybarbotomline));
            numberPayType.setTextColor(getResources().getColor(R.color.pay_type));
            numberPayType.setTypeface(null, Typeface.BOLD);
        }
    }

//    @Override
//    public boolean onKeyDown(int keyCode, @NonNull KeyEvent event) {
//        if (keyCode == KeyEvent.KEYCODE_BACK)
//            return true;
//        return super.onKeyDown(keyCode, event);
//    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (timeoutPayTimer != null) {
            timeoutPayTimer.cancel();
            timeoutPayTimer = null;
        }
        if (timer != null) {
            timer.cancel();
            timer = null;
        }

    }

    @Override
    public void commonSuccess(DataDTO dataDTO) {
        try {
            if (dataDTO == null || TextUtils.isEmpty(dataDTO.getPyUrl()) || TextUtils.isEmpty(dataDTO.getBnkPyOrdrNo()) || TextUtils.isEmpty(dataDTO.getCstmPyOrdrNo())) {
                ToastUtils.getInstance(PayActivity.this).toastError("订单生成失败，请重试!");
                ivQrPay.setVisibility(View.VISIBLE);
                ivQrBg.setVisibility(View.VISIBLE);
                tvOperate.setVisibility(View.VISIBLE);
                tvOperate.setText("支付异常，请点击刷新");
                return;
            }
            bnkPyOrdrNo = dataDTO.getBnkPyOrdrNo();
            cstmPyOrdrNo = dataDTO.getCstmPyOrdrNo();

            if (!TextUtils.isEmpty(dataDTO.getPyUrl())) {
                Bitmap bitmap = BitErCodeUtils.createQRImage(URLDecoder.decode(dataDTO.getPyUrl(), "UTF-8"));
                if (bitmap != null) {
                    ivQrPay.setVisibility(View.VISIBLE);
                    ivQrPay.setImageBitmap(bitmap);
                    tvOperate.setVisibility(View.GONE);
                    ivQrBg.setVisibility(View.GONE);
                    startTimerCommonPay();
                }
            } else {
                ToastUtils.getInstance(PayActivity.this).toastError("支付二维码生成失败,请重新下单");
                ivQrPay.setVisibility(View.VISIBLE);
                ivQrBg.setVisibility(View.VISIBLE);
                tvOperate.setVisibility(View.VISIBLE);
                tvOperate.setText("支付异常，请点击刷新");
            }

        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.getInstance(PayActivity.this).toastError("订单生成失败，请重试!");
            ivQrPay.setVisibility(View.VISIBLE);
            ivQrBg.setVisibility(View.VISIBLE);
            tvOperate.setVisibility(View.VISIBLE);
            tvOperate.setText("支付异常，请点击刷新");
            Log.e("Test", e.getMessage());
        }


    }

    @Override
    public void commonFail() {
        ToastUtils.getInstance(PayActivity.this).toastError("订单生成失败，请重试!");
        ivQrPay.setVisibility(View.VISIBLE);
        ivQrBg.setVisibility(View.VISIBLE);
        tvOperate.setVisibility(View.VISIBLE);
        tvOperate.setText("支付异常，请点击刷新");

    }

    @Override
    public void ccbisSuccess(DataDTO orderPayBean) {
        try {
            if (orderPayBean == null || orderPayBean.getPayUrl() == null) {
                ToastUtils.getInstance(mActivity).toastError("下单失败，请稍后再试");
                ivQrPay.setVisibility(View.VISIBLE);
                ivQrBg.setVisibility(View.VISIBLE);
                tvOperate.setVisibility(View.VISIBLE);
                tvOperate.setText("支付异常，请点击刷新");
                return;
            }
            payUrl = orderPayBean.getPayUrl();//支付h5地址
//                    linkOverTime = jsonObject.getString("linkOverTime");//剩余超时时间

            handler.post(new Runnable() {
                @Override
                public void run() {
                    Bitmap bitmap = null;
                    try {
                        bitmap = BitErCodeUtils.createQRImage(URLDecoder.decode(payUrl, "UTF-8"));
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }
                    if (bitmap != null) {
                        ivQrPay.setVisibility(View.VISIBLE);
                        ivQrPay.setImageBitmap(bitmap);
                        tvOperate.setVisibility(View.GONE);
                        ivQrBg.setVisibility(View.GONE);
                        startPayTimeOutCountDownTime();
                    } else {
                        ToastUtils.getInstance(mActivity).toastError("支付二维码生成失败,请重新下单");
                        ivQrPay.setVisibility(View.VISIBLE);
                        ivQrBg.setVisibility(View.VISIBLE);
                        tvOperate.setVisibility(View.VISIBLE);
                        tvOperate.setText("支付异常，请点击刷新");
                    }
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.getInstance(mActivity).toastError("下单失败，请稍后再试");
            ivQrPay.setVisibility(View.VISIBLE);
            ivQrBg.setVisibility(View.VISIBLE);
            tvOperate.setVisibility(View.VISIBLE);
            tvOperate.setText("支付异常，请点击刷新");
        }
    }


    @Override
    public void ccbisFail() {
        ToastUtils.getInstance(mActivity).toastError("下单失败，请稍后再试");
        ivQrPay.setVisibility(View.VISIBLE);
        ivQrBg.setVisibility(View.VISIBLE);
        tvOperate.setVisibility(View.VISIBLE);
        tvOperate.setText("支付异常，请点击刷新");

    }

    @Override
    public void orderCommonSuccess(PayBean bean) {
        {
            //1：待支付  2：成功  3：失败  4：不确定  5：未支付完成
            String payStatus = "4";
            if (bean == null || TextUtils.isEmpty(bean.getPayStatus())) {
//                            if (timer != null) {
//                                timer.cancel();
//                                timer = null;
//                            }
                return;
            }
            //将传统支付返回的status值转换为和数币一致
            switch (bean.getPayStatus()) {
                case "2":
                    payStatus = "1";
                    break;
                case "1":
                    payStatus = "2";
                    break;
                case "3":
                    payStatus = "4";
                    break;
                case "6":
                    payStatus = "3";
                    break;
                case "10":
                    payStatus = "10";
                    break;
            }
            if (TextUtils.equals(payStatus, "1") || TextUtils.equals(bean.getPayStatus(), "10")) {
                isPaySuccess = true;
                afterPay(payStatus, bean.getPayMessage());

            } else if (TextUtils.equals(payStatus, "3")) {
                //	1、成功 2、未支付 3、支付超时 4、支付失败
                isPaySuccess = false;
                afterPay(payStatus, bean.getPayMessage());

            } else if (TextUtils.equals(payStatus, "4")) {
                //	1、成功 2、未支付 3、支付超时 4、支付失败
                isPaySuccess = false;
                afterPay(payStatus, bean.getPayMessage());

            }
        }
    }

    @Override
    public void orderCommonFail(String msg) {
        if (!TextUtils.isEmpty(msg))
            Toasty.error(AppManager.getAppManager().currentActivity(), msg).show();
    }
}
