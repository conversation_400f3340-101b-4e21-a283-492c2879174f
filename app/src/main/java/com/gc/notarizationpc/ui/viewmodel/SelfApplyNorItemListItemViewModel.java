package com.gc.notarizationpc.ui.viewmodel;

import android.graphics.drawable.Drawable;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.NotaryMatterItem;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * 自助申办信息，公证事项
 */

public class SelfApplyNorItemListItemViewModel extends ItemViewModel<SelfApplyInfoViewModel> {
    public ObservableField<NotaryMatterItem.MattersInfosDTO> entity = new ObservableField<>();
    public Drawable drawableImg;

    public SelfApplyNorItemListItemViewModel(@NonNull SelfApplyInfoViewModel viewModel, NotaryMatterItem.MattersInfosDTO entity) {
        super(viewModel);
        this.entity.set(entity);
    }

    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
    public int getPosition() {
        return viewModel.getNorItemPosition(this);
    }

    //点击 +
    public BindingCommand plusClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.notaryItemOperateHandler("add", entity.get(), getPosition());
        }
    });

    //点击 -
    public BindingCommand subClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.notaryItemOperateHandler("sub", entity.get(), getPosition());
        }
    });

    //删除
    public BindingCommand delClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.notaryItemClickHandler("delete", entity.get(), getPosition());
        }
    });

    /**
     * 加公证书份数
     */
    public BindingCommand addNotarizationCopiesCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            if (!TextUtils.isEmpty(entity.get().getNotarizationNumber())) {
                int copies = Integer.parseInt(entity.get().getNotarizationNumber());
//                copies++;
                entity.get().setNotarizationNumber(copies + "");
                viewModel.notaryItemOperateHandler("input", entity.get(), getPosition());
            }

        }
    });


}
