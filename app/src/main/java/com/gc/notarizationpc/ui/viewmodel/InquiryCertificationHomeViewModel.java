package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * 首页快捷入口 viewmodel
 */

public class InquiryCertificationHomeViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    public class UIChangeObservable {

    }

    public InquiryCertificationHomeViewModel(@NonNull Application application) {
        super(application);
    }



    /**
     * 返回
     */
    public BindingCommand goBackEvent = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            finish();
        }
    });

    /**
     * 确定
     */
    public   BindingCommand decideEvent = new BindingCommand(new BindingAction(){
        @Override
        public void call() {
            finish();
        }
    });



}
