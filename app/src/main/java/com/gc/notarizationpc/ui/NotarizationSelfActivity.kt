package com.gc.notarizationpc.ui

import android.content.Intent
import android.util.Log
import android.view.View
import android.webkit.JavascriptInterface
import androidx.fragment.app.Fragment
import com.alibaba.fastjson.JSON
import com.example.framwork.utils.BitmapTool
import com.example.framwork.utils.DLog
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseActivity
import com.gc.notarizationpc.model.*
import com.gc.notarizationpc.ui.fragment.*
import com.gc.notarizationpc.ui.presenter.NotarizaSelfPresenter
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import java.util.*

/**
 * 自助公证
 */
class NotarizationSelfActivity : BaseActivity(), NotarizaSelfPresenter.INotarizaSelf, NotarizationPresenter.IVideoView {

    private var TAG = "NotarizationSelfActivity"
    private var oneFragment: SelfStepOneFragment? = null
    private var twoFragment: SelfStepTwoFragment? = null
    private var threeFragment: SelfStepThreeFragment? = null
    private var fourFragment: SelfStepFourFragment? = null
    private var fiveFragment: SelfStepFiveFragment? = null
    private var sixFragment: SelfStepSixFragment? = null
    private var noSomeFragment: SelfNoSomeFragment? = null;
    private var updateSignatureP: NotarizationPresenter? = null

    private val fragmentList: ArrayList<Fragment>? = ArrayList()
    private var notariPer: NotarizaSelfPresenter? = null;
    var choseList: ArrayList<NotrayBean.ChildrenDTO>? = ArrayList();
    var orderId: String? = "";

    var realNameCard: String? = "";
    var sexsCard: Int? = 1;
    var birthdayCard: String? = "";
    var cardNumberCard: String? = "";
    var mobilePhoneCard: String? = "";
    var addressCard: String? = "";
    var notariNameCard: String? = "";

    var notariIdCard: String? = "";
    var notaryEntity: NotaryEntity? = null;
    var imgPathList: List<String>? = null;
    var lastOrderInfo: OrderInfoEntity? = null;
    var orderStatu: Int? = 0//默认未支付
    var isProxy = false;//是否是代办
    private var proxyTwoFragment: SelfStepProxyTwoFragment? = null

    override fun getIntentData(intent: Intent?) {

        realNameCard = userInfo?.name;
        sexsCard = userInfo?.gender;
        cardNumberCard = userInfo?.idCard;
        mobilePhoneCard = userInfo?.mobile;
        birthdayCard = userInfo?.birthday;
        addressCard = userInfo?.address;

//        //todo  xhf
//        realNameCard = "徐海凤";
//        sexsCard = 0;
//        cardNumberCard = "321081198908153024";
//        mobilePhoneCard = "15850686312";
//        birthdayCard = "1989-08-15";
//        addressCard = "江苏省";

        notariIdCard = notaryId;
        isProxy = intent!!.getBooleanExtra("isProxy", false)

    }

    override fun initViewsAndEvents() {
        Log.i(TAG, "initViewsAndEvents")
        updateSignatureP = NotarizationPresenter(mActivity, this);
        notariPer = NotarizaSelfPresenter(mActivity, this);
        notariPer?.queryNotarialOfficeGById(notaryId);
//        notariPer?.getPayConfig(notaryId, this)
        initByFragmentsAndShowFirst();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_notarization_self;
    }

    private fun initByFragmentsAndShowFirst() {
        Log.i(TAG, "initByFragmentsAndShowFirst")
        oneFragment = SelfStepOneFragment();
        twoFragment = SelfStepTwoFragment();
        threeFragment = SelfStepThreeFragment();
        fourFragment = SelfStepFourFragment();
        fiveFragment = SelfStepFiveFragment();
        sixFragment = SelfStepSixFragment();
        noSomeFragment = SelfNoSomeFragment();
        proxyTwoFragment = SelfStepProxyTwoFragment()
//        addFragment(oneFragment!!);
//        showFragment(oneFragment!!);
        onNavigationItemSelected(0)
    }

    /*添加fragment*/
    private fun addFragment(fragment: Fragment) {
        /*判断该fragment是否已经被添加过  如果没有被添加  则添加*/
        if (!fragment.isAdded) {
            supportFragmentManager.beginTransaction().add(R.id.frameStep, fragment).commitAllowingStateLoss()
            fragmentList?.add(fragment)
        }
    }

    /*替换fragment*/
    private fun replaceFragment(fragment: Fragment) {
        /*判断该fragment是否已经被添加过*/
        if (!fragment.isAdded) {
            supportFragmentManager.beginTransaction().replace(R.id.frameStep, fragment).commitAllowingStateLoss()
//            fragmentList?.add(fragment)
        } else {
            showFragment(fragment)
        }
    }

    /*显示fragment*/
    private fun showFragment(fragment: Fragment) {
        if (fragmentList != null) {
            for (frag in fragmentList) {
                if (frag !== fragment) {
                    /*先隐藏其他fragment*/
                    supportFragmentManager.beginTransaction().hide(frag).commitAllowingStateLoss()
                }
            }
        }
        supportFragmentManager.beginTransaction().show(fragment).commitAllowingStateLoss()
    }

    public fun onNavigationItemSelected(pos: Int): Boolean {
        Log.i(TAG, "initByFragmentsAndShowFirst$pos")
        when (pos) {
            0 -> {
                if (oneFragment != null) {
                    fragmentList?.remove(oneFragment!!)
                }
                oneFragment = SelfStepOneFragment()
                addFragment(oneFragment!!);
                showFragment(oneFragment!!)
            }
            1 -> {
                if (!isProxy) {
                    if (twoFragment == null) {
                        twoFragment = SelfStepTwoFragment()
                    }
                    if (fragmentList?.size!! < 2)
                        addFragment(twoFragment!!)
                    else {
                        fragmentList?.remove(twoFragment!!)
                        twoFragment = SelfStepTwoFragment()
                        addFragment(twoFragment!!)
                    }
                    showFragment(twoFragment!!)
                } else {
                    if (proxyTwoFragment == null) {
                        proxyTwoFragment = SelfStepProxyTwoFragment()
                    }
                    if (fragmentList?.size!! < 2)
                        addFragment(proxyTwoFragment!!)
                    else {
                        fragmentList?.remove(proxyTwoFragment!!)
                        proxyTwoFragment = SelfStepProxyTwoFragment()
                        addFragment(proxyTwoFragment!!)
                    }
                    showFragment(proxyTwoFragment!!)
                }
            }
            2 -> {
                if (threeFragment == null) {
                    threeFragment = SelfStepThreeFragment()
                }
                if (fragmentList?.size!! < 3)
                    addFragment(threeFragment!!)
                else {
                    fragmentList.remove(threeFragment)
                    threeFragment = SelfStepThreeFragment()
                    addFragment(threeFragment!!)
                }
                showFragment(threeFragment!!)
            }
            3 -> {
                if (fourFragment == null) {
                    fourFragment = SelfStepFourFragment()
                }
                if (fragmentList?.size!! < 4)
                    addFragment(fourFragment!!)
                else {
                    fragmentList.remove(fourFragment)
                    fourFragment = SelfStepFourFragment()
                    addFragment(fourFragment!!)
                }
                showFragment(fourFragment!!)
            }
            4 -> {
                if (fiveFragment == null) {
                    fiveFragment = SelfStepFiveFragment()
                }
                if (fragmentList?.size!! < 5)
                    addFragment(fiveFragment!!)
                else {
                    fragmentList.remove(fiveFragment)
                    fiveFragment = SelfStepFiveFragment()
                    addFragment(fiveFragment!!)
                }
                showFragment(fiveFragment!!)
            }
            5 -> {
                if (sixFragment == null) {
                    sixFragment = SelfStepSixFragment()
                }
                if (fragmentList?.size!! < 6)
                    addFragment(sixFragment!!)
                else {
                    fragmentList.remove(sixFragment)
                    sixFragment = SelfStepSixFragment()
                    addFragment(sixFragment!!)
                }
                showFragment(sixFragment!!)
            }
            6 -> {
                if (noSomeFragment == null) {
                    noSomeFragment = SelfNoSomeFragment()
                }
                if (fragmentList?.size!! < 7)
                    addFragment(noSomeFragment!!)
                else {
                    fragmentList.remove(noSomeFragment)
                    noSomeFragment = SelfNoSomeFragment()
                    addFragment(noSomeFragment!!)
                }
                showFragment(noSomeFragment!!)
            }
        }
        return true
    }

    fun backClicked(view: View?) {
        Log.i(TAG, "backClicked")
        finish();
    }

    override fun queryNotaryEntitySuccess(bean: NotaryEntity?) {
        Log.i(TAG, "queryNotaryEntitySuccess")
        notaryEntity = bean;
        notariNameCard = notaryEntity?.notarialName;
    }

    override fun getPayConfigViewSuccess(msg: String?) {
//        sixFragment?.isShowOnlinePay = true
    }

    override fun getPayConfigViewFail(msg: String?) {
//        sixFragment?.isShowOnlinePay = false
    }

    @Subscribe
    fun onEventMainThread(eb: EBPhotoInfo) {
        Log.i(TAG, "onEventMainThread")
        fourFragment?.onEventMainThread(eb)
    }

//    public fun getHtmlObject(): Any? {
//        Log.i(TAG, "getHtmlObject")
//        return object : Any() {
//            @JavascriptInterface
//            open fun aliPay(msg: String?) {
//                DLog.d("androidObj  ", msg)
//                runOnUiThread {
//                    fiveFragment?.alertWebSign?.dismiss();
//                    fiveFragment?.initSign();
//                    var signatureInfo = JSON.parseObject(msg, SignatureInfo::class.java)
//                    updateSignatureP?.updateSignature(BitmapTool.stringToBitmap(signatureInfo.base64!!), signatureInfo.encDataFilePath)
//                }
//            }
//        }
//    }

    override fun updateSignatureSuccess(bean: ImageBean?, encData: String?) {
        Log.i(TAG, "updateSignatureSuccess")
        fiveFragment?.senderNextApi(bean, encData);
    }

    override fun updateSignatureFailed(msg: String?) {
    }

    fun change_location(newcity: String?) {
        if (!isProxy) {
            if (twoFragment != null) {
                twoFragment?.change_location_text(newcity)
            }
        } else {
            if (proxyTwoFragment != null) {
                proxyTwoFragment?.change_location_text(newcity)
            }
        }

    }

    fun changeLocation(view: View?) {
        if (!isProxy) {
            if (proxyTwoFragment != null) {
                proxyTwoFragment?.changeLocation()
            }
        } else {
            if (proxyTwoFragment != null) {
                proxyTwoFragment?.changeLocation()
            }
        }

    }
}