package com.gc.notarizationpc.ui.view;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.ui.video.CounselingRoomActivity;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.umeng.analytics.MobclickAgent;

import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.goldze.mvvmhabit.widget.DrawView;

/**
 * q签字
 */
public class SignDialog extends Dialog {
    private Context mContext;
    private static DrawView drawView = null;
    private PopwindowUtil.ResultSecondListener signatureListener = null;
    private String username;
    private TextView tvName;

    public SignDialog(Context context, String username, PopwindowUtil.ResultSecondListener resultListener) {
        super(context);
        mContext = context;
        signatureListener = resultListener;
        this.username = username;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_signature);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();

    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        TextView tvConfirm = findViewById(R.id.tv_confirm);
        Button btnClear = findViewById(R.id.btn_clear);
        LinearLayout mainLayout = findViewById(R.id.main_linlayout);
        LinearLayout signatureView = findViewById(R.id.signatureView);
        tvName = findViewById(R.id.name_pos);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        if (mContext instanceof CounselingRoomActivity) {
            ((CounselingRoomActivity) mContext).getWindowManager().getDefaultDisplay().getRealMetrics(displayMetrics);
        }

        tvName.setText(username);
        String name = username;

        if (mainLayout.getWidth() == 0) {
            ViewTreeObserver vto2 = mainLayout.getViewTreeObserver();
            vto2.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    Log.d("zpzp", "画布初始化完成");
                    mainLayout.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    setDrawView(mContext, name.length(), mainLayout);
                }
            });
        } else {
            if (drawView == null) {
                setDrawView(mContext, name.length(), mainLayout);
            } else {
                mainLayout.removeAllViews();
                setDrawView(mContext, name.length(), mainLayout);
            }
        }

        signatureView.setVisibility(View.VISIBLE);

        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @SuppressLint("CheckResult")
            @Override
            public void onClick(View v) {
                CommonUtil.disabledView(v);
                if (drawView != null && !drawView.isCanvasEmpty()) {
                    Bitmap bit = drawView.getPaintBitmap(name.length());
//                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
//                    bit.compress(Bitmap.CompressFormat.JPEG, 100, baos);
//                    byte[] imageBytes = baos.toByteArray();
                    if (signatureListener != null) {
                        signatureListener.result(bit);
                    }
                } else {
                    ToastUtils.toastError(Utils.getContext().getString(R.string.pleaseSignAtFirst));
                }
            }
        });

        btnClear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (drawView != null) {
                    drawView.clear();
                }

                try {
                    if (drawView == null) {
                        setDrawView(mContext, username.length(), mainLayout);
                    } else {
                        drawView.clear();
                        mainLayout.removeAllViews();
                        setDrawView(mContext, username.length(), mainLayout);
                    }
                    if (signatureView != null) {
                        signatureView.setVisibility(View.VISIBLE);
                    }

                    if (tvName != null) {
                        tvName.setText(username);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    MobclickAgent.reportError(mContext, e);
                }

            }
        });
        findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                signatureListener.secondResult(new Bitmap[] {null});
                if (isShowing()) {
                    dismiss();
                }
            }
        });
    }

    private static void setDrawView(Context context, Integer length, LinearLayout main_linlayout) {
        int with = main_linlayout.getWidth() * length;
        Double dou = 1.0 + (length - 1) * 0.5;
        if (dou != null) {
            with = (int) (dou * main_linlayout.getWidth()) + 1;
        }
        drawView = new DrawView(context, main_linlayout.getWidth(), main_linlayout.getHeight());
        main_linlayout.addView(drawView);
        drawView.requestFocus();
    }


    @Override
    public void show() {
        super.show();
    }

}
