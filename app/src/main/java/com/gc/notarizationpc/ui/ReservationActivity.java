package com.gc.notarizationpc.ui;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.amap.api.location.AMapLocation;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityReservationBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.bean.SerachBean;
import com.gc.notarizationpc.data.model.response.AreaModel;
import com.gc.notarizationpc.data.model.response.BindingNotaryOfficeModel;
import com.gc.notarizationpc.data.model.response.LanguageModel;
import com.gc.notarizationpc.data.model.response.MachineBindAreaResponse;
import com.gc.notarizationpc.data.model.response.NotaryAffairResponseModel;
import com.gc.notarizationpc.ui.inquirycertification.InquiryTypeHomeActivity;
import com.gc.notarizationpc.ui.view.BlockPuzzleDialog;
import com.gc.notarizationpc.ui.view.NotaryListByRegionAlert;
import com.gc.notarizationpc.ui.viewmodel.ReservationViewModel;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.GpsUtil;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.google.gson.Gson;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.utils.MaterialDialogUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class ReservationActivity extends BaseActivity<ActivityReservationBinding, ReservationViewModel> {

    private NotaryListByRegionAlert notaryListByRegionAlert;

    private List<SerachBean.Bean> selectSerachBeanList = new ArrayList<>();

    private List<MachineBindAreaResponse.ProvinceDTO.GroupDTO> currentCitys;//当前可切换的城市列表

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_reservation;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public ReservationViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(ReservationViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }

    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });
        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });

        binding.userAgreement.setOnCheckedChangeListener((compoundButton, b) -> viewModel.isRead.set(b));
        binding.edtRemark.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                viewModel.remark.set(editable.toString());
            }
        });
        binding.edtVerifycode.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                viewModel.messageCode.set(editable.toString());
            }
        });
        binding.edtPhone.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                viewModel.phoneNum.set(editable.toString());
            }
        });
        viewModel.getNotaryAreaList();
        viewModel.getNotaryLanguageList();
        viewModel.findIntegratedMachineBindArea();
    }


    @Override
    public void initViewObservable() {
        //申办成功
        viewModel.reservationSuccessClickEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object response) {
                MaterialDialogUtils.showBasicDialogNoCancel(ReservationActivity.this, getString(R.string.submit_success), getString(R.string.reservation_success_to_do)).onPositive(new MaterialDialog.SingleButtonCallback() {
                    @Override
                    public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                        finish();
                    }
                }).show();
                ;
            }
        });

        viewModel.showShenbanClickEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object response) {
                PopwindowUtil.showShenbanHintDialog(ReservationActivity.this);
            }
        });

        viewModel.notaryListSingleLiveEvent.observe(this, new Observer<List<BindingNotaryOfficeModel>>() {
            @Override
            public void onChanged(List<BindingNotaryOfficeModel> response) {
                if (response != null && !response.isEmpty() && response.size() == 1) {
                    viewModel.mCurrentNotaryOffice = response.get(0);
                } else if (response != null && response.size() > 0) {
                    chooseNotaryOfficeList(response);
                } else {
                    toastError(getString(R.string.no_find_notary_office_please_contact));
                }

            }
        });

        viewModel.machineBindAreaResponseSingleLiveEvent.observe(this, new Observer<MachineBindAreaResponse>() {
            @Override
            public void onChanged(MachineBindAreaResponse result) {
                if (result.getProvince() == null) {
                    return;
                }
                try {
                    if (result.getProvince().getGroup() != null && result.getProvince().getGroup().size() > 0 && result.getProvince().getGroup().get(0).getCityList() != null
                            && result.getProvince().getGroup().get(0).getCityList().size() > 0) {
                        currentCitys = result.getProvince().getGroup();
                        //至少有一个城市
                        if (result.getProvince().getGroup().get(0) != null && result.getProvince().getGroup().get(0).getCityList().size() == 1) {
                            viewModel.mSelectProvince = result.getProvince();
                            viewModel.mCity = result.getProvince().getGroup().get(0).getCityList().get(0);
                        } else {
                            //多个城市判断，当前机器所在地址的
                            GpsUtil.getlocation(ReservationActivity.this, new GpsUtil.MapGPS() {
                                @Override
                                public void success(AMapLocation aMapLocation) {
                                    if (aMapLocation != null) {
                                        //如果机器再所在城市里面，则展示aMapLocation.getCity()，反之则随便展示第一个绑定机器的城市;点击可以切换
                                        Log.e("Test", "result.getProvince().getGroup().toString()" + new Gson().toJson(result.getProvince().getGroup()));
                                        Log.e("Test", aMapLocation.getCity() + "aMapLocation.getCity()");
                                        if (new Gson().toJson(result.getProvince().getGroup()).contains(aMapLocation.getCity())) {
                                            viewModel.mSelectProvince = result.getProvince();
                                            for (MachineBindAreaResponse.ProvinceDTO.GroupDTO groupDTO : result.getProvince().getGroup()) {
                                                if (groupDTO != null) {
                                                    for (MachineBindAreaResponse.ProvinceDTO.GroupDTO.CityListDTO cityListDTO : groupDTO.getCityList()) {
                                                        if (cityListDTO != null && !TextUtils.isEmpty(cityListDTO.getName()) && cityListDTO.getName().contains(aMapLocation.getCity())) {
                                                            viewModel.mCity = cityListDTO;
                                                        }
                                                    }
                                                }
                                            }
                                        } else {

                                        }
                                    }

                                }

                                @Override
                                public void failed() {
                                    Log.e("Test", "定位失败");
                                }
                            });
                        }
                        viewModel.requestNotary(1);
                    } else {
                        //如果该一体机一个城市都没有绑定呢？
//                    if (result.getProvince().getGroup().get(0).getCityList() != null && result.getProvince().getGroup().get(0).getCityList().size() > 0) {
//                        binding.tvLocation.setText(result.getProvince().getGroup().get(0).getCityList().get(0).getName());
//                    }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    MobclickAgent.reportError(ReservationActivity.this, e);
                }
            }
        });
        viewModel.notaryLanguageOfficeListSingleLiveEvent.observe(this, new Observer<List<LanguageModel>>() {

            @Override
            public void onChanged(List<LanguageModel> languageModels) {
                if (languageModels == null || languageModels.size() == 0) {
                    ToastUtils.showShort(getString(R.string.getNotaryOfficeLanguageFail));
                    return;
                }
                List<SerachBean> serachBeans = new ArrayList<>();
                Set<String> keySet = new HashSet<>();
                Log.d("languageModels", languageModels + "");
                for (int i = 0; i < languageModels.size(); i++) {
                    keySet.add(CommonUtil.getFirstLetter(languageModels.get(i).getLabel()));
                }
                for (String key : keySet) {
                    List<SerachBean.Bean> beans = new ArrayList<>();
                    SerachBean serachBean = new SerachBean();
                    serachBean.setKey(key);
                    for (int i = 0; i < languageModels.size(); i++) {
                        if (key.equals(CommonUtil.getFirstLetter(languageModels.get(i).getLabel()))) {
                            SerachBean.Bean bean = new SerachBean.Bean();
                            bean.setName(languageModels.get(i).getLabel());
                            bean.setId(languageModels.get(i).getValue());
                            beans.add(bean);
                        }
                    }
                    serachBean.setBean(beans);
                    Log.d("languageModels" + key, beans.toString() + "");
                    serachBeans.add(serachBean);
                }
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    serachBeans.sort((o1, o2) -> o1.getKey().compareTo(o2.getKey()));
                }
                PopwindowUtil.showGroup(serachBeans, ReservationActivity.this, 1, new PopwindowUtil.ResultListener<List<SerachBean.Bean>>() {
                    @Override
                    public void result(List<SerachBean.Bean> value) {
//                        binding.edtNotarizationLanguage.setText(value);
                        viewModel.selectedNotaryLanguage.set(value.get(0).getName());
                    }
                });
            }
        });
        viewModel.areaListSingleLiveEvent.observe(this, new Observer<List<AreaModel>>() {
            @Override
            public void onChanged(List<AreaModel> areaModels) {
                if (areaModels == null || areaModels.size() == 0) {
                    ToastUtils.showShort(getString(R.string.getNotaryOfficeAreaFail));
                    return;
                }
                List<SerachBean> serachBeans = new ArrayList<>();
                Set<String> keySet = new HashSet<>();
                Log.d("areaModels", areaModels + "");
                for (int i = 0; i < areaModels.size(); i++) {
                    keySet.add(CommonUtil.getFirstLetter(areaModels.get(i).getZhName()));
                }
                for (String key : keySet) {
                    List<SerachBean.Bean> beans = new ArrayList<>();
                    SerachBean serachBean = new SerachBean();
                    serachBean.setKey(key);
                    for (int i = 0; i < areaModels.size(); i++) {
                        if (key.equals(CommonUtil.getFirstLetter(areaModels.get(i).getZhName()))) {
                            SerachBean.Bean bean = new SerachBean.Bean();
                            bean.setName(areaModels.get(i).getZhName());
                            bean.setId(areaModels.get(i).getId());
                            beans.add(bean);
                        }
                    }
                    serachBean.setBean(beans);
                    serachBeans.add(serachBean);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        serachBeans.sort((o1, o2) -> o1.getKey().compareTo(o2.getKey()));
                    }
                }
                PopwindowUtil.showGroup(serachBeans, ReservationActivity.this, 1, new PopwindowUtil.ResultListener<List<SerachBean.Bean>>() {
                    @Override
                    public void result(List<SerachBean.Bean> bean) {
//                        binding.edtNotarizationAddress.setText(value);
                        viewModel.selectedNotaryArea.set(bean.get(0).getName());
                    }
                });
            }

        });
        viewModel.notaryOfficeSingleLiveEvent.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                if (s == null || s.equals("")) {
                    ToastUtils.showShort(getString(R.string.getNotaryOfficeFail));
                    return;
                }
                binding.edtNotaryOffice.setText(s);
            }
        });

        viewModel.notaryMattersListSingleLiveEvent.observe(this, new Observer<List<NotaryAffairResponseModel>>() {
            @Override
            public void onChanged(List<NotaryAffairResponseModel> strings) {
                if (strings == null || strings.size() == 0) {
                    ToastUtils.showShort(getString(R.string.getNotaryOfficeMattersFail));
                    return;
                }
                List<SerachBean> serachBeans = new ArrayList<>();
                Set<String> keySet = new HashSet<>();
                Log.d("strings", strings + "");
                for (int i = 0; i < strings.size(); i++) {
                    keySet.add(CommonUtil.getFirstLetter(strings.get(i).getMattersName()));
                }
                for (String key : keySet) {
                    List<SerachBean.Bean> beans = new ArrayList<>();
                    SerachBean serachBean = new SerachBean();
                    serachBean.setKey(key);
                    for (int i = 0; i < strings.size(); i++) {
                        if (key.equals(CommonUtil.getFirstLetter(strings.get(i).getMattersName()))) {
                            SerachBean.Bean bean = new SerachBean.Bean();
                            bean.setName(strings.get(i).getMattersName());
                            bean.setId(strings.get(i).getMattersId());
                            beans.add(bean);
                        }
                    }
                    serachBean.setBean(beans);
                    serachBeans.add(serachBean);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        serachBeans.sort((o1, o2) -> o1.getKey().compareTo(o2.getKey()));
                    }
                }
                if (selectSerachBeanList != null && selectSerachBeanList.size() > 0) {
                    for (int i = 0; i < selectSerachBeanList.size(); i++) {
                        if (selectSerachBeanList.get(i) != null && serachBeans != null && serachBeans.size() > 0) {
                            for (int j = 0; j < serachBeans.size(); j++) {
                                if (serachBeans.get(j) != null && serachBeans.get(j).getBean() != null && serachBeans.get(j).getBean().size() > 0) {
                                    for (int k = 0; k < serachBeans.get(j).getBean().size(); k++) {
                                        if (serachBeans.get(j).getBean().get(k) != null && serachBeans.get(j).getBean().get(k).getId().equals(selectSerachBeanList.get(i).getId())) {
                                            serachBeans.get(j).getBean().get(k).setSelected(true);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                PopwindowUtil.showGroup(serachBeans, ReservationActivity.this, 5, new PopwindowUtil.ResultListener<List<SerachBean.Bean>>() {
                    @Override
                    public void result(List<SerachBean.Bean> bean) {
//                        binding.edtNotarizationAddress.setText(value);
                        try {
                            String tempValue = "";
//                            if (selectSerachBeanList != null && selectSerachBeanList.size() > 0) {
//                                if (bean != null && bean.size() > 0) {
//                                    List<SerachBean.Bean> tempBean = new ArrayList<>();
//                                    tempBean.addAll(selectSerachBeanList);
//                                    for (SerachBean.Bean bean1 : tempBean) {
//                                        for (SerachBean.Bean bean2 : bean) {
//                                            if (bean1.getName().equals(bean2.getName())) {
//                                                selectSerachBeanList.remove(bean1);
//                                            }
//                                        }
//                                    }
//                                    selectSerachBeanList.addAll(bean);
//                                } else {
//                                    return;
//                                }
//                            } else {
//                                selectSerachBeanList.addAll(bean);
//                            }
                            selectSerachBeanList.clear();
                            selectSerachBeanList.addAll(bean);
                            viewModel.notaryMatters.set("");
                            for (int i = 0; i < selectSerachBeanList.size(); i++) {
                                tempValue += selectSerachBeanList.get(i).getName() + "、";
                            }
                            if (TextUtils.isEmpty(viewModel.notaryMatters.get())) {
                                viewModel.notaryMatters.set(tempValue.substring(0, tempValue.length() - 1));
                            } else {
                                viewModel.notaryMatters.set(viewModel.notaryMatters.get() + "、" + tempValue.substring(0, tempValue.length() - 1));
                            }

                            binding.edtNotarizationItem.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                            binding.edtNotarizationItem.setSingleLine(true);
                            binding.edtNotarizationItem.setSelected(true);
                            binding.edtNotarizationItem.setFocusable(true);
                            binding.edtNotarizationItem.setFocusableInTouchMode(true);
                        } catch (Exception e) {
                            Log.e("Test", e.getMessage());
                            MobclickAgent.reportError(ReservationActivity.this, e);
                        }
                    }
                });
            }
        });

        viewModel.getCodeEvent.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String phone) {
                BlockPuzzleDialog blockPuzzleDialog = new BlockPuzzleDialog(ReservationActivity.this);
                blockPuzzleDialog.show();
                blockPuzzleDialog.setOnResultsListener(new BlockPuzzleDialog.OnResultsListener() {
                    @Override
                    public void onResultsClick(String result) {
                        //获取短信验证码
                        viewModel.getMessageCode(phone);
                        binding.getCodeTextView.setText("60s");
                        binding.getCodeTextView.setEnabled(false);
                        binding.getCodeTextView.setBackground(getResources().getDrawable(R.drawable.shape_corner5_e8e8e8));
                        CountDownTimer timer = new CountDownTimer(60000, 1000) {
                            @Override
                            public void onTick(long l) {
                                binding.getCodeTextView.setText(l / 1000 + "s");
                            }

                            @Override
                            public void onFinish() {
                                binding.getCodeTextView.setText(getResources().getString(R.string.send_code));
                                binding.getCodeTextView.setEnabled(true);
                                binding.getCodeTextView.setBackground(getResources().getDrawable(R.drawable.shape_corner5_3c6af4));
                            }
                        };
                        timer.start();
                    }
                });

            }
        });

    }

    /**
     * 选择公证处
     */

    private void chooseNotaryOfficeList(List<BindingNotaryOfficeModel> data) {
//        PopwindowUtil.showNotaryOffice(this, data);
        if (notaryListByRegionAlert == null || !notaryListByRegionAlert.isShowing()) {
            notaryListByRegionAlert = new NotaryListByRegionAlert(ReservationActivity.this, data, new PopwindowUtil.ResultListener<BindingNotaryOfficeModel>() {
                @Override
                public void result(BindingNotaryOfficeModel value) {
                    if (value == null) {
                        toastError(getString(R.string.select_notary_office_first_hint));
                        return;
                    }
                    Log.e("notaryListByRegionAlert", value.getMechanismName());
                    notaryListByRegionAlert.dismiss();
                    if (value != null && value.getMechanismName() != null) {
                        viewModel.mCurrentNotaryOffice = value;
                        viewModel.selectedNotaryOffice.set(value.getMechanismName());
                        binding.edtNotaryOffice.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                        binding.edtNotaryOffice.setSingleLine(true);
                        binding.edtNotaryOffice.setSelected(true);
                        binding.edtNotaryOffice.setFocusable(true);
                        binding.edtNotaryOffice.setFocusableInTouchMode(true);
                    } else {
                        toastError(getResources().getString(R.string.getNotaryOfficeFail));
                    }

                }
            });
            notaryListByRegionAlert.show();
            Window window = notaryListByRegionAlert.getWindow();
            window.getDecorView().setBackgroundColor(ReservationActivity.this.getResources().getColor(R.color.white));
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setPadding(0, 0, 0, 0);
            window.setLayout(
                    window.getContext().getResources().getDisplayMetrics().widthPixels - 200,
                    window.getContext().getResources().getDisplayMetrics().heightPixels - 120);

            window.setGravity(Gravity.CENTER);
            notaryListByRegionAlert.setCanceledOnTouchOutside(false);
        }
    }

    private String startTime;

    //    public class ProxyClick {
    public void showTimePickDialog(View view) {
        if (viewModel.mCurrentNotaryOffice != null && viewModel.mCurrentNotaryOffice.getMechanismName() != null) {
            CommonUtil.showTimePicker(ReservationActivity.this, new PopwindowUtil.ResultSecondListener() {
                @Override
                public void result(Object value) {
                    if (value != null) {
                        viewModel.startTime = (String) value;
                        startTime = ((String) value);
//                        binding.reservationDate.setText(value.toString());
//                        binding.reservationDate.setEllipsize(TextUtils.TruncateAt.MARQUEE);
//                        binding.reservationDate.setSingleLine(true);
//                        binding.reservationDate.setSelected(true);
//                        binding.reservationDate.setFocusable(true);
//                        binding.reservationDate.setFocusableInTouchMode(true);
                    }
                }

                @Override
                public void secondResult(Object value) {
                    if (value != null) {
                        try {
                            viewModel.endTime = (String) value;
                            binding.reservationDate.setText(startTime + "-" + viewModel.endTime.split(" ")[1]);
                            binding.reservationDate.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                            binding.reservationDate.setSingleLine(true);
                            binding.reservationDate.setSelected(true);
                            binding.reservationDate.setFocusable(true);
                            binding.reservationDate.setFocusableInTouchMode(true);
                        } catch (Exception e) {
                            e.printStackTrace();
                            MobclickAgent.reportError(ReservationActivity.this, e);
                        }
                    }
                }
            });

        } else {
            toastError(ReservationActivity.this.getString(R.string.select_notary_office_first_hint));
        }


    }

    //    public class ProxyClick {
    public void showReservationSuccess(View view) {
        PopwindowUtil.showReservationSuccess(this, new PopwindowUtil.ButtonClickListener() {
            @Override
            public void cancel() {

            }

            @Override
            public void decide() {

            }
        });
    }

    public void lookOnLineRule() {

    }


}
