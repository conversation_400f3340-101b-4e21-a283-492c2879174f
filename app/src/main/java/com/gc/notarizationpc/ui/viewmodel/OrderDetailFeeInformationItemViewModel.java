package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.VideoOrderDetailModel;

import me.goldze.mvvmhabit.base.ItemViewModel;

/**
 * Created by goldze on 2017/7/17.
 */

public class OrderDetailFeeInformationItemViewModel extends ItemViewModel<OrderDetailViewModel> {


    public ObservableField<VideoOrderDetailModel.PaymentListVODTO.MattersFeeListDTO> entity = new ObservableField<>();

    public OrderDetailFeeInformationItemViewModel(@NonNull OrderDetailViewModel viewModel, VideoOrderDetailModel.PaymentListVODTO.MattersFeeListDTO entity) {
        super(viewModel);
        Log.e("FragmentGridItemViewModel", "FragmentGridItemViewModel");
        this.entity.set(entity);
    }

}
