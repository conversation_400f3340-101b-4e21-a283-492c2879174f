package com.gc.notarizationpc.ui.fragment

import android.annotation.SuppressLint
import android.app.Dialog
import android.graphics.Rect
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.example.framwork.noHttp.Bean.BaseResponseBean
import com.example.framwork.utils.DialogUtils
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.*
import com.gc.notarizationpc.common.CommonInfo
import com.gc.notarizationpc.model.*
import com.gc.notarizationpc.ui.NotarizationSelfActivity
import com.gc.notarizationpc.ui.adapter.DeviceAdapter
import com.gc.notarizationpc.ui.adapter.SelectedInnerTwoGridItemsAdapter
import com.gc.notarizationpc.ui.presenter.MainPresenter
import com.gc.notarizationpc.ui.presenter.NotarizaSelfPresenter
import com.gc.notarizationpc.utils.CommonUtil
import com.gc.notarizationpc.utils.MacUtils
import com.gc.notarizationpc.utils.MapUtils
import com.gc.notarizationpc.widget.CustomSelectPicker
import com.gc.notarizationpc.widget.MyLayoutManager
import com.gc.notarizationpc.widget.editspinner.AppCompatEditSpinner
import kotlinx.android.synthetic.main.fragment_self_step_two.*
import org.json.JSONArray
import org.json.JSONObject


class SelfStepTwoFragment : BaseFragment(), NotarizaSelfPresenter.INotarizaSelfView, MainPresenter.IHomeView, OnItemClickListener, CustomSelectPicker.Callback {
    private var currentActivity: NotarizationSelfActivity? = null
    private var itemList = ArrayList<NotrayBean.ChildrenDTO>()
    private var countyList = ArrayList<CountyBean>()
    private var langList = ArrayList<LanguarBean>()
    private var notarHomeList = ArrayList<NotarhomeBean>()
    private var mAdapter: SelectedInnerTwoGridItemsAdapter? = null;
    private var notarizationP: NotarizaSelfPresenter? = null
    private var queryP: MainPresenter? = null
    private var customSelectPicker: CustomSelectPicker? = null;
    private var indexSelect: Int? = null;
    private var curLanguage: LanguarBean? = null;
    private var currCounty: CountyBean? = null;
    private var curGzs: NotarhomeBean? = null;
    private var mNotaryitems: String? = "";
    private var mApplyUsers: String? = "";
    private var TAG = "SelfStepTwoFragment"
    private var deviceDialog: Dialog? = null
    private var deviceAdapter: DeviceAdapter? = null
    var REFRESH_NOTARIZATION: Int? = 0x01
    private var changelocation: TextView? = null
    var city: String = ""//市
    var adcode: String = ""//区域码
    var longitude: String = ""
    var latitude: String = ""
    var address: String = ""
    private var countyListStr = ArrayList<String>()
    private var langListStr = ArrayList<String>()
    val handler: Handler = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message?) {
            super.handleMessage(msg)
            when (msg?.what) {
                REFRESH_NOTARIZATION -> {
                    changelocation?.text = city
                    Log.d("zpzp", "refresh adcode == " + adcode)
                    queryP?.getNotaryListNewOnline(adcode, CommonUtil.bd_decrypt(longitude, latitude), false)

                }
            }
        }
    }

    private fun initSomeDataOnPage() {
        Log.i(TAG, "initSomeDataOnPage")
        var gzc = (mActivity as NotarizationSelfActivity?)?.notaryEntity;
        Log.d(TAG, "gzc?.institutionType == " + gzc?.institutionType)
        if (!"3".equals(gzc?.institutionType)) {
            gzcChose.text = gzc?.notarialName;
        } else {
            gzcChose?.setOnClickListener {
                indexSelect = 3;
                if (!TextUtils.isEmpty(adcode) && adcode.length > 4)
                    adcode = adcode?.substring(0, 4) + "00"
                else
                    adcode = "320000"
                queryP?.getNotaryListNewOnline(adcode, CommonUtil.bd_decrypt(longitude, latitude), false)
            };
        }

        sexs.text = "女";
        if ((mActivity as NotarizationSelfActivity?)?.sexsCard == 1) {
            sexs.text = "男";
        }
        realName.text = (mActivity as NotarizationSelfActivity?)?.realNameCard;
        var birthdayTem = (mActivity as NotarizationSelfActivity?)?.birthdayCard?.split(" ");
        if (birthdayTem!=null && birthdayTem?.size!! > 0) {
            birthday.text = birthdayTem?.get(0);
        } else {
            birthday.text = (mActivity as NotarizationSelfActivity?)?.birthdayCard;
        }
        cardId.text = (mActivity as NotarizationSelfActivity?)?.cardNumberCard;
        mobilePhone.text = (mActivity as NotarizationSelfActivity?)?.mobilePhoneCard;
        addressDetail.text = (mActivity as NotarizationSelfActivity?)?.addressCard;
    }

    fun initWithTwoParamsToString(): Boolean {
        Log.i(TAG, "initWithTwoParamsToString")
        mNotaryitems = "";
        var jsonNotaryitemsArray = JSONArray();
        for (obj in itemList) {
            if (obj.isChecked) {
                var jsonNotaryitemsObj = JSONObject();
                jsonNotaryitemsObj.put("notaryItemId", obj.unitGuid);
                jsonNotaryitemsObj.put("notaryItemName", obj.name);
                jsonNotaryitemsObj.put("notaryNum", "1");
                jsonNotaryitemsArray.put(jsonNotaryitemsObj);
            }
        }
        mNotaryitems = jsonNotaryitemsArray.toString();
        var jsonApplyUsersArray = JSONArray();
        var jsonApplyUsersObj = JSONObject();
        jsonApplyUsersObj.put("name", (mActivity as NotarizationSelfActivity?)?.realNameCard);
        jsonApplyUsersObj.put("gender", ((mActivity as NotarizationSelfActivity?)?.sexsCard).toString());
        jsonApplyUsersObj.put("idCard", (mActivity as NotarizationSelfActivity?)?.cardNumberCard);
        jsonApplyUsersObj.put("mobile", (mActivity as NotarizationSelfActivity?)?.mobilePhoneCard);
        jsonApplyUsersObj.put("birthday", (mActivity as NotarizationSelfActivity?)?.birthdayCard);
        jsonApplyUsersObj.put("address", (mActivity as NotarizationSelfActivity?)?.addressCard);
        jsonApplyUsersArray.put(jsonApplyUsersObj);
        mApplyUsers = jsonApplyUsersArray.toString();
        return jsonNotaryitemsArray.length() == 0
    }

    override fun onResume() {
        super.onResume()
        val window = activity?.window
        window?.decorView?.viewTreeObserver?.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                val r = Rect()
                window.decorView.getWindowVisibleDisplayFrame(r)
                val screenHeight = window.decorView.rootView.height
                val keyboardHeight = screenHeight - r.bottom
                // 如果键盘高度小于屏幕高度的1/4，则认为软键盘已经关闭
                if (keyboardHeight < screenHeight / 4) {
                    // 软键盘已经关闭
                    // 在这里执行你的操作

                }
            }
        })
    }


    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {
        Log.i(TAG, "initViewsAndEvents")
        deviceAdapter = DeviceAdapter()
        deviceAdapter?.setOnItemClickListener(this)
        currentActivity = mActivity as NotarizationSelfActivity?;
        itemList = (mActivity as NotarizationSelfActivity?)?.choseList!!;
        notarizationP = NotarizaSelfPresenter(mActivity, this);
        queryP = MainPresenter(mActivity, this)
        notarizationP?.queryCountryArea();
        notarizationP?.queryCountryLanguage();
        notarizationP?.queryDepartmentTree();


        tvNextStep?.setOnClickListener {
            if (addressChose?.text.toString().isEmpty() || ywyyChose?.text.toString().isEmpty()) {
                toastInfo("请选择地区和语言")
            } else if (gzcChose?.text.toString().isEmpty()) {
                toastInfo("请选择公证处")
            }
//            else if (CommonUtil.compareListWithStr(countyListStr,addressChose.text)) {
////                toastInfo("请输入完整使用地区")
////            } else if (CommonUtil.compareListWithStr(langListStr,ywyyChose.text)) {
////                toastInfo("请输入完整译文语言")
////            }
            else if (!countyListStr.contains(addressChose.text)) {
                toastInfo("请输入完整使用地区")
            } else if (!langListStr.contains(ywyyChose.text)) {
                toastInfo("请输入完整译文语言")
            }
            else {
                val isNullItems = initWithTwoParamsToString();
                if (isNullItems) {
                    toastInfo("至少选择一个公证事项")
                } else {
                    val userId: String? = (mActivity as NotarizationSelfActivity?)?.userInfo?.userId;
                    val name: String? = (mActivity as NotarizationSelfActivity?)?.realNameCard;
                    val useArea: String? = addressChose?.text.toString();
                    val useLanguage: String? = ywyyChose?.text.toString();
                    val purposeName: String? = "";
                    val notaryId: String? = (mActivity as NotarizationSelfActivity?)?.notaryId;
                    val notaryitems: String? = mNotaryitems;
                    val applyUsers: String? = mApplyUsers;
                    val mac: String? = MacUtils.getMacAddress(mActivity)
                    val remarks:String? = edit_input?.text.toString()
                    notarizationP?.addNotaryorder(userId, name, useArea, useLanguage, purposeName, notaryId, notaryitems, applyUsers, mac,"0",remarks);
                }
            }
        };
        tvUpStep?.setOnClickListener {
            currentActivity?.onNavigationItemSelected(0)
        }
        initByRecyclerViewGrid();
        initSomeDataOnPage();
        customSelectPicker = CustomSelectPicker(mActivity, this);

        if (TextUtils.isEmpty(CommonInfo.city) || TextUtils.isEmpty(CommonInfo.adcode) ||
                TextUtils.isEmpty(CommonInfo.longitude) || TextUtils.isEmpty(CommonInfo.latitude) ||
                TextUtils.isEmpty(CommonInfo.address)) {
            Log.d(TAG, "gps info less: " + CommonInfo.tostring())
        }
        city = CommonInfo.city
        adcode = CommonInfo.adcode
        longitude = CommonInfo.longitude
        latitude = CommonInfo.latitude
        address = CommonInfo.address

        addressChose.callback = AppCompatEditSpinner.SelectItemCallback() { values: String, index: Int ->
            if (indexSelect == 1) {
                addressChose.text = values;
                currCounty = countyList[index];
            }
        }

        ywyyChose.callback = AppCompatEditSpinner.SelectItemCallback() { values: String, index: Int ->
            if (indexSelect == 2) {
                ywyyChose.text = values;
                curLanguage = langList[index];
            }
        }

    }

    /**
     * GCSWRW5067
     * 迷你一体机修改自助公证页面
     */
    private fun initByRecyclerViewGrid() {
        Log.i(TAG, "initByRecyclerViewGrid")
        addressChose.text = "";
        ywyyChose.text = "";
        gzcChose.text = "";


        realName.text = "";
        sexs.text = "";
        birthday.text = "";
        cardId.text = "";
        mobilePhone.text = "";
        addressDetail.text = "";

        val layout = MyLayoutManager()
        layout.isAutoMeasureEnabled = true;//防止recyclerview高度为wrap时测量item高度0(一定要加这个属性，否则显示不出来）
        recyclerView.isNestedScrollingEnabled = true
        recyclerView.layoutManager = layout
        mAdapter = SelectedInnerTwoGridItemsAdapter(true, mActivity);
        recyclerView.adapter = mAdapter;
        mAdapter?.itemsList = itemList;
    }

    override fun lazyInit(view: View?, savedInstanceState: Bundle?) {

    }

    override fun getContentViewLayoutID(): Int {
        return if (AppConfig.RGBPID == "c013") {
            R.layout.fragment_self_step_two;
        } else {
            R.layout.fragment_self_step_two_portrait;
        }
    }

    override fun sendNotarizationSuccess(items: List<NotrayBean>?) {
    }


    override fun queryResultLangeuList(bean: BaseResponseBean?) {
        Log.i(TAG, "queryResultLangeuList")
        var languageList: List<LanguarBean> = bean?.parseList(LanguarBean::class.java) as List<LanguarBean>
        langList = languageList as ArrayList<LanguarBean>;
        indexSelect = 2;
        langListStr = ArrayList<String>();
        for (obj in langList) {
            langListStr.add(obj.name);
        }
        ywyyChose?.setItemData(langListStr);
    }

    override fun queryResultByOrderId(bean: OrdersNotaryEntity?) {

    }

    override fun queryResultBySetState(bean: OrderInfoEntity?) {

    }

    override fun queryResultNotarList(bean: BaseResponseBean?) {
        Log.i(TAG, "queryResultNotarList")
        var NotarHomeList: List<NotarhomeBean> = bean?.parseList(NotarhomeBean::class.java) as List<NotarhomeBean>
        notarHomeList = NotarHomeList as ArrayList<NotarhomeBean>;
    }

    override fun addResultSuccess(bean: String?) {
        Log.i(TAG, "addResultSuccess")
        (mActivity as NotarizationSelfActivity?)?.orderId = bean;
        currentActivity?.onNavigationItemSelected(2)
    }

    override fun queryResultCountyList(bean: BaseResponseBean?) {
        Log.i(TAG, "queryResultCountyList")
        var countList: List<CountyBean> = bean?.parseList(CountyBean::class.java) as List<CountyBean>
        countyList = countList as ArrayList<CountyBean>;

        indexSelect = 1;
        countyListStr = ArrayList<String>();
        for (obj in countyList) {
            countyListStr.add(obj.zh);
        }
//            customSelectPicker?.show(list);
        addressChose.setItemData(countyListStr)
    }

    override fun onSelectItems(values: String?, index: Int) {
        Log.i(TAG, "onSelectItems$index---$values")

        if (indexSelect == 3) {
            gzcChose.text = values;
            curGzs = notarHomeList[index];
        }
    }

    override fun outCancel() {
    }


    override fun getMacAddress(): String {
        return MacUtils.getMacAddress(mActivity)
    }

    override fun showNotary(notaryId: String?, success: Boolean) {
    }

    override fun showNotaryList(bean: NotarizationBean?) {
        if (bean?.getItems() != null) {
            showDeviceDialog(bean?.getItems() as List<DeviceInfo>)
        } else {
            showDeviceDialog(listOf())
        }
    }

    override fun getAccessPublicKeySuccess() {
    }

    override fun getAccessPublicKeyFail(error: String?) {
    }

    override fun mechineAddSuccess() {
    }

    override fun mechineAddFail() {
    }

    private fun showDeviceDialog(l: List<DeviceInfo>) {
        if (deviceDialog == null) {
            deviceDialog = DialogUtils.getInstance().getRightDialog(mActivity, true, R.layout.dialog_divers)
            var dialogDeviceRv: RecyclerView? = deviceDialog?.findViewById(R.id.rvDevices)
            changelocation = deviceDialog?.findViewById(R.id.change_location)
            if (!TextUtils.isEmpty(city))
                changelocation?.setText(city)
            dialogDeviceRv?.adapter = deviceAdapter
        }
        deviceAdapter?.addNewData(l)
//        deviceDialog?.setCancelable(false)
//        deviceDialog?.setCanceledOnTouchOutside(false)
        deviceDialog?.show()
    }

    fun changeLocation() {
        if (null != deviceDialog && deviceDialog?.isShowing == true)
            deviceDialog?.dismiss()
        var pop = com.gc.notarizationpc.utils.PopwindowUtil()
        pop?.showpop(mActivity, mActivity.findViewById(R.id.self_notarization), mActivity)
    }

    private fun dismissDialog() {
        if (deviceDialog != null && deviceDialog?.isShowing == true) {
            deviceDialog?.dismiss()
        }
    }

    fun change_location_text(newcity: String?) {
        if (deviceDialog != null)
            deviceDialog?.show()
        //根据城市获取地区码和经纬度
        Thread(Runnable {
            try {
                var ll = MapUtils.getCoordinate(newcity)
                longitude = ll[0].toString()
                latitude = ll[1].toString()
                adcode = CommonUtil.getcode(mActivity, newcity)
                if (changelocation != null && !TextUtils.isEmpty(latitude) && !TextUtils.isEmpty(latitude)) {
                    //有网络 获取到经纬度的条件下 赋值  刷新
                    if (newcity != null) {
                        this.city = newcity
                    }
                    REFRESH_NOTARIZATION?.let { handler.sendEmptyMessage(it) }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }).start()

    }


    override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
        dismissDialog()
        var device: DeviceInfo? = adapter.getItem(position) as DeviceInfo?
//        if (!"3".equals(device?.institutionType))
//            isConfirm = true
//        notaryId = device?.notarialId
        Log.d("zpzp", (mActivity as NotarizationSelfActivity?)?.notaryId + "  " +
                device?.unitGuid)
        gzcChose.text = device?.notarialName
        (mActivity as NotarizationSelfActivity?)?.notaryId = device?.unitGuid
        AccountManger.getInstance(mActivity)?.updateNotaryId(device?.unitGuid)
    }

    override fun onDestroy() {
        dismissDialog()
        if (handler != null)
            REFRESH_NOTARIZATION?.let { handler?.removeMessages(it) }
        super.onDestroy()
    }
}