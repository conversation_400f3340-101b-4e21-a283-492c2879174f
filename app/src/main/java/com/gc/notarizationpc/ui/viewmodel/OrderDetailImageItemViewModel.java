package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.VideoOrderDetailModel;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * Created by goldze on 2017/7/17.
 */

public class OrderDetailImageItemViewModel extends ItemViewModel<OrderDetailViewModel> {


    public ObservableField<VideoOrderDetailModel.MaterialVoListDTO> entity = new ObservableField<>();

    public OrderDetailImageItemViewModel(@NonNull OrderDetailViewModel viewModel, VideoOrderDetailModel.MaterialVoListDTO entity) {
        super(viewModel);
        Log.e("FragmentGridItemViewModel", "FragmentGridItemViewModel");
        this.entity.set(entity);
    }

    // 图片预览
    public BindingCommand imagePreView = new BindingCommand(() -> {
        viewModel.imagePreView(this.entity.get());
    });


}
