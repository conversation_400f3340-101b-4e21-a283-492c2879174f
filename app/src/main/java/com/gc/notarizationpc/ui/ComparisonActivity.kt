package com.gc.notarizationpc.ui

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.SurfaceTexture
import android.hardware.usb.UsbDevice
import android.media.AudioManager
import android.media.SoundPool
import android.os.Handler
import android.util.Log
import android.view.TextureView.SurfaceTextureListener
import android.view.View
import android.view.ViewTreeObserver
import androidx.lifecycle.lifecycleScope
import com.eloam.gaopaiyi.util.UVCCameraUtil
import com.example.framwork.baseapp.AppManager
import com.example.framwork.utils.DLog
import com.example.framwork.utils.MyLogUtils
import com.example.framwork.utils.NetUtil
import com.example.framwork.utils.SPUtils
import com.example.scarx.idcardreader.utils.IdCardRenderUtils
import com.example.scarx.idcardreader.utils.imp.MyCallBack
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.AppConfig
import com.gc.notarizationpc.common.BaseActivity
import com.gc.notarizationpc.model.ImageBean
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter
import com.serenegiant.arcface.util.ImageStack
import com.serenegiant.arcface.util.ImageUtils
import com.serenegiant.usb.IFrameCallback
import com.serenegiant.usb.USBMonitor
import com.zkteco.android.IDReader.IDPhotoHelper
import com.zkteco.android.IDReader.WLTService
import com.zkteco.android.biometric.core.device.ParameterHelper
import com.zkteco.android.biometric.core.device.TransportType
import com.zkteco.android.biometric.core.utils.LogHelper
import com.zkteco.android.biometric.core.utils.ToolUtils
import com.zkteco.android.biometric.module.idcard.IDCardReaderFactory
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo
import kotlinx.android.synthetic.main.activity_comparison.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.joda.time.format.DateTimeFormat
import java.nio.ByteBuffer
import java.util.concurrent.CountDownLatch

/**
 * @author: wjw
 * @create：2020/7/14
 * @describe：
 */
class ComparisonActivity : BaseActivity(), ViewTreeObserver.OnGlobalLayoutListener, NotarizationPresenter.IUpdateImageView, NotarizationPresenter.IFaceView {
    companion object {
        const val TAG = "ComparisonActivity"
    }

    private var mUSBMonitor: USBMonitor? = null
    private val idCardReaderUtils: IdCardRenderUtils = IdCardRenderUtils()
    private var soundPool: SoundPool = SoundPool(100, AudioManager.STREAM_MUSIC, 0) //构建对象
    private var hasCardInfo = false
    private var isRunning = true
    private var isActivity = true
    private var isNoCard = true
    private var idcardinfo: IDCardInfo? = null
    private var idCardBmp: Bitmap? = null
    private var readSuccess: Int? = null
    private var readTips: Int? = null
    private var validateSuccess: Int? = null
    private var validateFail: Int? = null
    private var retry: Int? = null
    private var faceCamera: Int? = null
    private var toWhere: String? = "mobilePage"
    private var type: Int? = 1
    private var faceImagePath: String? = null

    private var updateP: NotarizationPresenter? = null
    private var faceP: NotarizationPresenter? = null

    // -----------[人脸采集相关]-----------------------------------------------------
    //人脸检测
    val imgKjStack = ImageStack(640, 480)
    private var fromVideoConsult: Boolean = false


    override fun getIntentData(intent: Intent?) {
        toWhere = intent?.getStringExtra("toWhere")
        type = intent?.getIntExtra("type", 1);
        Log.i(TAG, "toWhere = " + toWhere.toString() + " type = " + type)
        fromVideoConsult = intent!!.getBooleanExtra("fromVideoConsult", false);
    }

    override fun getContentViewLayoutID(): Int {
        return if (AppConfig.RGBPID == "c013") {
            Log.i(TAG, "getContentViewLayoutID hengping")
            MyLogUtils.i(TAG, "横屏")
            R.layout.activity_comparison
        } else {
            Log.i(TAG, "getContentViewLayoutID shuping")
            MyLogUtils.i(TAG, "竖屏")
            R.layout.activity_comparison_portrait
        }
    }

    override fun initViewsAndEvents() {
        updateP = NotarizationPresenter(mActivity, this as NotarizationPresenter.IUpdateImageView)
        faceP = NotarizationPresenter(mActivity, this as NotarizationPresenter.IFaceView)

        initFaceView()
        previewView.surfaceTextureListener = object : SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                setUVCCameraListener()
                loadSoundRes()
            }

            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {}
            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                return false
            }

            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {}
        }
        startRecognizeUser()
    }


    private fun initFaceView() {
        Log.i(TAG, "initFaceView")
        previewView.setAspectRatio(640, 480)
        //在布局结束后才做初始化操作
        previewView.viewTreeObserver.addOnGlobalLayoutListener(this)
        backClicked.setOnClickListener { _ ->
            AppManager.getAppManager().finishActivity(ComparisonActivity::class.java)
        }
    }


    override fun onStart() {
        super.onStart()
        Log.i(TAG, "onStart")
        isActivity = true


    }

    /**
     * 加载语音文件
     */
    private fun loadSoundRes() {
        Log.i(TAG, "loadSoundRes")
        lifecycleScope.launch(Dispatchers.IO) {
            //咚
            readSuccess = soundPool.load(mActivity, R.raw.readcard_success, 1)
            //请将身份证放置于阅读器上
            readTips = soundPool.load(mActivity, R.raw.idcardread, 1)
            //请正对摄像头
            faceCamera = soundPool.load(mActivity, R.raw.face_camera, 1)
            //请重试
            retry = soundPool.load(mActivity, R.raw.retry, 1)
            //比对成功
            validateSuccess = soundPool.load(mActivity, R.raw.validate_success, 1)
            //比对失败
            validateFail = soundPool.load(mActivity, R.raw.validate_fail, 1)
        }
    }

    /**
     * 1: 读卡成功   2：请将身份证放置到阅读器上  3：请正对摄像头   4：比对失败 5：比对成功  6：请重试
     */
    fun playSound(index: Int) {
        Log.i(TAG, "playSound index$index")
        when (index) {
            1 -> soundPool.play(readSuccess!!, 1f, 1f, 1, 0, 1f)
            2 -> soundPool.play(readTips!!, 1f, 1f, 1, 0, 1f)
            3 -> soundPool.play(faceCamera!!, 1f, 1f, 1, 0, 1f)
            4 -> soundPool.play(validateFail!!, 1f, 1f, 1, 0, 1f)
            5 -> soundPool.play(validateSuccess!!, 1f, 1f, 1, 0, 1f)
            6 -> soundPool.play(retry!!, 1f, 1f, 1, 0, 1f)
        }
    }

    private fun startIdCardRead() {
        Log.i(TAG, "startIdCardRead")
        LogHelper.setLevel(8)//身份证打印等级
        val ideograms = HashMap<String, Any>()
        ideograms[ParameterHelper.PARAM_KEY_VID] = 1024
        ideograms[ParameterHelper.PARAM_KEY_PID] = 50010
        val idCardReader = IDCardReaderFactory.createIDCardReader(
                ToolUtils.getApplicationContext(),
                TransportType.USB,
                ideograms
        )
        idCardReaderUtils.readerIdCard(idCardReader, CountDownLatch(1), object : MyCallBack {
            override fun onSuccess(idCardInfo: IDCardInfo?) {
                Log.i(TAG, "readerIdCard success 读卡成功")
                if (isNoCard && null == idcardinfo) {
                    runOnUiThread {
                        l_result.visibility = View.VISIBLE
                    }
                    idcardinfo = idCardInfo
                    playSound(1)
                    isNoCard = false
                    showIdCardInfo(idcardinfo!!)
                }
            }

            override fun onFail(error: String?) {
                MyLogUtils.i(TAG, "身份证读卡器出现异常")
                toastError("身份证读卡器出现异常,请检查设备")
                Log.i(TAG, "readerIdCard fail")
            }

            override fun onRequestDevicePermission() {
            }

            override fun onNoCards() {
                isNoCard = true
            }
        })
    }

    var hasCamra = false
    private fun setUVCCameraListener() {
        Log.i(TAG, "setUVCCameraListener")
        mUSBMonitor =
                UVCCameraUtil.initUSBMonitor(mActivity, object : UVCCameraUtil.OnMyDevConnectListener {
                    override fun onConnectDev(
                            device: UsbDevice,
                            ctrlBlock: USBMonitor.UsbControlBlock
                    ) {
                        try {
                            hasCamra = true
                            val pid = String.format("%x", device.productId)
                            if (pid == AppConfig.RGBPID) {
                                //打开彩色摄像头
                                UVCCameraUtil.openRGBCamera(previewView, ctrlBlock, object : IFrameCallback {
                                    override fun onFrame(frame: ByteBuffer?) {
                                        Log.i(TAG, "open camera success")
                                        if (frame != null) {
                                            Log.i(TAG, "start pushImageInfo")
                                            imgKjStack.pushImageInfo(frame, System.currentTimeMillis())
                                        }
                                    }

                                    override fun onFail() {
                                        Log.i(TAG, "摄像头异常")
                                        toastError("摄像头异常")
                                    }

                                })

                                //语音播报  请将身份证放置于阅读器上
                                Log.i(TAG, "start play sound")
                                lifecycleScope.launch(Dispatchers.IO) {
                                    delay(100L)
                                    withContext(Dispatchers.Main) {
                                        playSound(2)
                                        Handler().postDelayed(Runnable {  //开启身份证识别
                                            if (idCardReaderUtils.isbStoped()) {
                                                idCardReaderUtils.setbStoped(false)
                                            } else {
                                                startIdCardRead()
                                            }
                                        }, 3500)

                                    }
                                }
//                                UVCCameraUtil.openRGBCamera(previewView, ctrlBlock, object : IFrameCallback {
//                                    override fun onFrame(frame: ByteBuffer?) {
//                                        Log.i(TAG, "open camera success")
//                                        if (frame != null) {
//                                            Log.i(TAG, "start pushImageInfo")
//                                            imgKjStack.pushImageInfo(frame, System.currentTimeMillis())
//                                        }
//                                    }
//
//                                    override fun onFail() {
//                                        toastError("摄像头异常")
//                                    }
//
//                                })
                            } else {
                                toastError("未找到前置摄像头")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, e.message)
                        }
                    }
                })
        Handler().postDelayed(Runnable { if (!hasCamra) toastError("摄像头异常") }, 30000)
        mUSBMonitor?.register()
        UVCCameraUtil.requestPermission(mActivity, AppConfig.RGBPID, 300, mUSBMonitor!!)
    }


    private fun startRecognizeUser() {
        Log.i(TAG, "startRecognizeUser")
        imgKjStack.clearAll()
        lifecycleScope.launch(Dispatchers.IO) {
            delay(2000L)
            while (isActivity) {
                // 清空缓存记录
                if (hasCardInfo) {
                    val imgInfoKj = imgKjStack.pullImageInfo()
                    if (isRunning && imgInfoKj.isNew) {
                        val dataKj = imgInfoKj.data
                        val bitmapH = ImageUtils.getInstance().getBitmapFromYuv420(dataKj, 640, 480)
                        if (bitmapH != null) {
                            isRunning = false
                            withContext(Dispatchers.Main) {
                                headerImg.setImageBitmap(bitmapH)
                                updateP?.updateFace(bitmapH)
                            }
                        }
                        delay(100L)
                    }
                }
                delay(100L)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    fun showIdCardInfo(idcardinfo: IDCardInfo) {
        Log.i(TAG, "showIdCardInfo")
        lifecycleScope.launch {

            cardInfoTv_name.text = idcardinfo.name
            cardInfoTv_sex.text = idcardinfo.sex
            cardInfoTv_nation.text = idcardinfo.nation
            cardInfoTv_idcard.text = idcardinfo.id
            cardInfoTv_address.text = idcardinfo.address
            cardInfoTv_depart.text = idcardinfo.depart
            cardInfoTv_validityTime.text = idcardinfo.validityTime

            userInfo?.birthday = dateStringToString(idcardinfo.birth)
            if (idcardinfo.sex == "男") {
                userInfo?.gender = 1
            } else {
                userInfo?.gender = 2
            }
            userInfo?.idCard = idcardinfo.id
            userInfo?.nation = idcardinfo.nation
            userInfo?.name = idcardinfo.name
            userInfo?.address = idcardinfo.address
            val idCardImgbuf = ByteArray(WLTService.imgLength)
            val resultTok = WLTService.wlt2Bmp(idcardinfo.photo, idCardImgbuf)
            if (resultTok == 1) {
                idCardBmp = IDPhotoHelper.Bgr2Bitmap(idCardImgbuf)
                idImg.setImageBitmap(idCardBmp)
            }

            delay(500L)
            playSound(3)
            delay(3000L)
            hasCardInfo = true
        }
    }

    override fun onStop() {
        Log.i(TAG, "onStop")
        isActivity = false
        idCardReaderUtils.setbStoped(true)
        super.onStop()
    }

    override fun onDestroy() {
        Log.i(TAG, "onDestroy")
        isActivity = false
        try {
            soundPool.release()
            UVCCameraUtil.releaseRGBCamera()
            UVCCameraUtil.releaseIRCamera()
            //增加关闭高拍仪
            UVCCameraUtil.releaseHighCamera()
            mUSBMonitor?.unregister()
            mUSBMonitor?.destroy()
            mUSBMonitor = null
        } catch (e: Exception) {
            Log.e(TAG, e.message);
        }
        super.onDestroy()
    }

    override fun onGlobalLayout() {
        Log.i(TAG, "onGlobalLayout")
        previewView.viewTreeObserver.removeOnGlobalLayoutListener(this)
    }

    private fun dateStringToString(date: String?): String {
        Log.i(TAG, "dateStringToString")
        val dtf = DateTimeFormat.forPattern("yyyy年MM月dd日")
        val dateTime = dtf.parseDateTime(date)
        return dateTime?.toString("yyyy-MM-dd")!!
    }

    override fun faceComparisonSuccess(annexId: String) {
        Log.i("Test", "faceComparisonSuccess===$annexId")
        SPUtils.getInstance().put(this, "annexId", annexId)
        MyLogUtils.i(TAG, "人脸认证成功")
        lifecycleScope.launch(Dispatchers.IO) {
            delay(100L)
            playSound(5)
            delay(2000L)
            withContext(Dispatchers.Main) {
                if (toWhere?.equals("RemoteEmpowerPage")!!) {
                    readyGo(RemoteModelActivity::class.java)
                    finish()
                } else {
                    val intent: Intent;
                    if (fromVideoConsult) {
                        intent = Intent(mActivity, VideoConsultMobileActivity::class.java)
                        //公证咨询 公正类型为10
                        intent?.putExtra("type", 10);
                    } else {
                        intent = Intent(mActivity, MobileActivity::class.java)
                        //视频公证 公正类型为2 金融富强 3
                        intent?.putExtra("type", type);
                    }
                    startActivity(intent)
                    finish()
                }
            }
        }

    }

    override fun faceComparisonFail() {
        Log.i(TAG, "faceComparisonFail")
        MyLogUtils.i(TAG, "人脸认证失败")
        lifecycleScope.launch(Dispatchers.IO) {
            delay(100L)
            playSound(4)
            delay(2000L)
            isRunning = true
        }
    }

    override fun updateImageSuccess(bean: ImageBean?) {
        Log.i(TAG, "updateImageSuccess")
        faceImagePath = bean?.filePath
        DLog.d("测试：toWhere：$toWhere")
        if (toWhere?.equals("mobilePage") == true) {
            faceP?.faceComparison(userInfo?.name, userInfo?.idCard, faceImagePath)
        } else if (toWhere?.equals("CertReportPage") == true) {
            val intent = Intent(mActivity, ShowCertreprotActivity::class.java)
            intent.putExtra("idCard", userInfo?.idCard)
            intent.putExtra("name", userInfo?.name)
            intent.putExtra("image", faceImagePath)
            startActivity(intent)
        } else if (toWhere?.equals("RemoteEmpowerPage") == true) {
            faceP?.faceComparison(userInfo?.name, userInfo?.idCard, faceImagePath)
        }
    }

    override fun updateImageFail() {
        Log.i(TAG, "updateImageFail")
        MyLogUtils.i(TAG, "上传图片失败")
        lifecycleScope.launch(Dispatchers.IO) {
            delay(100L)
            playSound(4)
            delay(2000L)
            isRunning = true
        }
    }
}