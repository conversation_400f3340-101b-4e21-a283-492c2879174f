package com.gc.notarizationpc.ui;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSON;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.example.framwork.utils.NetUtil;
import com.gc.notarizationpc.R;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.common.BaseActivity;
import com.gc.notarizationpc.model.FrequentlyQuestion;
import com.gc.notarizationpc.model.NotarizationInfo;
import com.gc.notarizationpc.model.SocketMessageInfo;
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter;
import com.gc.notarizationpc.utils.MyOnclickClickListener;
import com.gc.notarizationpc.utils.SPMgr;
import com.gc.notarizationpc.websocket.MyMqttService;
import com.gc.notarizationpc.websocket.WebSocketCallBack;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;

import org.jetbrains.annotations.Nullable;

import java.util.List;

import es.dmoral.toasty.Toasty;

//视频咨询首界面
public class VideoConsultActivity extends BaseActivity implements NotarizationPresenter.INotarizationView, NotarizationPresenter.IFAQView,
        WebSocketCallBack {
    private final String TAG = VideoConsultActivity.class.getSimpleName();
    private View linearPic;
    private RecyclerView recyclerView;
    private SmartRefreshLayout refreshLayout;
    private MyMqttService myMqttService;
    private Boolean isWaiting = true;
    private NotarizationPresenter notarizationP = null;
    protected int limit = 10;       //条数
    protected int page = 1;         //页码
    private LinearLayout rnlAnswer;

    private List<FrequentlyQuestion.RecordsDTO> recordsDTOS;
    private FAQListItemAdapter faqListItemAdapter;
    private TextView tvQuestion;
    private TextView tvAnswer;
    private boolean jigouyichang, hasCardModule;
    private String notaryId;

    @SuppressLint("HandlerLeak")
    private Handler mHandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == 0x01) {

            }
        }
    };


    @Override
    protected void getIntentData(Intent intent) {
        jigouyichang = intent.getBooleanExtra("jigouyichang", false);
        notaryId = intent.getStringExtra("notaryId");
        hasCardModule = intent.getBooleanExtra("hasCardModule", hasCardModule);
    }

    @Override
    protected int getContentViewLayoutID() {
        return R.layout.activity_video_consult;
    }

    @Override
    protected void initViewsAndEvents() {
//        recordsDTOS = new ArrayList<>();
        tvAnswer = findViewById(R.id.msg_others_tv_text);
        tvQuestion = findViewById(R.id.msg_owner_tv_text);
        recyclerView = findViewById(R.id.rv_faq);
        refreshLayout = findViewById(R.id.refreshLayout);
        rnlAnswer = findViewById(R.id.rnl_answer);
        resetRecycler();
        notarizationP = new NotarizationPresenter(mActivity, this, this);
        if (getUserInfo() == null) {
            return;
        }

//        myMqttService = new MyMqttService(this, ".topic.consulting.collect." + getUserInfo().getIdCard(), ".topic.consulting." + getUserInfo().getIdCard(), AppConfig.HOST, this);
    }

    public void finish(View view) {
        finish();
    }

    @Override
    protected void onResume() {
        super.onResume();
        initDate(1);
    }

    public void changeQuestion(View view) {
        page++;
        if (page * limit <= total) {
        } else {
            page = 1;
        }
        initDate(page);
    }

    private void resetRecycler() {
        Log.i(TAG, "resetRecycler");
        LinearLayoutManager gridLayoutManager = new LinearLayoutManager(this);
        gridLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        recyclerView.setLayoutManager(gridLayoutManager);
        faqListItemAdapter = new FAQListItemAdapter(null) {

            @Override
            protected void convert(@NonNull BaseViewHolder helper, FrequentlyQuestion.RecordsDTO item) {
                super.convert(helper, item);
                if (item == null) {
                    return;
                }
                helper.setText(R.id.tv_faq, item.getQuestion());

                helper.getView(R.id.tv_faq).setOnClickListener(new MyOnclickClickListener() {
                    @Override
                    public void mOnClick(View v) {
                        tvQuestion.setText(item.getQuestion());
                        tvAnswer.setText(item.getAnswer());
                        rnlAnswer.setVisibility(View.VISIBLE);
                    }
                });
            }

        };
        recyclerView.setAdapter(faqListItemAdapter);
    }


    public class FAQListItemAdapter extends BaseQuickAdapter<FrequentlyQuestion.RecordsDTO, BaseViewHolder> {

        public FAQListItemAdapter(@Nullable List<FrequentlyQuestion.RecordsDTO> data) {
            super(R.layout.item_list_faq, data);
        }


        @Override
        protected void convert(@NonNull BaseViewHolder baseViewHolder, FrequentlyQuestion.RecordsDTO recordsDTO) {

        }
    }

    private void initDate(int page) {
        notarizationP.selectPage(limit, page);
    }

    private NotarizationInfo notarizationInfo;

    @Override
    public void sendNotarizationSuccess(NotarizationInfo info) {
        if (null != info) {
            notarizationInfo = info;
            waitNotarization();
            myMqttService.start();
        }
    }

    private int total;

    @Override
    public void getFaqSuccess(FrequentlyQuestion info) {
        if (info == null || info.getRecords() == null) {
            return;
        }
        if (info.getRecords() != null) {
            total = info.getTotal();
            if (info.getRecords().size() == 0) {
                rnlAnswer.setVisibility(View.GONE);
            } else {
                rnlAnswer.setVisibility(View.VISIBLE);
            }
        } else {
            rnlAnswer.setVisibility(View.GONE);
        }
        recordsDTOS = info.getRecords();
        initContent(recordsDTOS);
    }

    private void initContent(List<FrequentlyQuestion.RecordsDTO> remoteBean) {
        if (remoteBean != null && remoteBean.size() > 0) {
            Log.i(TAG, "initContent");
            faqListItemAdapter.setList(remoteBean);
            faqListItemAdapter.notifyDataSetChanged();
            tvQuestion.setText(remoteBean.get(0).getQuestion());
            tvAnswer.setText(remoteBean.get(0).getAnswer());
        }
    }

    @Override
    public void getFaqFail(String msg) {
        toastError(msg);
    }

    public interface ConfirmClickInterface {
        void onConfirm();

        void onCancel();
    }

    public void gotoVideoConsult(View view) {

//        String notaryId = (String) SPUtils.getInstance().get(this, "notaryId", "");
//        //判断是否是公证处 1,是公证处  3是法援
//        if ("3" == SPUtils.getInstance().get(mActivity, "institutionType", "1") || TextUtils.isEmpty(notaryId)) {
//            toastInfo("请先选择公证处");
//            return;
//        } else
//            notarizationP.sendNotarization(getUserInfo(), notaryId, CommonInfo.address, CommonInfo.longitude, CommonInfo.latitude, AppConfig.SN, 10);

        showProgress();
        new Thread(new Runnable() {
            @Override
            public void run() {
                if (!NetUtil.ping()) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            hideProgress();
                            toastError("网络异常");
                        }
                    });
                } else {
                    if (jigouyichang) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                hideProgress();
                                toastError("未查询到服务公证处，请联系客服人员：4008001820");
                            }
                        });
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                hideProgress();
                                if (notaryId != null) {
                                    hasCardModule = true;
                                    if (!hasCardModule) {
                                        Toast.makeText(mActivity, "缺少身份证模块，无法进行该操作！", Toast.LENGTH_LONG).show();
                                    } else {
                                        Intent intent = new Intent(VideoConsultActivity.this, ComparisonActivity.class);
                                        intent.putExtra("fromVideoConsult", true);
                                        intent.putExtra("toWhere", "mobilePage");
                                        intent.putExtra("type", 10);
                                        startActivity(intent);
                                    }
                                } else {
                                    Toasty.error(VideoConsultActivity.this, "请先注册公证处").show();
                                    finish();
//                                    showOneDialog(toastTxt + "！\n" + getString(R.string.online_phone), "知道了", new DialogInterface.OnClickListener() {
//                                        @Override
//                                        public void onClick(DialogInterface dialog, int which) {
//                                            dismissQuickDialog();
//                                            queryP.getNotary();
//                                        }
//                                    });
                                }
                            }
                        });
                    }
                }
            }
        }).start();
    }

    private void waitNotarization() {
        Log.i(TAG, "waitNotarization");
        showConnnectingDialog();
        if (myMqttService != null) {
            myMqttService.start();
        }
    }

    //展示视频咨询等待连接弹框
    private void showConnnectingDialog() {
        showConnectVideoConsult(this, new ConfirmClickInterface() {
            @Override
            public void onConfirm() {
                if (isWaiting == true) {
                    myMqttService.unSubscribe();
                    notarizationP.stopNotarization(notarizationInfo.unitGuid);
                    if (dialog != null && dialog.isShowing()) {
                        dialog.dismiss();
                    }
                    //结束等待
                    finish();
                }

            }

            @Override
            public void onCancel() {

            }
        });
    }

    public void onSocketMessage(String next) {
        Log.i(TAG, "onSocketMessage");
        try {
            if (!TextUtils.isEmpty(next) && next.contains("{") == true) {
                SocketMessageInfo info = JSON.parseObject(next, SocketMessageInfo.class);
                if (info != null && "dontWiting".equals(info.getInfo())) {
                    isWaiting = false;
                    if (null != info.getRoomId() && null != info.getGreffierName()) {
                        goVideoNotarization(info.getGreffierName(), info.getRoomId());
                    }
                } else {
                    dialog.dismiss();
                    toastError("未接收到房间号");
                    Log.i(TAG, "未接收到房间号");
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void goVideoNotarization(String greffierName, String roomId) {
        Log.i(TAG, "goVideoNotarization");
        myMqttService.unSubscribe();
        dialog.dismiss();
        Log.i(TAG, "进入公证咨询页面");
        Intent intent = null;
        if (!TextUtils.isEmpty(SPMgr.getInstance(mActivity).getElement(AppConfig.LOCAL_USB_NAME, "")))
            intent = new Intent(mActivity, VideoConsultNotarizationActivity.class);
//        else
//        intent = Intent(mActivity, VideoNotarizationUSBActivity::class.java)

        intent.putExtra("roomId", roomId);
        intent.putExtra("greffierName", greffierName);
        intent.putExtra("notarizationInfo", notarizationInfo);
        readyGo(intent);
        finish();
    }


    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy");
        super.onDestroy();
    }


    AlertDialog dialog;

    //正在连接视频咨询
    public void showConnectVideoConsult(final Context context, ConfirmClickInterface confirmClick) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.dialog_connect_video_consult, null);

        TextView mTvDisConnect = view.findViewById(R.id.tv_disconnect);
        TextView tvSecond = view.findViewWithTag(R.id.second);

        dialog = builder.create();
        dialog.show();

        CountDownTimer countTimer = new CountDownTimer(60 * 60 * 24 * 1000, 1000) {
            @SuppressLint("SetTextI18n")
            @Override
            public void onTick(long millisUntilFinished) {
                tvSecond.setText(millisUntilFinished / 1000 + "s");
            }

            @Override
            public void onFinish() {

            }
        };
        countTimer.start();// 开始计时

        Window window = dialog.getWindow();
        window.getDecorView().setBackgroundColor(context.getResources().getColor(R.color.transparent));
        window.setGravity(Gravity.CENTER);
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.gravity = Gravity.CENTER;
//            lp.width = DpUtil.dp2px(280);
        //固定高度，在不同机型会存在适配问题
        lp.width = LinearLayout.LayoutParams.WRAP_CONTENT;
        lp.height = LinearLayout.LayoutParams.WRAP_CONTENT;
        window.setAttributes(lp);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);

        mTvDisConnect.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (confirmClick != null) {
                    confirmClick.onConfirm();
                }
                dialog.dismiss();
            }
        });

    }

//    @Override
//    public void selectPageSuccess(CarOrderBean carOrderBean) {
//        if (carOrderBean.getRecords() != null) {
//            if (page == 1 && carOrderBean.getRecords().size() == 0) {
//                linearPic.setVisibility(View.VISIBLE);
//            } else {
//                linearPic.setVisibility(View.GONE);
//            }
//        } else {
//            linearPic.setVisibility(View.VISIBLE);
//        }
//        if (flag == 101) {
//            recordsDTOS.addAll(carOrderBean.getRecords());
//        } else {
//            recordsDTOS = carOrderBean.getRecords();
//        }
//        if (recordsDTOS.size() == 0) {
//            linearPic.setVisibility(View.VISIBLE);
//        }
//        initContent(recordsDTOS);
//    }

//    public class CarOrderListItemAdapter extends BaseQuickAdapter<CarOrderBean.RecordsDTO, BaseViewHolder> implements LoadMoreModule {
//
//        public CarOrderListItemAdapter(@Nullable List<CarOrderBean.RecordsDTO> data) {
//            super(R.layout.list_item_car_orders, data);
//        }
//
//        @Override
//        protected void convert(@NonNull BaseViewHolder helper, CarOrderBean.RecordsDTO item) {
//
//        }
//    }


//    private void initContent(List<CarOrderBean.RecordsDTO> remoteBean) {
//        Log.i(TAG, "initContent");
//        carOrderListItemAdapter.setList(remoteBean);
//        carOrderListItemAdapter.notifyDataSetChanged();
//        finishRefresh();
//    }


//    @Override
//    public void selectPageFail(String error) {
//        Toasty.error(this, error).show();
//        refreshLayout.finishRefresh();
//    }
}
