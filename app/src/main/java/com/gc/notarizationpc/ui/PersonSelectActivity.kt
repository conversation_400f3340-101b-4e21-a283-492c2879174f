package com.gc.notarizationpc.ui

import android.content.Intent
import android.view.View
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseActivity
import kotlinx.android.synthetic.main.activity_person_select.*

class PersonSelectActivity : BaseActivity() {

    private var toWhere: String? = "videoClick"


    override fun getIntentData(intent: Intent?) {
        toWhere = intent?.getStringExtra("toWhere");
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_person_select
    }

    override fun initViewsAndEvents() {
        isPersonView.isSelected = true
    }

    fun nextClick(view: View?) {
        val intent = Intent(this, DescriptionActivity::class.java)
        intent.putExtra("toWhere",toWhere);
        startActivity(intent)
        finish()
    }

    fun personClick(view: View?) {
        isPersonView.isSelected = true
        isProxyView.isSelected = false
    }

    fun proxyClick(view: View?) {
        toastInfo("功能升级中...")
        isPersonView.isSelected = true
        isProxyView.isSelected = false
    }

}