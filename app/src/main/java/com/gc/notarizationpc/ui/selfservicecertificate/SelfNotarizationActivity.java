package com.gc.notarizationpc.ui.selfservicecertificate;

import android.graphics.Bitmap;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Bundle;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.View;

import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.example.scarx.idcardreader.utils.IdCardRenderUtils;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivitySelfNotarizationBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.data.model.response.NotaryMatterItem;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.fragment.SelfApplyInfoFragment;
import com.gc.notarizationpc.ui.fragment.SelfServiceHasBeenFinishedFragment;
import com.gc.notarizationpc.ui.fragment.SelfServiceReadAndSignFragment;
import com.gc.notarizationpc.ui.fragment.SelfServiceUploadMaterialFragment;
import com.gc.notarizationpc.ui.viewmodel.SelfNotarizationViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.widget.NonScrollableViewPager;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.base.BaseFragment;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description: 自助办证
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class SelfNotarizationActivity extends BaseActivity<ActivitySelfNotarizationBinding, SelfNotarizationViewModel> {
    private String TAG = "Test";
    private IdCardRenderUtils idCardReaderUtils = new IdCardRenderUtils();
    private SoundPool soundPool = new SoundPool(100, AudioManager.STREAM_MUSIC, 0);
    private boolean isActivity = true;
    private boolean isNoCard = true;
    private IDCardInfo idcardinfo = null;
    private Bitmap idCardBmp = null;
    private Integer readSuccess = null;
    private Integer readTips = null;
    private Integer validateSuccess = null;
    private Integer validateFail = null;
    private Integer retry = null;
    private Integer faceCamera = null;
    private boolean hasCardInfo = false;
    private String useType;//出国留学--1->学习 定居移民--2->定居 探亲旅游--3->探亲 商务劳务--5-->劳务 境外其他--4-->其他

    private FragmentManager fragmentManager;

    private List<BaseFragment> fragmentList;

    private BaseFragment fragment1;

    private BaseFragment fragment2;

    private BaseFragment fragment3;

    private BaseFragment fragment4;

    private Handler mHandler;

    public NonScrollableViewPager viewPager;


    public class MyPagerAdapter extends FragmentPagerAdapter {
        private List<BaseFragment> fragments;

        public MyPagerAdapter(FragmentManager fm, List<BaseFragment> fragments) {
            super(fm);
            this.fragments = fragments;
        }

        @Override
        public BaseFragment getItem(int position) {
            // 返回不同的Fragment页面
            return fragments.get(position);
        }

        @Override
        public int getCount() {
            return fragments.size();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            startActivity(HomeActivity.class);
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_self_notarization;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public SelfNotarizationViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(SelfNotarizationViewModel.class);
    }


    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }


    @Override
    protected void onStop() {
        super.onStop();

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });

        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                setResult(RESULT_OK);
                finish();
            }
        });
        useType = getIntent().getStringExtra("useType");
        String fragmentId = getIntent().getStringExtra("fragment_id");
        String applicationItem = getIntent().getStringExtra("applicationItem");
        Bundle mBundle = new Bundle();
        mBundle.putString("useType", useType);
        mBundle.putString("fragment_id", fragmentId);
        fragmentManager = getSupportFragmentManager();
        fragmentList = new ArrayList<>();

        fragment1 = new SelfApplyInfoFragment();
        fragment1.setArguments(mBundle);
        fragment2 = new SelfServiceUploadMaterialFragment();
        fragment2.setArguments(mBundle);
        fragment3 = new SelfServiceReadAndSignFragment();
        fragment3.setArguments(mBundle);
//        fragment4 = new SelfServiceHasBeenFinishedFragment();
//        fragment4.setArguments(mBundle);
        fragmentList.add(fragment1);
        fragmentList.add(fragment2);
        fragmentList.add(fragment3);
//        fragmentList.add(fragment4);
        viewPager = binding.viewpager;
        MyPagerAdapter adapter = new MyPagerAdapter(fragmentManager, fragmentList);
        viewPager.setAdapter(adapter);
        viewPager.setOffscreenPageLimit(3);
        if (fragmentId == null) {
            viewPager.setCurrentItem(0, false);
        } else if (fragmentId != null && fragmentId.equals("fragment_0")) {
            viewPager.setCurrentItem(0, false);
        } else if (fragmentId != null && fragmentId.equals("fragment_1")) {
            viewPager.setCurrentItem(1, false);
//            ((SelfServiceUploadMaterialFragment)fragment2).setLastStepGone();
        } else if (fragmentId != null && fragmentId.equals("fragment_2")) {
            viewPager.setCurrentItem(2, false);
//            ((SelfServiceReadAndSignFragment)fragment3).setLastStepGone();
        }

//        if (applicationItem != null && !applicationItem.isEmpty()) {
//            List<NotaryMatterItem.MattersInfosDTO> matterList = new ArrayList<>();
//            List<Map<String, Object>> applicationItems = new Gson().fromJson(applicationItem, new TypeToken<List<Map<String, String>>>() {
//            }.getType());
//            if (applicationItems != null && applicationItems.size() > 0) {
//                for (Map<String, Object> data : applicationItems) {
//                    NotaryMatterItem.MattersInfosDTO mattersInfosDTO = new NotaryMatterItem.MattersInfosDTO();
//                    mattersInfosDTO.setMattersName(data.get("mattersName").toString());
//                    mattersInfosDTO.setNotarizationNumber(data.get("notarizationNumber").toString());
//                    mattersInfosDTO.setMattersId(data.get("detailMattersId").toString());
//                    mattersInfosDTO.setCopyFee(data.get("copyFee") == null ? 0 : Integer.valueOf(data.get("copyFee").toString()));
//                    mattersInfosDTO.setNotarizationFee(data.get("notarialFee") == null ? 0 : Integer.valueOf(data.get("notarialFee").toString()));
//                    matterList.add(mattersInfosDTO);
//                }
//                SelfServiceHasBeenFinishedFragment item = (SelfServiceHasBeenFinishedFragment) adapter.getItem(3);
//                item.setNotarialMatterItems(matterList);
//
//            }
//        }
    }


    @Override
    public void initViewObservable() {
        viewModel.tabClickEvent.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
//                if (integer == 1) {
//                    binding.inquiryOrderListNotaryOrderTab.setTextColor(getResources().getColor(colorPrimary));
//                    binding.inquiryOrderListSelfServiceTab.setTextColor(getResources().getColor(color3333));
//                    binding.inquiryOrderListMyAppointmentTab.setTextColor(getResources().getColor(color3333));
//                    binding.inquiryOrderListNotaryOrderLine.setVisibility(View.VISIBLE);
//                    binding.inquiryOrderListSelfServiceLine.setVisibility(View.GONE);
//                    binding.inquiryOrderListMyAppointmentLine.setVisibility(View.GONE);
//                    binding.inquiryOrderListNotaryOrderTab.setTypeface(Typeface.DEFAULT_BOLD);
//                    binding.inquiryOrderListSelfServiceTab.setTypeface(Typeface.DEFAULT);
//                    binding.inquiryOrderListMyAppointmentTab.setTypeface(Typeface.DEFAULT);
//                } else if (integer == 2) {
//                    binding.inquiryOrderListNotaryOrderTab.setTextColor(getResources().getColor(color3333));
//                    binding.inquiryOrderListSelfServiceTab.setTextColor(getResources().getColor(colorPrimary));
//                    binding.inquiryOrderListMyAppointmentTab.setTextColor(getResources().getColor(color3333));
//                    binding.inquiryOrderListNotaryOrderLine.setVisibility(View.GONE);
//                    binding.inquiryOrderListSelfServiceLine.setVisibility(View.VISIBLE);
//                    binding.inquiryOrderListMyAppointmentLine.setVisibility(View.GONE);
//                    binding.inquiryOrderListNotaryOrderTab.setTypeface(Typeface.DEFAULT);
//                    binding.inquiryOrderListSelfServiceTab.setTypeface(Typeface.DEFAULT_BOLD);
//                    binding.inquiryOrderListMyAppointmentTab.setTypeface(Typeface.DEFAULT);
//                } else if (integer == 3) {
//                    binding.inquiryOrderListNotaryOrderTab.setTextColor(getResources().getColor(color3333));
//                    binding.inquiryOrderListSelfServiceTab.setTextColor(getResources().getColor(color3333));
//                    binding.inquiryOrderListMyAppointmentTab.setTextColor(getResources().getColor(colorPrimary));
//                    binding.inquiryOrderListNotaryOrderLine.setVisibility(View.GONE);
//                    binding.inquiryOrderListSelfServiceLine.setVisibility(View.GONE);
//                    binding.inquiryOrderListMyAppointmentLine.setVisibility(View.VISIBLE);
//                    binding.inquiryOrderListNotaryOrderTab.setTypeface(Typeface.DEFAULT);
//                    binding.inquiryOrderListSelfServiceTab.setTypeface(Typeface.DEFAULT);
//                    binding.inquiryOrderListMyAppointmentTab.setTypeface(Typeface.DEFAULT_BOLD);
//                }
                viewPager.setCurrentItem(integer - 1, false);
            }
        });

    }


}
