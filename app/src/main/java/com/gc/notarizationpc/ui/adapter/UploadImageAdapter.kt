package com.gc.notarizationpc.ui.adapter

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.gc.notarizationpc.R
import com.gc.notarizationpc.model.ItemsChoseEntity
import com.gc.notarizationpc.model.UploadItemsBean

/**
 * 自定义布局，网络图片
 */
class UploadImageAdapter(val mContext: Context) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

     var mCallBack: CallBack? = null;
     var itemsList: ArrayList<UploadItemsBean>? = null;
     var mustShowNum: Int? = -1;

    inner class ItemsViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
        val contentText: TextView = view.findViewById(R.id.uploadNameText);
        val upImage: ImageView = view.findViewById(R.id.uploadImage);
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val leftView = LayoutInflater.from(parent.context).inflate(R.layout.upload_image_griditem, parent, false)
        return ItemsViewHolder(leftView)
    }

    override fun getItemCount(): Int {
        return itemsList?.size!!;
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val textItem = itemsList?.get(position);
        when (holder) {
            is ItemsViewHolder -> {
                if (textItem != null) {
                        holder.contentText.text = textItem.name;
                        if (!TextUtils.isEmpty(textItem.filePath)) {
                            Glide.with(mContext).load(textItem?.filePath).into(holder.upImage);
                        } else {
                            holder.upImage.setImageDrawable(mContext.getResources().getDrawable(R.drawable.ic_add_photo));
                        }
                };
                holder.upImage.setOnClickListener {
                    if (textItem != null) {
                        mCallBack?.callBack(textItem, position)
                    };
                }
            };
        }
    }

    interface CallBack {
        fun callBack(entity: UploadItemsBean, position: Int);
    }

}