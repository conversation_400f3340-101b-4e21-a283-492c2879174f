package com.gc.notarizationpc.ui.viewmodel;

import static me.goldze.mvvmhabit.utils.Utils.getContext;

import android.app.Application;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.FileInformationModel;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.util.CommonUtil;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

/**
 * 高拍
 */
public class SelfTakePhotoViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<Boolean> delMaterialEvent = new SingleLiveEvent<>();//视频咨询连线 删除图片

    public class UIChangeObservable {

    }

    public SelfTakePhotoViewModel(@NonNull Application application) {
        super(application);
    }

    public SingleLiveEvent uploadedFile = new SingleLiveEvent<>();


    public void uploadSignPic(Bitmap bitmap) {
        showDialog();
        File file = CommonUtil.bitmapToFile(bitmap);

        RequestBody requestFile = RequestBody.create(MediaType.parse("image/png"), file);
        MultipartBody.Part body = MultipartBody.Part.createFormData("file", file.getName(), requestFile);
        RequestUtil.uploadFile(body, new MyObserver<UploadFileBean>() {
            @Override
            public void onSuccess(UploadFileBean result) {
                dismissDialog();
                toastSuccess(Utils.getContext().getString(R.string.uploadImagesSuccess));
                dismissDialog();
                if (result != null) {
                    uploadedFile.setValue(result);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                Log.e("Test", "图片上传失败");
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.uploadImagesFail) : errorMsg);
            }
        });
    }


    //返回
    public BindingCommand backClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            finish();
        }
    });


    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
