package com.gc.notarizationpc.ui.fragment;


import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.lifecycle.Observer;

import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentSelfServiceHasBeenFinishedBinding;
import com.gc.notarizationpc.data.model.response.NotaryMatterItem;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.view.SelfServiceHasBeenFinishedAlert;
import com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceHasBeenFinishedViewModel;
import com.gc.notarizationpc.ui.viewmodel.SelfFeeItemViewModel;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.widget.SelfServiceStepBar;

import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseFragment;
import me.tatarka.bindingcollectionadapter2.BR;

/**
 * 这个是自助办证完成自助申请
 */
public class SelfServiceHasBeenFinishedFragment extends BaseFragment<FragmentSelfServiceHasBeenFinishedBinding, FragmentSelfServiceHasBeenFinishedViewModel> {

    private SelfServiceStepBar selfServiceStepBar;

    private SelfServiceHasBeenFinishedAlert alert;

    private List<NotaryMatterItem.MattersInfosDTO> notarialMatterItems = new ArrayList<>();

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_self_service_has_been_finished;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public void initData() {
        super.initData();
        initSelfServiceStepBar();
        if (notarialMatterItems.size() > 0) {
            int total = 0;
            viewModel.feeInformationList.clear();
            for (NotaryMatterItem.MattersInfosDTO notaryMatterItem : notarialMatterItems) {
                if (notaryMatterItem != null) {
                    SelfFeeItemViewModel feeItemViewModel = new SelfFeeItemViewModel(viewModel, notaryMatterItem);
                    total += notaryMatterItem.getNotarizationFee() + notaryMatterItem.getCopyFee();
                    viewModel.feeInformationList.add(feeItemViewModel);
                }
            }
            binding.tvOtherFee.setText("0");
            binding.tvReduceFee.setText("0");
            binding.tvTotalFee.setText(total + "");
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && viewModel != null) {
            int total = 0;
            viewModel.feeInformationList.clear();
            for (NotaryMatterItem.MattersInfosDTO notaryMatterItem : notarialMatterItems) {
                if (notaryMatterItem != null) {
                    SelfFeeItemViewModel feeItemViewModel = new SelfFeeItemViewModel(viewModel, notaryMatterItem);
                    total += notaryMatterItem.getNotarizationFee() + notaryMatterItem.getCopyFee();
                    viewModel.feeInformationList.add(feeItemViewModel);
                }
            }
            binding.tvOtherFee.setText("0");
            binding.tvReduceFee.setText("0");
            binding.tvTotalFee.setText(total + "");
        }

    }

    public List<NotaryMatterItem.MattersInfosDTO> getNotarialMatterItems() {
        return notarialMatterItems;
    }

    public void setNotarialMatterItems(List<NotaryMatterItem.MattersInfosDTO> notarialMatterItems) {
        this.notarialMatterItems = notarialMatterItems;
    }

    private void initSelfServiceStepBar() {
        selfServiceStepBar = (SelfServiceStepBar) getView().findViewById(R.id.self_service_step_bar);
        TextView tvFirstNumView = selfServiceStepBar.getTv_first_num();
        tvFirstNumView.setBackground(ResourcesCompat.getDrawable(getResources(), R.mipmap.icon_work_online, null));
        tvFirstNumView.setText("");
        TextView tvFirstView = selfServiceStepBar.getTv_first();
        tvFirstView.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color3333));
        selfServiceStepBar.setTv_first_num(tvFirstNumView);
        selfServiceStepBar.setTv_first(tvFirstView);

        TextView tvSecondNum = selfServiceStepBar.getTv_second_num();
        tvSecondNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.mipmap.icon_work_online, null));
        tvSecondNum.setText("");
        TextView tvSecond = selfServiceStepBar.getTv_second();
        tvSecond.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color3333));
        selfServiceStepBar.setTv_second_num(tvSecondNum);
        selfServiceStepBar.setTv_second(tvSecond);

        TextView tvThreeNum = selfServiceStepBar.getTv_three_num();
        tvThreeNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.mipmap.icon_work_online, null));
        tvThreeNum.setText("");
        TextView tvThree = selfServiceStepBar.getTv_three();
        tvThree.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color3333));
        selfServiceStepBar.setTv_three_num(tvThreeNum);
        selfServiceStepBar.setTv_three(tvThree);

        TextView tvFourNum = selfServiceStepBar.getTv_four_num();
        tvFourNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.shape_circle_3c6af4, null));
        tvFourNum.setText("4");
        TextView tvFour = selfServiceStepBar.getTv_four();
        tvFour.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color3333));
        selfServiceStepBar.setTv_four_num(tvFourNum);
        selfServiceStepBar.setTv_four(tvFour);
    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();
        viewModel.finishEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object o) {
                if (alert == null || !alert.isShowing()) {
                    alert = new SelfServiceHasBeenFinishedAlert(getContext(), new PopwindowUtil.ResultListener() {
                        @Override
                        public void result(Object value) {
                            alert.dismiss();
                            startActivity(HomeActivity.class);
                        }
                    });
                    alert.show();
                }
            }
        });

    }
}
