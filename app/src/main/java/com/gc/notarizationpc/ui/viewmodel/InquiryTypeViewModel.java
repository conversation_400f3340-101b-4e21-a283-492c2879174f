package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.MenuInfo;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.util.PopwindowUtil;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * 首页快捷入口 viewmodel
 */

public class InquiryTypeViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    public class UIChangeObservable {

    }

    public InquiryTypeViewModel(@NonNull Application application) {
        super(application);
    }

    //给RecyclerView添加ObservableList
    public ObservableList<HomeQuickListItemViewModel> observableList = new ObservableArrayList<>();

    public SingleLiveEvent<Boolean> isRefreshing = new SingleLiveEvent<>();

    public  SingleLiveEvent<Boolean> showPhoneAlert = new SingleLiveEvent<>();

    public  SingleLiveEvent<Boolean> showIdCardInformationAlert = new SingleLiveEvent<>();


    //给RecyclerView添加ItemBinding
    public ItemBinding<HomeQuickListItemViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.item_home_quick_enter);//这里指定了item的布局

    /**
     * 网络请求方法，在ViewModel中调用Model层，通过Okhttp+Retrofit+RxJava发起请求
     */
    public void requestNetWork() {
        for (int i = 0; i < 10; i++) {
            MenuInfo info = new MenuInfo("电子打印",ContextCompat.getDrawable(getApplication(), R.mipmap.icon_dayin));
//            HomeQuickListItemViewModel itemViewModel = new HomeQuickListItemViewModel(VideoConnectionViewModel.this, info);
            //双向绑定动态添加Item
//            observableList.add(itemViewModel);
        }

    }

    /**
     * 获取短信验证码
     */
    public void getMessageCode(String mobile, PopwindowUtil.ResultListener listener) {
        HashMap<String, String> param = new HashMap<>();
        param.put("mobile", "+86-" + mobile);
        RequestUtil.getMessageCode(param, new MyObserver<Double>() {
            @Override
            public void onSuccess(Double response) {
                Log.e("获取短信验证码", "onSuccess: "+response);
                listener.result(response);
                toastSuccess(Utils.getContext().getString(R.string.getMessageCodeSuccess));
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.getMessageCodeFail) : errorMsg);
            }
        });
    }

    // 刷身份证网络请求
    public void readIdCard(Map data) {
//        RequestUtil.readIdCard(new MyObserver() {
//            @Override
//            public void onSuccess(Object response) {
//                toastError("读取身份证成功");
//            }
//
//            @Override
//            public void onFailure(Throwable e, String errorMsg) {
//                toastError(errorMsg == null ? "读取身份证失败" : errorMsg);
//            }
//        });
    }

    // 输入手机号网络请求
    public void inputPhone(Map data) {
//        RequestUtil.inputPhone(new MyObserver() {
//            @Override
//            public void onSuccess(Object response) {
//                toastError("输入手机号成功");
//            }
//
//            @Override
//            public void onFailure(Throwable e, String errorMsg) {
//                toastError(errorMsg == null ? "输入手机号失败" : errorMsg);
//            }
//        });
    }




    /**
     * 身份识别
     */

  public   BindingCommand identityRecognition = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
          isRefreshing.setValue(true);
        }
    });

    /**
     * 手机号验证
     */

    public BindingCommand phoneVerification = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            showPhoneAlert.setValue(true);
        }
    });

    /**
     * 身份信息录入
     */
    public  BindingCommand inputIdentityInformation = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            showIdCardInformationAlert.setValue(true);
        }
    });

    /**
     * 获取用户信息
     */
    public void getUserInfor(Map<String,Object> param,PopwindowUtil.ResultSecondListener resultListener){
        RequestUtil.getUserInformation(param, new MyObserver() {
            @Override
            public void onSuccess(Object result) {
                resultListener.result(result);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                resultListener.secondResult("");
                toastError(errorMsg == null ? "查询信息失败" : errorMsg);
            }
        });
    }

    /**
     * 校验手机验证码
     */
    public void checkSMSCode(String phone,String code, PopwindowUtil.ResultSecondListener resultListener){
         Map<String,Object> data = new HashMap<>();
        data.put("mobileNum","+86-" + phone);
        data.put("smsCode",code);
        showDialog();
        RequestUtil.verifyPhoneCode(data, new MyObserver() {
            @Override
            public void onSuccess(Object result) {
                dismissDialog();

                resultListener.result(result);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                resultListener.secondResult("");
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.verifyMessageCodeFail) : errorMsg);
            }
        });
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
