package com.gc.notarizationpc.ui.view;

import static me.goldze.mvvmhabit.utils.ToastUtils.toastInfo;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.MaterialListResponse;
import com.gc.notarizationpc.ui.adapter.PictureOperationAdapter;
import com.gc.notarizationpc.ui.inquirycertification.InquiryOrderListHomeActivity;
import com.gc.notarizationpc.ui.inquirycertification.OrderDetailActivity;
import com.gc.notarizationpc.ui.video.CounselingRoomActivity;
import com.gc.notarizationpc.ui.video.TakePhotoActivity;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.util.ResultListener;

import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseAdapter;
import me.goldze.mvvmhabit.base.BaseViewHolder;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.GlideRoundedCornersTransform;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * 视频连线咨询受理室 材料上传
 */
public class VideoLineMaterialDialog extends Dialog implements ResultListener {
    private Context mContext;
    private TextView tvScanUpload;
    private RecyclerView rvBooklet, rvContent, rvDegree, rvIdCard, rvParent;
    private ImageView ivDetail;
    private int currentClickIndex;//点击了哪个事项
    private List<MaterialListResponse> materialListResponses;
    private ParentAdatper parentAdatper;
    private String recordId;
    private RelativeLayout rnlPreview;

    private static final int MAX_SELECT_NUMBER = 30;

    // 来源渠道
    private String mSourceWay;

    // 当前材料的类型id
    private List<String> materialDataTypeIdList = new ArrayList<>();

    // 保存一个idlist 方便后续清空时使用
    private List<String> materialIdList = new ArrayList<>();

    private PopwindowUtil.ResultSecondListener mResultListener;

    private int mSourceFrom;


    public VideoLineMaterialDialog(Context context, String recordId, String sourceWay, List<MaterialListResponse> materialListResponseList, int from, PopwindowUtil.ResultSecondListener resultListener) {
        super(context);
        mContext = context;
        materialListResponses = materialListResponseList;
        this.recordId = recordId;
        mSourceWay = sourceWay;
        mResultListener = resultListener;
        mSourceFrom = from;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_video_notarization_material);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();

    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        takePhotoListener = this;
        rnlPreview = findViewById(R.id.rnl_preview);
        rvParent = findViewById(R.id.rv_parent);
        tvScanUpload = findViewById(R.id.tv_scan_upload);
        ivDetail = findViewById(R.id.iv_detail);
        childPhotoList = new ArrayList<>();
        findViewById(R.id.iv_close_img).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                rnlPreview.setVisibility(View.GONE);
            }
        });

        parentAdatper = new ParentAdatper(mContext, R.layout.item_list_law_material_parent, 0);
        rvParent.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false));
        rvParent.setAdapter(parentAdatper);
        childPhotoList.addAll(materialListResponses);

        parentAdatper.refreshAdapter(childPhotoList);
        parentAdatper.setOnItemClickListener(new BaseViewHolder.OnItemClickListener<MaterialListResponse>() {
            @Override
            public void onItemClick(BaseViewHolder viewHolder, int position, MaterialListResponse itemData, Object type) {
                currentClickIndex = position;
            }

            @Override
            public void onItemLongClick(BaseViewHolder viewHolder, int position, MaterialListResponse itemData, Object type) {

            }
        });


        findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowing()) {
                    dismiss();
                }
            }
        });

        findViewById(R.id.iv_refresh).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //刷新
                listOtherMaterial();
            }
        });

    }

    //视频连线查询上传材料
    public void listOtherMaterial() {
        showProgress();
        RequestUtil.listOtherMaterial(recordId, new MyObserver<List<MaterialListResponse.MaterialVoListDTO>>() {
            @Override
            public void onSuccess(List<MaterialListResponse.MaterialVoListDTO> materialVoListDTO) {
                hideProgress();
                if (materialVoListDTO != null) {
                    List<MaterialListResponse> listResponses = new ArrayList<>();
                    MaterialListResponse materialListResponse = new MaterialListResponse();
                    //这是为了保持和视频办证上传材料 接口保持格式一致
                    materialListResponse.setMaterialTypeName(Utils.getContext().getString(R.string.other_material));
                    if (materialVoListDTO == null || materialVoListDTO.size() == 0) {
                        materialVoListDTO = new ArrayList<>();
                        MaterialListResponse.MaterialVoListDTO materialVoListDTO1 = new MaterialListResponse.MaterialVoListDTO("CREATE", "");
                        materialVoListDTO.add(materialVoListDTO1);
                    } else if (materialVoListDTO != null && (materialVoListDTO.size() < 30 && materialVoListDTO.size() > 0)) {
                        MaterialListResponse.MaterialVoListDTO materialVoListDTO1 = new MaterialListResponse.MaterialVoListDTO("CREATE", "");
                        materialVoListDTO.add(materialVoListDTO1);
                    }
                    materialListResponse.setMaterialVoList(materialVoListDTO);
                    listResponses.add(materialListResponse);
                    materialListResponses = listResponses;
                    parentAdatper.refreshAdapter(childPhotoList);
                    parentAdatper.notifyDataSetChanged();
                } else {
                    toastInfo(WorkstatusEnum.PLEASE_UPLOAD_HINT.msg);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                hideProgress();
                toastInfo(WorkstatusEnum.PLEASE_UPLOAD_HINT.msg);
            }
        });

    }
    private void showProgress() {
        if (mContext instanceof CounselingRoomActivity) {
            ((CounselingRoomActivity) mContext).showProgress();
        } else if (mContext instanceof OrderDetailActivity) {
            ((OrderDetailActivity) mContext).showProgress();
        } else if (mContext instanceof InquiryOrderListHomeActivity) {
            ((InquiryOrderListHomeActivity) mContext).showProgress();
        }

    }

    private void hideProgress() {
        if (mContext instanceof CounselingRoomActivity) {
            ((CounselingRoomActivity) mContext).hideProgress();
        } else if (mContext instanceof OrderDetailActivity) {
            ((OrderDetailActivity) mContext).hideProgress();
        } else if (mContext instanceof InquiryOrderListHomeActivity) {
            ((InquiryOrderListHomeActivity) mContext).hideProgress();
        }
    }



    List<MaterialListResponse> childPhotoList = new ArrayList<>();
    PictureOperationAdapter currentChildAdapter;


    private class ParentAdatper extends BaseAdapter<MaterialListResponse> {
        public ParentAdatper(Context context, int itemLayoutRes, @Nullable Object type) {
            super(context, itemLayoutRes, type);
            mContext = context;
        }

        @Override
        public void bind(@NonNull BaseViewHolder viewHolder, MaterialListResponse materialListResponse, int position) {
            if (materialListResponse == null) {
                return;
            }
            viewHolder.setText(R.id.tv_name, materialListResponse.getMaterialTypeName());
            RecyclerView rvChild = viewHolder.getView(R.id.rv_child);
            rvChild.setLayoutManager(new GridLayoutManager(mContext, 3));
            PictureOperationAdapter contentAdapter = new PictureOperationAdapter(mContext, R.layout.list_item_operation_picture, "0");
            List<MaterialListResponse.MaterialVoListDTO> childs = childPhotoList.get(position).getMaterialVoList();
//            if (childs != null) {
//                if ((childs.size() > 1 && childs.size() < MAX_SELECT_NUMBER) && "PICTURE".equals(childs.get(childs.size() - 1).getType())) {
//                    Log.e("Test", "add  CREATE");
//                    childs.add(new MaterialListResponse.MaterialVoListDTO("CREATE", ""));
//                } else if (childs.size() == 1) {
//                    if (childs.get(0).getStatus() != null && childs.get(0).getStatus() == -1) {
//                        childs.get(0).setType("CREATE");
//                    } else {
//                        childs.add(new MaterialListResponse.MaterialVoListDTO("CREATE", ""));
//                    }
//
//                }
//            }
            contentAdapter.refreshAdapter(childs);
            rvChild.setAdapter(contentAdapter);
            contentAdapter.notifyDataSetChanged();
            contentAdapter.setOnItemViewClickListener(new BaseViewHolder.OnItemViewClickListener<MaterialListResponse.MaterialVoListDTO>() {
                @Override
                public void onItemViewClick(BaseViewHolder viewHolder, View view, int pos, MaterialListResponse.MaterialVoListDTO itemData, Object type) {
                    currentClickIndex = position;
                    if (view.getId() == R.id.work_iv_wip_delete) {
                        try {
                            currentClickIndex = position;
                            if ("PICTURE".equals(childPhotoList.get(currentClickIndex).getMaterialVoList().get(pos).getType())) {
                                //获取文书
                                if (childPhotoList.get(currentClickIndex).getMaterialVoList().get(pos) == null) {
                                    return;
                                }

                                RequestUtil.delOtherMaterial(recordId, childPhotoList.get(currentClickIndex).getMaterialVoList().get(pos).getFileId(), new MyObserver<Object>() {
                                    @Override
                                    public void onSuccess(Object result) {
                                        try {
                                            childPhotoList.get(currentClickIndex).getMaterialVoList().remove(pos);
                                            if (pos <= MAX_SELECT_NUMBER - 1 && !"CREATE".equals(childPhotoList.get(currentClickIndex).getMaterialVoList().get(childPhotoList.get(currentClickIndex).getMaterialVoList().size() - 1).getType())) {
                                                childPhotoList.get(currentClickIndex).getMaterialVoList().add(new MaterialListResponse.MaterialVoListDTO("CREATE", ""));
                                            }
                                            contentAdapter.refreshAdapter(childPhotoList.get(currentClickIndex).getMaterialVoList());
                                            mResultListener.secondResult(childPhotoList.get(currentClickIndex).getMaterialVoList());
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }

                                    @Override
                                    public void onFailure(Throwable e, String errorMsg) {
                                        ToastUtils.toastError(Utils.getContext().getString(R.string.deleteFailed));
                                    }
                                });
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            Log.e("Test", e.getMessage());
                        }
                    }
                    if (view.getId() == R.id.work_iv_wip_create) {
                        try {
                            currentClickIndex = position;
                            currentChildAdapter = contentAdapter;
                            Intent intent = new Intent(mContext, TakePhotoActivity.class);
                            intent.putExtra("recordId", recordId);
                            intent.putExtra("sourceWay", mSourceWay);
//                        intent.putExtra("materialTypeId", materialDataTypeIdList.get(position));
//                        intent.putExtra("materialId", materialIdList.get(position));
                            intent.putExtra("materialListCount", childPhotoList.get(position).getMaterialVoList().size() - 1);
                            intent.putExtra("from", mSourceFrom);
                            mContext.startActivity(intent);
                        } catch (Exception e) {
                            e.printStackTrace();
                            Log.e("Test", e.getMessage());
                        }

                    } else if (view.getId() == R.id.work_iv_wip_picture) {
                        rnlPreview.setVisibility(View.VISIBLE);
//                    //查看大图
                        RequestOptions options = new RequestOptions();
                        //圆角
                        options = options.transform(new
                                GlideRoundedCornersTransform(mContext, 6f, GlideRoundedCornersTransform.CornerType.ALL));
//            if (!TextUtils.isEmpty(itemData.getName())) {
                        try {
                            Glide.with(viewHolder.itemView.getContext())
                                    .load(childPhotoList.get(currentClickIndex).getMaterialVoList().get(pos).getFileUrl())
                                    .apply(options).placeholder(R.mipmap.erroimage)
                                    .into(ivDetail);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }
                }

                @Override
                public void onItemViewLongClick(BaseViewHolder viewHolder, View view, int position, MaterialListResponse.MaterialVoListDTO itemData, Object type) {

                }


            });
        }

        @Override
        public int viewType(MaterialListResponse itemData) {
            return 0;
        }
    }


    @Override
    public void result(List<MaterialListResponse.MaterialVoListDTO> pictureInfoBeans) {
        Log.e("Test", "currentClickIndex==" + currentClickIndex);
        if (pictureInfoBeans != null && pictureInfoBeans.size() > 0) {

            try {
                if (childPhotoList.get(currentClickIndex).getMaterialVoList().size() == 1) {
                    //上一个是status为-1 未上传的占位材料,删除
                    childPhotoList.get(currentClickIndex).setMaterialVoList(new ArrayList<>());
                }
                if (childPhotoList.get(currentClickIndex).getMaterialVoList().size() > 0) {
                    childPhotoList.get(currentClickIndex).getMaterialVoList().remove(childPhotoList.get(currentClickIndex).getMaterialVoList().size() - 1);
                }
                childPhotoList.get(currentClickIndex).getMaterialVoList().addAll(pictureInfoBeans);
                List<MaterialListResponse.MaterialVoListDTO> listDTOS = childPhotoList.get(currentClickIndex).getMaterialVoList();
                if (childPhotoList.get(currentClickIndex).getMaterialVoList().size() < MAX_SELECT_NUMBER && !childPhotoList.get(currentClickIndex).getMaterialVoList().get(childPhotoList.get(currentClickIndex).getMaterialVoList().size() - 1).getType().equals("CREATE")) {
//                    listDTOS.add(new MaterialListResponse.MaterialVoListDTO("CREATE", ""));
                    childPhotoList.get(currentClickIndex).getMaterialVoList().add(new MaterialListResponse.MaterialVoListDTO("CREATE", ""));
                }
                currentChildAdapter.refreshAdapter(listDTOS);

                mResultListener.result(pictureInfoBeans);


//                //图片上传成功，发送mqtt
//                Map<String, Object> data = new HashMap<>();
//                data.put("code", "update_consult_materials");
//                data.put("content", "");
//                data.put("name", "小一体机");
//                String json = JSON.toJSONString(data);
//                if (mContext instanceof CounselingRoomActivity) {
//                    ((CounselingRoomActivity)mContext).myMqttService.publish(json);
//                }
            } catch (Exception e) {
                e.printStackTrace();
                Log.e("Test",e.getMessage());
            }
        }
    }


    @Override
    public void show() {
        super.show();
    }

    @Override
    public void dismiss() {
        Log.e("Test", "dismiss");
        super.dismiss();

    }

    public static ResultListener takePhotoListener;


}
