package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.common.CommonRequest;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.NotaryPurposeModel;
import com.gc.notarizationpc.data.model.response.VideoOrderDetailModel;
import com.gc.notarizationpc.util.PopwindowUtil;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * 首页快捷入口 viewmodel
 */

public class OrderDetailViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    public class UIChangeObservable {

    }

    public OrderDetailViewModel(@NonNull Application application) {
        super(application);
    }

    public String orderStatus = "";

    public ObservableField<String> receiveWay = new ObservableField<>();

    // 底部状态按钮显示和隐藏
    public Boolean bottomStatusVisibility = false;

    public ObservableField<VideoOrderDetailModel> videoOrderDetailModel = new ObservableField<>();

    // 阅读文书
    public SingleLiveEvent<Boolean> readDocumentEvent = new SingleLiveEvent<>();

    // 补充材料
    public SingleLiveEvent<Boolean> applyMaterialEvent = new SingleLiveEvent<>();

    // 进入受理室
    public SingleLiveEvent<Boolean> enterAcceptanceRoomEvent = new SingleLiveEvent<>();

    // 终止案件
    public SingleLiveEvent<Boolean> stopCaseEvent = new SingleLiveEvent<>();

    // 公证用途数据
    public List<NotaryPurposeModel> notaryMattersList = new ArrayList<NotaryPurposeModel>();

    // 公证用途
    public String notaryPurpose = "";

    // 终止案件
    public SingleLiveEvent<Boolean> getStopCaseEvent = new SingleLiveEvent<>();

    // 文书预览
    public SingleLiveEvent<List<String>> documentPreViewEvent = new SingleLiveEvent<>();
    // 单位信息展示
    public SingleLiveEvent<Boolean> unitInformationVisibilityEvent = new SingleLiveEvent<>();
    // 费用显示
    public SingleLiveEvent<Boolean> feeVisibilityEvent = new SingleLiveEvent<>();
    // 自然人信息
    public ObservableList<OrderDetailNaturalInformationItemViewModel> observableNaturalInformationList = new ObservableArrayList<>();

    // 单位信息
    public ObservableList<OrderDetailUnitInformationItemViewModel> observableUnitInformationList = new ObservableArrayList<>();

    // 费用信息
    public ObservableList<OrderDetailFeeInformationItemViewModel> observableFeeInformationList = new ObservableArrayList<>();

    // 材料图片信息
    public ObservableList<OrderDetailImageItemViewModel> observableImageList = new ObservableArrayList<>();

    // 文书图片信息
    public ObservableList<OrderDetailDocumentItemViewModel> observableDocumentList = new ObservableArrayList<>();

    public ItemBinding<OrderDetailNaturalInformationItemViewModel> naturalInformationItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_order_detail_natural_information);

    public ItemBinding<OrderDetailUnitInformationItemViewModel> unitInformationItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_order_detail_unit_information);

    public ItemBinding<OrderDetailFeeInformationItemViewModel> feeInformationItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_order_detail_fee_information);

    public ItemBinding<OrderDetailImageItemViewModel> imageItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_order_detail_image);

    public ItemBinding<OrderDetailDocumentItemViewModel> documentsItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_order_detail_document);

    /**
     * 网络请求方法，在ViewModel中调用Model层，通过Okhttp+Retrofit+RxJava发起请求
     */
    public void requestNetWork(String orderId) {
        showDialog(Utils.getContext().getString(R.string.loading));
        RequestUtil.getVideoOrderDetail(orderId, new MyObserver<VideoOrderDetailModel>() {
            @Override
            public void onSuccess(VideoOrderDetailModel response) {
                dismissDialog();
                observableNaturalInformationList.clear();
                observableUnitInformationList.clear();
                observableFeeInformationList.clear();
                observableDocumentList.clear();
                observableImageList.clear();
                videoOrderDetailModel.set(response);
                feeVisibilityEvent.setValue(videoOrderDetailModel.get().getPaymentListVO().getHadSetBilling() == 1 ? true : false);
                unitInformationVisibilityEvent.setValue(videoOrderDetailModel.get().getCaseCorporatonApplicantVos().size() > 0 ? true : false);
                if (videoOrderDetailModel.get().getReceiveWay() == null) {
                    receiveWay.set("自取");
                } else {
                    if (videoOrderDetailModel.get().getReceiveWay() == 2) {
                        receiveWay.set("邮寄（到付）");
                    } else {
                        receiveWay.set("自取");
                    }
                }
//                for (NotaryPurposeModel model :
//                        notaryMattersList) {
//                    if (videoOrderDetailModel.get().getUsedPurpose() == model.getMechanismId().toString()){
//                        notaryPurpose = model.getDescription();
//                    }
//                }
                notaryPurpose = "videoOrderDetailModel.get().getUsedPurpose()";

                if (response.getCaseApplicantVos() != null && !response.getCaseApplicantVos().isEmpty()) {
                    for (int i = 0; i < response.getCaseApplicantVos().size(); i++) {
                        VideoOrderDetailModel.CaseApplicantVosDTO caseApplicantVosDTO = response.getCaseApplicantVos().get(i);
                        OrderDetailNaturalInformationItemViewModel itemViewModel = new OrderDetailNaturalInformationItemViewModel(OrderDetailViewModel.this, caseApplicantVosDTO);
                        itemViewModel.personType = "申请人";
                        itemViewModel.sexString = caseApplicantVosDTO.getGender() == null ? "" : caseApplicantVosDTO.getGender() == 1 ? "男" : "女";
                        observableNaturalInformationList.add(itemViewModel);
                    }
                }

                if (response.getCaseApplicantAgentVos() != null && !response.getCaseApplicantAgentVos().isEmpty()) {
                    for (int i = 0; i < response.getCaseApplicantAgentVos().size(); i++) {
                        VideoOrderDetailModel.CaseApplicantAgentVosDTO caseApplicantAgentVosDTO = response.getCaseApplicantAgentVos().get(i);
                        VideoOrderDetailModel.CaseApplicantVosDTO caseApplicantVosDTO = new VideoOrderDetailModel.CaseApplicantVosDTO();
                        caseApplicantVosDTO.setAddress(caseApplicantAgentVosDTO.getAddress());
                        caseApplicantVosDTO.setApplicantName(caseApplicantAgentVosDTO.getAgentName());
                        caseApplicantVosDTO.setBirthday(caseApplicantAgentVosDTO.getBirthday());
                        caseApplicantVosDTO.setCity(caseApplicantAgentVosDTO.getCity());
                        caseApplicantVosDTO.setContactNum(caseApplicantAgentVosDTO.getContactNum());
                        caseApplicantVosDTO.setCredentialNum(caseApplicantAgentVosDTO.getCredentialNum());
                        caseApplicantVosDTO.setCredentialType(caseApplicantAgentVosDTO.getCredentialType());
                        caseApplicantVosDTO.setCredentialTypeStr(caseApplicantAgentVosDTO.getCredentialTypeStr());
                        caseApplicantVosDTO.setEmail(caseApplicantAgentVosDTO.getEmail());
                        caseApplicantVosDTO.setGender(caseApplicantAgentVosDTO.getGender());
                        caseApplicantVosDTO.setId(caseApplicantAgentVosDTO.getId());
                        caseApplicantVosDTO.setNationality(caseApplicantAgentVosDTO.getNationality());
                        caseApplicantVosDTO.setRace(caseApplicantAgentVosDTO.getRace());
                        caseApplicantVosDTO.setRemark(caseApplicantAgentVosDTO.getRemark());
                        OrderDetailNaturalInformationItemViewModel itemViewModel = new OrderDetailNaturalInformationItemViewModel(OrderDetailViewModel.this, caseApplicantVosDTO);
                        itemViewModel.personType = "代理人";
                        itemViewModel.sexString = caseApplicantVosDTO.getGender() == null ? "" : caseApplicantVosDTO.getGender() == 1 ? "男" : "女";
                        observableNaturalInformationList.add(itemViewModel);
                    }
                }

                if (response.getCaseCorporatonApplicantVos() != null && !response.getCaseCorporatonApplicantVos().isEmpty()) {
                    for (int i = 0; i < response.getCaseCorporatonApplicantVos().size(); i++) {
                        VideoOrderDetailModel.CaseCorporatonApplicantVosDTO caseCorporatonApplicantVosDTO = response.getCaseCorporatonApplicantVos().get(i);
                        OrderDetailUnitInformationItemViewModel itemViewModel1 = new OrderDetailUnitInformationItemViewModel(OrderDetailViewModel.this, caseCorporatonApplicantVosDTO);
                        observableUnitInformationList.add(itemViewModel1);

                    }
                }

                if (response.getPaymentListVO() != null && response.getPaymentListVO().getMattersFeeList() != null && !response.getPaymentListVO().getMattersFeeList().isEmpty()) {
                    for (int i = 0; i < response.getPaymentListVO().getMattersFeeList().size(); i++) {
                        VideoOrderDetailModel.PaymentListVODTO.MattersFeeListDTO mattersFeeListDTO = response.getPaymentListVO().getMattersFeeList().get(i);
                        OrderDetailFeeInformationItemViewModel itemViewModel2 = new OrderDetailFeeInformationItemViewModel(OrderDetailViewModel.this, mattersFeeListDTO);
                        observableFeeInformationList.add(itemViewModel2);

                    }
                }

                if (response.getMaterialVoList() != null && !response.getMaterialVoList().isEmpty()) {
                    for (int i = 0; i < response.getMaterialVoList().size(); i++) {
                        VideoOrderDetailModel.MaterialVoListDTO materialVoListDTO = response.getMaterialVoList().get(i);
                        OrderDetailImageItemViewModel itemViewModel3 = new OrderDetailImageItemViewModel(OrderDetailViewModel.this, materialVoListDTO);
                        observableImageList.add(itemViewModel3);

                    }
                }

                if (response.getDocTypeList() != null && !response.getDocTypeList().isEmpty()) {
                    for (int i = 0; i < response.getDocTypeList().size(); i++) {
                        VideoOrderDetailModel.DocTypeListDTO docTypeListDTO = response.getDocTypeList().get(i);
                        OrderDetailDocumentItemViewModel itemViewModel4 = new OrderDetailDocumentItemViewModel(OrderDetailViewModel.this, docTypeListDTO);
                        if (docTypeListDTO.getDocumentVOList().get(0).getSignStatus() > 0) {
                            itemViewModel4.documentPath = docTypeListDTO.getDocumentVOList().get(0).getSignDocumentFileUrl().get(0);
                        } else {
                            itemViewModel4.documentPath = docTypeListDTO.getDocumentVOList().get(0).getUnsignDocumentFileUrl().get(0);
                        }
                        observableDocumentList.add(itemViewModel4);

                    }
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.failedToGetOrderDetail) : errorMsg);
            }

        });

    }

    // 获取公证用途数据
    public void getNotaryPurposeData() {
        CommonRequest.getNotaryUse("case_billing_status", new PopwindowUtil.ResultListener() {
            @Override
            public void result(Object value) {
                if (value != null) {
                    List newValue = new ArrayList<>();
                    newValue.addAll((Collection) value);
                    for (int i = 0; i < newValue.size(); i++) {
                        NotaryPurposeModel model = new NotaryPurposeModel();
                        model.fromJson((Map<String, Object>) newValue.get(i));
                        notaryMattersList.add(model);
                    }
                }

            }
        });
    }

    // 终止案件
    public void stopCase(String caseInfoId) {
        Map<String, String> newMap = new HashMap<>();
        newMap.put("caseInfoId", caseInfoId);
        newMap.put("operationContent", "小一体终止案件");
        RequestUtil.terminateCase(newMap, new MyObserver<Boolean>() {
            @Override
            public void onSuccess(Boolean response) {
                getStopCaseEvent.setValue(true);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.failureToTerminateCase) : errorMsg);
            }
        });
    }


    //阅读文书
    public BindingCommand readDocuments = new BindingCommand(() -> {
        //请求网络数据
        readDocumentEvent.setValue(true);
    });

    // 补充材料
    public BindingCommand applyMaterial = new BindingCommand(() -> {
        //请求网络数据
        applyMaterialEvent.setValue(true);
    });

    // 进入受理室
    public BindingCommand enterAcceptanceRoom = new BindingCommand(() -> {
        //请求网络数据
        enterAcceptanceRoomEvent.setValue(true);
    });

    // 终止案件
    public BindingCommand stopCase = new BindingCommand(() -> {
        //请求网络数据
        stopCaseEvent.setValue(true);
    });

    // 返回
    public BindingCommand back = new BindingCommand(() -> {
        //请求网络数据
        finish();
    });

    // 图片预览
    public void imagePreView(VideoOrderDetailModel.MaterialVoListDTO entity) {
        List<String> filePageList = new ArrayList<>();
        if (entity.getFileUrl() != null && !entity.getFileUrl().isEmpty()) {
            filePageList.add(entity.getFileUrl());
            documentPreViewEvent.setValue(filePageList);
        }
    }

    //文书预览
    public void documentPreView(VideoOrderDetailModel.DocTypeListDTO entity) {
        List<String> filePageList = new ArrayList<>();
        for (int i = 0; i < entity.getDocumentVOList().size(); i++) {
            if (entity.getDocumentVOList().get(i).getSignStatus() > 0) {
                filePageList.addAll(entity.getDocumentVOList().get(i).getSignDocumentFileUrl());
            } else {
                filePageList.addAll(entity.getDocumentVOList().get(i).getUnsignDocumentFileUrl());
            }
        }

        documentPreViewEvent.setValue(filePageList);

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
