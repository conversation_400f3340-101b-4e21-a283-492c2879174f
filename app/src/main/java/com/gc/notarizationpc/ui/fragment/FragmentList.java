package com.gc.notarizationpc.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;


import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentListBinding;
import com.gc.notarizationpc.ui.viewmodel.FragmentListItemViewModel;
import com.gc.notarizationpc.ui.viewmodel.FragmentListModel;
import com.lcodecore.tkrefreshlayout.footer.LoadingView;
import com.lcodecore.tkrefreshlayout.header.progresslayout.ProgressLayout;

import me.goldze.mvvmhabit.base.BaseFragment;
import me.goldze.mvvmhabit.utils.MaterialDialogUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;

/**
 *
 */
public class FragmentList extends BaseFragment<FragmentListBinding,FragmentListModel> {
    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_list;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public void initData() {
        super.initData();

        viewModel.requestNetWork();

        //设置下拉刷新按钮头尾部 样式
        ProgressLayout
                headerView = new ProgressLayout(getContext());
        headerView.setColorSchemeResources(me.goldze.mvvmhabit.R.color.gray);
        binding.twinklingRefreshLayout.setHeaderView(headerView);
        // 设置尾部
        LoadingView ballPulseView = new LoadingView(getContext());
        binding.twinklingRefreshLayout.setBottomView(ballPulseView);
        //给recyclerView设置上拉/下拉回弹效果
//        binding.twinklingRefreshLayout.setPureScrollModeOn();
    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();
        //监听下拉刷新完成
        viewModel.uc.finishRefreshing.observe(this, new Observer() {
            @Override
            public void onChanged(@Nullable Object o) {
                //结束刷新
                binding.twinklingRefreshLayout.finishRefreshing();
            }
        });
        //监听上拉加载完成
        viewModel.uc.finishLoadmore.observe(this, new Observer() {
            @Override
            public void onChanged(@Nullable Object o) {
                //结束刷新
                binding.twinklingRefreshLayout.finishLoadmore();
            }
        });


        //监听删除条目
        viewModel.deleteItemLiveData.observe(this, new Observer<FragmentListItemViewModel>() {
            @Override
            public void onChanged(@Nullable final FragmentListItemViewModel netWorkItemViewModel) {
                int index = viewModel.getItemPosition(netWorkItemViewModel);
                //删除选择对话框
                MaterialDialogUtils.showBasicDialog(getContext(), getString(R.string.hint), "是否删除【" + netWorkItemViewModel.entity.get().getEmployeeName() + "】？ position：" + index)
                        .onNegative(new MaterialDialog.SingleButtonCallback() {
                            @Override
                            public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                ToastUtils.showShort("取消");
                            }
                        }).onPositive(new MaterialDialog.SingleButtonCallback() {
                            @Override
                            public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                viewModel.deleteItem(netWorkItemViewModel);
                            }
                        }).show();
            }
        });
    }
}
