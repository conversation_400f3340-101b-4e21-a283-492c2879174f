package com.gc.notarizationpc.ui.inquirycertification;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.example.scarx.idcardreader.utils.IdCardRenderUtils;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityOrderDetailBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.common.CommonRequest;
import com.gc.notarizationpc.data.model.request.SignDocRequest;
import com.gc.notarizationpc.data.model.response.DocInfoResponse;
import com.gc.notarizationpc.data.model.response.MaterialListResponse;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.data.model.response.VideoOrderListModel;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.video.CounselingRoomActivity;
import com.gc.notarizationpc.ui.view.ImagePreviewDialog;
import com.gc.notarizationpc.ui.view.SignDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationApplyInfoDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationLawDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationMaterialDialog;
import com.gc.notarizationpc.ui.viewmodel.OrderDetailViewModel;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.websocket.MyMqttService;
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo;

import java.util.ArrayList;
import java.util.List;

import cc.shinichi.library.ImagePreview;
import cc.shinichi.library.bean.ImageInfo;
import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.utils.ConvertUtils;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class OrderDetailActivity extends BaseActivity<ActivityOrderDetailBinding, OrderDetailViewModel> {
    private String TAG = "Test";
    private IdCardRenderUtils idCardReaderUtils = new IdCardRenderUtils();
    private SoundPool soundPool = new SoundPool(100, AudioManager.STREAM_MUSIC, 0);
    private boolean isActivity = true;
    private boolean isNoCard = true;
    private IDCardInfo idcardinfo = null;
    private Bitmap idCardBmp = null;
    private Integer readSuccess = null;
    private Integer readTips = null;
    private Integer validateSuccess = null;
    private Integer validateFail = null;
    private Integer retry = null;
    private Integer faceCamera = null;
    private boolean hasCardInfo = false;
    private SignDialog signDialog;

    private MyMqttService myMqttService;

    private FragmentManager fragmentManager = getSupportFragmentManager();

    private Fragment fragment;

    private VideoNotarizationApplyInfoDialog dialog;

    private VideoNotarizationLawDialog lawDialog;

    private Handler mHandler;

    private VideoOrderListModel.DataListDTO model;

    // 图片预览
    private ImagePreviewDialog imagePreviewDialog;


    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_order_detail;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public OrderDetailViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(OrderDetailViewModel.class);
    }


    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }


    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }


    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });
        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });
//        viewModel.getNotaryPurposeData();
        model = (VideoOrderListModel.DataListDTO) getIntent().getSerializableExtra("orderData");
        /**
         * 案件状态 5-受理中 6-待审批 7-待发证 8-待归档 9-已归档 10-待阅读文书 11-待补充材料 12-待支付 91-已终止
         */
        // 待补充材料
        if (model.getStatus() == 11) {
            viewModel.orderStatus = getString(R.string.waitToSupplementaryMaterials);
        } else if (model.getStatus() == 10) {
            // 待阅读文书
            viewModel.orderStatus = getString(R.string.waitingToReadInstrument);
        } else if (model.getStatus() == 12) {
            // 待支付
            viewModel.orderStatus = getString(R.string.waitToPay);
        } else if (model.getStatus() == 91) {
            // 已终止
            viewModel.orderStatus = getString(R.string.terminated);
        } else if (model.getStatus() == 8 || model.getStatus() == 9) {
            // 已归档 (已出证)
            viewModel.orderStatus = getString(R.string.hasBeenTestified);
        } else if (model.getStatus() == 7) {
            // 待发证
            viewModel.orderStatus = getString(R.string.toBeIssued);
        } else if (model.getStatus() == 5 || model.getStatus() == 6) {
            // 待发证
            viewModel.orderStatus = getString(R.string.underConsideration);
        }
        if (model.getStatus() == 9 || model.getStatus() == 91 || model.getStatus() == 8 || model.getStatus() == 7) {
            viewModel.bottomStatusVisibility = false;
            LinearLayout layout = findViewById(R.id.activity_order_detail_main_layout);
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) layout.getLayoutParams();
            layoutParams.bottomMargin = getResources().getDimensionPixelSize(R.dimen.space_30);
            layout.setLayoutParams(layoutParams);
        } else {
            viewModel.bottomStatusVisibility = true;
        }
        viewModel.requestNetWork(model.getId());
        mHandler = new MyHandler(this);

//        fragment = new  OrderDetailApplyInformationFragment();
//        fragmentManager.beginTransaction().replace(R.id.order_detail_apply_information, fragment).commit();

    }

    @Override
    public void initViewObservable() {
        viewModel.unitInformationVisibilityEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                binding.unitInformationList.setVisibility(View.VISIBLE);
            } else {
                binding.unitInformationList.setVisibility(View.GONE);
            }
        });
        viewModel.feeVisibilityEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                binding.feeInformationList.setVisibility(View.VISIBLE);
            } else {
                binding.feeInformationList.setVisibility(View.GONE);
            }
        });
        viewModel.applyMaterialEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                showProgress(false, getString(R.string.loading) + "...");
                CommonRequest.listMaterialByCaseInfoId(viewModel.videoOrderDetailModel.get().getId(), new PopwindowUtil.ResultSecondListener() {
                    @Override
                    public void result(Object value) {
                        hideProgress();
                        if (value != null) {
                            List<MaterialListResponse> materialListResponses = (List<MaterialListResponse>) value;
                            hideProgress();
                            VideoNotarizationMaterialDialog materialDialog = new VideoNotarizationMaterialDialog(OrderDetailActivity.this, viewModel.videoOrderDetailModel.get().getId(), "03", materialListResponses, CommonUtil.SOURCE_FROM_SPBZ, new PopwindowUtil.ResultThirdListener() {
                                @Override
                                public void result(Object value) {
                                    viewModel.requestNetWork(viewModel.videoOrderDetailModel.get().getId());
                                }

                                @Override
                                public void secondResult(Object value) {

                                }

                                @Override
                                public void thirdResult(Object value) {

                                }
                            });
                            if (!materialDialog.isShowing()) {
                                materialDialog.show();
                                CommonRequest.settingDialog(materialDialog, OrderDetailActivity.this);
                            }
                        } else {
                            toastInfo(Utils.getContext().getString(R.string.theMaterialToBeUploadedIsNotFound));
                        }


                    }

                    @Override
                    public void secondResult(Object value) {
                        hideProgress();
                        toastError(value == null ? Utils.getContext().getString(R.string.failedToLoadTheMaterialInformation) : value.toString());
                    }
                });
            }
        });

        viewModel.readDocumentEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                readDocument();
            }
        });
        viewModel.enterAcceptanceRoomEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                if (!TextUtils.isEmpty(viewModel.videoOrderDetailModel.get().getRoomId())) {
                    Intent intent = new Intent(this, CounselingRoomActivity.class);
                    intent.putExtra("roomId", "notary2.0-" + viewModel.videoOrderDetailModel.get().getRoomId());
                    intent.putExtra("orderId", viewModel.videoOrderDetailModel.get().getId());
                    intent.putExtra("mechanismName", viewModel.videoOrderDetailModel.get().getCreatedOrganName());
                    intent.putExtra("from", CommonUtil.SOURCE_FROM_ORDER);
                    intent.putExtra("notaryName", viewModel.videoOrderDetailModel.get().getOfficerName());
                    intent.putExtra("caseInfoId", viewModel.videoOrderDetailModel.get().getId());
                    intent.putExtra("notaryId", viewModel.videoOrderDetailModel.get().getOfficerId());
                    startActivity(intent);
                } else {
                    toastError(getString(R.string.notReceivedRoomNumber));
                }

            }
        });

        viewModel.stopCaseEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                PopwindowUtil.showNormalDialog(this, getString(R.string.whetherToTerminateCase), getString(R.string.theOrdeStatusChangesToTerminatedAfterTermination), getString(R.string.confirm), getString(R.string.cancel), new PopwindowUtil.ButtonClickListener() {

                    @Override
                    public void cancel() {

                    }

                    @Override
                    public void decide() {
                        viewModel.stopCase(model.getId());
                    }
                });
            }
        });

        viewModel.getStopCaseEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                setResult(1, new Intent());
                finish();
                toastSuccess(getString(R.string.successToTerminateCase));
            }
        });

        viewModel.documentPreViewEvent.observe(this, new Observer<List<String>>() {
            @Override
            public void onChanged(List<String> strings) {
                if (strings == null || strings.isEmpty()) {
                    return;
                }
                for (String imageString:strings) {
                    List<ImageInfo> imgs = new ArrayList<>();
                    ImageInfo imageInfo = new ImageInfo();
                    imageInfo.setThumbnailUrl(imageString);
                    imageInfo.setOriginUrl(imageString);
                    imgs.add(imageInfo);
                    ImagePreview.getInstance().setContext(OrderDetailActivity.this).setShowCloseButton(false).setShowDownButton(false).setImageInfoList(imgs).setTransitionShareElementName("shared_element_container").start();
                }
//                if (imagePreviewDialog == null || !imagePreviewDialog.isShowing()) {
//                    ImageInfo imageInfo = new ImageInfo();
//                    imageInfo.setThumbnailUrl(itemData);
//                    imageInfo.setOriginUrl(itemData);
//                    imgs.add(imageInfo);
////                    imagePreviewDialog = new ImagePreviewDialog(OrderDetailActivity.this, strings);
////                    imagePreviewDialog.show();
//                    ImagePreview.getInstance().setContext(mContext).setShowCloseButton(false).setShowDownButton(false).setImageInfoList(imgs).setTransitionShareElementName("shared_element_container").start();
//
//                }
            }
        });


    }

    /**
     * 阅读文书
     */
    private void readDocument() {
        showProgress();
        CommonRequest.readDocument(model.getId(), new PopwindowUtil.ResultSecondListener<List<DocInfoResponse>>() {
            @Override
            public void result(List<DocInfoResponse> value) {
                hideProgress();
                if (value != null && value.size() > 0) {
                    if (lawDialog == null || !lawDialog.isShowing()) {
                        VideoNotarizationLawDialog lawDialog = new VideoNotarizationLawDialog(OrderDetailActivity.this, mHandler, value, model.getId(), "", true, new PopwindowUtil.ResultListener<DocInfoResponse>() {
                            @Override
                            public void result(DocInfoResponse value) {
                                showSignDialog(value);
                            }
                        });
                        if (!lawDialog.isShowing()) {
                            lawDialog.show();
                            Window dialogWindow = lawDialog.getWindow();
                            dialogWindow.getDecorView().setBackgroundColor(getResources().getColor(android.R.color.white));
                            dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
                            // dialogWindow.setWindowAnimations(R.style.mystyle);
                            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
                            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
                            lp.height = WindowManager.LayoutParams.MATCH_PARENT;
                            lp.alpha = 1.0f;
                            dialogWindow.setAttributes(lp);
                            dialogWindow.setGravity(Gravity.RIGHT);
                            lawDialog.setCanceledOnTouchOutside(false);
                        }
                    }
                }
            }

            @Override
            public void secondResult(List<DocInfoResponse> value) {
                hideProgress();
            }
        });
    }

    /**
     * 展示签字
     */
    private void showSignDialog(DocInfoResponse docInfoResponse) {

        signDialog = new SignDialog(this, docInfoResponse.getApplicantName(), new PopwindowUtil.ResultSecondListener() {
            @Override
            public void result(Object value) {
                if (value != null) {
                    Bitmap bitmap = (Bitmap) value;
                    showProgress(getString(R.string.uploadingSignatureFile) + "...");
                    CommonRequest.uploadSignPic(bitmap, new PopwindowUtil.ResultSecondListener() {

                        @Override
                        public void result(Object value) {
                            UploadFileBean uploadFileBean = (UploadFileBean) value;
                            hideProgress();
                            if (uploadFileBean != null) {
                                SignDocRequest signDocRequest = new SignDocRequest();
                                IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(OrderDetailActivity.this, "idcardinfo");
                                if (idCardUserInfo != null) {
                                    signDocRequest.setUserId(idCardUserInfo.getUserId());
                                }
                                signDocRequest.setPicFileId(uploadFileBean.getId());
                                signDocRequest.setCaseInfoId(viewModel.videoOrderDetailModel.get().getId());
                                signDocRequest.setPicFileUrl(uploadFileBean.getSharedUrl());
                                signDocRequest.setIdentityCards(docInfoResponse.getCredentialNum());
                                signDocRequest.setRepresentativeName(docInfoResponse.getApplicantName());
                                List<String> tempList = new ArrayList<>();
                                for (int i = 0; i < docInfoResponse.getDocTypeList().size(); i++) {
                                    if (docInfoResponse.getDocTypeList().get(i) != null) {
                                        for (int j = 0; j < docInfoResponse.getDocTypeList().get(i).getDocumentVOList().size(); j++) {
                                            if (docInfoResponse.getDocTypeList().get(i).getDocumentVOList().get(j) != null) {
                                                tempList.add(docInfoResponse.getDocTypeList().get(i).getDocumentVOList().get(j).getId());
                                            }
                                        }
                                    }

                                }
                                signDocRequest.setIdList(tempList);
                                showProgress(getString(R.string.uploadingSignatureFile) + "...");
                                CommonRequest.signMultiDocument(signDocRequest, new PopwindowUtil.ResultSecondListener() {

                                    @Override
                                    public void result(Object value) {
                                        hideProgress();
                                        toastSuccess(Utils.getContext().getString(R.string.signSuccess));
                                        if (signDialog != null && signDialog.isShowing()) {
                                            signDialog.dismiss();
                                        }
                                        viewModel.readDocumentEvent.setValue(true);
                                        viewModel.requestNetWork(model.getId());
                                    }

                                    @Override
                                    public void secondResult(Object value) {
                                        hideProgress();
                                        toastError(value == null ? Utils.getContext().getString(R.string.signFail) : value.toString());
                                    }

                                });

                            }
                        }

                        @Override
                        public void secondResult(Object value) {
                            hideProgress();
                            toastError(value == null ? Utils.getContext().getString(R.string.failedToUploadTheSignedFile) : value.toString());

                        }
                    });
                }
            }

            @Override
            public void secondResult(Object value) {

            }
        });
        if (!signDialog.isShowing()) {
            signDialog.show();
            Window dialogWindow = signDialog.getWindow();
            dialogWindow.getDecorView().setBackgroundColor(this.getResources().getColor(android.R.color.white));
            dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
            // dialogWindow.setWindowAnimations(R.style.mystyle);
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = ConvertUtils.dp2px(1000f);
            lp.height = ConvertUtils.dp2px(700f);
            lp.alpha = 1.0f;
            dialogWindow.setAttributes(lp);
            dialogWindow.setGravity(Gravity.CENTER);
            signDialog.setCanceledOnTouchOutside(false);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return false;
        }
        return super.onKeyDown(keyCode, event);
    }
}
