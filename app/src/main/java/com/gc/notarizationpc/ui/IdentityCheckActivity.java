package com.gc.notarizationpc.ui;

import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityReservationBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.ui.viewmodel.IdentityCheckViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.gc.notarizationpc.util.PopwindowUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.http.BaseResponse;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class IdentityCheckActivity extends BaseActivity<ActivityReservationBinding, IdentityCheckViewModel> {


    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_identity_check;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public IdentityCheckViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(IdentityCheckViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }

    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });
        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });
    }


    @Override
    public void initViewObservable() {
        viewModel.notaryListSingleLiveEvent.observe(this, new Observer<BaseResponse>() {
            @Override
            public void onChanged(BaseResponse response) {
//                if (response.isSuccess) {
                List<String> list=new ArrayList<>();
                list.add("1");list.add("1");list.add("1");list.add("1");list.add("1");
//                    PopwindowUtil.showNotaryOffice( IdentityCheckActivity.this,list);
//                }

            }
        });

    }


    //    public class ProxyClick {
    public void showTimePickDialog(View view) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");

        //时间选择器
        TimePickerView timePicker = new TimePickerBuilder(IdentityCheckActivity.this, (date, v1) -> {

            String dateString = formatter.format(date);
            Log.d("timePicker", dateString);

            binding.reservationDate.setText(dateString);
        })//年月日时分秒 的显示与否，不设置则默认全部显示
                .setType(new boolean[]{true, true, true, true, true, false})
                .setLabel("年", "月", "日", "时", "分", "秒")
                .setContentTextSize(16)//字体大小
                .setDate(Calendar.getInstance())//设置参数
                .build();
        timePicker.show();
    }

    //    public class ProxyClick {
    public void showReservationSuccess(View view) {
       PopwindowUtil.showReservationSuccess(this, new PopwindowUtil.ButtonClickListener() {
           @Override
           public void cancel() {

           }

           @Override
           public void decide() {

           }
       });
    }




}
