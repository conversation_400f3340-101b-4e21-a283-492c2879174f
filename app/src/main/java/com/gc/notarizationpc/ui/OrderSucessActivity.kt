package com.gc.notarizationpc.ui

import android.content.Intent
import android.view.View
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseActivity

class OrderSucessActivity : BaseActivity() {

    override fun initViewsAndEvents() {


    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_order_sucess;
    }

    override fun getIntentData(intent: Intent?) {


    }

    fun backClicked(view: View?) {
        val intent = Intent(applicationContext, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        startActivity(intent)
        finish();
    }

    fun backToHome(view: View?) {
        val intent = Intent(applicationContext, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        startActivity(intent)
        finish();
    }
}