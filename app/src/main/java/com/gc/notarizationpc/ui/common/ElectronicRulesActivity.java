package com.gc.notarizationpc.ui.common;

import android.os.Bundle;
import android.view.View;

import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityElectronicRuleBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.viewmodel.ElectronicRulesViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.utils.CommonUtil;

/**
 * 在线受理服务使用规则
 */
public class ElectronicRulesActivity extends BaseActivity<ActivityElectronicRuleBinding, ElectronicRulesViewModel> {

    //ActivityLoginBinding类是databinding框架自定生成的,对应activity_login.xml

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_electronic_rule;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public ElectronicRulesViewModel initViewModel() {
//        使用自定义的ViewModelFactory来创建ViewModel，如果不重写该方法，则默认会调用LoginViewModel(@NonNull Application application)构造方法
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(ElectronicRulesViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        //设置状态栏深浅模式false为浅色
        changeStatusBarLight(true);
    }

    @Override
    public void initData() {
        super.initData();
        String version = CommonUtil.getVersion(this);
//        if (!TextUtils.isEmpty(version)) {
//            binding.tvVersion.setText(version);
//        }
//        viewModel.requestNetWork();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });
        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });
    }

    @Override
    public void initViewObservable() {

//        viewModel.loginInfoSingleLiveEvent.observe(this, new Observer<LoginInfo>() {
//            @Override
//            public void onChanged(LoginInfo loginInfo) {
//                Toast.makeText(HomeActivity.this, "登录成功", Toast.LENGTH_SHORT).show();
//            }
//        });
//
//        viewModel.errorStr.observe(this, new Observer<String>() {
//            @Override
//            public void onChanged(String s) {
//                Toast.makeText(HomeActivity.this, "登录失败" + s, Toast.LENGTH_SHORT).show();
//            }
//        });

    }

    public void getVersion(View view) {

    }


}
