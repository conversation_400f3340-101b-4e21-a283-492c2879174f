package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableList;

import com.alibaba.android.arouter.utils.TextUtils;
import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.request.UpdateSelfServiceMaterialRequest;
import com.gc.notarizationpc.data.model.response.BindingNotaryOfficeModel;
import com.gc.notarizationpc.data.model.response.ListRequiredMaterialResponse;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * 这个是自助办证流程中上传材料fragment中材料上传部分的viewModel
 */
public class FragmentDomesticEconomyUploadMaterialViewModel extends BaseViewModel {

    // 获取手机号
    public ObservableField<String> phoneNum = new ObservableField<>("");
    public ObservableField<String> recipient = new ObservableField<>("");//寄件人
    public ObservableField<String> address = new ObservableField<>("");//邮寄地址

    public FragmentDomesticEconomyUploadMaterialViewModel(@NonNull Application application) {
        super(application);
    }

    //给RecyclerView添加ObservableList
    public ObservableList<FragmentDomesticEconomyMaterialToUploadGridViewModel> observableList = new ObservableArrayList<>();
    public List<ListRequiredMaterialResponse.MaterialTypeVoListDTO> parentList = new ObservableArrayList<>();
    //给RecyclerView添加ItemBinding
    public ItemBinding<FragmentDomesticEconomyMaterialToUploadGridViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.fragment_domestic_economy_material_to_upload_grid);//这里指定了item的


    public SingleLiveEvent lastStepEvent = new SingleLiveEvent();

    public SingleLiveEvent nextStepEvent = new SingleLiveEvent();

    public SingleLiveEvent<Map<String, Object>> childItemClickEvent = new SingleLiveEvent();

    public SingleLiveEvent postInfoEvent = new SingleLiveEvent();
    /**
     * 更新材料
     */
    public void updateSelfServiceMaterial(BindingNotaryOfficeModel notaryOfficeModel, int receiveWay, List<UpdateSelfServiceMaterialRequest.MaterialInfoListDTO> materialInfoListDTOList) {
        //1-自取，2-邮寄(到付)
        if (receiveWay == 2) {
            if (TextUtils.isEmpty(recipient.get())) {
                toastError(Utils.getContext().getString(R.string.username_hint));
                return;
            } else if (TextUtils.isEmpty(phoneNum.get())) {
                toastError(Utils.getContext().getString(R.string.phone_hint));
                return;
            } else if (phoneNum.get().length() != 11) {
                toastError(Utils.getContext().getString(R.string.pleaseInputCorrectPhoneNumber));
                return;
            } else if (TextUtils.isEmpty(address.get())) {
                toastError(Utils.getContext().getString(R.string.address_hint));
                return;
            }
        }

        UpdateSelfServiceMaterialRequest request = new UpdateSelfServiceMaterialRequest();
        String recordId = SPUtils.getInstance().getString("self_recordId");
        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
        request.setSelfId(recordId);
        if (idCardUserInfo != null) {
            request.setSysUserId(idCardUserInfo.getUserId());
        }
        if (notaryOfficeModel != null) {
            request.setCreatedOrgan(notaryOfficeModel.getId());//公证处ID
        }
        request.setReceiveWay(receiveWay);
        request.setPhoneNum(phoneNum.get());
        request.setConsigneeName(recipient.get());
        request.setPostAddress(address.get());

        request.setMaterialInfoList(materialInfoListDTOList);

        RequestUtil.updateRecordPreProcessMaterial(request, new MyObserver() {
            @Override
            public void onSuccess(Object response) {
                Map<String, Object> param = new HashMap<>();
                param.put("flag", 1004);
                param.put("value", null);
                childItemClickEvent.setValue(param);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.update_self_service_material) : errorMsg);
            }
        });
    }


    //查询所需材料
    public void listRequiredMaterial(BindingNotaryOfficeModel selectNotary, String recordId) {
//        showDialog();
        if (selectNotary == null) {
            return;
        }
        RequestUtil.listRequiredRecordPreProcessMaterial(selectNotary.getId(), recordId, new MyObserver<ListRequiredMaterialResponse>() {
            @Override
            public void onSuccess(ListRequiredMaterialResponse response) {
                if (response == null || response.getMaterialTypeVoList()==null || response.getMaterialTypeVoList().size() == 0) {
                    return;
                }
                parentList = response.getMaterialTypeVoList();
                postInfoEvent.setValue(response.getPostInfo());
//                dismissDialog();
                observableList.clear();
                for (ListRequiredMaterialResponse.MaterialTypeVoListDTO tmp : response.getMaterialTypeVoList()) {
                    FragmentDomesticEconomyMaterialToUploadGridViewModel fragmentDomesticEconomyMaterialToUploadGridViewModel = new FragmentDomesticEconomyMaterialToUploadGridViewModel(FragmentDomesticEconomyUploadMaterialViewModel.this, tmp);
                    fragmentDomesticEconomyMaterialToUploadGridViewModel.childObservableList.clear();
                    for (ListRequiredMaterialResponse.MaterialVoListDTO tmpDto : tmp.getMaterialVoList()) {
                        tmpDto.setShowDel(true);
                        fragmentDomesticEconomyMaterialToUploadGridViewModel.childObservableList.add(new FragmentDomesticEconomyMaterialToUploadGridItemViewModel(fragmentDomesticEconomyMaterialToUploadGridViewModel, tmpDto));
                    }
                    if (fragmentDomesticEconomyMaterialToUploadGridViewModel.childObservableList.size() < 30) {
                        //增加一个+
                        ListRequiredMaterialResponse.MaterialVoListDTO addDto = new ListRequiredMaterialResponse.MaterialVoListDTO();
                        addDto.setFileUrl("");
                        addDto.setShowDel(false);
                        fragmentDomesticEconomyMaterialToUploadGridViewModel.childObservableList.add(new FragmentDomesticEconomyMaterialToUploadGridItemViewModel(fragmentDomesticEconomyMaterialToUploadGridViewModel, addDto));
                    }
                    observableList.add(fragmentDomesticEconomyMaterialToUploadGridViewModel);
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
//                dismissDialog();
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.failedToInquryMaterials) : errorMsg);
            }
        });
    }


    // 上一步
    public BindingCommand lastStep = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            Log.i("lastStep", "是否调用了");
            lastStepEvent.setValue("");
        }
    });

    // 下一步
    public BindingCommand nextStep = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            Log.i("nextStep", "是否调用了");
            nextStepEvent.setValue("");
        }
    });


    public int getItemCount() {
        return observableList.size();
    }
}
