package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.VideoOrderListModel;
import com.gc.notarizationpc.ui.inquirycertification.OrderDetailActivity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * Created by goldze on 2017/7/17.
 */

public class FragmentGridViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    private int pageNo = 1;
    private int pageSize = 9;

    public class UIChangeObservable {
        //下拉刷新完成
        public SingleLiveEvent finishRefreshing = new SingleLiveEvent<>();
        //上拉加载完成
        public SingleLiveEvent finishLoadmore = new SingleLiveEvent<>();
    }

    public FragmentGridViewModel(@NonNull Application application) {
        super(application);
        Log.e("FragmentGridViewModel", "FragmentGridViewModel");

    }


    // 下拉上拉刷新动画事件
    public SingleLiveEvent<Integer> refreshEvent = new SingleLiveEvent<>();

    // 手机号
    public ObservableField<String> mPhoneNumber = new ObservableField<>();

    // 身份证号
    public ObservableField<String> mIdCardNumber = new ObservableField<>();

    public SingleLiveEvent<List<VideoOrderListModel.DataListDTO>> notaryOrderListEvent = new SingleLiveEvent<>();

    private List<VideoOrderListModel.DataListDTO> notaryOrderList = new ArrayList<>();

    // 进入详情界面
    public SingleLiveEvent<VideoOrderListModel.DataListDTO> comeIntoOrderDetail = new SingleLiveEvent<>();

    // 阅读文书
    public SingleLiveEvent<VideoOrderListModel.DataListDTO> readDocumentsEvent = new SingleLiveEvent<>();

    // 补充材料
    public  SingleLiveEvent<VideoOrderListModel.DataListDTO> applyMaterialEvent = new SingleLiveEvent<>();

    // 进入受理室
    public  SingleLiveEvent<VideoOrderListModel.DataListDTO> enterRoomEvent = new SingleLiveEvent<>();

    //给RecyclerView添加ObservableList
    public ObservableList<FragmentGridItemViewModel> observableList = new ObservableArrayList<>();
    //给RecyclerView添加ItemBinding
    public ItemBinding<FragmentGridItemViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.item_grid_notary_order);//这里指定了item的布局

    //请求出错了接口回调
    public SingleLiveEvent<Boolean> networkErrorEvent = new SingleLiveEvent<>();
    //下拉刷新
    public BindingCommand onRefreshCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            pageNo = 1;
            requestNetWork();
            refreshEvent.setValue(0);
        }
    });
    //上拉加载
    public BindingCommand onLoadMoreCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            pageNo++;
            requestNetWork();
            refreshEvent.setValue(1);

        }
    });


    /**
     * 网络请求方法，在ViewModel中调用Model层，通过Okhttp+Retrofit+RxJava发起请求
     * currentPage	当前页	query	true
     * integer(int32)
     * pageSize	当前页条数	query	true
     * integer(int32)
     * idCardNum	身份证号	query	false
     * string
     * phoneNum	手机号	query	false
     * string
     * searchType	搜索条件类型:1-身份证号，2-手机号	query	true
     * integer(int32)
     * validNum	验证码	query	false
     * string
     * VERSION	灰度路由版本信息	header	false
     * string
     */
    public void requestNetWork() {
//        showDialog("加载中");
        showDialog(Utils.getContext().getString(R.string.loading));
        Map dataSource = new HashMap();
        if (!TextUtils.isEmpty(mPhoneNumber.get())){
            dataSource.put("phoneNum", mPhoneNumber.get());
            dataSource.put("searchType",2);
        }
        if (!TextUtils.isEmpty(mIdCardNumber.get())){
            dataSource.put("idCardNum", mIdCardNumber.get());
            dataSource.put("searchType",1);
        }

        dataSource.put("currentPage",pageNo);
        dataSource.put("pageSize",pageSize);
        RequestUtil.getOrderList(dataSource, new MyObserver<VideoOrderListModel>() {
            @Override
            public void onSuccess(VideoOrderListModel result) {
               dismissDialog();
                if (result !=null && result.getDataList()!=null){
                    if (pageNo == 1){
                        observableList.clear();
                        notaryOrderList.clear();
                    }
                    notaryOrderList.addAll(result.getDataList());
                    if (result.getDataList().size() > 0){
                        notaryOrderListEvent.setValue(result.getDataList());
                    }
                    if (result.getTotal()==notaryOrderList.size()){
                        refreshEvent.setValue(2);
                    }

                }
                if (notaryOrderList!=null && notaryOrderList.size() >0){
                    networkErrorEvent.setValue(false);
                }else {
                    networkErrorEvent.setValue(true);
                }
            }
            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
//                Log.i("onFailure",e.toString() + errorMsg);
                toastError(errorMsg == null || errorMsg.isEmpty() ? Utils.getContext().getString(R.string.requestFailTrylater) :errorMsg);
                networkErrorEvent.setValue(true);
            }

        });

    }

    // 网络请求接口数据处理
    public void dealNetWorkData(List<VideoOrderListModel.DataListDTO> modelList, Context context) {
        for (VideoOrderListModel.DataListDTO model : modelList) {
            FragmentGridItemViewModel itemViewModel = new FragmentGridItemViewModel(FragmentGridViewModel.this, model);
            /**
             * 案件状态 5-受理中 6-待审批 7-待发证 8-待归档 9-已归档 10-待阅读文书 11-待补充材料 12-待支付 91-已终止
             */
            // 待补充材料
            if (model.getStatus() == 11) {
                itemViewModel.itemGridNotaryOrderNotaryStatusString = Utils.getContext().getString(R.string.waitToSupplementaryMaterials);
                itemViewModel.itemGridNotaryOrderNotaryStatusColor = R.drawable.notary_item_state_background_fcedce;
                itemViewModel.itemGridNotaryOrderNotaryStatusTextColor =  context.getResources().getColor(R.color.colorF5B73D);//Color.parseColor("#F5B73D");
                itemViewModel.itemGridNotaryEnterRoomStatus = View.VISIBLE;
                itemViewModel.itemGridNotarySupplyMaterialsStatus =  View.VISIBLE;
                itemViewModel.itemGridNotaryReadInstrumentStatus = View.GONE;
            } else if (model.getStatus() == 10) {
                // 待阅读文书
                itemViewModel.itemGridNotaryOrderNotaryStatusString = Utils.getContext().getString(R.string.waitingToReadInstrument);
                itemViewModel.itemGridNotaryOrderNotaryStatusColor = R.drawable.notary_item_state_background_ffe8de;
                itemViewModel.itemGridNotaryOrderNotaryStatusTextColor =  context.getResources().getColor(R.color.colorFB956A);//Color.parseColor("#FB956A");//R.color.colorFB956A;
                itemViewModel.itemGridNotaryEnterRoomStatus = View.VISIBLE;
                itemViewModel.itemGridNotarySupplyMaterialsStatus = View.GONE;
                itemViewModel.itemGridNotaryReadInstrumentStatus = View.VISIBLE;

            } else if (model.getStatus() == 12) {
                // 待支付
                itemViewModel.itemGridNotaryOrderNotaryStatusString = Utils.getContext().getString(R.string.waitToPay);
                itemViewModel.itemGridNotaryOrderNotaryStatusColor = R.drawable.notary_item_state_background_fbeae9;
                itemViewModel.itemGridNotaryOrderNotaryStatusTextColor = context.getResources().getColor(R.color.colorE88379) ;//Color.parseColor("#E88379");//R.color.colorE88379;
                itemViewModel.itemGridNotaryEnterRoomStatus = View.VISIBLE;
                itemViewModel.itemGridNotarySupplyMaterialsStatus = View.GONE;
                itemViewModel.itemGridNotaryReadInstrumentStatus = View.GONE;
            } else if (model.getStatus() == 91) {
                // 已终止
                itemViewModel.itemGridNotaryOrderNotaryStatusString = Utils.getContext().getString(R.string.terminated);
                itemViewModel.itemGridNotaryOrderNotaryStatusColor = R.drawable.notary_item_state_background_e8e8eb;
                itemViewModel.itemGridNotaryOrderNotaryStatusTextColor = context.getResources().getColor(R.color.colorB9BBC2);// Color.parseColor("#B9BBC2"); //R.color.colorB9BBC2;
                itemViewModel.itemGridNotaryEnterRoomStatus = View.GONE;
                itemViewModel.itemGridNotarySupplyMaterialsStatus = View.GONE;
                itemViewModel.itemGridNotaryReadInstrumentStatus = View.GONE;

            } else if (model.getStatus() == 8 || model.getStatus() ==9) {
                // 已归档 (已出证)
                itemViewModel.itemGridNotaryOrderNotaryStatusString = Utils.getContext().getString(R.string.hasBeenTestified);
                itemViewModel.itemGridNotaryOrderNotaryStatusColor = R.drawable.notary_item_state_background_e7ecfe;
                itemViewModel.itemGridNotaryOrderNotaryStatusTextColor =  context.getResources().getColor(R.color.color6D8FF7);//Color.parseColor("#6D8FF7");//R.color.color6D8FF7;
                itemViewModel.itemGridNotaryEnterRoomStatus = View.GONE;
                itemViewModel.itemGridNotarySupplyMaterialsStatus = View.GONE;
                itemViewModel.itemGridNotaryReadInstrumentStatus = View.GONE;
            } else if (model.getStatus() == 7) {
                // 待发证
                itemViewModel.itemGridNotaryOrderNotaryStatusString = Utils.getContext().getString(R.string.toBeIssued);
                itemViewModel.itemGridNotaryOrderNotaryStatusColor = R.drawable.notary_item_state_background_e7ecfe;
                itemViewModel.itemGridNotaryOrderNotaryStatusTextColor = context.getResources().getColor(R.color.color6D8FF7);//Color.parseColor("#6D8FF7");//R.color.color6D8FF7;
                itemViewModel.itemGridNotaryEnterRoomStatus = View.GONE;
                itemViewModel.itemGridNotarySupplyMaterialsStatus = View.GONE;
                itemViewModel.itemGridNotaryReadInstrumentStatus = View.GONE;
            } else if (model.getStatus() == 5 || model.getStatus() ==6){
                // 待发证
                itemViewModel.itemGridNotaryOrderNotaryStatusString = Utils.getContext().getString(R.string.underConsideration);
                itemViewModel.itemGridNotaryOrderNotaryStatusColor = R.drawable.notary_item_state_background_e3f0ff;
                itemViewModel.itemGridNotaryOrderNotaryStatusTextColor = context.getResources().getColor(R.color.color6DB2F7) ;//Color.parseColor("#6DB2F7");//R.color.color6DB2F7;
                itemViewModel.itemGridNotaryEnterRoomStatus = View.VISIBLE;
                itemViewModel.itemGridNotarySupplyMaterialsStatus = View.VISIBLE;
                itemViewModel.itemGridNotaryReadInstrumentStatus = View.GONE;
            }
            observableList.add(itemViewModel);

        }
    }

    /**
     * 删除条目
     *
     * @param netWorkItemViewModel
     */
    public void deleteItem(FragmentGridItemViewModel netWorkItemViewModel) {
        //点击确定，在 observableList 绑定中删除，界面立即刷新
        observableList.remove(netWorkItemViewModel);
    }

    /**
     * 获取条目下标
     *
     * @param netWorkItemViewModel
     * @return
     */
    public int getItemPosition(FragmentGridItemViewModel netWorkItemViewModel) {
        return observableList.indexOf(netWorkItemViewModel);
    }

    public  int getItemCount(){
        return  observableList.size();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    // 订单详情
    public void itemClick(VideoOrderListModel.DataListDTO entity) {
        Log.i("FragmentGridViewModel","FragmentGridViewModel itemClick");
        Bundle mBundle = new Bundle();
        mBundle.putSerializable("orderData",entity);
        startActivity(OrderDetailActivity.class,mBundle);
//        startActivityForResult(OrderDetailActivity.class,mBundle);
    }

    // 阅读文书
    public void readDocument(VideoOrderListModel.DataListDTO entity){
        readDocumentsEvent.setValue(entity);
    }

    // 补充材料
    public void applyMaterial(VideoOrderListModel.DataListDTO entity){
        applyMaterialEvent.setValue(entity);
    }

    // 进入受理室
    public void enterRoom(VideoOrderListModel.DataListDTO entity){
        enterRoomEvent.setValue(entity);
    }

}
