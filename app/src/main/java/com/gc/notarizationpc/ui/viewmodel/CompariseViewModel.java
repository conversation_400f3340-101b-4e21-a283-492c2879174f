package com.gc.notarizationpc.ui.viewmodel;

import static com.gc.notarizationpc.util.CommonUtil.bitmapToFile;
import static me.goldze.mvvmhabit.utils.Utils.getContext;

import android.app.Application;
import android.graphics.Bitmap;
import android.util.Log;

import androidx.annotation.NonNull;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.SimilarityResponse;
import com.google.gson.Gson;
import com.serenegiant.arcface.util.BitmapUtils;

import java.io.File;
import java.util.HashMap;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;


public class CompariseViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<SimilarityResponse> faceComparisePassEvent = new SingleLiveEvent<>();//人脸对比通过

    public class UIChangeObservable {

    }

    public CompariseViewModel(@NonNull Application application) {
        super(application);
    }


    /**
     * 返回
     */
    public BindingCommand goBackEvent = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            finish();
        }
    });

    /**
     * 确定
     */
    public BindingCommand decideEvent = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            finish();
        }
    });

    /**
     * 人脸相似度
     */
    public void universalSimilarity(Bitmap bitmap) {
        if (bitmap != null) {
            File file = bitmapToFile(bitmap);
            RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);
            MultipartBody.Part body = MultipartBody.Part.createFormData("file", file.getName(), requestFile);
            IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(getContext(), "idcardinfo");

            if (idCardUserInfo != null) {
                HashMap<String, String> tmp = new HashMap<>();
                tmp.put("userName", idCardUserInfo.getName());
                tmp.put("idCard", idCardUserInfo.getIdCard());
                Gson gson = new Gson();

                showDialog("");//RequestBody.create(MediaType.parse("encryptStr"),
                RequestUtil.universalSimilarity(RequestBody.create(MediaType.parse("encryptStr"),gson.toJson(tmp)), body, new MyObserver<SimilarityResponse>() {
                    @Override
                    public void onSuccess(SimilarityResponse result) {
                        dismissDialog();
                        if (result != null) {
                            faceComparisePassEvent.setValue(result);
                        }
                    }

                    @Override
                    public void onFailure(Throwable e, String errorMsg) {
                        dismissDialog();
                        Log.e("Test", "图片上传失败");
                        faceComparisePassEvent.setValue(null);
                        toastError(errorMsg == null ? Utils.getContext().getString(R.string.face_comparise_fail) : errorMsg);
                    }
                });
            }


//            if (idCardUserInfo != null) {
//                HashMap<String, Object> params = new HashMap<>();
//                params.put("idName", idCardUserInfo.getName());
//                params.put("idCardNo", idCardUserInfo.getIdCard());
//                params.put("fileContent", BitmapUtils.bitmapToBase64Ini(bitmap).replaceAll("\n",""));
//                params.put("bizId", System.currentTimeMillis()+"");
//
//                showDialog("");//RequestBody.create(MediaType.parse("encryptStr"),
//                RequestUtil.faceDetectToPDF(params, new MyObserver<SimilarityResponse>() {
//                    @Override
//                    public void onSuccess(SimilarityResponse result) {
//                        dismissDialog();
//                        if (result != null) {
//                            faceComparisePassEvent.setValue(result);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(Throwable e, String errorMsg) {
//                        dismissDialog();
//                        Log.e("Test", "图片上传失败");
//                        faceComparisePassEvent.setValue(null);
//                        toastError(errorMsg == null ? Utils.getContext().getString(R.string.face_comparise_fail) : errorMsg);
//                    }
//                });
//            }


        }

    }


}
