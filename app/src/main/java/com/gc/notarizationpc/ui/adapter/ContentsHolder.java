package com.gc.notarizationpc.ui.adapter;

import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.gc.notarizationpc.R;


public class ContentsHolder extends RecyclerView.ViewHolder {
    public TextView descView;

    public ContentsHolder(View itemView) {
        super(itemView);
        initView();
    }

    private void initView() {
        descView = (TextView) itemView.findViewById(R.id.tv_desc);
    }
}
