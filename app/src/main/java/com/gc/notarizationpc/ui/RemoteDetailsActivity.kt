package com.gc.notarizationpc.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Message
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.fastjson.JSON
import com.baidu.location.BDLocation
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.example.framwork.utils.DialogUtils
import com.example.framwork.utils.MyLogUtils
import com.example.framwork.utils.SPUtils
import com.example.framwork.utils.ToastUtils
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.AppConfig
import com.gc.notarizationpc.common.BaseActivity
import com.gc.notarizationpc.common.NotarizationBean
import com.gc.notarizationpc.common.RemotesBean.ItemsDTO
import com.gc.notarizationpc.model.*
import com.gc.notarizationpc.ui.adapter.DeviceAdapter
import com.gc.notarizationpc.ui.adapter.FullyGridLayoutManager
import com.gc.notarizationpc.ui.adapter.GridImageAdapter
import com.gc.notarizationpc.ui.adapter.RemoteDetailsAdapter
import com.gc.notarizationpc.ui.presenter.LocationPresenter
import com.gc.notarizationpc.ui.presenter.LocationSuccessListener
import com.gc.notarizationpc.ui.presenter.MainPresenter
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter
import com.gc.notarizationpc.utils.CommonUtil
import com.gc.notarizationpc.utils.MacUtils
import com.gc.notarizationpc.utils.MapUtils
import com.gc.notarizationpc.websocket.MyMqttService
import com.gc.notarizationpc.websocket.WebSocketCallBack
import com.ldoublem.loadingviewlib.view.LVPlayBall
import com.tencent.trtc.TRTCCloud
import kotlinx.android.synthetic.main.activity_remote_details.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.Subscribe
import java.lang.reflect.Method


class RemoteDetailsActivity : BaseActivity(), NotarizationPresenter.INotarizationView, WebSocketCallBack, NotarizationPresenter.IUpdateRemoteImageView, NotarizationPresenter.UpdateRemoteView, NotarizationPresenter.DeletePhotoView, LocationSuccessListener, MainPresenter.IHomeView, OnItemClickListener {
    private var TAG: String = "RemoteDetailsActivityTAG"
    private var notarizationP: NotarizationPresenter? = null
    private var backClicked: ImageView? = null
    private var rvApplicant: RecyclerView? = null
    private var propertyInformationRv: RecyclerView? = null
    private var propertyInformationLL: LinearLayout? = null
    private var rvProxy: RecyclerView? = null
    private var bt_initiateNotarization: Button? = null
    private var isWaiting: Boolean? = true
    var stopP: NotarizationPresenter? = null
    var ImgList = ArrayList<String>()

    private var bean: ItemsDTO? = null
    private var position: Int? = null

    private var quickAdapter: BaseQuickAdapter<ItemsDTO?, BaseViewHolder>? = null
    private var notarizationInfo: NotarizationInfo? = null
    private var waitDialog: Dialog? = null
    private var playBall: LVPlayBall? = null

    private var adapter: GridImageAdapter? = null
    private val maxSelectNum = 10
    private val selectList = ArrayList<ImageRemoteBean>()

    private var socketSendCode: String? = null
    private var gridImageAdapter: GridImageAdapter? = null
    private var mTRTCCloud // SDK 核心类
            : TRTCCloud? = null
    private var updateP: NotarizationPresenter? = null
    private var updateRemoteP: NotarizationPresenter? = null
    private var deletePhotoP: NotarizationPresenter? = null
    private var propertyInformationAdapter: RemoteDetailsAdapter? = null
    private var myMqttService: MyMqttService? = null
    var mOnAddPicClickListener: GridImageAdapter.onAddPicClickListener? = null
    var mOnDelPicClickListener: GridImageAdapter.onDelPicClickListener? = null
    var gao: Boolean = false
    var longitude: String = ""
    var latitude: String = ""
    var address: String = ""

    var REFRESH_NOTARIZATION: Int? = 0x01
    private var queryP: MainPresenter? = null
    private var deviceAdapter: DeviceAdapter? = null
    private var deviceDialog: Dialog? = null
    private var changelocation: TextView? = null
    var city: String = ""//市
    var adcode: String = ""//区域码
    var isConfirm: Boolean = false
    val handler: Handler = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message?) {
            super.handleMessage(msg)
            when (msg?.what) {
                REFRESH_NOTARIZATION -> {
                    changelocation?.text = city
                    Log.d("zpzp", "refresh adcode == " + adcode)
                    queryP?.getNotaryList(adcode, CommonUtil.bd_decrypt(longitude, latitude),true)

                }
            }
        }
    }
    override fun getIntentData(intent: Intent) {
        bean = intent.getSerializableExtra("bean") as ItemsDTO
        position = intent.getIntExtra("position",0)
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_remote_details
    }

    override fun initViewsAndEvents() {
        deviceAdapter = DeviceAdapter()
        deviceAdapter?.setOnItemClickListener(this)
        queryP = MainPresenter(mActivity, this)
        Log.i(TAG, "initViewsAndEvents")
        backClicked = findViewById(R.id.backClicked)
        rvApplicant = findViewById(R.id.rvApplicant)
        propertyInformationRv = findViewById(R.id.propertyInformationRv)
        propertyInformationLL = findViewById(R.id.propertyInformationLL)
        rvProxy = findViewById(R.id.rvProxy)
        bt_initiateNotarization = findViewById(R.id.bt_initiateNotarization)
        myMqttService = MyMqttService(this, ".topic.bank.collect." + MacUtils.getMacAddress(this), ".topic.bank." + MacUtils.getMacAddress(this), AppConfig.HOST, this)
        notarizationP = NotarizationPresenter(mActivity, this as NotarizationPresenter.INotarizationView)
        updateP = NotarizationPresenter(mActivity, this as NotarizationPresenter.IUpdateRemoteImageView)
        updateRemoteP = NotarizationPresenter(mActivity, this as NotarizationPresenter.UpdateRemoteView)
        deletePhotoP = NotarizationPresenter(mActivity, this as NotarizationPresenter.DeletePhotoView)
        stopP = NotarizationPresenter(mActivity)
        getApplicantSent()
        initPropertyInformation()
        initData()
        LocationPresenter.getInstance().initLocation(mActivity, this)
        LocationPresenter.getInstance().signIn()
    }

    override fun locationFaild() {
        toastError("获取位置失败，请检查权限是否开启")
    }

    override fun locationSuccess(activity: Activity?, bdLocation: BDLocation?) {
        if (bdLocation != null && null != bdLocation.addrStr) {
            LocationPresenter.getInstance().stopLocation()
            Log.i(TAG, bdLocation.addrStr)
            address = bdLocation.addrStr
            longitude = bdLocation.longitude.toString() + ""
            latitude = bdLocation.latitude.toString() + ""
            city = bdLocation.city
            adcode = CommonUtil.getcode(mActivity, city)
            takelocation()
            Log.i(TAG, "longitude ---" + longitude + "latitude---" + latitude)
        } else {
            MyLogUtils.i(TAG, "获取位置失败")
            toastError("获取位置失败，请检查权限是否开启")
        }
    }

    /**
     * 借款信息
     */
    private fun getApplicantSent() {
        Log.i(TAG, "getApplicantSent")
        rvApplicant?.isNestedScrollingEnabled = false
        rvApplicant?.adapter = object : BaseQuickAdapter<ItemsDTO?, BaseViewHolder>(R.layout.item_small_amount_information) {
            override fun convert(holder: BaseViewHolder, item: ItemsDTO?) {
                holder.setText(R.id.tv_purpose, item?.purpose)
                holder.setText(R.id.tv_loanStartDate, item?.loanStartDate)
                holder.setText(R.id.tv_loanEndDate, item?.loanEndDate)
                holder.setText(R.id.tv_term, item?.term + "天")
                holder.setText(R.id.tv_repaymentType, item?.repaymentType)
                holder.setText(R.id.tv_fee, item?.fee)
            }
        }.also { quickAdapter = it }
        quickAdapter!!.addData(bean)
    }

    /**
     * 财产信息
     */
    private fun initPropertyInformation() {
        Log.i(TAG, "initPropertyInformation")
        if (null == bean?.html || bean?.html!!.size == 0) {
            propertyInformationLL?.visibility = View.GONE
        } else {
            propertyInformationLL?.visibility = View.VISIBLE
            val linearLayoutManager: LinearLayoutManager = object : LinearLayoutManager(mActivity, VERTICAL, false) {
                override fun canScrollVertically(): Boolean {
                    return false
                }
            }
            propertyInformationRv?.layoutManager = linearLayoutManager
            propertyInformationAdapter = RemoteDetailsAdapter(mActivity, bean?.html!!)
            propertyInformationRv?.adapter = propertyInformationAdapter
        }
    }

    /**
     * 材料信息
     */
    private fun initData() {
        Log.i(TAG, "initData")
        val split = bean?.material?.split("、")
        val count = if (AppConfig.RGBPID == "c013") {
            5
        } else {
            3
        }
        val linearLayoutManager: LinearLayoutManager = object : FullyGridLayoutManager(this, count, GridLayoutManager.VERTICAL, false) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
        mOnAddPicClickListener = GridImageAdapter.onAddPicClickListener() {
            Log.i(TAG, "takePhoto click")
            takePhoto(it)
        }
        mOnDelPicClickListener = GridImageAdapter.onDelPicClickListener {
            Log.i(TAG, "del photo click")
            deletePhotoP?.deletePhoto(selectList[it].unitGuid, it)
        }
        imgProxyRv.layoutManager = linearLayoutManager
        gridImageAdapter = GridImageAdapter(mActivity, mOnAddPicClickListener, mOnDelPicClickListener)
        if (null != split && split.isNotEmpty()) {
            for (i in split.indices) {
                val imageBean = ImageRemoteBean()
                imageBean.materialName = split[i]
                for (j in bean?.imgPath?.indices!!) {
                    if (split[i] == bean?.imgPath!![j].materialName) {
                        imageBean.filePath = bean?.imgPath!![j].path
                        imageBean.unitGuid = bean?.imgPath!![j].unitGuid
                    }
                }
                selectList.add(imageBean)
            }
        }
        gridImageAdapter?.setList(selectList)
        gridImageAdapter?.setSelectMax(maxSelectNum)
        imgProxyRv.adapter = gridImageAdapter
    }

    /**
     * 删除照片成功
     */
    override fun deletePhotoSuccess(position: Int) {
        Log.i(TAG, "deletePhotoSuccess")
        selectList[position].filePath = ""
        gridImageAdapter?.setList(selectList)
        gridImageAdapter?.notifyDataSetChanged()
        ToastUtils.getInstance(mActivity).toastSuccess("删除成功")
    }

    /**
     * 跳转到拍照页面
     */
    private fun takePhoto(materialName: String) {
        Log.i(TAG, "takePhoto")
        toastInfo("准备拍摄照片,请做好准备")
        val intent = Intent(mActivity, TakePhotoActivity::class.java)
        intent.putExtra("come", 3)
        intent.putExtra("materialName", materialName)
        readyGo(intent)
    }

    /**
     * 先把照片上传到服务器
     */
    @Subscribe
    fun onEventMainThread(eb: RemotePhotoInfo) {
        Log.i(TAG, "takePhoto")
        updateP?.updateRemoteImage(eb.path, eb.materialName)
    }

    /**
     * 上传照片到服务器成功
     */
    override fun updateRemoteImageSuccess(eb: ImageBean?, materialName: String?) {
        Log.i(TAG, "updateImageSuccess")
        updateRemoteP?.updateInfo(eb, bean?.bankOrderId, materialName)
    }

    /**
     * 上传照片到服务器失败
     */
    override fun updateRemoteImageFail() {
        Log.i(TAG, "updateImageFail")
        ToastUtils.getInstance(this).toastError("上传失败")
    }

    /**
     * 照片和订单id绑定成功
     */
    override fun updateRemoteSuccess(bean: ImageBean?, unitGuid: String, materialName: String?) {
        Log.i(TAG, "updateRemoteSuccess")
        val imageBean = ImageRemoteBean()
        imageBean.filePath = bean?.filePath
        imageBean.unitGuid = unitGuid
        imageBean.materialName = materialName
        for (i in selectList.indices) {
            if (selectList[i].materialName == materialName) {
                selectList[i] = imageBean
            }
        }
        gridImageAdapter?.setList(selectList)
        gridImageAdapter?.notifyDataSetChanged()
    }

    fun backClicked(v: View) {
        Log.i(TAG, "backClicked")
        finish()
    }

    fun btInitiateNotarization(v: View) {
        Log.i(TAG, "btInitiateNotarization")
        initNotar()
    }

    override fun getMacAddress(): String {
        return MacUtils.getMacAddress(mActivity)
    }

    override fun showNotary(notaryId: String?, success: Boolean) {
    }

    override fun showNotaryList(bean: NotarizationBean?) {
        if (null != bean?.getItems()) {
            showDeviceDialog(bean?.getItems() as List<DeviceInfo>)
        } else {
            MyLogUtils.i(TAG, "该地区暂无在线公证人员")
            showDeviceDialog(listOf())
        }
    }

    override fun getAccessPublicKeySuccess() {
    }

    override fun getAccessPublicKeyFail(error: String?) {
    }

    override fun mechineAddSuccess() {
    }

    override fun mechineAddFail() {
    }

    private fun showDeviceDialog(l: List<DeviceInfo>) {
        if (deviceDialog == null) {
            deviceDialog = DialogUtils.getInstance().getRightDialog(mActivity, true, R.layout.dialog_divers)
            var dialogDeviceRv: RecyclerView? = deviceDialog?.findViewById(R.id.rvDevices)
            changelocation = deviceDialog?.findViewById(R.id.change_location)
            if (!TextUtils.isEmpty(city))
                changelocation?.setText(city)
            dialogDeviceRv?.adapter = deviceAdapter
        }
        deviceAdapter?.addNewData(l)
//        deviceDialog?.setCancelable(false)
//        deviceDialog?.setCanceledOnTouchOutside(false)
        deviceDialog?.show()
    }

    override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
        dismissDialog()
        var device: DeviceInfo? = adapter.getItem(position) as DeviceInfo?
//        queryP?.addNotary(device?.unitGuid, device?.notarialName)
        if (!"3".equals(device?.institutionType))
            isConfirm = true
        notaryId = device?.unitGuid
    }

    private fun dismissDialog() {
        if (deviceDialog != null && deviceDialog?.isShowing == true) {
            deviceDialog?.dismiss()
        }
    }
    fun change_location_text(newcity: String?) {
        if (deviceDialog != null)
            deviceDialog?.show()
        //根据城市获取地区码和经纬度
        Thread(Runnable {
            try {
                var ll = MapUtils.getCoordinate(newcity)
                longitude = ll[0].toString()
                latitude = ll[1].toString()
                adcode = CommonUtil.getcode(mActivity, newcity)
                if (changelocation != null && !TextUtils.isEmpty(latitude) && !TextUtils.isEmpty(latitude)) {
                    //有网络 获取到经纬度的条件下 赋值  刷新
                    if (newcity != null) {
                        this.city = newcity
                    }
                    REFRESH_NOTARIZATION?.let { handler.sendEmptyMessage(it) }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }).start()

    }
    fun takelocation() {
        if ("3".equals(SPUtils.getInstance().get(mActivity, "institutionType", "1")) && !isConfirm) {
//            //重新选择公证处
            adcode = CommonUtil.subcode(adcode);
            queryP?.getNotaryList(adcode, CommonUtil.bd_decrypt(longitude, latitude),true)
        }
    }
    fun changeLocation(view: View?) {
        if (null != deviceDialog && deviceDialog?.isShowing == true)
            deviceDialog?.dismiss()
        var pop = com.gc.notarizationpc.utils.PopwindowUtil()
        pop?.showpop(mActivity, findViewById(R.id.remote_pop),mActivity)
    }
    /**
     * 发起公证
     */
    private fun initNotar() {
        Log.i(TAG, "initNotar")
        if ("3".equals(SPUtils.getInstance().get(mActivity, "institutionType", "1"))&&!isConfirm) {
//            //重新选择公证处
            queryP?.getNotaryList(adcode, CommonUtil.bd_decrypt(longitude, latitude),true)
            ToastUtils.getInstance(mActivity).toastInfo("请先选择公证处")
        } else {
            notarizationP?.sendNotarizationRemote(userInfo, notaryId, notaryPublicId, bean?.bankOrderId, address, longitude, latitude, AppConfig.SN)
        }
    }

    override fun onResume() {
        super.onResume()
        Log.i(TAG, "onResume")
    }

    /**
     * 发起公证成功
     */
    override fun sendNotarizationSuccess(info: NotarizationInfo?) {
        Log.i(TAG, "sendNotarizationSuccess")
        MyLogUtils.i(TAG, "发起公证成功")
        notarizationInfo = info
        myMqttService?.start()
        waitNotarization()
    }

    /**
     * 等待dontWiting
     */
    private fun waitNotarization() {
        Log.i(TAG, "waitNotarization")
        showWaitDialog()
        lifecycleScope.launch(Dispatchers.IO) {
            delay(60000L)
            withContext(Dispatchers.Main) {
                if (isWaiting == true) {
                    myMqttService?.unSubscribe()
                    stopP?.stopNotarization(notarizationInfo?.unitGuid)
                    dismissWait()
                    MyLogUtils.i(TAG, "公证员正忙，请稍候再试！")
                    toastError("公证员正忙，请稍候再试！")
                }
            }
        }
    }

    private fun showWaitDialog() {
        Log.i(TAG, "showWaitDialog")
        if (waitDialog == null) {
            waitDialog = DialogUtils.getInstance().getCenterDialog(mActivity, false, R.layout.dialog_wait)
            playBall = waitDialog?.findViewById(R.id.lv_playball)
            playBall?.setViewColor(R.color.black)
            playBall?.setBallColor(ContextCompat.getColor(mActivity, R.color.colorPrimary))
        }
        if (waitDialog?.isShowing == false) {
            playBall?.startAnim()

            waitDialog?.show()
        }
    }

    private fun dismissWait() {
        Log.i(TAG, "dismissWait")
        if (waitDialog?.isShowing == true) {
            playBall?.stopAnim()
            waitDialog?.dismiss()
            waitDialog = null
        }
    }

    /**
     * 收到dontWiting后进入公证页面
     */
    override fun onSocketMessage(next: String?) {
        Log.i(TAG, "onSocketMessage")
        if (next?.contains("{") == true) {
            val info: SocketMessageInfo = JSON.parseObject(next, SocketMessageInfo::class.java)
            if (info.code == "dontWiting") {
                isWaiting = false
                if (null != info.roomId) {
                    val roomId = info.roomId
                    lifecycleScope.launch(Dispatchers.Main) {
                        goVideoNotarization(roomId!!)
                    }
                } else {
                    dismissWait()
                    MyLogUtils.i(TAG, "未接收到房间号")
                    toastError("未接收到房间号")
                }
            }
        }
    }

    /**
     * 进入公证页面
     */
    private fun goVideoNotarization(roomId: String) {
        Log.i(TAG, "goVideoNotarization")
        dismissWait()
        MyLogUtils.i(TAG, "进入小额公证")
        val intent = Intent(mActivity, RemoteNotarizationActivity::class.java)
        intent.putExtra("roomId", roomId)
        intent.putExtra("notarizationInfo", notarizationInfo)
        intent.putExtra("bean", bean)
        readyGo(intent)
    }

    override fun onDestroy() {
        Log.i(TAG, "onDestroy")
        dismissDialog()
        dismissWait()
        gao = false
        super.onDestroy()
    }

    override fun isUseEventBus(): Boolean {
        return true
    }
}