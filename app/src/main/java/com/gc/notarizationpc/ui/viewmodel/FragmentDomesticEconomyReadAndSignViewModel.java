package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.ListDocumentResponse;
import com.gc.notarizationpc.ui.HomeActivity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * 这个是自助办证流程中上传材料fragment中
 */
public class FragmentDomesticEconomyReadAndSignViewModel extends BaseViewModel {
    public FragmentDomesticEconomyReadAndSignViewModel(@NonNull Application application) {
        super(application);
    }


    //右侧文书展示的ItemBinding
    public ItemBinding<DomesticEconomyReadAndSignGridItemViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.item_domestic_economy_read_and_sign_grid);
    // 右侧文文书展示的数据源
    public ObservableList<DomesticEconomyReadAndSignGridItemViewModel> documentList = new ObservableArrayList();

    // 左侧文书预览的itemBinding
    public ItemBinding<DomesticEconomyReadAndSignDocumentGridItemViewModel> documentItemBinding = ItemBinding.of(BR.viewModel, R.layout.item_domestic_economy_read_and_sign_document_grid);
    // 左侧文书展示的数据源
    public ObservableList<DomesticEconomyReadAndSignDocumentGridItemViewModel> documentPreViewList = new ObservableArrayList();

    public SingleLiveEvent firstButtonClickEvent = new SingleLiveEvent();


    public SingleLiveEvent secondButtonClickEvent = new SingleLiveEvent();

    public SingleLiveEvent<List<String>> previewDocEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<String> amplifyDocEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<Boolean> uploadFileSingleLiveEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<Boolean> getDocumentListEvent = new SingleLiveEvent<>();

    public SingleLiveEvent<Integer> getInfoErrorEvent = new SingleLiveEvent<>();

    public List<ListDocumentResponse> documentSourceList;

    public List<String> tempDoc = new ArrayList<>();

    public SingleLiveEvent<Integer> currentDocumentIndex = new SingleLiveEvent<>();

    /**
     * 获取文书
     */
    public void listDocument(String recordId, String userId) {
        documentList.clear();
        Map<String, Object> param = new HashMap<>();
        param.put("recordId", recordId);
        param.put("sysUserId", userId);
//        请求网络数据
        RequestUtil.recordPreListDocument(param, new MyObserver<List<ListDocumentResponse>>() {
            @Override
            public void onSuccess(List<ListDocumentResponse> response) {
                if (response != null && response.size() > 0) {
                    documentSourceList = response;
                    tempDoc.clear();
                    for (ListDocumentResponse listDocumentResponse : response) {
                        if (listDocumentResponse.getSignDocumentVOList() != null &&
                                listDocumentResponse.getSignDocumentVOList().size() > 0 && listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages() != null && listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages().size() > 0) {
                            listDocumentResponse.setFirstDocUrl(listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages().get(0).toString());
                            tempDoc.addAll(listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages());
                        }
                        DomesticEconomyReadAndSignGridItemViewModel documentGridItemViewModel = new DomesticEconomyReadAndSignGridItemViewModel(FragmentDomesticEconomyReadAndSignViewModel.this, listDocumentResponse);
                        documentList.add(documentGridItemViewModel);
                    }
                    previewDoc(tempDoc);
                }
                getDocumentListEvent.setValue(true);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? Utils.getContext().getString(R.string.failedToLoadDocumentAndTryLater) : errorMsg);
            }
        });
    }

    //左侧预览文书
    public void previewDoc(List<String> listDocumentResponse) {
        if (listDocumentResponse != null) {
            previewDocEvent.setValue(listDocumentResponse);
        }
    }

    // 获取右侧的点击位置
    public void getCurrentIndex(ListDocumentResponse listDocumentResponse){
        if (listDocumentResponse.getSignDocumentVOList() != null &&
                listDocumentResponse.getSignDocumentVOList().size() > 0 && listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages() != null && listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages().size() > 0){
            Integer currentIndex = tempDoc.indexOf(listDocumentResponse.getSignDocumentVOList().get(0).getDocumentImages().get(0));
            currentDocumentIndex.setValue(currentIndex);

        }
    }

    //放大图片
    public void amplifyDoc(String filePath) {
        amplifyDocEvent.setValue(filePath);
    }

    public BindingCommand firstButtonClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            firstButtonClickEvent.setValue("");
        }
    });

    public BindingCommand secondButtonClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            secondButtonClickEvent.setValue("");
        }
    });

    public void submit(String recordId){
        RequestUtil.recordPreProcessSubmit(recordId,new MyObserver(){

            @Override
            public void onSuccess(Object result) {
                toastSuccess(Utils.getContext().getString(R.string.orderCreateSuccess));
                startActivity(HomeActivity.class);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                toastError(errorMsg == null ? "":errorMsg);
            }
        });
    }
}
