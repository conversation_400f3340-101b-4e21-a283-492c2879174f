package com.gc.notarizationpc.ui.viewmodel;

import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.request.AddSelfServiceRequest;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.binding.command.CustomerBindingConsumer;
import me.goldze.mvvmhabit.binding.viewadapter.spinner.IKeyAndValue;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * 自助申办信息，个人列表
 */

public class DomesticEconomyPersonalListItemViewModel extends ItemViewModel<DomesticEconomyApplyInfoViewModel> {
    public ObservableField<AddSelfServiceRequest.RecordApplicantsDTO> entity = new ObservableField<>();
    public Drawable drawableImg;

    public ObservableInt canDel = new ObservableInt();


    public DomesticEconomyPersonalListItemViewModel(@NonNull DomesticEconomyApplyInfoViewModel viewModel, AddSelfServiceRequest.RecordApplicantsDTO entity) {
        super(viewModel);
        this.entity.set(entity);
        //默认展示的登录人当事人无删除按钮和编辑按钮
        if (entity.isCanOperater()) {
            canDel.set(View.VISIBLE);
        } else {
            canDel.set(View.GONE);
        }

    }

    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
    public int getPosition() {
        return viewModel.getPersonalItemPosition(this);
    }

    //选择 申请人，代理人
    public BindingCommand<IKeyAndValue> selectClick = new BindingCommand<>(new CustomerBindingConsumer<IKeyAndValue>() {

        @Override
        public void call(IKeyAndValue iKeyAndValue, int position) {
            Log.e("Test", "选择了" + iKeyAndValue.getKey() + "==" + iKeyAndValue.getValue() + "==" + getPosition());
            //改为选择代理人
            int pos = getPosition();
            if ("1".equals(iKeyAndValue.getKey())) {
                if (!TextUtils.isEmpty(iKeyAndValue.getKey())) {
                    viewModel.observablePersonalList.get(pos).entity.get().setSubstitute(Integer.parseInt(iKeyAndValue.getKey()));
                }
                //如果列表中非第一行有选择了了代理人，那么当前刷身份证的申请人一定要改成 代理人
                if (pos != 0) {
                    AddSelfServiceRequest.RecordApplicantsDTO recordApplicantsDTO = viewModel.observablePersonalList.get(0).entity.get();
                    recordApplicantsDTO.setSubstitute(1);
                    recordApplicantsDTO.setSubstituteStr("代理人");
                    DomesticEconomyPersonalListItemViewModel selfApplyPersonalListItemViewModel = new DomesticEconomyPersonalListItemViewModel(viewModel, recordApplicantsDTO);
                    viewModel.observablePersonalList.set(0, selfApplyPersonalListItemViewModel);
                }

                //如果其他
//                if (pos == 0) {
//                    //若将该条信息角色修改为代理人，需要判断当事人列表内是否有申请人，否则提示：“请添加申请人”
//                    boolean isExitApplyer = false;
//                    for (int i = 0; i < viewModel.observablePersonalList.size(); i++) {
//                        if (i != pos && viewModel.observablePersonalList.get(i) != null && viewModel.observablePersonalList.get(i).entity.get() != null) {
//                            if (viewModel.observablePersonalList.get(i).entity.get().getSubstitute() == -1) {
//                                isExitApplyer = true;
//                            }
//                        }
//                    }
//                    if (!isExitApplyer) {
//                        ToastUtils.toastError(Utils.getContext().getString(R.string.add_applyer_first));
//                        //todo 这里没生效
//                        AddSelfServiceRequest.RecordApplicantsDTO recordApplicantsDTO = viewModel.observablePersonalList.get(pos).entity.get();
//                        recordApplicantsDTO.setSubstitute(-1);
//                        recordApplicantsDTO.setSubstituteStr("申请人");
////                        viewModel.observablePersonalList.remove(pos);
////                        viewModel.observablePersonalList.add(pos, new SelfApplyPersonalListItemViewModel(viewModel, recordApplicantsDTO));
//                        viewModel.observablePersonalList.get(pos).entity.set(recordApplicantsDTO);
//                        return;
//                    }
//                }
//                if (!TextUtils.isEmpty(iKeyAndValue.getKey())) {
//                    viewModel.observablePersonalList.get(pos).entity.get().setSubstitute(Integer.parseInt(iKeyAndValue.getKey()));
//                }
            } else {
                //改为选择申请人
                //若该条信息角色由代理人修改为申请人时需要判断案件是否有代理人，有则提示“该案件存在代理人，不支持修改登录用户角色”；没有则支持修改为申请
                //也就是说 有代理人，当前申请人必须为代理人
                if (pos == 0) {
                    boolean isExitProxy = false;
                    for (int i = 0; i < viewModel.observablePersonalList.size(); i++) {
                        if (i != 0 && viewModel.observablePersonalList.get(i) != null && viewModel.observablePersonalList.get(i).entity.get() != null) {
                            if (viewModel.observablePersonalList.get(i).entity.get().getSubstitute() == 1) {
                                isExitProxy = true;

                                AddSelfServiceRequest.RecordApplicantsDTO recordApplicantsDTO = viewModel.observablePersonalList.get(pos).entity.get();
                                recordApplicantsDTO.setSubstitute(1);
                                recordApplicantsDTO.setSubstituteStr("代理人");
                                viewModel.observablePersonalList.remove(pos);
                                viewModel.observablePersonalList.add(pos, new DomesticEconomyPersonalListItemViewModel(viewModel, recordApplicantsDTO));
                            }
                        }
                    }
                    if (isExitProxy) {
                        ToastUtils.toastError(Utils.getContext().getString(R.string.choose_applyer_hint));
                        return;
                    }
                }
                if (!TextUtils.isEmpty(iKeyAndValue.getKey())) {
                    viewModel.observablePersonalList.get(pos).entity.get().setSubstitute(Integer.parseInt(iKeyAndValue.getKey()));
                }
            }
            viewModel.observablePersonalList.get(pos).entity.get().setSubstituteStr(iKeyAndValue.getValue());

        }
    });

    //编辑
    public BindingCommand editClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.applicantClickHandler("edit", entity.get(), getPosition());
        }
    });

    //删除
    public BindingCommand delClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.applicantClickHandler("delete", entity.get(), getPosition());
        }
    });


}
