package com.gc.notarizationpc.ui.fragment;


import static me.goldze.mvvmhabit.utils.ToastUtils.toastError;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.utils.TextUtils;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentSelfServiceReadAndSignBinding;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.ReservationActivity;
import com.gc.notarizationpc.ui.selfservicecertificate.SelfNotarizationActivity;
import com.gc.notarizationpc.ui.view.SelfServiceHasBeenFinishedAlert;
import com.gc.notarizationpc.ui.view.SignDialog;
import com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceReadAndSignViewModel;
import com.gc.notarizationpc.ui.viewmodel.SelfServiceReadAndSignDocumentGridItemViewModel;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.widget.SelfServiceStepBar;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.List;

import cc.shinichi.library.ImagePreview;
import cc.shinichi.library.bean.ImageInfo;
import me.goldze.mvvmhabit.base.BaseFragment;
import me.goldze.mvvmhabit.utils.ConvertUtils;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.BR;

/**
 * 这个是自助办证流程中上传材料的fragment
 */
public class SelfServiceReadAndSignFragment extends BaseFragment<FragmentSelfServiceReadAndSignBinding, FragmentSelfServiceReadAndSignViewModel> {

    private SelfServiceStepBar selfServiceStepBar;

    private SignDialog signDialog;

    private CountDownTimer countDownTimer;

    private SelfServiceHasBeenFinishedAlert alert;

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_self_service_read_and_sign;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public void initData() {
        super.initData();
        initSelfServiceStepBar();
        String fragment_id = getArguments().getString("fragment_id");
        if (fragment_id != null) {
            binding.tvFirstText.setVisibility(View.GONE);
        }
        String recordId = SPUtils.getInstance().getString("self_recordId");
        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
        if (!TextUtils.isEmpty(recordId) && idCardUserInfo != null && !TextUtils.isEmpty(idCardUserInfo.getUserId())) {
            // 自主订单列表跳转阅读文书才进行接口请求
            if (fragment_id != null && fragment_id.equals("fragment_2")) {
                viewModel.listDocument(recordId, idCardUserInfo.getUserId());
            }
        }


    }

    public void setLastStepGone() {
        binding.tvFirstText.setVisibility(View.GONE);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && viewModel != null) {
            binding.tvSecond.setBackgroundDrawable(getContext().getDrawable(R.drawable.shape_corner5_e8e8e8));
            binding.tvSecond.setEnabled(false);
            binding.tvSecond.setText(getString(R.string.read_and_sign));
            if (countDownTimer != null) {
                countDownTimer.cancel();
            }
            String recordId = SPUtils.getInstance().getString("self_recordId");
            IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
            if (!TextUtils.isEmpty(recordId) && idCardUserInfo != null && !TextUtils.isEmpty(idCardUserInfo.getUserId())) {
                viewModel.listDocument(recordId, idCardUserInfo.getUserId());
            }
        }
    }

    private void initSelfServiceStepBar() {
        selfServiceStepBar = (SelfServiceStepBar) getView().findViewById(R.id.self_service_step_bar);
        TextView tvFirstNumView = selfServiceStepBar.getTv_first_num();
        tvFirstNumView.setBackground(ResourcesCompat.getDrawable(getResources(), R.mipmap.icon_work_online, null));
        tvFirstNumView.setText("");
        TextView tvFirstView = selfServiceStepBar.getTv_first();
        tvFirstView.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color3333));
        selfServiceStepBar.setTv_first_num(tvFirstNumView);
        selfServiceStepBar.setTv_first(tvFirstView);

        TextView tvSecondNum = selfServiceStepBar.getTv_second_num();
        tvSecondNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.mipmap.icon_work_online, null));
        tvSecondNum.setText("");
        TextView tvSecond = selfServiceStepBar.getTv_second();
        tvSecond.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color3333));
        selfServiceStepBar.setTv_second_num(tvSecondNum);
        selfServiceStepBar.setTv_second(tvSecond);

        TextView tvThreeNum = selfServiceStepBar.getTv_three_num();
        tvThreeNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.shape_circle_3c6af4, null));
        tvThreeNum.setText("3");
        TextView tvThree = selfServiceStepBar.getTv_three();
        tvThree.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color_A5A5A5));
        selfServiceStepBar.setTv_three_num(tvThreeNum);
        selfServiceStepBar.setTv_three(tvThree);

        TextView tvFourNum = selfServiceStepBar.getTv_four_num();
        tvFourNum.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.shape_circle_cccccc, null));
        tvFourNum.setText("4");
        TextView tvFour = selfServiceStepBar.getTv_four();
        tvFour.setTextColor(getResources().getColor(me.goldze.mvvmhabit.R.color.color_A5A5A5));
        selfServiceStepBar.setTv_four_num(tvFourNum);
        selfServiceStepBar.setTv_four(tvFour);
        binding.tvSecond.setBackgroundDrawable(getContext().getDrawable(R.drawable.shape_corner5_e8e8e8));
        binding.tvSecond.setEnabled(false);
        binding.tvSecond.setText(getString(R.string.read_and_sign));
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();

//        单个签字图片文件上传成功
        viewModel.uploadFileSingleLiveEvent.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean response) {
                if (response != null && response) {
                    signDialog.dismiss();
                    try {
//                        ((SelfNotarizationActivity) getActivity()).viewPager.setCurrentItem(3, false);
                        if (alert == null || !alert.isShowing()) {
                            alert = new SelfServiceHasBeenFinishedAlert(getContext(), new PopwindowUtil.ResultListener() {
                                @Override
                                public void result(Object value) {
                                    alert.dismiss();
                                    startActivity(HomeActivity.class);
                                }
                            });
                            alert.show();
                        }
                    } catch (Exception e) {
                        MobclickAgent.reportError(getActivity(), e);
                        e.printStackTrace();
                    }
                    //签完字后，直接跳转到缴费页面
//                    binding.tvSecond.setText(R.string.done);
//                    String recordId = SPUtils.getInstance().getString("self_recordId");
//                    IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
//                    if (!TextUtils.isEmpty(recordId) && idCardUserInfo != null && !TextUtils.isEmpty(idCardUserInfo.getUserId())) {
//                        viewModel.listDocument(recordId, idCardUserInfo.getUserId());
//                    }
                }
            }
        });

        viewModel.getDocumentListEvent.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (binding.tvSecond.getText().toString().equals(getString(R.string.done))) {
                    return;
                }
                if (viewModel.documentSourceList != null && viewModel.documentSourceList.size() > 0) {
                    binding.tvSecond.setBackgroundDrawable(getContext().getDrawable(R.drawable.shape_corner5_e8e8e8));
                    binding.tvSecond.setEnabled(false);
                    binding.tvSecond.setText(getString(R.string.read_and_sign) + viewModel.documentSourceList.size() * 5);
                    countDownTimer = new CountDownTimer(viewModel.documentSourceList.size() * 5 * 1000, 1000) {
                        @Override
                        public void onTick(long l) {
                            binding.tvSecond.setText(getString(R.string.read_and_sign) + l / 1000 + "s");
                        }

                        @Override
                        public void onFinish() {
                            binding.tvSecond.setBackgroundDrawable(getContext().getDrawable(R.drawable.shape_corner5_3c6af4));
                            binding.tvSecond.setEnabled(true);
                            binding.tvSecond.setText(getString(R.string.complete_read));
                        }
                    };
                    countDownTimer.start();
                } else {
                    binding.tvSecond.setBackgroundDrawable(getContext().getDrawable(R.drawable.shape_corner5_3c6af4));
                    binding.tvSecond.setEnabled(true);
                    binding.tvSecond.setText(getString(R.string.done));
                }
            }
        });

        viewModel.getInfoErrorEvent.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer response) {
                if (WorkstatusEnum.APPLICATION_INFO_FAIL.code == response) {
                    toastError(WorkstatusEnum.APPLICATION_INFO_FAIL.msg);
                } else if (WorkstatusEnum.FEE_INFO_FAIL.code == response) {
                    toastError(WorkstatusEnum.FEE_INFO_FAIL.msg);
                } else if (WorkstatusEnum.DOC_INFO_FAIL.code == response) {
                    toastError(WorkstatusEnum.DOC_INFO_FAIL.msg);
                } else if (WorkstatusEnum.UPLOAD_SIGN_FAIL.code == response) {
                    toastError(WorkstatusEnum.UPLOAD_SIGN_FAIL.msg);
                } else if (WorkstatusEnum.SIGN_DOC_FAIL.code == response) {
                    toastError(WorkstatusEnum.SIGN_DOC_FAIL.msg);
                } else if (WorkstatusEnum.LIST_MATERIAL_FAIL.code == response) {
                    toastError(WorkstatusEnum.LIST_MATERIAL_FAIL.msg);
                }
            }
        });

        //点击放大
        viewModel.amplifyDocEvent.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String o) {
                try {
                    if (viewModel.tempDoc != null && viewModel.tempDoc.size() > 0) {
                        List<ImageInfo> imgs = new ArrayList<>();
                        for (String filePath : viewModel.tempDoc) {
                            ImageInfo imageInfo = new ImageInfo();
                            imageInfo.setThumbnailUrl(filePath);
                            imageInfo.setOriginUrl(filePath);
                            imgs.add(imageInfo);
                        }
                        Integer index = viewModel.tempDoc.indexOf(o);
                        ImagePreview.getInstance().setContext(getActivity()).setShowCloseButton(false).setShowDownButton(false).setImageInfoList(imgs).setTransitionShareElementName("shared_element_container").setIndex(index).start();
                    }
                } catch (Exception e) {
                    MobclickAgent.reportError(getActivity(), e);
                    Log.e("ERROR", e.getMessage());
                }
            }
        });
        //点击文书进行预览
        viewModel.previewDocEvent.observe(this, new Observer<List<String>>() {
            @Override
            public void onChanged(List<String> o) {
                binding.viewPager.setVisibility(View.VISIBLE);
                if (o != null && o.size() > 0) {
                    viewModel.documentPreViewList.clear();
                    for (String filePath : o) {
                        viewModel.documentPreViewList.add(new SelfServiceReadAndSignDocumentGridItemViewModel(viewModel, filePath));
                    }
                }

            }
        });

        viewModel.currentDocumentIndex.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (integer != null) {
                    binding.viewPager.scrollToPosition(integer);
                }
            }
        });

        viewModel.firstButtonClickEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object o) {
                ((SelfNotarizationActivity) getActivity()).viewPager.setCurrentItem(1, false);
            }
        });

        viewModel.secondButtonClickEvent.observe(this, new Observer() {
            @Override
            public void onChanged(Object o) {
                if (binding.tvSecond.getText().toString().equals(getString(R.string.complete_read))) {
                    showSignDialog();
                } else {
                    try {
//                        ((SelfNotarizationActivity) getActivity()).viewPager.setCurrentItem(3, false);
                        if (alert == null || !alert.isShowing()) {
                            alert = new SelfServiceHasBeenFinishedAlert(getContext(), new PopwindowUtil.ResultListener() {
                                @Override
                                public void result(Object value) {
                                    alert.dismiss();
                                    startActivity(HomeActivity.class);
                                }
                            });
                            alert.show();
                        }
                    } catch (Exception e) {
                        Log.e("Test", e.getMessage());
                        MobclickAgent.reportError(getActivity(), e);
                        e.printStackTrace();
                    }
                }
            }
        });
    }


    public void showSignDialog() {
        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
        if (idCardUserInfo == null) {
            return;
        }
        signDialog = new SignDialog(getContext(), idCardUserInfo.getName(), new PopwindowUtil.ResultSecondListener() {
            @Override
            public void result(Object value) {
                if (value != null) {
                    Bitmap bitmap = (Bitmap) value;
                    viewModel.uploadSignPic(bitmap);
                }
            }

            @Override
            public void secondResult(Object value) {

            }
        });

        if (!signDialog.isShowing()) {
            signDialog.show();
            Window dialogWindow = signDialog.getWindow();
            dialogWindow.getDecorView().setBackgroundColor(this.getResources().getColor(android.R.color.white));
            dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
            // dialogWindow.setWindowAnimations(R.style.mystyle);
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = ConvertUtils.dp2px(1000f);
            lp.height = ConvertUtils.dp2px(700f);
            lp.alpha = 1.0f;
            dialogWindow.setAttributes(lp);
            dialogWindow.setGravity(Gravity.CENTER);
            signDialog.setCanceledOnTouchOutside(false);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }
}
