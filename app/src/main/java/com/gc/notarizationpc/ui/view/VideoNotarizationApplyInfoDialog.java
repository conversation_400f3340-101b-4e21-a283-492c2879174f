package com.gc.notarizationpc.ui.view;

import static me.goldze.mvvmhabit.utils.ToastUtils.toastInfo;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.MenuInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.ApplyInfoResponse;
import com.gc.notarizationpc.ui.adapter.ApplyInfoAdapter;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.widget.BaseDialog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.ToastUtils;

/**
 * 受理室申请信息
 */
public class VideoNotarizationApplyInfoDialog extends BaseDialog {

    private Context mContext;
    private TextView tvApplyDate, tvApplyItem, tvApplyCert, tvApplyLan, tvApplyAddress;
    private ApplyInfoResponse mApplyResponse;
    private RecyclerView rvApply, rvProxy;
    private List<MenuInfo> applicant;
    private List<MenuInfo> proxyList;
    private ApplyInfoAdapter applyInfoAdapter;
    private ApplyInfoAdapter proxyAdapter;
    private String mCaseInfoId;

    public VideoNotarizationApplyInfoDialog(Context context, String caseInfoId, ApplyInfoResponse applicationAgentResponse) {
        super(context);
        mContext = context;
        mApplyResponse = applicationAgentResponse;
        this.mCaseInfoId = caseInfoId;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_video_notarization_info);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();
        showData();

    }

    private void showData() {
        applicant = new ArrayList<>();
        proxyList = new ArrayList<>();
        if (mApplyResponse != null) {
//            tvApplyItem.setText(mApplyResponse.getApplicationItem());
            tvApplyAddress.setText(mApplyResponse.getUsedPlaceStr());
            tvApplyLan.setText(mApplyResponse.getTranslateLanguageStr());
            tvApplyCert.setText(mApplyResponse.getUsedPurposeStr());
//            String itemJson = mApplyResponse.getApplicationItem();
//            List<ApplyInfoResponse.ApplicationItem> applicationItems = new Gson().fromJson(itemJson, new TypeToken<List<ApplyInfoResponse.ApplicationItem>>() {
//            }.getType());
//            String mattersName = "";
//            if (applicationItems != null) {
//                for (ApplyInfoResponse.ApplicationItem applicationItem : applicationItems) {
//                    mattersName = mattersName.concat(applicationItem.getMattersName()) + "，";
//                }
//            }
//            if (!TextUtils.isEmpty(mattersName) && mattersName.length() > 0) {
//                tvApplyItem.setText(mattersName.substring(0, mattersName.length() - 1));
//            }
            tvApplyItem.setText(mApplyResponse.getApplicationItemStr());

            tvApplyDate.setText(mApplyResponse.getApplyDate());

            if (mApplyResponse.getApplicantList() != null) {
                for (ApplyInfoResponse.ApplicantListDTO applicantListDTO : mApplyResponse.getApplicantList()) {
                    if (applicantListDTO.getApplicantType() == CommonUtil.APPLY_PERSON) {
                        applicant.add(new MenuInfo(mContext.getString(R.string.nameString), applicantListDTO.getApplicantName()));
                        applicant.add(new MenuInfo(mContext.getString(R.string.phone_num), applicantListDTO.getContactNum()));
                        applicant.add(new MenuInfo(mContext.getString(R.string.certificate_type), applicantListDTO.getCredentialTypeStr()));
                        applicant.add(new MenuInfo(mContext.getString(R.string.certificate_number), applicantListDTO.getCredentialNum()));
                        applicant.add(new MenuInfo("divider", ""));
                    }

                    if (applicantListDTO.getApplicantType() == CommonUtil.APPLY_AGENT) {
                        applicant.add(new MenuInfo(mContext.getString(R.string.designation), applicantListDTO.getApplicantName()));
                        applicant.add(new MenuInfo(mContext.getString(R.string.certificate_type), applicantListDTO.getCredentialTypeStr()));
                        applicant.add(new MenuInfo(mContext.getString(R.string.certificate_number), applicantListDTO.getCredentialNum()));
                        applicant.add(new MenuInfo(mContext.getString(R.string.legalPerson), applicantListDTO.getLegalPerson()));
                        applicant.add(new MenuInfo(mContext.getString(R.string.contact_phone), applicantListDTO.getContactNum()));
                    }
                    if (applicantListDTO.getApplicantType() == CommonUtil.APPLY_COMPANY) {
                        proxyList.add(new MenuInfo(mContext.getString(R.string.nameString), applicantListDTO.getApplicantName()));
                        proxyList.add(new MenuInfo(mContext.getString(R.string.phone_num), applicantListDTO.getContactNum()));
                        proxyList.add(new MenuInfo(mContext.getString(R.string.certificate_type), applicantListDTO.getCredentialTypeStr()));
                        proxyList.add(new MenuInfo(mContext.getString(R.string.certificate_number), applicantListDTO.getCredentialNum()));
                        proxyList.add(new MenuInfo(mContext.getString(R.string.principal), applicantListDTO.getPrincipal()));
                    }
                }
                applyInfoAdapter.refreshAdapter(applicant);
                applyInfoAdapter.setData(applicant);
                applyInfoAdapter.notifyDataSetChanged();
                proxyAdapter.refreshAdapter(proxyList);
                proxyAdapter.notifyDataSetChanged();
            }

//            tvApplyLan.setText();
        }
    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        tvApplyAddress = findViewById(R.id.tv_apply_address);
        tvApplyCert = findViewById(R.id.tv_apply_cert);
        tvApplyDate = findViewById(R.id.tv_apply_date);
        tvApplyItem = findViewById(R.id.tv_apply_item);
        tvApplyLan = findViewById(R.id.tv_apply_lan);

        rvApply = findViewById(R.id.rv_apply);
        rvProxy = findViewById(R.id.rv_proxy);

        rvApply.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });

        rvProxy.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        applyInfoAdapter = new ApplyInfoAdapter(mContext, R.layout.item_list_apply_info, 0);
        rvApply.setAdapter(applyInfoAdapter);
        applyInfoAdapter.refreshAdapter(applicant);
        applyInfoAdapter.setData(applicant);

        proxyAdapter = new ApplyInfoAdapter(mContext, R.layout.item_list_apply_info, 0);
        rvProxy.setAdapter(proxyAdapter);
        proxyAdapter.refreshAdapter(proxyList);

        findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowing()) {
                    dismiss();
                }
            }
        });

        findViewById(R.id.iv_refresh).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //刷新
                getCaseInfo();
            }
        });
    }

    public void getCaseInfo() {
        showProgress();
        RequestUtil.getCaseInfo(mCaseInfoId, new MyObserver<ApplyInfoResponse>() {
            @Override
            public void onSuccess(ApplyInfoResponse result) {
                hideProgress();
                if (result != null) {
                    mApplyResponse = result;
                    showData();
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                hideProgress();
                toastInfo(WorkstatusEnum.APPLICATION_INFO_FAIL.msg);
            }
        });

    }

    @Override
    public void show() {
        super.show();
    }


}
