package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.greendao.model.LoginInfo;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 * Created by goldze on 2017/7/17.
 */

public class FragmentListModel extends BaseViewModel {
    public SingleLiveEvent<FragmentListItemViewModel> deleteItemLiveData = new SingleLiveEvent<>();
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();

    private int pageNo = 1;
    private int pageSize = 10;

    public class UIChangeObservable {
        //下拉刷新完成
        public SingleLiveEvent finishRefreshing = new SingleLiveEvent<>();
        //上拉加载完成
        public SingleLiveEvent finishLoadmore = new SingleLiveEvent<>();
    }

    public FragmentListModel(@NonNull Application application) {
        super(application);
    }

    //给RecyclerView添加ObservableList
    public ObservableList<FragmentListItemViewModel> observableList = new ObservableArrayList<>();
    //给RecyclerView添加ItemBinding
    public ItemBinding<FragmentListItemViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.item_list);//这里指定了item的布局
    //下拉刷新
    public BindingCommand onRefreshCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            requestNetWork();
        }
    });
    //上拉加载
    public BindingCommand onLoadMoreCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            if (observableList.size() > 50) {
                ToastUtils.showLong(Utils.getContext().getString(R.string.noMoreData));
                uc.finishLoadmore.call();
                return;
            }
            //模拟网络上拉加载更多
//            model.loadMore()
//                    .compose(RxUtils.schedulersTransformer()) //线程调度
//                    .doOnSubscribe(NetWorkViewModel.this) //请求与ViewModel周期同步
//                    .doOnSubscribe(new Consumer<Disposable>() {
//                        @Override
//                        public void accept(Disposable disposable) throws Exception {
//                            ToastUtils.showShort("上拉加载");
//                        }
//                    })
//                    .subscribe(new Consumer<DemoEntity>() {
//                        @Override
//                        public void accept(DemoEntity entity) throws Exception {
            for (int i = 0; i < 10; i++) {
                LoginInfo info = new LoginInfo();
                info.setEmployeeName("xhf test");
                FragmentListItemViewModel itemViewModel = new FragmentListItemViewModel(FragmentListModel.this, info);
                //双向绑定动态添加Item
                observableList.add(itemViewModel);
            }
            //刷新完成收回
            uc.finishLoadmore.call();
//                        }
//                    });
        }
    });

    /**
     * 网络请求方法，在ViewModel中调用Model层，通过Okhttp+Retrofit+RxJava发起请求
     */
    public void requestNetWork() {

        //可以调用addSubscribe()添加Disposable，请求与View周期同步
//        model.demoGet()
//                .compose(RxUtils.schedulersTransformer()) //线程调度
//                .compose(RxUtils.exceptionTransformer()) // 网络错误的异常转换, 这里可以换成自己的ExceptionHandle
//                .doOnSubscribe(this)//请求与ViewModel周期同步
//                .doOnSubscribe(new Consumer<Disposable>() {
//                    @Override
//                    public void accept(Disposable disposable) throws Exception {
//                        showDialog("正在请求...");
//                    }
//                })
//                .subscribe(new DisposableObserver<BaseResponse<DemoEntity>>() {
//                    @Override
//                    public void onNext(BaseResponse<DemoEntity> response) {
//                        //清除列表
//                        observableList.clear();
//                        //请求成功
//                        if (response.getCode() == 1) {
//                            for (DemoEntity.ItemsEntity entity : response.getResult().getItems()) {
//                                NetWorkItemViewModel itemViewModel = new NetWorkItemViewModel(NetWorkViewModel.this, entity);
//                                //双向绑定动态添加Item
//                                observableList.add(itemViewModel);
//                            }
//                        } else {
//                            //code错误时也可以定义Observable回调到View层去处理
//                            ToastUtils.showShort("数据错误");
//                        }
//                    }
//
//                    @Override
//                    public void onError(Throwable throwable) {
//                        //关闭对话框
//                        dismissDialog();
//                        //请求刷新完成收回
//                        uc.finishRefreshing.call();
//                        if (throwable instanceof ResponseThrowable) {
//                            ToastUtils.showShort(((ResponseThrowable) throwable).message);
//                        }
//                    }
//
//                    @Override
//                    public void onComplete() {
//                        //关闭对话框
//                        dismissDialog();
//                        //请求刷新完成收回
//                        uc.finishRefreshing.call();
//                    }
//                });
        for (int i = 0; i < 10; i++) {
            LoginInfo info = new LoginInfo();
            info.setEmployeeName("xhf test");
            FragmentListItemViewModel itemViewModel = new FragmentListItemViewModel(FragmentListModel.this, info);
            //双向绑定动态添加Item
            observableList.add(itemViewModel);
        }
        //刷新完成收回
        uc.finishRefreshing.call();
    }

    /**
     * 删除条目
     *
     * @param netWorkItemViewModel
     */
    public void deleteItem(FragmentListItemViewModel netWorkItemViewModel) {
        //点击确定，在 observableList 绑定中删除，界面立即刷新
        observableList.remove(netWorkItemViewModel);
    }

    /**
     * 获取条目下标
     *
     * @param netWorkItemViewModel
     * @return
     */
    public int getItemPosition(FragmentListItemViewModel netWorkItemViewModel) {
        return observableList.indexOf(netWorkItemViewModel);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
