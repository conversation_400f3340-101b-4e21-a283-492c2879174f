package com.gc.notarizationpc.ui.viewmodel;

import android.graphics.drawable.Drawable;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.databinding.ObservableField;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.model.response.ListDocumentResponse;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * Created by goldze on 2017/7/17.
 */

public class SelfServiceReadAndSignGridItemViewModel extends ItemViewModel<FragmentSelfServiceReadAndSignViewModel> {


    public ObservableField<ListDocumentResponse> entity = new ObservableField<>();

    public Drawable drawableImg;


    public SelfServiceReadAndSignGridItemViewModel(@NonNull FragmentSelfServiceReadAndSignViewModel viewModel, ListDocumentResponse entity) {
        super(viewModel);
        Log.e("FragmentGridItemViewModel", "FragmentGridItemViewModel");
        this.entity.set(entity);

        //ImageView的占位图片，可以解决RecyclerView中图片错误问题
        drawableImg = ContextCompat.getDrawable(viewModel.getApplication(), R.mipmap.erroimage);
    }


    /**
     * // 点击预览文书
     */
    public BindingCommand previewDoc = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.getCurrentIndex(entity.get());
        }
    });





}
