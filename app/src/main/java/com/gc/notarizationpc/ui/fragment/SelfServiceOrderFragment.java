package com.gc.notarizationpc.ui.fragment;

import static android.app.Activity.RESULT_OK;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentSelfServiceOrderGridBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.common.CommonRequest;
import com.gc.notarizationpc.data.model.request.SignDocRequest;
import com.gc.notarizationpc.data.model.response.DocInfoResponse;
import com.gc.notarizationpc.data.model.response.SelfServiceOrderListModel;
import com.gc.notarizationpc.data.model.response.UploadFileBean;
import com.gc.notarizationpc.ui.selfservicecertificate.SelfNotarizationActivity;
import com.gc.notarizationpc.ui.view.CustomerFooter;
import com.gc.notarizationpc.ui.view.SignDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationApplyInfoDialog;
import com.gc.notarizationpc.ui.view.VideoNotarizationLawDialog;
import com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderGridViewModel;
import com.gc.notarizationpc.util.PopwindowUtil;
import com.gc.notarizationpc.websocket.MyMqttService;

import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseFragment;
import me.goldze.mvvmhabit.utils.ConvertUtils;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.ToastUtils;
import me.goldze.mvvmhabit.utils.Utils;
import me.tatarka.bindingcollectionadapter2.BR;

public class SelfServiceOrderFragment extends BaseFragment<FragmentSelfServiceOrderGridBinding, SelfServiceOrderGridViewModel> {

    private VideoNotarizationApplyInfoDialog dialog;

    private VideoNotarizationLawDialog lawDialog;

    private MyMqttService myMqttService;

    private SelfServiceOrderListModel.DataListDTO mDataListDTO;
    private SignDialog signDialog;

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_self_service_order_grid;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public void initParam() {
        super.initParam();

    }

    @Override
    public SelfServiceOrderGridViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getActivity().getApplication());

        return ViewModelProviders.of(this, factory).get(SelfServiceOrderGridViewModel.class);
    }

    @Override
    public void initData() {
        super.initData();
        getArguments().getInt("type");
        viewModel.mPhoneNumber.set((String) getArguments().get("phoneNumber"));
        viewModel.mIdCardNumber.set((String) getArguments().get("idCard"));
        binding.twinklingRefreshLayout.setBottomView(new CustomerFooter(getContext(), false));
    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();

        viewModel.refreshEvent.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer aBoolean) {
                if (aBoolean == 0) {
                    binding.twinklingRefreshLayout.finishRefreshing();
                }
                if (aBoolean == 1) {
                    binding.twinklingRefreshLayout.finishLoadmore();
                }
                if (aBoolean == 2) {
                    binding.twinklingRefreshLayout.setBottomView(new CustomerFooter(getContext(), true));
                }
            }
        });
//        viewModel.comeIntoOrderDetail.observe(this, new Observer<Boolean>() {
//            @Override
//            public void onChanged(Boolean aBoolean) {
//                if (aBoolean){
//                    startActivity(OrderDetailActivity.class);
//                }
//            }
//    });
        // 补充材料
        viewModel.supplementaryMaterialsEvent.observe(this, new Observer<SelfServiceOrderListModel.DataListDTO>() {
            @Override
            public void onChanged(SelfServiceOrderListModel.DataListDTO dataListDTO) {
                Intent intent = new Intent(getContext(), SelfNotarizationActivity.class);
                intent.putExtra("fragment_id", "fragment_1"); // 传递要显示的 Fragment 的标识符
                intent.putExtra("useType", dataListDTO.getUsedPurpose());
                intent.putExtra("applicationItem",dataListDTO.getApplicationItem());
                SPUtils.getInstance().put("self_recordId", dataListDTO.getId());
                startActivityForResult(intent,1001);
            }

        });

        // 继续填写
        viewModel.continueWriteEvent.observe(this, new Observer<SelfServiceOrderListModel.DataListDTO>() {
            @Override
            public void onChanged(SelfServiceOrderListModel.DataListDTO dataListDTO) {
                Intent intent = new Intent(getContext(), SelfNotarizationActivity.class);
                intent.putExtra("fragment_id", "fragment_0"); // 传递要显示的 Fragment 的标识符
                intent.putExtra("useType", dataListDTO.getUsedPurpose());
                intent.putExtra("applicationItem",dataListDTO.getApplicationItem());
                SPUtils.getInstance().put("self_recordId", dataListDTO.getId());
                startActivityForResult(intent,1001);
            }
        });
        viewModel.deleteEvent.observe(this, new Observer<SelfServiceOrderListModel.DataListDTO>() {
            @Override
            public void onChanged(SelfServiceOrderListModel.DataListDTO dataListDTO) {
                PopwindowUtil.showNormalDialog(getContext(), getContext().getString(R.string.hint), getContext().getString(R.string.deleteOrderOrNot) + "?", getContext().getString(R.string.sureText), getContext().getString(R.string.cancel), new PopwindowUtil.ButtonClickListener() {
                    @Override
                    public void cancel() {

                    }

                    @Override
                    public void decide() {
                        viewModel.deleteOrder(dataListDTO);
                    }
                });
            }
        });

        viewModel.readDocumentEvent.observe(this, new Observer<SelfServiceOrderListModel.DataListDTO>() {
            @Override
            public void onChanged(SelfServiceOrderListModel.DataListDTO dataListDTO) {
                readDocument(dataListDTO);
            }
        });
        viewModel.networkErrorEvent.observe(this, aBoolean -> {
            if (aBoolean) {
                binding.fragmentGridviewEmpty.setVisibility(View.VISIBLE);
                binding.twinklingRefreshLayout.setVisibility(View.GONE);
            } else {
                binding.fragmentGridviewEmpty.setVisibility(View.GONE);
                binding.twinklingRefreshLayout.setVisibility(View.VISIBLE);
            }
        });

        viewModel.notaryOrderListEvent.observe(this, new Observer<List<SelfServiceOrderListModel.DataListDTO>>() {
            @Override
            public void onChanged(List<SelfServiceOrderListModel.DataListDTO> modelList) {
                viewModel.dealNetWorkData(modelList, getContext());
            }
        });

    }

    /**
     * 阅读文书
     */
    private void readDocument(SelfServiceOrderListModel.DataListDTO dataListDTO) {
        Intent intent = new Intent(getContext(), SelfNotarizationActivity.class);
        intent.putExtra("fragment_id", "fragment_2"); // 传递要显示的 Fragment 的标识符
        intent.putExtra("useType", dataListDTO.getUsedPurpose());
        intent.putExtra("applicationItem",dataListDTO.getApplicationItem());
        SPUtils.getInstance().put("self_recordId", dataListDTO.getId());
        startActivity(intent);
    }

    /**
     * 展示签字
     */
    public void showSignDialog(DocInfoResponse docInfoResponse, SelfServiceOrderListModel.DataListDTO dataListDTO) {
        signDialog = new SignDialog(getContext(), docInfoResponse.getApplicantName(), new PopwindowUtil.ResultSecondListener() {
            @Override
            public void result(Object value) {
                if (value != null) {
                    Bitmap bitmap = (Bitmap) value;
                    showProgress(false, Utils.getContext().getString(R.string.uploadingSignatureFile) + "...");
                    CommonRequest.uploadSignPic(bitmap, new PopwindowUtil.ResultSecondListener() {

                        @Override
                        public void result(Object value) {
                            hideProgress();
                            UploadFileBean uploadFileBean = (UploadFileBean) value;
                            if (value != null) {
                                SignDocRequest signDocRequest = new SignDocRequest();
                                IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(getContext(), "idcardinfo");
                                if (idCardUserInfo != null) {
                                    signDocRequest.setUserId(idCardUserInfo.getUserId());
                                }

                                signDocRequest.setPicFileId(uploadFileBean.getId());
                                signDocRequest.setCaseInfoId(dataListDTO.getId());
                                signDocRequest.setPicFileUrl(uploadFileBean.getSharedUrl());
                                signDocRequest.setIdentityCards(docInfoResponse.getCredentialNum());
                                signDocRequest.setRepresentativeName(docInfoResponse.getApplicantName());
                                List<String> tempList = new ArrayList<>();
                                for (int i = 0; i < docInfoResponse.getDocTypeList().size(); i++) {
                                    if (docInfoResponse.getDocTypeList().get(i) != null) {
                                        for (int j = 0; j < docInfoResponse.getDocTypeList().get(i).getDocumentVOList().size(); j++) {
                                            if (docInfoResponse.getDocTypeList().get(i).getDocumentVOList().get(j).getId() != null)
                                                tempList.add(docInfoResponse.getDocTypeList().get(i).getDocumentVOList().get(j).getId());
                                        }
                                    }
                                }
                                signDocRequest.setIdList(tempList);
                                showProgress(false, Utils.getContext().getString(R.string.uploadingSignatureFile) + "...");
                                CommonRequest.signMultiDocument(signDocRequest, new PopwindowUtil.ResultSecondListener() {
                                    @Override
                                    public void result(Object value) {
                                        hideProgress();
                                        if (signDialog != null && signDialog.isShowing()) {
                                            signDialog.dismiss();
                                        }
                                        readDocument(mDataListDTO);
                                        viewModel.requestNetWork();
                                        ToastUtils.showShort(Utils.getContext().getString(R.string.signSuccess));
                                    }

                                    @Override
                                    public void secondResult(Object value) {
                                        hideProgress();
                                        ToastUtils.showShort(value == null ? Utils.getContext().getString(R.string.signFail) : value.toString());
                                    }
                                });

                            }
                        }

                        @Override
                        public void secondResult(Object value) {
                            hideProgress();
                            ToastUtils.showLong(value == null ? Utils.getContext().getString(R.string.failedToUploadTheSignedFile) : value.toString());
                        }
                    });
                }
            }

            @Override
            public void secondResult(Object value) {

            }
        });
        if (!signDialog.isShowing()) {
            signDialog.show();
            Window dialogWindow = signDialog.getWindow();
            dialogWindow.getDecorView().setBackgroundColor(this.getResources().getColor(android.R.color.white));
            dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
            // dialogWindow.setWindowAnimations(R.style.mystyle);
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = ConvertUtils.dp2px(1000f);
            lp.height = ConvertUtils.dp2px(700f);
            lp.alpha = 1.0f;
            dialogWindow.setAttributes(lp);
            dialogWindow.setGravity(Gravity.CENTER);
            signDialog.setCanceledOnTouchOutside(false);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        viewModel.requestNetWork();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1001 && resultCode == RESULT_OK) {
            viewModel.pageNo = 1;
            viewModel.pageSize = 9;
            viewModel.requestNetWork();
        }
    }
}
