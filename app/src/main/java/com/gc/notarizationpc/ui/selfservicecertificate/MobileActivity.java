package com.gc.notarizationpc.ui.selfservicecertificate;

import static me.goldze.mvvmhabit.utils.Utils.getContext;

import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.View;

import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.ActivityMobileBinding;
import com.gc.notarizationpc.AppViewModelFactory;
import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.ui.HomeActivity;
import com.gc.notarizationpc.ui.view.BlockPuzzleDialog;
import com.gc.notarizationpc.ui.viewmodel.MobileViewModel;
import com.gc.notarizationpc.util.MyOnclickClickListener;
import com.zkteco.android.biometric.module.idcard.meta.IDCardInfo;

import me.goldze.mvvmhabit.base.BaseActivity;
import me.goldze.mvvmhabit.utils.SPUtils;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/13
 */
public class MobileActivity extends BaseActivity<ActivityMobileBinding, MobileViewModel> {
    private String TAG = "Test";
    private boolean isNoCard = true;
    private IDCardInfo idcardinfo = null;
    private Bitmap idCardBmp = null;
    private Integer readSuccess = null;
    private Integer readTips = null;
    private Integer validateSuccess = null;
    private Integer validateFail = null;
    private Integer retry = null;
    private Integer faceCamera = null;
    private boolean hasCardInfo = false;
    private IdCardUserInfo userInfo;

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_mobile;
    }

    @Override
    public int initVariableId() {
        return BR.viewModel;
    }

    @Override
    public MobileViewModel initViewModel() {
        AppViewModelFactory factory = AppViewModelFactory.getInstance(getApplication());
        return ViewModelProviders.of(this, factory).get(MobileViewModel.class);
    }

    @Override
    protected void onStart() {
        super.onStart();
        changeStatusBarLight(true);
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }


    @Override
    public void initData() {
        super.initData();
        findViewById(R.id.tv_home).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                startActivity(HomeActivity.class);
            }
        });

        findViewById(R.id.tv_back).setOnClickListener(new MyOnclickClickListener() {
            @Override
            public void mOnClick(View v) {
                finish();
            }
        });
        IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(getContext(), "idcardinfo");
        if (idCardUserInfo != null) {
            binding.tvName.setText(idCardUserInfo.getName());
            binding.tvIdcard.setText(idCardUserInfo.getIdCard());
        }

    }

    @Override
    public void initViewObservable() {
        viewModel.verifySuccess.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                IdCardUserInfo idCardUserInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(getContext(), "idcardinfo");
                if (idCardUserInfo != null) {
                    idCardUserInfo.setMobile(binding.edtPhone.getText().toString());
                    SPUtils.getInstance().saveObject(getContext(), "idcardinfo", idCardUserInfo);
                }
                Intent intent = new Intent(MobileActivity.this, SelfNotarizationActivity.class);
                SPUtils.getInstance().put("self_recordId", "");
                startActivity(intent);
            }
        });

        viewModel.getCodeTextView.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String phone) {
                BlockPuzzleDialog blockPuzzleDialog = new BlockPuzzleDialog(MobileActivity.this);
                blockPuzzleDialog.show();
                blockPuzzleDialog.setOnResultsListener(new BlockPuzzleDialog.OnResultsListener() {
                    @Override
                    public void onResultsClick(String result) {
                        toastSuccess("校验通过");
                        viewModel.getMessageCode(phone);
                        CountDownTimer timer = new CountDownTimer(60000, 1000) {
                            @Override
                            public void onTick(long l) {
                                binding.tvGetCode.setText(l / 1000 + getString(R.string.second_send_hint));
                                binding.tvGetCode.setBackgroundResource(R.drawable.shape_corner5_d0d2d6);
                                binding.tvGetCode.setClickable(false);
//                        binding..setValue(l / 1000 + "s");
//                        getCodeTextView.
                            }

                            @Override
                            public void onFinish() {
                                binding.tvGetCode.setText(getString(R.string.getVerfyCode));
                                binding.tvGetCode.setBackgroundResource(R.drawable.shape_corner5_3c6af4);
                                binding.tvGetCode.setClickable(true);
                            }
                        };
                        timer.start();
                    }
                });

            }
        });


        viewModel.getInfoErrorEvent.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer response) {
                if (WorkstatusEnum.PHONT_HINT.code == response) {
                    toastError(WorkstatusEnum.PHONT_HINT.msg);
                } else if (WorkstatusEnum.VERIFY_CODE_HINT.code == response) {
                    toastError(WorkstatusEnum.VERIFY_CODE_HINT.msg);
                } else if (WorkstatusEnum.READ_RULE_HINT.code == response) {
                    toastError(WorkstatusEnum.READ_RULE_HINT.msg);
                }
            }
        });

    }


}
