package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.gc.notarizationpc.bean.IdCardUserInfo;
import com.gc.notarizationpc.bean.WorkstatusEnum;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.request.CancelConnectOfficeRequest;
import com.gc.notarizationpc.data.model.request.ConnectNotaryRequest;
import com.gc.notarizationpc.data.model.request.VideoAddRequest;
import com.gc.notarizationpc.ui.SelectNotaryActivity;
import com.gc.notarizationpc.util.CommonUtil;

import java.util.Timer;
import java.util.TimerTask;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.http.BaseResponse;
import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.SPUtils;
import me.goldze.mvvmhabit.utils.Utils;

/**
 * 匹配，创建受理室匹配公证员
 */
public class VideoConnectionViewModel extends BaseViewModel {

    public SingleLiveEvent<String> matchCallbackEvent = new SingleLiveEvent<String>();
    public SingleLiveEvent<Boolean> matchNotaryFailEvent = new SingleLiveEvent<Boolean>();
    public SingleLiveEvent<Boolean> showConnectionDialogCallbackEvent = new SingleLiveEvent<Boolean>();

    private String recordId;

    public VideoConnectionViewModel(@NonNull Application application) {
        super(application);
    }

    //给RecyclerView添加ObservableList
    public ObservableList<HomeQuickListItemViewModel> observableList = new ObservableArrayList<>();

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private Timer timer;
    private TimerTask timerTask;
    private IdCardUserInfo userInfo;


    /**
     * 选择公证人员
     */
    public BindingCommand<Object> chooseNotaryEvent = new BindingCommand<Object>(new BindingAction() {
        @Override
        public void call() {
            startActivity(SelectNotaryActivity.class);

        }
    });


    /**
     * 匹配公证人员
     */

//    public BindingCommand<Object> matchNotaryEvent = new BindingCommand<Object>(new BindingAction() {
//        @Override
//        public void call() {
//            showConnectionDialogCallbackEvent.setValue(true);
//            addVideoOrder();
//        }
//    });

    @Override
    public void onCreate() {
        super.onCreate();
        userInfo = (IdCardUserInfo) SPUtils.getInstance().readObject(Utils.getContext(), "idcardinfo");
    }

    //视频受理室创建订单
    public void addVideoOrder(int sourceFrom) {
        VideoAddRequest request = new VideoAddRequest();
        request.setDispatchMode(2);//记录分发模式(1-指定公证员，2-匹配公证处)
        request.setSourceType("4");
        request.setMobile(userInfo.getMobile());
        request.setUserId(userInfo.getUserId());
        request.setUserName(userInfo.getName());

        RequestUtil.addVideoOrder(request, new MyObserver<String>() {
            @Override
            public void onSuccess(String response) {
                recordId = response;
                matchCallbackEvent.setValue(recordId);
                matchNotary(sourceFrom);

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                matchCallbackEvent.setValue(WorkstatusEnum.ADD_VIDEO_ORDER_FAIL.code.toString());
            }
        });
    }

    //视频咨询连线创建订单
    public void addVideoLineOrder(int sourceFrom) {
        VideoAddRequest request = new VideoAddRequest();
        request.setDispatchMode(2);//记录分发模式(1-指定公证员，2-匹配公证处)
        request.setSourceType("4");
//        request.setMobile(userInfo.getMobile());
        String sysUserId = SPUtils.getInstance().getString("userId");
        request.setUserId(sysUserId);//视频咨询连线没有刷身份证认证信息，所以这里用注册mac地址时候返回的userid
//        request.setUserName(userInfo.getName());

        RequestUtil.addVideoLineOrder(request, new MyObserver<String>() {
            @Override
            public void onSuccess(String response) {
                recordId = response;
                matchCallbackEvent.setValue(recordId);
                matchNotary(sourceFrom);

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                matchCallbackEvent.setValue(WorkstatusEnum.ADD_VIDEO_ORDER_FAIL.code.toString());
            }
        });
    }

    public void cancelTimer() {
//        if (timerTask != null) {
//            Log.e("Test", "取消匹配定时器任务");
//            timerTask.cancel();
//            timerTask = null;
//        }
//        if (timer != null) {
//            Log.e("Test", "取消匹配定时器");
//            timer.cancel();
//            timer.purge();
//            timer = null;
//        }

    }

    /**
     * 取消匹配公证员
     */
    public void cancelMatch(int sourceFrom) {
        CancelConnectOfficeRequest request = new CancelConnectOfficeRequest();
        request.setUserId(userInfo.getUserId());
        request.setRecordId(recordId);
        request.setRecordType(sourceFrom);
        RequestUtil.cancelNotaryMatch(request, new MyObserver<Boolean>() {
            @Override
            public void onSuccess(Boolean result) {
                if (result) {
                    Log.e("Test", "取消匹配成功");
                    matchCallbackEvent.setValue(WorkstatusEnum.CANCEL_MATCH.code.toString());
                }
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                matchCallbackEvent.setValue(WorkstatusEnum.CANCEL_MATCH_FAIL.code.toString());
            }
        });
    }

    /**
     * 匹配公证人员
     */
    public void matchNotary(int sourceFrom) {
        ConnectNotaryRequest request = new ConnectNotaryRequest();
        request.setRecordId(recordId);
        request.setMacAddress(AppConfig.macAddress);
        request.setMobile(userInfo.getMobile());
        request.setUserId(userInfo.getUserId());
        request.setUserName(userInfo.getName());
        request.setType(sourceFrom);//，1-咨询，2-视频公证
        RequestUtil.matchNotary(request, new MyObserver<Boolean>() {
            @Override
            public void onSuccess(Boolean result) {
                matchCallbackEvent.setValue(recordId);
            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                BaseResponse<String> response = new BaseResponse<>();
                response.setResult(errorMsg);
                response.setSuccess(false);
                //返回false，则匹配失败
                matchNotaryFailEvent.setValue(false);

            }
        });

    }

}
