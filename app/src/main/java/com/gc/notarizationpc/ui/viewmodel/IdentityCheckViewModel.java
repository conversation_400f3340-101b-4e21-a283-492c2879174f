package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.http.BaseResponse;

/**
 * @ProjectName:
 * @Package: com.gc.notarizationpc.ui.viewmodel
 * @Description:
 * @Author: xuhaifeng
 * @CreateDate: 2024/1/12
 */
public class IdentityCheckViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<BaseResponse> notaryListSingleLiveEvent = new SingleLiveEvent<>();
    //预约时间
    public ObservableField<String> reservationDate = new ObservableField<>("");

    public class UIChangeObservable {

        //显示公证处
        public ObservableBoolean showNotaryAddressDialog;

        public UIChangeObservable() {
            showNotaryAddressDialog = new ObservableBoolean(false);
        }
    }


    public IdentityCheckViewModel(@NonNull Application application) {
        super(application);
    }





    public BindingCommand notaryOnClickCommand = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            //获取公证处地区接口数据
            notaryListSingleLiveEvent.setValue(new BaseResponse());
        }
    });



    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}

