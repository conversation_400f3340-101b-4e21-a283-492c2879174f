package com.gc.notarizationpc.ui.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.MenuInfo;
import com.gc.notarizationpc.common.AppConfig;
import com.gc.notarizationpc.ui.adapter.FeeParentAdapter;
import com.serenegiant.usb.USBMonitor;

import java.util.ArrayList;
import java.util.List;

/**
 * 高拍仪
 */
public class GPCameraDialog extends Dialog {
    private Context mContext;
    private TextView tvOfflinePay, tvTotal;
    private RecyclerView rvFeeParent;

    private USBMonitor mUSBMonitor = null;
    private int come = 0;
    private String materialName = "";
    private String pidGPY = AppConfig.HIGHPID; // 10a0 is the original ID for high-shooting instrument
    private boolean captureStillTwo = false;

    public GPCameraDialog(Context context) {
        super(context);
        mContext = context;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_take_photo);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        //初始化界面控件 初始化界面控件的事件
        initViewAndEvent();

    }

    /**
     * 初始化界面的确定和取消监听器
     */
    private void initViewAndEvent() {
        tvOfflinePay = findViewById(R.id.tv_offline_pay);
        tvTotal = findViewById(R.id.tv_total);
        rvFeeParent = findViewById(R.id.rv_fee);

        List<MenuInfo> list = new ArrayList<>();
        list.add(new MenuInfo());
        list.add(new MenuInfo());
        list.add(new MenuInfo());
        list.add(new MenuInfo());
        list.add(new MenuInfo());
        list.add(new MenuInfo());

        rvFeeParent.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });

        FeeParentAdapter feeParentAdapter = new FeeParentAdapter(mContext, R.layout.item_list_fee_parent, 0);
        rvFeeParent.setAdapter(feeParentAdapter);
        feeParentAdapter.refreshAdapter(list);

        findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowing()) {
                    dismiss();
                }
            }
        });
    }

    @Override
    public void show() {
        super.show();
    }


}
