package com.gc.notarizationpc.ui

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.hardware.usb.UsbDevice
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.eloam.gaopaiyi.util.UVCCameraUtil
import com.example.framwork.utils.MyLogUtils
import com.example.framwork.widget.kprogresshud.KProgressHUD
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.AppConfig
import com.gc.notarizationpc.common.BaseActivity
import com.gc.notarizationpc.model.BitmapInfo
import com.gc.notarizationpc.model.EBPhotoInfo
import com.gc.notarizationpc.model.RemotePhotoInfo
import com.gc.notarizationpc.model.TakePhotoInfo
import com.gc.notarizationpc.utils.*
import com.serenegiant.usb.IFrameCallback
import com.serenegiant.usb.USBMonitor
import kotlinx.android.synthetic.main.layout_information_photo.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import java.nio.ByteBuffer


class TakePhotoActivity : BaseActivity() {
    private var TAG = "TakePhotoActivity"
    private var mUSBMonitor: USBMonitor? = null
    private var come: Int = 0
    private var materialName: String? = ""
    private var pidGPY: String = AppConfig.HIGHPID //10a0 高拍仪原ID
    private var captureStillTwo = false //高拍  拍照
    protected var progressTakeHUD: KProgressHUD? = null
    private val WIDTH: Int = 1280
    private val HEIGHT: Int = 720
    private val PHOTO_SIZE = WIDTH * HEIGHT * 3 / 2
    private var imgKjStack = ImageStack(WIDTH, HEIGHT)

    //全局定义
    private var lastClickTime = 0L
    private var lastClickTimeT = 0L
    private var isBack: Boolean = false

    private var initTime: Long = 0;

    // 两次点击间隔不能少于1000ms
    private val FAST_CLICK_DELAY_TIME = 1800
    override fun getContentViewLayoutID(): Int {
        return if (AppConfig.RGBPID == "c013") {
            R.layout.layout_information_photo;
        } else {
            R.layout.layout_information_photo_portrait;
        }
    }

    override fun initViewsAndEvents() {
        Log.i(TAG, "initViewsAndEvents")
        initTime = System.currentTimeMillis();
        hintPhoto.text = "点击下方拍照按钮, 上传文件"
        if (come == 1) {
            btnTake.visibility = View.INVISIBLE
            hintPhoto.text = "公证员正在拍照, 请勿操作机器"
            captureStillTwo = true
        } else if (come == 3) {
            hintPhoto.visibility = View.VISIBLE
        } else if (come == 4) {
            hintPhoto.visibility = View.VISIBLE
        } else
            hintPhoto.visibility = View.VISIBLE
        YuvUtils.init(this)
//        photoBackClicked?.setOnClickListener(object : MyOnclickClickListener() {
//            override fun mOnClick(v: View?) {
//                finish()
//            }
//        })
        photoBackClicked.setOnClickListener {
            if (System.currentTimeMillis() - initTime > 2000) {
                if (!isBack) {
                    isBack = true
                    finish()
                }
            } else {
                toastInfo("正在退出相机")
            }
        }
//        photoBackClicked.setOnClickListener(object :MyOnclickClickListener(){
//            override fun mOnClick(v: View?) {
//                toastInfo("正在退出相机")
//                photoBackClicked.isEnabled =false
//                finish()
//            }
//
//        })
        btnTake.setOnClickListener {
            if (System.currentTimeMillis() - lastClickTime >= FAST_CLICK_DELAY_TIME) {
                Log.i(TAG, "takePhotoClick")
                captureStillTwo = true
                lastClickTime = System.currentTimeMillis()
            } else {
                Log.i(TAG, "double takePhotoClick")
            }
        }
        openCamera()
    }

    override fun showProgress(isCancel: Boolean?, hint: String?) {
        if (!isFinishing) {
            if (progressTakeHUD == null) {
                progressTakeHUD = KProgressHUD.create(this).setStyle(KProgressHUD.Style.SPIN_INDETERMINATE).setDimAmount(0.5f)
            }
            if (!progressTakeHUD?.isShowing!!) {
                if (!TextUtils.isEmpty(hint)) {
                    progressTakeHUD!!.setLabel(hint)
                } else {
                    progressTakeHUD!!.setLabel("正在拍照上传...")
                }
                if (isCancel != null) {
                    progressTakeHUD!!.setCancellable(isCancel)
                }
                if (!progressTakeHUD!!.isShowing) progressTakeHUD!!.show()
            }
        }
    }

    override fun hideProgress() {
        if (progressTakeHUD != null && progressTakeHUD!!.isShowing) {
            progressTakeHUD!!.dismiss()
        }
    }

    /**
     * 打开相机
     */
    private fun openCamera() {
        Log.i(TAG, "openCamera")

        mUSBMonitor =
                UVCCameraLiceo.initUSBMonitor(mActivity, object : UVCCameraLiceo.OnMyDevConnectListener {
                    override fun onConnectDev(
                            device: UsbDevice,
                            ctrlBlock: USBMonitor.UsbControlBlock
                    ) {
                        if (System.currentTimeMillis() - lastClickTimeT >= FAST_CLICK_DELAY_TIME) {
                            Log.i(TAG, "takePhotoClick")
                            val pid = String.format("%x", device.productId)
                            Log.i(TAG, "onConnectDev() pid:" + pid)
                            Log.i(TAG, "onConnectDev() pidGPY:" + pidGPY)
                            if (pid == pidGPY) { //打开高拍 1054
                                UVCCameraLiceo.openRGBCamera(
                                        informationPhoto,
                                        ctrlBlock,
                                        object : IFrameCallback {
                                            override fun onFail() {
                                                Log.i(TAG, "openCamera fail")
                                                hideProgress()
                                                MyLogUtils.i(TAG, "高拍仪打开失败")
                                                toastError("高拍仪打开失败")
                                                finish()
                                            }

                                            override fun onFrame(frame: ByteBuffer?) {
//                                                imgKjStack.pushImageInfo(frame!!, System.currentTimeMillis())
                                                if (captureStillTwo) {
                                                    captureStillTwo = false
                                                    takePhoto()
                                                } else
                                                    hintPhoto.visibility = View.VISIBLE
                                            }
                                        })

                            }
                            lastClickTimeT = System.currentTimeMillis()
                        } else {
                            Log.i(TAG, "double takePhotoClick")
                        }
                    }
                })
        var hasHigh = false;
        val devList = UVCCameraUtil.getUsbDeviceList(this, mUSBMonitor!!)
        for (usbDevice in devList) {
            val productid = String.format("%x", usbDevice?.productId)
            Log.e(TAG, "找到了: $productid")
            if (productid == "1054") {
                hasHigh = true
            }
        }
        if (!hasHigh) {
            toastError("高拍仪连接出现异常")
        }
    }

    @Subscribe
    fun onEventMainThread(eb: TakePhotoInfo) {
        Log.i(TAG, "onEventMainThread")
        if (eb.info == 0) {
            captureStillTwo = true
        } else if (eb.info == 1) {
            finish()
        }
    }

    /**
     * 拍照
     */
    private fun takePhoto() {
        Log.i(TAG, "takePhoto")
        lifecycleScope.launch(Dispatchers.IO) {
            runOnUiThread {
                showProgress(false, "图片保存中")
            }
//            var imgInfo = imgKjStack.pullImageInfo()
//            if (imgInfo.isNew) {
//                val data = imgInfo.data
//                if (data.size >= PHOTO_SIZE) {

            val faceBitmap = informationPhoto?.getBitmap()
//                    val faceBitmap = YuvUtils.nv21ToBitmap(data, WIDTH, HEIGHT)
            if (faceBitmap != null && !faceBitmap.isRecycled) {
                // 修复图片被旋转的角度
                val bitmap: Bitmap? = rotateBitmap2(faceBitmap!!, 180)
//                        val bitmap: Bitmap? = faceBitmap
                if (come == 5) {
//                    if (!isTaked) {
//                        isTaked = true
                    withContext(Dispatchers.Main) {
                        EventBus.getDefault().post(BitmapInfo(bitmap, 0))
//                                    delay(500)
                        finish()
                    }
//                    }
                } else if (come == 6) {
//                    if (!isTaked) {
//                        isTaked = true
                    withContext(Dispatchers.Main) {
                        EventBus.getDefault().post(BitmapInfo(bitmap, 1))
//                                    delay(500)
                        finish()
                    }
//                    }
                } else if (come == 4) {
                    withContext(Dispatchers.Main) {
                        EventBus.getDefault().post(EBPhotoInfo(bitmap))
                        delay(500)
                        finish()
                    }
                } else if (come == 3) {
                    withContext(Dispatchers.Main) {
                        EventBus.getDefault().post(RemotePhotoInfo(bitmap, materialName))
                        delay(500)
                        finish()
                    }
                } else {
                    /*if (come == 1) {
                        delay(1000)
                    }*/
                    withContext(Dispatchers.Main) {
                        EventBus.getDefault().post(EBPhotoInfo(bitmap))
                    }
                }
            }
//                }
//            } else {
//                Log.i(TAG, "imgInfo is null or imgInfo is not new.")
//            }
            runOnUiThread {
                hideProgress()
                toastInfo("图片保存成功")
            }
        }

    }

    private fun rotateBitmap2(bm: Bitmap, orientationDegree: Int): Bitmap? {
        val ma = Matrix()
        ma.setRotate(orientationDegree.toFloat(), bm.width.toFloat() / 2, bm.height.toFloat() / 2)

        try {
            return Bitmap.createBitmap(bm, 0, 0, bm.width, bm.height, ma, true)
        } catch (ex: OutOfMemoryError) {
            Log.e(TAG, "位图旋转出错")
        }
        //方便判断，角度都转换为正值
        var degree = orientationDegree
        if (degree < 0) {
            degree = 360 + orientationDegree
        }
        val srcW = bm.width
        val srcH = bm.height
        val m = Matrix()
        m.setRotate(degree.toFloat(), srcW.toFloat() / 2, srcH.toFloat() / 2)
        val targetX: Float
        val targetY: Float
        var destH = srcH
        var destW = srcW

        //根据角度计算偏移量，原理不明
        if (degree == 90) {
            targetX = srcH.toFloat()
            targetY = 0f
            destH = srcW
            destW = srcH
        } else if (degree == 270) {
            targetX = 0f
            targetY = srcW.toFloat()
            destH = srcW
            destW = srcH
        } else if (degree == 180) {
            targetX = srcW.toFloat()
            targetY = srcH.toFloat()
        } else {
            return bm
        }
        val values = FloatArray(9)
        m.getValues(values)
        val x1 = values[Matrix.MTRANS_X]
        val y1 = values[Matrix.MTRANS_Y]
        m.postTranslate(targetX - x1, targetY - y1)

        //注意destW 与 destH 不同角度会有不同
        val bm1 = Bitmap.createBitmap(destW, destH, Bitmap.Config.ARGB_8888)
        val paint = Paint()
        val canvas = Canvas(bm1)
        canvas.drawBitmap(bm, m, paint)
        return bm1

    }

    override fun onResume() {
        super.onResume()
        UVCCameraLiceo.registerUsbMonitor(mUSBMonitor!!)
        UVCCameraLiceo.requestPermissionRGB(mActivity, mUSBMonitor!!, pidGPY)
    }

    override fun onPause() {
        super.onPause()
        try {
            UVCCameraLiceo.releaseRGBCamera()
            UVCCameraLiceo.unRegisterUsbMonitor(mUSBMonitor!!)
        } catch (e: Exception) {
            Log.e(TAG, "onPause:" + e.message)
        }
    }

    //--------------------------
    override fun onStart() {
        Log.i(TAG, "onStart")
        super.onStart()
    }

    override fun onStop() {
        Log.i(TAG, "onStop")
        /*mUSBMonitor?.unregister()*/
        super.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            YuvUtils.destroy()
            if (come != 1) {
                UVCCameraLiceo.destroyUSBMonitor(mUSBMonitor!!)
                mUSBMonitor = null
            }
        } catch (e: Exception) {
            Log.i(TAG, "onDestroy")
        }

        Log.i(TAG, "onDestroy")
    }

    override fun getIntentData(intent: Intent?) {
        come = intent?.getIntExtra("come", 0)!!
        if (come == 3) {
            materialName = intent.getStringExtra("materialName")
        }
    }

    override fun isUseEventBus(): Boolean {
        return true
    }
}