package com.gc.notarizationpc.ui

import android.content.Intent
import android.media.MediaPlayer
import android.view.View
import com.example.framwork.baseapp.AppManager
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.AppConfig.IS_PROXY
import com.gc.notarizationpc.common.BaseActivity
import kotlinx.android.synthetic.main.activity_person_select.*
import java.io.IOException

/**
 * 自助公证 ，选择角色
 */
class SelfNotarizationSelectRoleActivity : BaseActivity() {
    var isProxy = false;
    private var mediaPlayer: MediaPlayer? = null

    override fun getIntentData(intent: Intent?) {
    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.activity_person_select
    }

    fun backToMain(view: View) {
        AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
    }

    override fun initViewsAndEvents() {
        isPersonView.isSelected = true
        playMp3()
    }

    private fun playMp3() {
        mediaPlayer = MediaPlayer.create(this, R.raw.isdaiban)
        try {
//            mediaPlayer?.reset()
            mediaPlayer?.start()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        mediaPlayer?.setOnPreparedListener { mp ->
            mediaPlayer?.seekTo(0)
            mediaPlayer?.start()
        }
        mediaPlayer?.setOnErrorListener { mp, what, extra ->
            false
        }
    }


    fun nextClick(view: View?) {
        val intent = Intent(this, NotarizationSelfActivity::class.java)
        intent.putExtra("isProxy", isProxy)
        startActivity(intent)
        finish()
    }

    fun personClick(view: View?) {
        isPersonView.isSelected = true
        isProxyView.isSelected = false
        isProxy = false
        IS_PROXY = false
    }

    fun proxyClick(view: View?) {
        isPersonView.isSelected = false
        isProxyView.isSelected = true
        isProxy = true
        IS_PROXY = true
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mediaPlayer != null) {
            if (mediaPlayer!!.isPlaying) mediaPlayer!!.stop()
            mediaPlayer!!.release()
            mediaPlayer = null
        }
    }

}