package com.gc.notarizationpc.ui.fragment;


import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.Nullable;

import com.gc.mininotarization.R;
import com.gc.mininotarization.databinding.FragmentSelfServiceMaterialToUploadGridBinding;
import com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceMaterialToUploadGridViewModel;
import com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceUploadMaterialViewModel;

import me.goldze.mvvmhabit.base.BaseFragment;

/**
 * 这个是自助办证流程中上传材料fragment中材料上传的fragment
 */

public class SelfServiceMaterialToUploadGridFragment extends BaseFragment<FragmentSelfServiceMaterialToUploadGridBinding, FragmentSelfServiceUploadMaterialViewModel> {
    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_self_service_material_to_upload_grid;
    }

    @Override
    public int initVariableId() {
        return 0;
    }
}
