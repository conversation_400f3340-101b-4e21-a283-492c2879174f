package com.gc.notarizationpc.ui.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.gc.mininotarization.BR;
import com.gc.mininotarization.R;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.NotaryItemModel;

import java.util.ArrayList;
import java.util.List;

import me.goldze.mvvmhabit.base.BaseViewModel;
import me.goldze.mvvmhabit.bus.event.SingleLiveEvent;
import me.goldze.mvvmhabit.network.MyObserver;
import me.tatarka.bindingcollectionadapter2.ItemBinding;

/**
 *
 */

public class SelfServiceCertificateHomeViewModel extends BaseViewModel {
    //封装一个界面发生改变的观察者
    public UIChangeObservable uc = new UIChangeObservable();
    public SingleLiveEvent<String> goToSelfNor = new SingleLiveEvent<>();

    public List<NotaryItemModel> notaryItemModelList = new ArrayList<>();


    //给RecyclerView添加ObservableList
    public ObservableList<NotaryGridItemViewModel> observableList = new ObservableArrayList<>();
    //给RecyclerView添加ItemBinding
    public ItemBinding<NotaryGridItemViewModel> itemBinding = ItemBinding.of(BR.viewModel, R.layout.item_notary);//这里指定了item的布局

    public class UIChangeObservable {

    }

    public SelfServiceCertificateHomeViewModel(@NonNull Application application) {
        super(application);
    }

    /**
     * 请求网络接口
     */
    public void requestNetwork(){
//        showDialog("");
        RequestUtil.getNotaryItemByUser(new MyObserver<List<NotaryItemModel>>() {
            @Override
            public void onSuccess(List<NotaryItemModel> result) {
                dismissDialog();
                notaryItemModelList = result;
                if (result != null && result.size() > 0){
                    for (int i = 0; i < result.size(); i++) {
                        NotaryItemModel notaryItemModel = result.get(i);
                        NotaryGridItemViewModel notaryGridItemViewModel = new NotaryGridItemViewModel(SelfServiceCertificateHomeViewModel.this,notaryItemModel);
                        switch (result.get(i).getValue()){
                            case "1":  // 出国留学
                                notaryGridItemViewModel.background = R.mipmap.go_abroad;
                                break;
                            case "2": // 定居移民
                                notaryGridItemViewModel.background = R.mipmap.settled_immigrant;
                                break;
                            case "3": // 探亲旅游
                                notaryGridItemViewModel.background = R.mipmap.travel;
                                break;
                            case "4": // 境外其他
                                notaryGridItemViewModel.background = R.mipmap.other_abroad;
                                break;
                            case "5": // 商务劳务
                                notaryGridItemViewModel.background = R.mipmap.business_service;
                                break;
                            case "6": // 公证预受理
                                notaryGridItemViewModel.background = R.mipmap.domestic_economy;
                                break;
                        }
                        observableList.add(notaryGridItemViewModel);
                    }
                }

            }

            @Override
            public void onFailure(Throwable e, String errorMsg) {
                dismissDialog();
                toastError(errorMsg == null ? "" : errorMsg);

            }
        });

    }

    /**
     * 选中某个item
     */

    public void itemClick(NotaryItemModel entity){
        Log.d("NotaryItemModel--value",entity.getValue());
        goToSelfNor.setValue(entity.getValue());
    }


//
//    /**
//     * 出国留学
//     * 根据用途获取事项 useType 出国留学--1->学习 定居移民--2->定居 探亲旅游--3->探亲 商务劳务--5-->劳务 境外其他--4-->其他
//     */
//   public BindingCommand goAbroadEvent = new BindingCommand(new BindingAction(){
//        @Override
//        public void call() {
//            goToSelfNor.setValue(CommonUtil.GO_ABROAD);
//
//
//        }
//    });
//
//    /**
//     * 境外其他
//     */
//    public  BindingCommand otherAbroadEvent = new BindingCommand(new BindingAction() {
//        @Override
//        public void call() {
//            goToSelfNor.setValue(CommonUtil.OTHER);
//        }
//    });
//
//
//    /**
//     * 定居移民
//     */
//    public BindingCommand settledImmigrantEvent = new BindingCommand(new BindingAction() {
//        @Override
//        public void call() {
//            goToSelfNor.setValue(CommonUtil.DINGJU);
//        }
//    });
//
//    /**
//     * 探亲旅游
//     */
//    public   BindingCommand travelEvent = new BindingCommand(new BindingAction() {
//        @Override
//        public void call() {
//            goToSelfNor.setValue(CommonUtil.TANQING);
//        }
//    });
//
//    /**
//     * 商务劳务
//     */
//    public   BindingCommand businessServiceEvent = new BindingCommand(new BindingAction() {
//        @Override
//        public void call() {
//            goToSelfNor.setValue(CommonUtil.LAOWU);
//        }
//    });
//
//    /**
//     *  进入其他界面
//     */
//    private void gotoOtherPage(Class pageClass) {
//        startActivity(pageClass);
//    }



}
