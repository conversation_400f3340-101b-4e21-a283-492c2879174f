package com.gc.notarizationpc.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.gc.notarizationpc.R
import com.gc.notarizationpc.ui.viewholder.BannerHolder
import com.youth.banner.adapter.BannerAdapter

/**
 * 自定义布局，网络图片
 */
class CustomBannerAdapter(mDatas: List<String?>?) : BannerAdapter<String?, BannerHolder>(mDatas) {
    override fun onCreateHolder(parent: ViewGroup, viewType: Int): BannerHolder {
        return BannerHolder(LayoutInflater.from(parent.context).inflate(R.layout.banner_image, parent, false))
    }

    override fun onBindView(holder: BannerHolder?, data: String?, position: Int, size: Int) {
        holder?.imageView?.setImageURI(data)
    }

}