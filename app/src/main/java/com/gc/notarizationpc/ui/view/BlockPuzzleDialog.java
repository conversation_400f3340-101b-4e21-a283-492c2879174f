package com.gc.notarizationpc.ui.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ProgressBar;

import androidx.annotation.NonNull;

import com.gc.mininotarization.R;
import com.gc.notarizationpc.bean.CaptchaCheckOt;
import com.gc.notarizationpc.bean.CaptchaGetIt;
import com.gc.notarizationpc.data.RequestUtil;
import com.gc.notarizationpc.data.model.response.Input;
import com.gc.notarizationpc.util.AESUtil;
import com.gc.notarizationpc.util.CommonUtil;
import com.gc.notarizationpc.widget.DragImageView;
import com.google.gson.Gson;
import com.serenegiant.arcface.util.BitmapUtils;

import java.util.HashMap;
import java.util.Map;

import me.goldze.mvvmhabit.network.MyObserver;
import me.goldze.mvvmhabit.utils.ConvertUtils;


/**
 * Date:2020/5/6
 * 图形滑动验证 弹框
 * author:wuyan
 */
public class BlockPuzzleDialog extends Dialog {
    private String baseImageBase64 = ""; // Background image
    private String slideImageBase64 = ""; // Sliding image
    private String key = ""; // AES encryption key
    private String codeToken = "";
    private Handler handler;
    private OnResultsListener mOnResultsListener;
    private DragImageView dragView;
    private ProgressBar rl_pb;

    public BlockPuzzleDialog(@NonNull Context mContext) {
        this(mContext, 0);
    }

    public BlockPuzzleDialog(@NonNull Context mContext, int themeResId) {
        super(mContext, R.style.dialog);
        if (getWindow() != null) {
            getWindow().setGravity(Gravity.CENTER);
            getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            Activity activity = (Activity) mContext;
            int width = activity.getWindowManager().getDefaultDisplay().getWidth();
            getWindow().getAttributes().width = width * 9 / 10; // Set width to 90% of screen
            setCanceledOnTouchOutside(false); // Dialog does not dismiss on outside touch


//
//            getWindow().getDecorView().setBackgroundColor(mContext.getResources().getColor(android.R.color.white));
//            getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // Set Dialog background to transparent
//            WindowManager.LayoutParams lp = getWindow().getAttributes();
//            lp.width = ConvertUtils.dp2px(450f);
//            lp.height = ConvertUtils.dp2px(350f);
//            lp.alpha = 1.0f;
//            getWindow().setAttributes(lp);
//            getWindow().setGravity(Gravity.CENTER);
//            setCanceledOnTouchOutside(false);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_block_puzzle);
        dragView = findViewById(R.id.dragView);
        rl_pb = findViewById(R.id.rl_pb);
        findViewById(R.id.tv_delete).setOnClickListener(v -> dismiss());
        findViewById(R.id.tv_refresh).setOnClickListener(v -> loadCaptcha());

        // Set default image
        Bitmap bitmap = CommonUtil.getBitmap(getContext(), R.mipmap.bg_default);
        dragView.setUp(bitmap, bitmap);
        dragView.setSBUnMove(false);
        loadCaptcha();
    }

    private void loadCaptcha() {
        try {
            codeToken="";
            dragView.setVisibility(View.INVISIBLE);
            rl_pb.setVisibility(View.VISIBLE);
            Map<String, Object> params = new HashMap<>();
            params.put("captchaType", "blockPuzzle");
            RequestUtil.getGraphCode(params, new MyObserver<Input<CaptchaGetIt>>() {
                @Override
                public void onSuccess(Input<CaptchaGetIt> result) {
                    if (result.getRepData() != null && "0000".equals(result.getRepCode())) {
                        baseImageBase64 = result.getRepData().getOriginalImageBase64();
                        slideImageBase64 = result.getRepData().getJigsawImageBase64();
                        key = result.getRepData().getSt();
                        codeToken = result.getRepData().getTn();
                        Log.e("http", "=====" + key);
                        dragView.setUp(
                                BitmapUtils.base64ToBitmap(baseImageBase64),
                                BitmapUtils.base64ToBitmap(slideImageBase64)
                        );
                        dragView.setSBUnMove(true);
                        initEvent();
                    } else {
                        dragView.setSBUnMove(false);
                        dragView.setVisibility(View.VISIBLE);
                        rl_pb.setVisibility(View.GONE);
                    }

                    dragView.setVisibility(View.VISIBLE);
                    rl_pb.setVisibility(View.GONE);
                }

                @Override
                public void onFailure(Throwable e, String errorMsg) {
                    dragView.setSBUnMove(false);
                    dragView.setVisibility(View.VISIBLE);
                    rl_pb.setVisibility(View.GONE);
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            dragView.setSBUnMove(false);
            dragView.setVisibility(View.VISIBLE);
            rl_pb.setVisibility(View.GONE);
        }

    }

    private void checkCaptcha(double sliderXMoved) {
        Point point = new Point((int) sliderXMoved, 5);
        String pointStr = new Gson().toJson(point);
        try {
//            Map<String,Object> params=new HashMap<>();
//            params.put("captchaType","blockPuzzle");
//            params.put("pointJson",AESUtil.encode(pointStr, key));
//            params.put("token",codeToken);
            RequestUtil.checkGraphCode("blockPuzzle", AESUtil.encode(pointStr, key), codeToken, new MyObserver<Input<CaptchaCheckOt>>() {
                @Override
                public void onSuccess(Input<CaptchaCheckOt> result) {
                    if (result != null && "0000".equals(result.getRepCode())) {
                        dragView.ok();
                        runUIDelayed(() -> {
                            dragView.reset();
                            dismiss();
                            loadCaptcha();
                        }, 2000);
                        String param = codeToken + "---" + pointStr;
                        if (mOnResultsListener != null) {
                            mOnResultsListener.onResultsClick(AESUtil.encode(param, key));
                        }
                    } else {
                        dragView.fail();
                        loadCaptcha();
                    }
                }

                @Override
                public void onFailure(Throwable e, String errorMsg) {
                    dragView.fail();
                    loadCaptcha();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            dragView.fail();
            loadCaptcha();
        }
    }

    public void initEvent() {
        dragView.setDragListenner(new DragImageView.DragListenner() {
            @Override
            public void onDrag(double position) {
                checkCaptcha(position);
            }
        });
    }

    public void runUIDelayed(Runnable run, int de) {
        if (handler == null) {
            handler = new Handler(Looper.getMainLooper());
        }
        handler.postDelayed(run, de);
    }

    public interface OnResultsListener {
        void onResultsClick(String result);
    }

    public void setOnResultsListener(OnResultsListener mOnResultsListener) {
        this.mOnResultsListener = mOnResultsListener;
    }
}