<template>
  <div class="app-container">
    <div class="video-group">
      <div class="player" id="local-player"></div>
    </div>

    <div class="record-page">
      <video
        autoplay
        controls
        playsinline
        ref="video"
        style="display: none"
      ></video>
    </div>
  </div>
</template>

<script>
import $ from "jQuery";
import RecordRTC from "recordrtc";
import apiServe from "@/plugins/apiServe";
import commonJs from '@/utils/common'

export default {
  name: "AgoraRTC",
  props: ["roomId"],
  data() {
    return {
      videoSwitchText: "关闭视频",
      screenRecordText: "开始录屏",
      audioSwitchText: "关闭音频",
      videoSwitchFlag: true,

      cameraIndex: 0,
      cameras: [],

      options: {
        appid: "",
        channel: "",
        uid: 111,
        token: "",
      },
      client: "",
      localTracks: {
        videoTrack: null,
        audioTrack: null,
      },
      remoteUsers: {},

      //录屏组件
      video: null,
      videoStart: false,
      recorder: null,
      screenRecordText: "开始录屏",
      isShowTimer: false,

      //计时器组件
      timer: "",
      content: "",
      hour: 0,
      minutes: 0,
      seconds: 0,

      deviceList:[],
    };
  },
  created() {
    console.log(AgoraRTC, "7878");
    this.getAgoraToken();
  },
  mounted() {
    console.log(this.roomId, "房间号");

    //测试用，所以直接创建了，其他需求可自行更改
    this.video = document.querySelector("video");
    // this.getCameras();
    // this.startRecording();
    // console.log(document.querySelector('.userVideo'));
  },
  methods: {
    // 获取声网token
    getAgoraToken() {
      let params = {
        channelName: this.roomId,
      };
      apiServe.getAgoraToken(params).then((res) => {
        this.options.appid = res.appId;
        console.log(typeof this.roomId, 7878);
        this.options.channel = this.roomId;
        this.options.token = res.token;
        console.log(this.options, "9999");
        //加入房间
        this.getCameras();
      });
    },

    //获取本地camera列表
    getCameras() {
      this.cameras = [];
      AgoraRTC.getCameras().then((devices) => {
        this.cameras = devices;
        devices.forEach((dev) => {
          console.log(
            "摄像头列表+++++++++++++++++camera label: " +
              dev.label +
              " deviceId: " +
              dev.deviceId
          );
        });
        this.join();
      });
    },

    async join() {
      this.client = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });
      // add event listener to play remote tracks when remote user publishs.
      this.client.on("user-published", this.handleUserPublished);
      this.client.on("user-unpublished", this.handleUserUnpublished);

      // join a channel and create local tracks, we can use Promise.all to run them concurrently
      console.log(this.options.uid, "6666");
      [
        this.options.uid,
        this.localTracks.audioTrack,
        this.localTracks.videoTrack,
      ] = await Promise.all([
        // join the channel
        this.client.join(
          this.options.appid,
          this.options.channel,
          this.options.token || null,
          this.options.uid
        ),
        // create local tracks, using microphone and camera
        AgoraRTC.createMicrophoneAudioTrack(),
        AgoraRTC.createCameraVideoTrack({cameraId: this.cameras[commonJs.getCameraIndex()].deviceId}),
      ]);

      console.log(this.options.uid, "6666");

      // play local video track
      this.localTracks.videoTrack.play("local-player");
      $("#local-player-name").text(`localVideo(${this.options.uid})`);

      // publish local tracks to channel
      await this.client.publish(Object.values(this.localTracks));
      console.log("publish success");
      console.log("~~~~~~~~~~~~~~~~~~~~加入房间成功~~~~~~~~~~~~~~~~~~~~~~~~");
    },

    async leave() {
      console.log("我调用了关闭视频方法");
      for (let trackName in this.localTracks) {
        var track = this.localTracks[trackName];
        if (track) {
          track.stop();
          track.close();
          this.localTracks[trackName] = undefined;
        }
      }

      // remove remote users and player views
      this.remoteUsers = {};
      // $(".video-group").html("");

      // leave the channel
      await this.client.leave();

      // $("#local-player-name").text("");

      // $('#join').attr('disabled', false)
      // $('#leave').attr('disabled', true)
      console.log("client leaves channel success");
    },

    async subscribe(user, mediaType) {
      const uid = user.uid;
      // subscribe to a remote user
      await this.client.subscribe(user, mediaType);
      console.log("subscribe success");
      if (mediaType === "video") {
        //     const player = $(`
        //   <div id="player-wrapper-${uid}">
        //     <p class="player-name">remoteUser(${uid})</p>
        //     <div id="player-${uid}" class="player"></div>
        //   </div>
        // `);

        const player = $(`
      <div id="player-wrapper-${uid}" class="remote_videobox">
        <div id="player-${uid}" class="player"></div>
      </div>
    `);
        $(".video-group").prepend(player);
        user.videoTrack.play(`player-${uid}`);
      }
      if (mediaType === "audio") {
        user.audioTrack.play();
      }
    },

    handleUserPublished(user, mediaType) {
      const id = user.uid;
      this.remoteUsers[id] = user;
      this.subscribe(user, mediaType);
    },

    handleUserUnpublished(user) {
      const id = user.uid;
      delete this.remoteUsers[id];
      $(`#player-wrapper-${id}`).remove();
    },
    //关闭视频
    videoSwitch() {
      if ((this.videoSwitchText = "关闭视频")) {
        this.videoSwitchText = "打开视频";

        this.client.unpublish(Object.values(this.localTracks));

        // console.log(this.localTracks.videoTrack);
        // if (this.videoSwitchFlag) {
        //   this.videoSwitchFlag = false;
        //   this.videoSwitchText = "打开视频";
        //   this.localTracks.videoTrack.stop();
      } else {
        this.videoSwitchText = "打开视频";
        this.handleUserPublished();
        // this.client.publish(Object.values(this.localTracks))

        // this.videoSwitchFlag = true;
        // this.videoSwitchText = "关闭视频";
        // this.localTracks.videoTrack.play();
      }
    },
    //切换摄像头
    changeCamera() {
      let cameraId = this.cameras[this.cameraIndex].deviceId;
      this.localStream.switchDevice("video", cameraId).then(() => {
        console.log("switch camera success");
      });
    },
    // 录屏按钮点击
    screenRecord() {
      if (this.videoStart) {
        this.stopRecording();
      } else {
        this.startRecording();
      }
    },

    startRecording() {
      this.captureCamera((camera) => {
        this.videoStart = true;
        this.video.muted = true;
        this.video.volume = 0;
        this.video.srcObject = camera;
        this.recorder = RecordRTC(camera, {
          type: "video",
        });
        this.recorder.startRecording();
        this.recorder.camera = camera;
        this.screenRecordText = "结束录屏";
        this.timer = setInterval(this.startTimer, 1000);
        this.isShowTimer = true;
      });
    },

    // 开始计时器
    startTimer() {
      this.seconds += 1;
      if (this.seconds >= 60) {
        this.seconds = 0;
        this.minutes = this.minutes + 1;
      }
      this.$refs.startTimer.innerHTML =
        "正在录屏 " +
        (this.minutes < 10 ? "0" + this.minutes : this.minutes) +
        ":" +
        (this.seconds < 10 ? "0" + this.seconds : this.seconds);
    },

    stopRecording() {
      this.recorder.stopRecording(this.stopRecordingCallback);
    },
    stopRecordingCallback() {
      // this.video.src = this.video.srcObject = null;
      // this.video.muted = false;
      // this.video.volume = 1;
      // this.video.src = URL.createObjectURL(this.recorder.getBlob());
      // window.open(URL.createObjectURL(this.recorder.getBlob()));
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(this.recorder.getBlob());
      link.download = "录屏.mp4";
      link.click();
      window.URL.revokeObjectURL(link.href);
      this.recorder.camera.stop();
      this.recorder.destroy();
      this.recorder = null;
      this.videoStart = false;
      clearInterval(this.timer);
      this.isShowTimer = false;
      this.screenRecordText = "开始录屏";
    },

    //录屏操作
    captureCamera(callback) {
      let that = this;
      navigator.mediaDevices
        .getDisplayMedia({ audio: true, video: true })
        .then(function (Mediastream) {
          that.mixAudioStream(Mediastream).then((audioStream) => {
            let audioTracks = audioStream.getAudioTracks();
            let tracks = Mediastream.getTracks(); //需要移除音轨，添加混流后的音轨
            let findAudioTrack = tracks.find((a) => a.kind === "audio");
            if (findAudioTrack) {
              Mediastream.removeTrack(findAudioTrack);
            }
            console.log(6);
            Mediastream.addTrack(audioTracks[0]); //注！此处添加麦克风音轨无效
            console.log(7);
            callback(Mediastream);
          });
        })
        .catch((error) => {
          this.$message.error("未录制视频！");
        });
    },

    getMicroAudioStream() {
      return navigator.mediaDevices.getUserMedia({ audio: true, video: false });
    },

    //混合音轨
    mixAudioStream(Mediastream) {
      let systemAudioTrack = Mediastream.getAudioTracks()[0]; //获取强制获取的桌面音【轨】
      return new Promise((resolve, reject) => {
        this.getMicroAudioStream()
          .then((audioStream) => {
            //获取麦克风音频【流】
            let audioContext = new AudioContext(); //创建音频上下文
            let microphoneStreamNode = audioContext.createMediaStreamSource(
              audioStream
            ); //创建节点
            let sysAudioStream = new MediaStream(); //创建一个媒体流
            sysAudioStream.addTrack(systemAudioTrack); //把系统音轨添加到新的媒体流
            let sysAudioStreamNode = audioContext.createMediaStreamSource(
              sysAudioStream
            ); //创建系统音频节点
            let mixedOutput = audioContext.createMediaStreamDestination(); //创建一个输出媒体流节点
            microphoneStreamNode.connect(mixedOutput); //把麦克风节点和系统音节点添加到输出媒体流
            sysAudioStreamNode.connect(mixedOutput); //把麦克风节点和系统音节点添加到输出媒体流
            resolve(mixedOutput.stream); //返回混合后的媒体流
          })
          .catch((err) => {
            reject();
          });
      });
    },
    //音频开关
    audioSwitch() {
      if (this.audioSwitchFlag) {
        this.audioSwitchFlag = false;
        this.audioSwitchText = "打开音频";
        this.localStream.muteAudio();
      } else {
        this.audioSwitchFlag = true;
        this.audioSwitchText = "关闭音频";
        this.localStream.unmuteAudio();
      }
    },
  },
};
</script>

<style >
.banner {
  padding: 0;
  background-color: #52575c;
  color: white;
}

.banner-text {
  padding: 8px 20px;
  margin: 0;
}


#join-form {
  margin-top: 10px;
}

.tips {
  font-size: 12px;
  margin-bottom: 2px;
  color: gray;
}

.join-info-text {
  margin-bottom: 2px;
}


.player {
  width: 100%;
  height: 100%;
}

.player-name {
  margin: 8px 0;
}

#success-alert, #success-alert-with-token {
  display: none;
}

/* @media (max-width: 640px) {
  .player {
    width: 320px;
    height: 240px;
  }
} */
</style>


<style lang="scss">
.video-group {
  padding: 20px;
  width: 100%;
  height: 430px;
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  background: url("../../assets/img/video_bg_banner.jpg") no-repeat;
  background-size: cover;
  #local-player , .remote_videobox {
    padding: 14px 24px 26px 26px;
    margin: 0 5px;
    width: 530px;
    height: 380px;
    background-color: #000000;
    background: url("../../assets/img/video_banner.png") no-repeat;
    background-size: 100%;
  }
}
.tools_box {
  width: 160px;
  height: 360px;
  display: flex;
  justify-content: space-around;
  justify-items: center;
  align-items: center;
  flex-wrap: wrap;
  border: 1px solid #eeeeee;
  background: url("../../assets/img/video_tool.png") no-repeat;
  background-color: #f2f2f2;
}
.timer {
  position: fixed;
  z-index: 1000000;
  top: 20px;
  right: 20px;
  width: 140px;
  height: 35px;
  border-radius: 50px;
  background-color: #dc143c;
  color: white;
  font-size: 16px;
  line-height: 35px;
  text-align: center;
}

</style>
