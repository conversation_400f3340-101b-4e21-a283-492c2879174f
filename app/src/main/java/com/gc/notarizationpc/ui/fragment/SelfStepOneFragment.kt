package com.gc.notarizationpc.ui.fragment

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.framwork.noHttp.Bean.BaseResponseBean
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseFragment
import com.gc.notarizationpc.model.NotrayBean
import com.gc.notarizationpc.model.OrderInfoEntity
import com.gc.notarizationpc.model.OrdersNotaryEntity
import com.gc.notarizationpc.ui.NotarizationSelfActivity
import com.gc.notarizationpc.ui.adapter.SelectedGridItemsAdapter
import com.gc.notarizationpc.ui.adapter.SelectedInnerGridItemsAdapter
import com.gc.notarizationpc.ui.presenter.NotarizaSelfPresenter
import com.gc.notarizationpc.widget.MyLayoutManager
import kotlinx.android.synthetic.main.fragment_self_step_one.*

/**
 * 自助公证申请
 */
class SelfStepOneFragment : BaseFragment(), NotarizaSelfPresenter.INotarizaSelfView {

    private var itemList = ArrayList<NotrayBean>()
    private var mAdapter: SelectedGridItemsAdapter? = null;
    private var mInnerAdapter: SelectedInnerGridItemsAdapter? = null;
    private var currentActivity: NotarizationSelfActivity? = null;
    private var notarizationP: NotarizaSelfPresenter? = null
    private var itemsEntity: ArrayList<NotrayBean>? = null;
    private var choseListC: ArrayList<NotrayBean.ChildrenDTO>? = ArrayList();
    private var TAG = "SelfStepOneFragment"

    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {
        Log.i(TAG, "initViewsAndEvents")
        currentActivity = mActivity as NotarizationSelfActivity?;
        notarizationP = NotarizaSelfPresenter(mActivity, this, true);
        notarizationP?.queryNotaryitemmodelSelectLetter(false);
        tvNextStep?.setOnClickListener {
            if (!checkNotarizationFun()) {
                toastInfo("请选择公证用途")
            } else if (choseListC?.size == 0) {
                toastInfo("请选择公证事项")
            } else {
                (mActivity as NotarizationSelfActivity?)?.choseList = choseListC;
                currentActivity?.onNavigationItemSelected(1)
            }
        };
    }

    //校验公证用途
    fun checkNotarizationFun(): Boolean {
        if (itemsEntity == null) {
            return false
        }
        for (tmp in itemsEntity!!) {
            if (tmp.isSelect) {
                return true
            }
        }
        return false
    }

    /**
     * GCSWRW5067
     * 迷你一体机修改自助公证页面
     */
    private fun initByRecyclerViewGrid() {
        Log.i(TAG, "initByRecyclerViewGrid")
        val layout = MyLayoutManager()
        layout.isAutoMeasureEnabled = true;//防止recyclerview高度为wrap时测量item高度0(一定要加这个属性，否则显示不出来）
        recyclerView.layoutManager = layout
        mAdapter = SelectedGridItemsAdapter(false, mActivity);
        recyclerView.adapter = mAdapter;
        mAdapter?.itemsList = itemsEntity as ArrayList<NotrayBean>?;
        mAdapter!!.mActivity = mActivity
        mAdapter!!.callBack = object : SelectedGridItemsAdapter.CallBack {
            override fun callBack(entity: NotrayBean, position: Int) {
                for (i in itemsEntity!!.indices) {
                    itemsEntity!![i].isSelect = i == position
                }
                mAdapter?.itemsList = itemsEntity
                mAdapter?.notifyDataSetChanged()
                initInnerByRecyclerViewGrid(itemsEntity!![position].children as ArrayList<NotrayBean.ChildrenDTO>?)
            }
        }
    }

    private fun initInnerByRecyclerViewGrid(childrenDTO: ArrayList<NotrayBean.ChildrenDTO>?) {
        Log.i(TAG, "initByRecyclerViewGrid")
        val layout = MyLayoutManager()
        layout.isAutoMeasureEnabled = true;//防止recyclerview高度为wrap时测量item高度0(一定要加这个属性，否则显示不出来）
        recyclerViewInner.layoutManager = layout
        mInnerAdapter = SelectedInnerGridItemsAdapter(false, mActivity);
        recyclerViewInner.adapter = mInnerAdapter;
        mInnerAdapter?.itemsList = childrenDTO as ArrayList<NotrayBean.ChildrenDTO>?;
        mInnerAdapter!!.mActivity = mActivity
        mInnerAdapter!!.callBack = object : SelectedInnerGridItemsAdapter.CallBack {
            override fun callBack(entity: NotrayBean.ChildrenDTO, position: Int) {
                childrenDTO!![position].isChecked = !childrenDTO!![position].isChecked
                if (childrenDTO!![position].isChecked) {
                    choseListC!!.add(childrenDTO!![position])
                } else {
                    choseListC!!.remove(childrenDTO!![position])
                }
                mInnerAdapter?.itemsList = childrenDTO
                mInnerAdapter!!.notifyDataSetChanged()
            }
        }
    }

    override fun sendNotarizationSuccess(data: List<NotrayBean>?) {
        Log.i(TAG, "sendNotarizationSuccess")
        itemsEntity = data as ArrayList<NotrayBean>?;
        initByRecyclerViewGrid();
    }

    override fun queryResultLangeuList(bean: BaseResponseBean?) {
        Log.i(TAG, "queryResultLangeuList")
    }

    override fun queryResultByOrderId(bean: OrdersNotaryEntity?) {
        Log.i(TAG, "queryResultByOrderId")
    }

    override fun queryResultBySetState(bean: OrderInfoEntity?) {
        Log.i(TAG, "queryResultBySetState")
    }

    override fun queryResultNotarList(bean: BaseResponseBean?) {
        Log.i(TAG, "queryResultNotarList")
    }

    override fun addResultSuccess(orderId: String?) {
        Log.i(TAG, "addResultSuccess")
    }

    override fun queryResultCountyList(bean: BaseResponseBean?) {
        Log.i(TAG, "queryResultCountyList")
    }


    override fun lazyInit(view: View?, savedInstanceState: Bundle?) {

    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.fragment_self_step_one;
    }
}
