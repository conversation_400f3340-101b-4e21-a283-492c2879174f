package com.gc.notarizationpc.ui.viewmodel;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.gc.notarizationpc.data.model.response.VideoOrderListModel;

import me.goldze.mvvmhabit.base.ItemViewModel;
import me.goldze.mvvmhabit.binding.command.BindingAction;
import me.goldze.mvvmhabit.binding.command.BindingCommand;

/**
 * Created by goldze on 2017/7/17.
 */

public class FragmentGridItemViewModel extends ItemViewModel<FragmentGridViewModel> {


    public ObservableField<VideoOrderListModel.DataListDTO> entity = new ObservableField<>();

    // 公证类型
   public String itemGridNotaryOrderNotaryTypeString ;

    // 订单状态
    public String itemGridNotaryOrderNotaryStatusString;

    // 订单状态颜色
    public Integer itemGridNotaryOrderNotaryStatusColor;

    // 订单状态字体颜色
    public Integer itemGridNotaryOrderNotaryStatusTextColor;

    // 阅读文书状态
    public Integer itemGridNotaryReadInstrumentStatus;

    // 补充文书状态
    public Integer itemGridNotarySupplyMaterialsStatus;

    //进入受理室状态
    public Integer itemGridNotaryEnterRoomStatus;




    public FragmentGridItemViewModel(@NonNull FragmentGridViewModel viewModel, VideoOrderListModel.DataListDTO entity) {
        super(viewModel);
        this.entity.set(entity);

        //ImageView的占位图片，可以解决RecyclerView中图片错误问题
//        drawableImg = ContextCompat.getDrawable(viewModel.getApplication(), R.mipmap.ic_launcher);
    }



    /**
     * 获取position的方式有很多种,indexOf是其中一种，常见的还有在Adapter中、ItemBinding.of回调里
     *
     * @return
     */
    public int getPosition() {
        return viewModel.getItemPosition(this);
    }

    public  int getCount(){
        return viewModel.getItemCount();
    }

    /**
     * 进入详情
     */
    public BindingCommand itemClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {

            Log.i("FragmentGridItemViewModel","FragmentGridItemViewModel itemClick");
            viewModel.itemClick(entity.get());
        }
    });

    /**
     * 补充材料
     */
    public BindingCommand applyMaterialClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.applyMaterial(entity.get());
        }
    });

    /**
     * 进入受理室
     */
    public BindingCommand enterRoomClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.enterRoom(entity.get());
        }
    });

    /**
     * 阅读文书
     */
    public BindingCommand readDocumentClick = new BindingCommand(new BindingAction() {
        @Override
        public void call() {
            viewModel.readDocument(entity.get());
        }
    });

}
