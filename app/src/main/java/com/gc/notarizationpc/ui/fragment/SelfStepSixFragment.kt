package com.gc.notarizationpc.ui.fragment

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import com.example.framwork.baseapp.AppManager
import com.example.framwork.noHttp.Bean.BaseResponseBean
import com.example.framwork.utils.ToastUtils
import com.gc.notarizationpc.R
import com.gc.notarizationpc.common.BaseFragment
import com.gc.notarizationpc.common.ResponseBean
import com.gc.notarizationpc.model.AliPayInfo
import com.gc.notarizationpc.model.NotaryEntity
import com.gc.notarizationpc.model.OrderInfoEntity
import com.gc.notarizationpc.model.PayBean
import com.gc.notarizationpc.ui.MainActivity
import com.gc.notarizationpc.ui.NotarizationSelfActivity
import com.gc.notarizationpc.ui.OrderSucessActivity
import com.gc.notarizationpc.ui.PayActivity
import com.gc.notarizationpc.ui.presenter.NotarizaSelfPresenter
import com.gc.notarizationpc.ui.presenter.NotarizationPresenter
import com.gc.notarizationpc.utils.BitErCodeUtils
import kotlinx.android.synthetic.main.fragment_self_step_six.*
import java.math.BigDecimal
import java.util.*
import kotlin.concurrent.fixedRateTimer

class SelfStepSixFragment : BaseFragment(), NotarizaSelfPresenter.INotarizaSelfSecond, NotarizaSelfPresenter.INotarizaSelf {

    private var currentActivity: NotarizationSelfActivity? = null
    private var noelfPresenter: NotarizaSelfPresenter? = null
    private var timer: Timer? = null
    private var timer2: Timer? = null
    private var payTypeCur: Int? = 1
    private var TAG = "SelfStepSixFragment"
    var isShowOnlinePay: Boolean = true

    override fun initViewsAndEvents(view: View?, savedInstanceState: Bundle?) {
        Log.i(TAG, "initViewsAndEvents")
        currentActivity = mActivity as NotarizationSelfActivity?
        noelfPresenter = NotarizaSelfPresenter(mActivity, this, AliPayInfo())
        noelfPresenter?.getPayConfig(notaryId, this)
        priceNumber.text = currentActivity?.lastOrderInfo?.fee;
//        if (isShowOnlinePay) {
//            onlinePay.visibility = View.VISIBLE;
//        } else {
//            onlinePay.visibility = View.GONE;
//        }
        tvNextStep?.setOnClickListener {
            //线下支付
            // 自助办证的时候，一体机这里需要判断金额为0点击“线上支付”或“线下支付”，直接完成支付，无需跳转。
//            if (currentActivity?.lastOrderInfo?.fee!!.toBigDecimal().compareTo(BigDecimal.ZERO) == 0) {
            ToastUtils.getInstance(mActivity).toastSuccess("订单支付完成")
            AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
//            } else {
//                noelfPresenter = NotarizaSelfPresenter(mActivity, this, "sss");
//                noelfPresenter?.addNotaryDoSetState(currentActivity?.lastOrderInfo?.unitGuid, "30", "pt006")
//            }
        };
        onlinePay?.setOnClickListener {
//            if (currentActivity?.orderStatu == 1) {
//                noelfPresenter = NotarizaSelfPresenter(mActivity, this, "sss");
//                noelfPresenter?.OnlineDoSetState(currentActivity?.lastOrderInfo?.unitGuid,
//                        "15", "pt002", object : NotarizaSelfPresenter.DosetStatus {
//                    override fun queryResultBySetStateSecond(bean: OrderInfoEntity?, notaryState: String?) {
//                        val intent = Intent(mActivity, PayActivity::class.java)
//                        intent.putExtra("unitGuid", currentActivity?.lastOrderInfo?.unitGuid)
//                        intent.putExtra("money", currentActivity?.lastOrderInfo?.fee)
//                        startActivity(intent)
//                    }
//
//                    override fun failed(msg: String?) {
//                        mActivity.runOnUiThread {
//                            ToastUtils.getInstance(mActivity).toastError("订单生成失败，请重试。")
//                        }
//                    }
//                })
//
//            } else {

            //自助办证的时候，一体机这里需要判断金额为0点击“线上支付”或“线下支付”，直接完成支付，无需跳转。
            if (currentActivity?.lastOrderInfo?.fee!!.toBigDecimal().compareTo(BigDecimal.ZERO) == 0) {
                ToastUtils.getInstance(mActivity).toastSuccess("订单支付完成")
                AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
            } else {
                NotarizationPresenter(mActivity).queyCcbisOrderInfo(currentActivity?.lastOrderInfo?.orderNo, object : NotarizationPresenter.OrderCommonView {
                    override fun orderCommonSuccess(bean: PayBean) {
                        if (bean == null || TextUtils.isEmpty(bean.payStatus)) {
                            return
                        }
                        if (TextUtils.equals(bean.payStatus, "1")) {
                            //支付成功
                            ToastUtils.getInstance(mActivity).toastError("该笔订单已支付，请勿重复下单");
                            activity?.finish()
                            readyGo(Intent(activity, OrderSucessActivity::class.java))
                            return
                        } else {
                            gotoPay()
                        }

                    }

                    override fun orderCommonFail(code: String) {
                        gotoPay()
                    }
                })

            }

//            }
        }


    }

    fun gotoPay() {
        val intent = Intent(mActivity, PayActivity::class.java)
        intent.putExtra("intoType", 1);//1自助公证进入 2视频公证进入
        Log.e("Test", "订单id===========" + currentActivity?.lastOrderInfo?.unitGuid);
        intent.putExtra("unitGuid", currentActivity?.lastOrderInfo?.unitGuid)
        intent.putExtra("money", currentActivity?.lastOrderInfo?.fee)
        intent.putExtra("orderNo", currentActivity?.lastOrderInfo?.orderNo)
        startActivity(intent)
    }

    override fun lazyInit(view: View?, savedInstanceState: Bundle?) {


    }

    override fun getContentViewLayoutID(): Int {
        return R.layout.fragment_self_step_six
    }

    override fun queryResultselectNotaryItem(bean: BaseResponseBean?) {

    }

    override fun queryWxpayGetpay(bean: ResponseBean?) {
        Log.i(TAG, "queryWxpayGetpay")
        var httpWx = bean?.msg
        payQrCode.setImageBitmap(BitErCodeUtils.createQRImage(httpWx));
        if (payTypeCur == 2) {
            stopTimer()
            startTimer()
        }
    }

    override fun addMaterialSuccess(arrayList: MutableList<String>?) {
        Log.i(TAG, "addMaterialSuccess")
    }

    override fun addNotaryDoSetSignName(bean: OrderInfoEntity?) {
        Log.i(TAG, "addNotaryDoSetSignName")
    }

    override fun queryResultBySetStateSecond(bean: OrderInfoEntity?, notaryState: String) {
        Log.i(TAG, "queryResultBySetStateSecond")
        if (notaryState == "91") {
            mActivity.runOnUiThread {
                ToastUtils.getInstance(mActivity).toastError("支付超时,此笔订单结束")
                AppManager.getAppManager().finishAllActivity(MainActivity::class.java)
            }
        } else {
            readyGo(OrderSucessActivity::class.java)
        }
    }

    override fun saveFaceReportSuccess(bean: BaseResponseBean?) {
    }

    override fun saveFaceReportFail(error: String?) {
    }

    override fun addalipaySetQrCode(bean: AliPayInfo?) {
        Log.i(TAG, "addalipaySetQrCode")
        var httpAlip = bean?.msg;
        payQrCode.setImageBitmap(BitErCodeUtils.createQRImage(httpAlip));
        if (payTypeCur == 1) {
            stopTimer()
            startTimer()
        }
    }

    override fun addalipaySetQrCodeReslut(bean: AliPayInfo?) {
        Log.i(TAG, "addalipaySetQrCodeReslut")
        noelfPresenter = NotarizaSelfPresenter(mActivity, this, "sss")
        if (null == bean?.tradeStatus) {
            Log.i(TAG, "alipay 未支付")
        } else if (bean.tradeStatus.equals("TRADE_SUCCESS")) {
            stopTimer()
            readyGo(OrderSucessActivity::class.java)
//            noelfPresenter?.addNotaryDoSetState(currentActivity?.lastOrderInfo?.unitGuid, "30", "pt001")
        }
    }

    override fun queryWxpayGetpayReslut(bean: AliPayInfo?) {
        Log.i(TAG, "queryWxpayGetpayReslut")
        noelfPresenter = NotarizaSelfPresenter(mActivity, this, "sss");
        if (bean?.tradeStatus.equals("SUCCESS")) {
            stopTimer();
            readyGo(OrderSucessActivity::class.java)
//            noelfPresenter?.addNotaryDoSetState(currentActivity?.lastOrderInfo?.unitGuid, "30", "pt002");
        } else if (bean?.tradeStatus.equals("NOTPAY")) {
            Log.i(TAG, "wxipay 未支付")
        }
    }

    private fun startTimer() {
        Log.i(TAG, "startTimer")
        timer = fixedRateTimer("stopQuery", false, 5000, 5000) {
            atTimerToApi();
        }
        timer2 = fixedRateTimer("stopQuery", false, 65000, 500000) {
//            orderFail();
        }
    }

    private fun orderFail() {
        stopTimer()
        noelfPresenter = NotarizaSelfPresenter(mActivity, this, OrderInfoEntity());
        if (payTypeCur == 1) {
            noelfPresenter?.addNotaryDoSetState(currentActivity?.lastOrderInfo?.unitGuid, "91", "pt001");
        } else if (payTypeCur == 2) {
            noelfPresenter?.addNotaryDoSetState(currentActivity?.lastOrderInfo?.unitGuid, "91", "pt002");
        } else {
            return
        }
    }

    override fun onPause() {
        super.onPause()
        stopTimer()
    }

    override fun onDestroy() {
        super.onDestroy()
        stopTimer()
    }

    private fun stopTimer() {
        Log.i(TAG, "stopTimer")
        mActivity.runOnUiThread {
            if (timer != null) {
                timer!!.cancel()
                timer!!.purge()
                timer = null
            }
            if (timer2 != null) {
                timer2!!.cancel()
                timer2!!.purge()
                timer2 = null
            }
        }
    }

    private fun atTimerToApi() {
        Log.i(TAG, "atTimerToApi")
        mActivity.runOnUiThread {
            noelfPresenter = NotarizaSelfPresenter(mActivity, this, AliPayInfo())
            if (payTypeCur == 1) {
                noelfPresenter?.alipayQuery(currentActivity?.lastOrderInfo?.orderNo)
            } else if (payTypeCur == 2) {
                noelfPresenter?.wxpayOrderQuery(currentActivity?.lastOrderInfo?.orderNo)
            } else {
            }
        }
    }

    override fun queryNotaryEntitySuccess(bean: NotaryEntity?) {
        TODO("Not yet implemented")
    }

    override fun getPayConfigViewSuccess(msg: String?) {
        onlinePay.visibility = View.VISIBLE;
    }

    override fun getPayConfigViewFail(msg: String?) {
        onlinePay.visibility = View.GONE;
    }

}