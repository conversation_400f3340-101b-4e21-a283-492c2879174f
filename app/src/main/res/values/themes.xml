<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
<!--    <style name="Theme.MainApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">-->
<!--        &lt;!&ndash; Primary brand color. &ndash;&gt;-->
<!--        <item name="colorPrimary">@color/purple_500</item>-->
<!--        <item name="colorPrimaryVariant">@color/purple_700</item>-->
<!--        <item name="colorOnPrimary">@color/white</item>-->
<!--        &lt;!&ndash; Secondary brand color. &ndash;&gt;-->
<!--        <item name="colorSecondary">@color/teal_200</item>-->
<!--        <item name="colorSecondaryVariant">@color/teal_700</item>-->
<!--        <item name="colorOnSecondary">@color/black</item>-->
<!--        &lt;!&ndash; Status bar color. &ndash;&gt;-->
<!--        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>-->
<!--        &lt;!&ndash; Customize your theme here. &ndash;&gt;-->
<!--    </style>-->
</resources>