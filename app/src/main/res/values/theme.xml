<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="tv_white_wrap">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/font_size_14</item>
    </style>

    <style name="v_level">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingLeft">@dimen/space_5</item>
        <item name="android:paddingRight">@dimen/space_5</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="tv_white_match_16">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/font_size_16</item>
    </style>

    <style name="tv_black_wrap_16">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_color</item>
        <item name="android:textSize">@dimen/font_size_16</item>
    </style>

    <style name="tv_black_wrap">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_color</item>
        <item name="android:textSize">@dimen/font_size_14</item>
    </style>

    <style name="tv_gray_match">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/gray_9b</item>
        <item name="android:textSize">@dimen/font_size_14</item>
    </style>

    <style name="tv_gray_wrap">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/gray_9b</item>
        <item name="android:textSize">@dimen/font_size_14</item>
    </style>

    <style name="tv_gray_wrap_13">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/gray_9b</item>
        <item name="android:textSize">@dimen/font_size_13</item>
    </style>

    <style name="tv_black_match_bwhite">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_color</item>
        <item name="android:textSize">@dimen/font_size_14</item>
        <item name="background">@color/white</item>
    </style>

    <style name="ll_match_parent_horizontal">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="background">@color/white</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="tv_default_match_next">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/font_size_14</item>
        <item name="android:textColor">@color/text_color</item>
        <item name="android:drawableRight">@drawable/ic_next</item>
        <item name="android:drawablePadding">@dimen/space_10</item>
        <item name="background">@color/white</item>
    </style>

    <style name="tv_right">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/font_size_14</item>
        <item name="android:textColorHint">@color/gray_98</item>
        <item name="android:textColor">@color/gray_98</item>
        <item name="android:gravity">right</item>
        <item name="android:drawableRight">@drawable/ic_next</item>
        <item name="android:drawablePadding">@dimen/space_10</item>
        <item name="android:layout_marginLeft">@dimen/space_10</item>
        <item name="background">@color/white</item>
    </style>

    <style name="ll_match_parent_horizontal_10_15">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="background">@color/white</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:paddingTop">@dimen/space_20</item>
        <item name="android:paddingBottom">@dimen/space_20</item>
        <item name="android:paddingLeft">@dimen/space_15</item>
        <item name="android:paddingRight">@dimen/space_15</item>
    </style>

    <style name="et_match_default">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/font_size_14</item>
        <item name="android:textColorHint">@color/text_hint_color</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:drawablePadding">@dimen/space_10</item>
        <item name="android:maxLines">1</item>
        <item name="android:background">@null</item>
    </style>

    <style name="tv_black_wrap_18">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">@dimen/font_size_18</item>
    </style>

    <style name="tv_black_wrap_2_18">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/select</item>
        <item name="android:textSize">@dimen/font_size_18</item>
    </style>
</resources>