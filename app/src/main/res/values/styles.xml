<resources>

    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/white</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="android:windowBackground">@color/white</item>
    </style>

    <style name="SelectedMoreDialogStyles" parent="android:style/Theme.Dialog">
        <!--背景颜色及和透明程度-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--是否去除标题 -->
        <item name="android:windowNoTitle">true</item>
        <!--是否去除边框-->
        <item name="android:windowFrame">@null</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsFloating">true</item>
        <!--是否模糊-->
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="edit_spinner_edit_sty">
        <item name="android:background">@null</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:textSize">14sp</item>
    </style>

    <!--  DatePicker   -->
    <style name="date_picker_dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@color/white</item>
    </style>

    <style name="Dialog_Fullscreen" parent="@android:style/Theme.Dialog">

        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 背景透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <declare-styleable name="FlowTextStyle">
        <attr name="horizontalSpace" format="dimension" />
        <attr name="verticalSpace" format="dimension" />
    </declare-styleable>

    <style name="dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
    </style>
</resources>
