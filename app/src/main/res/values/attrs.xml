<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="TwinklingRefreshLayout">
        <attr name="tr_wave_height" format="dimension" />
        <attr name="tr_head_height" format="dimension" />
        <attr name="tr_bottom_height" format="dimension" />
        <attr name="tr_overscroll_height" format="dimension" />
        <attr name="tr_enable_loadmore" format="boolean" />
        <attr name="tr_pureScrollMode_on" format="boolean" />
        <attr name="tr_show_overlay_refreshview" format="boolean" />
        <attr name="tr_headerView" format="dimension" />
        <attr name="tr_bottomView" format="dimension" />
        <attr name="onRefreshCommand" format="reference" />
        <attr name="onLoadMoreCommand" format="reference" />
    </declare-styleable>

    <declare-styleable name="MaterialProgress">
        <attr name="mp_startColor" format="color" />
        <attr name="mp_midColor" format="color" />
        <attr name="mp_endColor" format="color" />
        <attr name="mp_radius" format="dimension" />
        <attr name="mp_strokeWidth" format="dimension" />
        <attr name="mp_speed" format="float" />
    </declare-styleable>

    <declare-styleable name="AppCompatEditSpinner">
        <!--AppCompatEditSpinner内EditText的相关属性-->
        <!--文本-->
        <attr name="text" format="reference|string" />
        <!--提示文本-->
        <attr name="hint" format="reference|string" />
        <!--提示文本的颜色-->
        <attr name="hintTextColor" format="reference|color" />
        <!--背景-->
        <attr name="editBackground" format="reference|color" />
        <!--字体大小，单位sp，dp自动转为sp、px不支持-->
        <attr name="editTextSize" format="reference|dimension" />
        <!--字体颜色-->
        <attr name="editTextColor" format="reference|color" />
        <!--最大输入行数-->
        <attr name="editMaxLines" format="integer" />
        <!--最大输入字符数-->
        <attr name="editMaxLength" format="integer" />
        <!--输入字符限制-->
        <attr name="editDigits" format="reference|string" />
        <!--输入字符类型-->
        <attr name="editInputType">
            <flag name="none" value="0x00000000" />
            <flag name="text" value="0x00000001" />
            <flag name="textCapCharacters" value="0x00001001" />
            <flag name="textCapWords" value="0x00002001" />
            <flag name="textCapSentences" value="0x00004001" />
            <flag name="textAutoCorrect" value="0x00008001" />
            <flag name="textAutoComplete" value="0x00010001" />
            <flag name="textMultiLine" value="0x00020001" />
            <flag name="textImeMultiLine" value="0x00040001" />
            <flag name="textNoSuggestions" value="0x00080001" />
            <flag name="textUri" value="0x00000011" />
            <flag name="textEmailAddress" value="0x00000021" />
            <flag name="textEmailSubject" value="0x00000031" />
            <flag name="textShortMessage" value="0x00000041" />
            <flag name="textLongMessage" value="0x00000051" />
            <flag name="textPersonName" value="0x00000061" />
            <flag name="textPostalAddress" value="0x00000071" />
            <flag name="textPassword" value="0x00000081" />
            <flag name="textVisiblePassword" value="0x00000091" />
            <flag name="textWebEditText" value="0x000000a1" />
            <flag name="textFilter" value="0x000000b1" />
            <flag name="textPhonetic" value="0x000000c1" />
            <flag name="textWebEmailAddress" value="0x000000d1" />
            <flag name="textWebPassword" value="0x000000e1" />
            <flag name="number" value="0x00000002" />
            <flag name="numberSigned" value="0x00001002" />
            <flag name="numberDecimal" value="0x00002002" />
            <flag name="numberPassword" value="0x00000012" />
            <flag name="phone" value="0x00000003" />
            <flag name="datetime" value="0x00000004" />
            <flag name="date" value="0x00000014" />
            <flag name="time" value="0x00000024" />
        </attr>

        <!--AppCompatEditSpinner右侧ImageView的相关属性-->
        <!--图片-->
        <attr name="rightImage" format="reference|color" />
        <!--是否隐藏下拉选-->
        <attr name="rightImageGone" format="boolean" />
        <!--右侧图片展开下拉选时是否显示全部数据，默认：点击时显示和当前输入匹配的数据-->
        <attr name="rightImageDropShowAllItem" format="boolean" />

        <!--AppCompatEditSpinner的ListPopupWindow布局的相关属性-->
        <!--待选项的背景，drawable或color资源-->
        <attr name="spinnerBackground"  format="reference|color" />

        <!--AppCompatEditSpinner的SimpleAdapter布局的相关属性-->
        <!--待选项的字体大小，单位sp，dp自动转为sp、px不支持-->
        <attr name="spinnerItemTextSize" format="reference|dimension" />
        <!--待选项的颜色，默认黑色-->
        <attr name="spinnerItemTextColor" format="reference|color" />
        <!--匹配字符的颜色，默认无颜色-->
        <attr name="spinnerItemMatchTextColor" format="reference|color" />
        <!--匹配字符时是否忽略字母大小写，默认不忽略。忽略时“spinnerItemTextColor”属性无效-->
        <attr name="spinnerItemMatchIgnoreCase" format="boolean" />
        <attr name="anchorView"  format="reference|integer" />
    </declare-styleable>
</resources>