<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_notary_item"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_5"
            android:layout_weight="3"
            android:gravity="left"
            android:padding="@dimen/space_8"
            android:text="@string/gz_fee"
            android:textColor="@color/color6666"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/tv_item"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_5"
            android:layout_weight="2"
            android:gravity="center"
            android:paddingHorizontal="@dimen/space_15"
            android:paddingVertical="@dimen/space_8"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14"
            tools:text="200元" />

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_5"
            android:layout_weight="1.5"
            android:gravity="center"
            android:paddingHorizontal="@dimen/space_15"
            android:paddingVertical="@dimen/space_8"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14"
            tools:text="200元" />
    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E8E8EB"
        android:layout_marginHorizontal="@dimen/space_15"/>

</LinearLayout>