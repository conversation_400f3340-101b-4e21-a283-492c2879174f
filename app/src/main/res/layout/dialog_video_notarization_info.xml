<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_corner8_white_left"
    android:paddingBottom="@dimen/space_8"
    android:orientation="vertical"
    android:padding="@dimen/space_20">
    <RelativeLayout
        android:id="@+id/rnl_search_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title_bar"
        android:padding="@dimen/space_15">

        <TextView
            android:id="@+id/tv_location"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:drawableLeft="@mipmap/icon_apply_title"
            android:drawablePadding="@dimen/space_5"
            android:text="@string/apply_info"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size18"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_refresh"
            android:layout_width="@dimen/space_40"
            android:layout_height="@dimen/space_40"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/space_40"
            android:layout_toLeftOf="@+id/iv_close"
            android:padding="@dimen/space_10"
            android:src="@mipmap/icon_refresh" />


        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/space_40"
            android:layout_height="@dimen/space_40"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/space_10"
            android:src="@mipmap/icon_close" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E8E8EB"
        android:layout_marginHorizontal="@dimen/space_10"/>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/space_20"
        android:paddingRight="@dimen/space_30">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:gravity="right"
                    android:padding="@dimen/space_5"
                    android:text="@string/apply_date"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_apply_date"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:layout_weight="4"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:paddingVertical="@dimen/space_8"
                    android:paddingHorizontal="@dimen/space_15"
                    android:textColor="#BFBFBF"
                    android:text="2024-03-20"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="@dimen/space_8">

                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:gravity="right"
                    android:padding="@dimen/space_5"
                    android:text="@string/apply_item"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_apply_item"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:layout_weight="4"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:paddingVertical="@dimen/space_8"
                    android:paddingHorizontal="@dimen/space_15"
                    android:textColor="#BFBFBF"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="@dimen/space_8">

                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:gravity="right"
                    android:padding="@dimen/space_5"
                    android:text="@string/apply_cert"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_apply_cert"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:layout_weight="4"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:paddingVertical="@dimen/space_8"
                    android:paddingHorizontal="@dimen/space_15"
                    android:textColor="#BFBFBF"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="@dimen/space_8">

                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:gravity="right"
                    android:padding="@dimen/space_5"
                    android:text="@string/apply_address"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_apply_address"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:layout_weight="4"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:paddingVertical="@dimen/space_8"
                    android:paddingHorizontal="@dimen/space_15"
                    android:textColor="#BFBFBF"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="@dimen/space_8">

                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:gravity="right"
                    android:padding="@dimen/space_5"
                    android:text="@string/apply_lan"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_apply_lan"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:layout_weight="4"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:paddingVertical="@dimen/space_8"
                    android:paddingHorizontal="@dimen/space_15"
                    android:textColor="#BFBFBF"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_15">

                <View
                    android:layout_width="4dp"
                    android:layout_height="12dp"
                    android:background="@drawable/shape_corner2_gradient"
                    android:layout_centerVertical="true"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_10"
                    android:text="@string/applicatant_info"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size14"
                    android:textStyle="bold" />
            </RelativeLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_apply"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_10"/>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_15">

                <View
                    android:layout_width="4dp"
                    android:layout_height="12dp"
                    android:background="@drawable/shape_corner2_gradient"
                    android:layout_centerVertical="true"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_10"
                    android:text="@string/proxy_info"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size14"
                    android:textStyle="bold" />
            </RelativeLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_proxy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_10"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</LinearLayout>