<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/dialog_scan_idcard"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_gravity="center"
    android:visibility="visible">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/shape_corner10_white_all"
        android:layout_marginHorizontal="@dimen/space_150"
        android:layout_marginTop="@dimen/space_70"
        android:layout_marginBottom="@dimen/space_90"
        android:layout_gravity="center">

        <TextView
            android:id="@+id/readIdCardInformationTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/read_idcard_icon"
            android:layout_centerHorizontal="true"
            android:drawableTop="@mipmap/read_idcard_icon"
            android:gravity="center"
            android:layout_marginTop="@dimen/space_15"
            android:text="@string/readIdCardInformationTitle"
            android:singleLine="true"
            android:drawablePadding="@dimen/space_26"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:textSize="@dimen/font_size24" />
        <TextView
            android:id="@+id/connectionTimeOut"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/space_15"
            android:text="@string/readIdCardInformationSubTitle"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="@dimen/font_size16" />

    </LinearLayout>
    <ImageView
        android:id="@+id/image_close"
        android:layout_width="@dimen/space_40"
        android:layout_height="@dimen/space_40"
        android:scaleType="fitXY"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/space_20"
        android:layout_marginRight="@dimen/space_20"
        android:src="@mipmap/close_grey"/>

</RelativeLayout>
