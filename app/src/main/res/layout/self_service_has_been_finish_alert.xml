<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/connectionVideoAlert"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:visibility="visible">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_corner10_white_all"
        android:gravity="center"
        android:orientation="vertical">
        <ImageView
            android:id="@+id/qr_code"
            android:layout_marginTop="@dimen/space_70"
            android:layout_width="@dimen/space_170"
            android:layout_height="@dimen/space_170"
            android:padding="@dimen/space_15"/>
        <TextView
            android:layout_marginTop="@dimen/space_15"
            android:layout_marginBottom="@dimen/space_30"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/alertToScanQRCode"
            android:textSize="@dimen/font_size16"
            android:textColor="@color/color3333"/>
        <Button
            android:id="@+id/back_to_home"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_corner5_3c6af4"
            android:layout_marginTop="@dimen/space_15"
            android:layout_marginBottom="@dimen/space_60"
            android:onClick="CloseTransaction"
            android:paddingHorizontal="@dimen/space_54"
            android:paddingVertical="@dimen/space_17"
            android:text="@string/sureText"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/font_size16" />
    </LinearLayout>

    <TextView
        android:id="@+id/time_decrease_text"
        android:layout_marginTop="@dimen/space_30"
        android:layout_marginRight="@dimen/space_30"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_corner5_f1f3f6_all"
        android:drawableLeft="@mipmap/clock_icon"
        android:paddingHorizontal="@dimen/space_10"
        android:drawablePadding="@dimen/space_21"
        android:textColor="@color/color2C98F0"
        android:paddingVertical="@dimen/space_7"
        android:textSize="@dimen/font_size16"/>

</RelativeLayout>
