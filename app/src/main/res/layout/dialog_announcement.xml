<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/space_80"
    android:layout_marginRight="@dimen/space_80"
    android:background="@drawable/shape_corner10_white_all"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/space_20">

    <TextView
        android:id="@+id/dialog_title"
        android:textColor="@color/color3333"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:text="@string/notarization_desc"
        android:textSize="@dimen/font_size18"
        android:textStyle="bold"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <TextView
            android:id="@+id/dialog_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_20"
            android:layout_marginBottom="@dimen/space_20"
            android:lineSpacingExtra="@dimen/space_5"
            android:text="@string/shenban_desc"
            android:textSize="@dimen/font_size16" />
    </ScrollView>


    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_ok"
        android:layout_width="140dp"
        android:layout_height="@dimen/space_50"
        android:layout_marginTop="@dimen/space_30"
        android:background="@drawable/shape_corner5_3c6af4"
        android:text="@string/i_know"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size16" />


</LinearLayout>