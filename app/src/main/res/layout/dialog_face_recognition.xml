<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/connectionVideoAlert"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/shape_corner10_white_all"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="960dp"
        android:layout_height="600dp"
        android:background="@drawable/shape_corner5_37393d">

        <View
            android:layout_width="500dp"
            android:layout_height="500dp"
            android:background="@drawable/shape_corner6_97bbf7"
            android:layout_centerInParent="true"/>

        <TextureView
            android:id="@+id/previewView"
            android:layout_width="480dp"
            android:layout_height="480dp"
            android:layout_centerInParent="true" />
    </RelativeLayout>


    <TextView
        android:id="@+id/connectionTimeOut"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/connecting_text"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/space_30"
        android:text="@string/face_recognition_tip"
        android:textColor="@color/color3333"
        android:textSize="@dimen/font_size16" />


</LinearLayout>
