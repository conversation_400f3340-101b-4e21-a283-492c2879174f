<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rnl_content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/space_10"
    android:background="@drawable/bg_select_notary"
    android:padding="@dimen/space_10">

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginRight="@dimen/space_5"
        android:drawableLeft="@drawable/bg_select_notary_icon"
        android:drawablePadding="@dimen/space_8"
        android:paddingLeft="@dimen/space_15"
        android:paddingTop="@dimen/space_9"
        android:paddingRight="@dimen/space_15"
        android:paddingBottom="@dimen/space_9"
        android:text="南京石城公证处"
        android:textColor="@drawable/bg_white_and_3333"
        android:textSize="@dimen/font_size16"
        android:textStyle="bold"
        />

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_name"
        android:layout_centerHorizontal="true"
        android:layout_alignParentLeft="true"
        android:text="时段：09:00-12:00  14:00-17:00"
        android:layout_marginLeft="@dimen/space_45"
        android:textColor="@drawable/bg_white_and_6666"
        android:textSize="@dimen/font_size14"
        />

    <TextView
        android:id="@+id/tv_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_time"
        android:layout_alignLeft="@+id/tv_time"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="@dimen/space_45"
        android:text="地址：南京市鼓楼区紫峰大厦15楼"
        android:textColor="@drawable/bg_white_and_6666"
        android:textSize="@dimen/font_size14" />
</RelativeLayout>
