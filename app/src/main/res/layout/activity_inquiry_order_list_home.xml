<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    >

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.InquiryOrderListHomeViewModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />

    </data>


    <LinearLayout
        android:id="@+id/inquiry_order_list_home"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_margin="@dimen/space_30"
            android:background="@drawable/shape_corner10_white_all">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/space_64"
                    android:layout_marginLeft="@dimen/space_40"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">
                    <Button
                        android:id="@+id/inquiry_order_list_notary_order_tab"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/space_60"
                        android:background="@color/color00000000"
                        android:textSize="@dimen/font_size16"
                        android:textColor="@color/colorPrimary"
                        binding:onClickCommand="@{viewModel.notaryOrderClick}"
                        binding:isThrottleFirst="@{Boolean.FALSE}"
                        android:textStyle="bold"
                        android:text="@string/notaryOrder"/>
                     <TextView
                         android:id="@+id/inquiry_order_list_notary_order_line"
                         android:layout_width="@dimen/space_70"
                         android:layout_height="@dimen/space_4"
                         android:gravity="center_horizontal"
                         android:background="@drawable/shape_corner2_3c6af4"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/space_64"
                    android:orientation="vertical"
                    android:gravity="center">
                    <Button
                        android:id="@+id/inquiry_order_list_self_service_tab"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/space_60"
                        android:background="@color/color00000000"
                        android:textSize="@dimen/font_size16"
                        binding:onClickCommand="@{viewModel.selfServiceOrderClick}"
                        android:textColor="@color/color3333"
                        android:text="@string/selfServiceOrder"/>
                    <TextView
                        android:id="@+id/inquiry_order_list_self_service_line"
                        android:layout_width="@dimen/space_70"
                        android:layout_height="@dimen/space_4"
                        android:gravity="center_horizontal"
                        android:visibility="gone"
                        android:background="@drawable/shape_corner2_3c6af4"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/space_64"
                    android:orientation="vertical"
                    android:gravity="center">
                    <Button
                        android:id="@+id/inquiry_order_list_my_appointment_tab"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/space_60"
                        android:background="@color/color00000000"
                        android:textSize="@dimen/font_size16"
                        android:textColor="@color/color3333"
                        binding:onClickCommand="@{viewModel.myReservationClick}"
                        android:text="@string/myAppointment"/>
                    <TextView
                        android:id="@+id/inquiry_order_list_my_appointment_line"
                        android:layout_width="@dimen/space_70"
                        android:layout_height="@dimen/space_4"
                        android:gravity="center_horizontal"
                        android:visibility="gone"
                        android:background="@drawable/shape_corner2_3c6af4"/>
                </LinearLayout>

            </LinearLayout>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_2"
                android:background="@color/colorE8E8EB"/>
            <FrameLayout
                android:id="@+id/inquiry_order_list_home_fragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>

        </LinearLayout>


    </LinearLayout>
</layout>