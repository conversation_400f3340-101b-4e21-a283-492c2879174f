<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">
        <data>
            <import type="com.gc.notarizationpc.ui.viewmodel.FragmentDomesticEconomyReadAndSignViewModel" />
            <variable
                name="viewModel"
                type="com.gc.notarizationpc.ui.viewmodel.FragmentDomesticEconomyReadAndSignViewModel" />

            <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

            <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />
        </data>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.gc.notarizationpc.widget.DomesticEconomyStepBar
                android:id="@+id/domestic_economy_step_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <LinearLayout
                android:id="@+id/fragment_gridview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/shape_corner5_e8e8e8"
                android:layout_margin="@dimen/space_30"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="14"
                    android:background="#DCDDE0">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/viewPager"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:visibility="gone"
                        binding:itemBinding="@{viewModel.documentItemBinding}"
                        binding:items="@{viewModel.documentPreViewList}"
                        binding:layoutManager="@{LayoutManagers.linear()}"
                        binding:lineManager="@{LineManagers.vertical()}" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_weight="9"
                    android:background="@drawable/shape_corner5_white"
                    android:layout_height="match_parent">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:padding="@dimen/space_30">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tv_location"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:drawableLeft="@mipmap/icon_alert_blue_cirle"
                                android:drawablePadding="@dimen/space_10"
                                android:text="@string/readAndAgreeToSign"
                                android:textColor="@color/color3333"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <!--                    <ImageView-->
                            <!--                        android:id="@+id/iv_refresh"-->
                            <!--                        android:layout_width="@dimen/space_40"-->
                            <!--                        android:layout_height="@dimen/space_40"-->
                            <!--                        android:layout_centerVertical="true"-->
                            <!--                        android:layout_marginRight="@dimen/space_40"-->
                            <!--                        android:layout_toLeftOf="@+id/iv_close"-->
                            <!--                        android:padding="@dimen/space_10"-->
                            <!--                        android:src="@mipmap/icon_refresh" />-->


                            <!--                    <ImageView-->
                            <!--                        android:id="@+id/iv_close"-->
                            <!--                        android:layout_width="@dimen/space_40"-->
                            <!--                        android:layout_height="@dimen/space_40"-->
                            <!--                        android:layout_alignParentRight="true"-->
                            <!--                        android:layout_centerVertical="true"-->
                            <!--                        android:padding="@dimen/space_10"-->
                            <!--                        android:src="@mipmap/icon_close" />-->
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginVertical="@dimen/space_20"
                            android:background="#E8E8EB" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_parent"
                            android:layout_marginBottom="@dimen/space_90"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            binding:layoutManager="@{LayoutManagers.grid(3)}"
                            binding:itemBinding="@{viewModel.itemBinding}"
                            binding:items="@{viewModel.documentList}"/>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_alignParentBottom="true"
                        android:layout_marginBottom="@dimen/space_30"
                        android:gravity="center">
                        <TextView
                            android:id="@+id/tv_first_text"
                            android:layout_width="@dimen/space_185"
                            android:layout_height="@dimen/space_50"
                            android:background="@drawable/shape_corner5_e8e8e8"
                            android:text="@string/lastStep"
                            android:gravity="center"
                            android:textColor="@color/color3333"
                            binding:onClickCommand="@{viewModel.firstButtonClick}"
                            binding:isThrottleFirst="@{Boolean.FALSE}"
                            android:textSize="@dimen/font_size16" />
                        <TextView
                            android:id="@+id/tv_second"
                            android:layout_marginLeft="@dimen/space_10"
                            android:layout_width="@dimen/space_185"
                            android:layout_height="@dimen/space_50"
                            android:background="@drawable/shape_corner5_3c6af4"
                            android:gravity="center"
                            android:text="@string/complete_read"
                            android:textColor="@color/white"
                            binding:onClickCommand="@{viewModel.secondButtonClick}"
                            binding:isThrottleFirst="@{Boolean.FALSE}"
                            android:textSize="@dimen/font_size16" />
                    </LinearLayout>
                </RelativeLayout>



            </LinearLayout>
        </LinearLayout>
</layout>