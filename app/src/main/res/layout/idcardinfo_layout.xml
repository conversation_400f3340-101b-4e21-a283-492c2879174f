<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:gravity="top|center_vertical"
        >
        <TextView
            android:id="@+id/textName"
            android:layout_width="@dimen/space_100"
            android:layout_height="30dp"
            android:text="姓&#12288;&#12288;名："
            android:gravity="right|center_vertical"
            android:textSize="@dimen/font_size_18" />
        <TextView
            android:id="@+id/textValue"
            android:layout_width="0dp"
            android:layout_height="30dp"
            android:text="张三"
            android:layout_weight="1"
            android:gravity="left|center_vertical"
            android:textSize="@dimen/font_size_18"
            android:paddingLeft="10dp"
            android:textColor="#FF458AB0"
            ></TextView>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>