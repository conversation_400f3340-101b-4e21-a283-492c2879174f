<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/stepSelf"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_20"
        android:src="@drawable/zz_06"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:id="@+id/hint4"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/space_220"
        android:layout_marginTop="80dp"
        android:layout_marginRight="@dimen/space_220"
        android:layout_marginBottom="@dimen/space_100"
        android:background="@drawable/shape_line_grey"
        android:orientation="vertical"
        android:padding="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:id="@+id/zfbfs"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@color/white"
                android:drawableLeft="@drawable/pay_zhi"
                android:drawablePadding="5dp"
                android:gravity="center"
                android:text="支付宝支付"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/font_size_15" />

            <TextView
                android:id="@+id/wxfs"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="#F5F5F5"
                android:drawableLeft="@drawable/pay_wei"
                android:drawablePadding="5dp"
                android:gravity="center"
                android:text="微信支付"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/font_size_15" />

            <TextView
                android:id="@+id/xxfs"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="#F5F5F5"
                android:drawableLeft="@drawable/pay_underline"
                android:drawablePadding="5dp"
                android:gravity="center"
                android:text="线下支付"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/font_size_15" />

        </LinearLayout>

        <TextView
            android:id="@+id/payInfo"
            style="@style/tv_black_wrap_18"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="40dp"
            android:text="点击线下支付按钮,选择线下支付,公证员会联系您!"
            android:textSize="@dimen/font_size_18"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="订单缴费金额"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/font_size_15"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/priceNumber"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:gravity="center"
                android:text="120"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/font_size_22"
                android:textStyle="bold" />

            <TextView
                android:layout_width="20dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:text="元"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/font_size_15"
                android:textStyle="bold" />

        </LinearLayout>

        <ImageView
            android:id="@+id/payQrCode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="30dp"
            android:background="@drawable/shi_qrcode"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="400dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_marginTop="@dimen/space_15">
            <TextView
                android:id="@+id/onlinePay"
                android:layout_height="wrap_content"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/space_20"
                android:layout_marginBottom="@dimen/space_20"
                android:background="@drawable/shape_tag"
                android:paddingLeft="15dp"
                android:paddingTop="5dp"
                android:paddingRight="15dp"
                android:paddingBottom="5dp"
                android:text="线上支付"
                android:layout_alignParentLeft="true"
                android:textColor="@color/white"
                android:layout_marginLeft="@dimen/space_25"
                android:layout_marginRight="@dimen/space_25"
                android:textSize="@dimen/font_size_18"
                android:gravity="center"
                />

            <TextView
                android:id="@+id/tvNextStep"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:layout_height="wrap_content"
                android:textSize="@dimen/font_size_18"
                android:layout_alignParentRight="true"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/space_20"
                android:layout_marginBottom="@dimen/space_20"
                android:layout_marginLeft="@dimen/space_25"
                android:layout_marginRight="@dimen/space_25"
                android:background="@drawable/shape_tag"
                android:paddingLeft="15dp"
                android:paddingTop="5dp"
                android:paddingRight="15dp"
                android:paddingBottom="5dp"
                android:text="线下支付"
                android:textColor="@color/white" />
        </LinearLayout>

    </LinearLayout>


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:text="付款过程中请勿切换支付方式, 否则可能造成资金损失, 本产品概不负责"
        android:textColor="@color/red"
        android:textSize="18sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>