<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <!--    <data>-->

    <!--        <import type="com.gc.notarizationpc.ui.viewmodel.HomeQuickListItemViewModel" />-->

    <!--        <variable-->
    <!--            name="viewModel"-->
    <!--            type="com.gc.notarizationpc.ui.viewmodel.HomeQuickListItemViewModel" />-->


    <!--    </data>-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_corner6_left_white"
            android:orientation="vertical"
            android:padding="@dimen/space_15">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableLeft="@mipmap/icon_chat_ask"
                android:drawablePadding="@dimen/space_6"
                android:text="常见问题"
                android:textColor="#001534"
                android:textSize="@dimen/font_size20"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_faq"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/space_25"
                android:layout_weight="1" />


            <TextView
                android:id="@+id/tv_change"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_30"
                android:background="@drawable/blue_fillcolor"
                android:gravity="center"
                android:padding="@dimen/space_6"
                android:text="更换问题"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size20" />
        </LinearLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="#DEDEDE" />

        <!--right-->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:background="@drawable/shape_corner6_right_top_gray"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:layout_margin="@dimen/space_15"
                android:src="@mipmap/icon_close" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_answer"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/space_15"
                android:layout_weight="1" />

            <LinearLayout
                android:id="@+id/rn_input"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_below="@+id/rv_answer"
                android:layout_marginRight="@dimen/space_10"
                android:background="@drawable/shape_corner6_right_bottom_white"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/iv_speak"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="@dimen/space_15"
                    android:layout_marginTop="@dimen/space_6"
                    android:layout_marginRight="@dimen/space_15"
                    android:layout_marginBottom="@dimen/space_6"
                    android:src="@mipmap/icon_yuyin" />

                <EditText
                    android:id="@+id/edt_input"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/space_6"
                    android:layout_marginRight="@dimen/space_10"
                    android:layout_marginBottom="@dimen/space_6"
                    android:layout_toRightOf="@+id/iv_speak"
                    android:layout_weight="1"
                    android:lineSpacingExtra="@dimen/space_8"
                    android:maxEms="100"
                    android:background="#F5F5F5" />

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/space_6"
                    android:layout_marginRight="@dimen/space_6"
                    android:layout_marginBottom="@dimen/space_6"
                    android:layout_toRightOf="@+id/edt_input">

                    <TextView
                        android:id="@+id/tv_clear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentTop="true"
                        android:background="@drawable/shape_corner2_e8e8e8"
                        android:paddingLeft="@dimen/space_12"
                        android:paddingTop="@dimen/space_5"
                        android:paddingRight="@dimen/space_12"
                        android:paddingBottom="@dimen/space_5"
                        android:text="清空" />

                    <TextView
                        android:id="@+id/tv_send"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/blue_gradient_corner2"
                        android:paddingLeft="@dimen/space_12"
                        android:paddingTop="@dimen/space_5"
                        android:paddingRight="@dimen/space_12"
                        android:paddingBottom="@dimen/space_5"
                        android:text="发送" />
                </RelativeLayout>
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</layout>