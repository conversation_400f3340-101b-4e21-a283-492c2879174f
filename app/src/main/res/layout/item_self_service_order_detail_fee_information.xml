<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    >

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderDetailFeeInformationItemViewModel" />

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/space_40"
        android:gravity="center_vertical"
        android:background="@drawable/shape_corner4_white_all"
        android:paddingHorizontal="@dimen/space_30"
        android:layout_marginBottom="@dimen/space_5"
        android:orientation="horizontal">
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/notarization_item"
                android:textAlignment="textStart"
                android:textColor="@color/color8A8E99"
                android:textSize="@dimen/font_size14" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                binding:text="@{viewModel.entity.mattersName}"
                android:textAlignment="textStart"
                android:paddingLeft="@dimen/space_16"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14" />
        </LinearLayout>

       <LinearLayout
           android:layout_width="0dp"
           android:layout_height="wrap_content"
           android:layout_weight="1"
           android:orientation="horizontal">
           <TextView
               android:layout_width="@dimen/space_100"
               android:layout_height="wrap_content"
               android:text="@string/gz_fee"
               android:textAlignment="textEnd"
               android:textColor="@color/color8A8E99"
               android:textSize="@dimen/font_size14" />

           <TextView
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_marginLeft="@dimen/space_15"
               binding:text="@{viewModel.entity.notarizationFee.toString()}"
               android:textAlignment="textStart"
               android:textColor="@color/color3333"
               android:textSize="@dimen/font_size14" />
           <TextView
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_marginLeft="@dimen/space_15"
               android:text="元"
               android:textAlignment="textStart"
               android:textColor="@color/color3333"
               android:textSize="@dimen/font_size14" />
       </LinearLayout>

       <LinearLayout
           android:layout_width="0dp"
           android:layout_height="wrap_content"
           android:layout_weight="1"
           android:orientation="horizontal">
           <TextView
               android:layout_width="@dimen/space_100"
               android:layout_height="wrap_content"
               android:text="@string/fb_fee"
               android:textAlignment="textEnd"
               android:textColor="@color/color8A8E99"
               android:textSize="@dimen/font_size14" />

           <TextView
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_marginLeft="@dimen/space_15"
               binding:text="@{viewModel.entity.copyFee.toString()}"
               android:textAlignment="textStart"
               android:textColor="@color/color3333"
               android:textSize="@dimen/font_size14" />
           <TextView
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_marginLeft="@dimen/space_15"
               android:text="元"
               android:textAlignment="textStart"
               android:textColor="@color/color3333"
               android:textSize="@dimen/font_size14" />
       </LinearLayout>




    </LinearLayout>
</layout>