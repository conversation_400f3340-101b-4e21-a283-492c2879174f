<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">
<data>
    <variable
        name="viewModel"
        type="com.gc.notarizationpc.ui.viewmodel.SelfServiceCertificateHomeViewModel" />
    <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

    <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />
</data>
<RelativeLayout
    android:id="@+id/SelfServiceCertificate_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/bg_main"
    android:visibility="visible">
    <include
        android:id="@+id/title_bar"
        layout="@layout/title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/space_70" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/title_bar"
        android:paddingLeft="@dimen/space_30"
        android:paddingTop="@dimen/space_32"
        android:paddingBottom="@dimen/space_53"
        android:gravity="center"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@mipmap/mask_bg"
            android:paddingStart="@dimen/space_22"
            android:paddingTop="@dimen/space_30"
            android:paddingEnd="@dimen/space_30">

            <TextView
                android:id="@+id/need_to_know_text_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_alignParentTop="true"
                android:drawableStart="@mipmap/need_to_know"
                android:drawablePadding="@dimen/space_10"
                android:text="@string/needToKnow"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size16"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/needToKnowTitle_text_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/need_to_know_text_id"
                android:layout_marginStart="@dimen/space_21"
                android:layout_marginTop="@dimen/space_25"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/needToKnowTitle"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/needToKnowContentOne_text_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/needToKnowTitle_text_id"
                android:layout_marginStart="@dimen/space_21"
                android:layout_marginTop="@dimen/space_30"
                android:text="@string/needToKnowContentOne"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/needToKnowContentTwo_text_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/needToKnowContentOne_text_id"
                android:layout_marginStart="@dimen/space_21"
                android:layout_marginTop="@dimen/space_5"
                android:text="@string/needToKnowContentTwo"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/needToKnowContentThree_text_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/needToKnowContentTwo_text_id"
                android:layout_marginStart="@dimen/space_21"
                android:layout_marginTop="@dimen/space_5"
                android:text="@string/needToKnowContentThree"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/needToKnowContentFour_text_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/needToKnowContentThree_text_id"
                android:layout_marginStart="@dimen/space_21"
                android:layout_marginTop="@dimen/space_5"
                android:text="@string/needToKnowContentFour"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/needToKnowContentFive_text_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/needToKnowContentFour_text_id"
                android:layout_marginStart="@dimen/space_21"
                android:layout_marginTop="@dimen/space_5"
                android:text="@string/needToKnowContentFive"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/needToKnowContentEnd_text_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/needToKnowContentFive_text_id"
                android:layout_marginStart="@dimen/space_21"
                android:layout_marginTop="@dimen/space_30"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/needToKnowContentEnd"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14" />
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:layout_width="0dp"
            android:layout_weight="3"
            android:layout_marginLeft="@dimen/space_30"
            android:layout_height="match_parent"
            binding:itemBinding="@{viewModel.itemBinding}"
            binding:items="@{viewModel.observableList}"
            binding:layoutManager="@{LayoutManagers.grid(3)}"/>
    </LinearLayout>
</RelativeLayout>
</layout>
