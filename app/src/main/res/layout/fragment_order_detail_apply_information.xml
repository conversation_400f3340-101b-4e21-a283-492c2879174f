<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    >

    <data>
        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.FragmentOrderDetailApplyInformationViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/order_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@mipmap/split_vertical_icon"
            android:drawablePadding="@dimen/space_10"
            android:paddingLeft="@dimen/space_2"
            android:text="@string/apply_info"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_10"
            android:background="@drawable/shape_corner8_f5f5f7"
            android:orientation="vertical"
            android:padding="@dimen/space_30">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/order_detail_apply_order_number_label"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/applyOrderNumber"
                    android:textAlignment="textEnd"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_order_number"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_weight="6"
                    android:text="13289149843543"
                    android:textAlignment="textStart"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_notary_destination_label"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/notaryDestination"
                    android:textAlignment="textEnd"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_notary_destination"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_weight="4"
                    android:text="13289149843543"
                    android:textAlignment="textStart"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_order_time_label"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/orderTime"
                    android:textAlignment="textEnd"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_order_time"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_weight="6"
                    android:text="13289149843543"
                    android:textAlignment="textStart"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/space_10"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/order_detail_apply_notary_office_label"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/notary_office"
                    android:textAlignment="textEnd"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_notary_office"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_weight="6"
                    android:text="13289149843543"
                    android:textAlignment="textStart"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_notarization_item_label"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/notarization_item"
                    android:textAlignment="textEnd"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_notarization_item"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_weight="4"
                    android:text="13289149843543"
                    android:textAlignment="textStart"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_apply_lan_label"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/apply_lan"
                    android:textAlignment="textEnd"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_apply_lan"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_weight="6"
                    android:text="13289149843543"
                    android:textAlignment="textStart"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/order_detail_apply_pickup_address_label"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/pickUpAddress"
                    android:textAlignment="textEnd"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_pickup_address"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_weight="6"
                    android:text="13289149843543"
                    android:textAlignment="textStart"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_pickup_way_label"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/pickUpWay"
                    android:textAlignment="textEnd"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_pickup_way"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_weight="4"
                    android:text="13289149843543"
                    android:textAlignment="textStart"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_notarization_address_label"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/notarization_address"
                    android:textAlignment="textEnd"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/order_detail_apply_notarization_address"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_weight="6"
                    android:text="13289149843543"
                    android:textAlignment="textStart"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</layout>