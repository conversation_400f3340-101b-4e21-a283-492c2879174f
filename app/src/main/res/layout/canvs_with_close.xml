<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/activity_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="55dp">
        <TextView
            android:id="@+id/sign_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_centerInParent="true"
            android:layout_marginTop="10dp"
            android:text="签名区域(请使用正楷签名)"
            android:textSize="20sp" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/space_20"
            android:padding="@dimen/space_10"
            android:src="@mipmap/close" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="800dp"
        android:layout_height="700dp"
        android:layout_gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/main_linlayout"
            android:layout_width="750dp"
            android:layout_height="500dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/shape_f5f5f7_round"
            android:orientation="vertical" />


        <RelativeLayout
            android:layout_width="700dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="5dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/bt_clear"
                android:layout_width="100dp"
                android:layout_height="40dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="50dp"
                android:background="@drawable/shape_corner_white"
                android:gravity="center"
                android:text="重签"
                android:textColor="@color/black"
                android:textSize="20sp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center"
                    android:text="签"
                    android:textSize="20sp" />

                <TextView
                    android:id="@+id/name_pos"
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="10dp"
                    android:padding="@dimen/space_5"
                    android:background="@drawable/shape_corner_white"
                    android:gravity="center"
                    android:textColor="#0F41A6"
                    android:textSize="25sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="10dp"
                    android:gravity="center"
                    android:text="字"
                    android:textSize="20sp" />
            </LinearLayout>

            <Button
                android:id="@+id/bt"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="50dp"
                android:background="@drawable/shape_corner_blue"
                android:gravity="center"
                android:paddingHorizontal="@dimen/space_5"
                android:text="完成"
                android:textColor="@color/white"
                android:textSize="20sp" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:visibility="gone" />
    </LinearLayout>
</LinearLayout>
