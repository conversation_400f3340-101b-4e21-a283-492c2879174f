<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="100dp"
    android:layout_height="110dp">

    <ImageView
        android:id="@+id/work_iv_wip_picture"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_centerInParent="true"
        android:scaleType="fitXY"
        android:src="@mipmap/erroimage" />

    <ImageView
        android:id="@+id/work_iv_wip_create"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:scaleType="fitXY"
        android:layout_centerInParent="true"
        android:src="@mipmap/icon_add"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/work_iv_wip_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:paddingLeft="20dp"
        android:layout_marginTop="@dimen/space_5"
        android:paddingBottom="20dp"
        android:src="@mipmap/icon_round_del" />
</RelativeLayout>