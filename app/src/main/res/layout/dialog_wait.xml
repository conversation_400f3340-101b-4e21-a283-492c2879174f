<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/space_500"
    android:layout_height="400dp"
    android:layout_gravity="center"
    android:background="@drawable/border_round_white"
    android:gravity="center"
    android:orientation="vertical">

    <com.ldoublem.loadingviewlib.view.LVPlayBall
        android:id="@+id/lv_playball"
        android:layout_width="@dimen/space_100"
        android:layout_height="@dimen/space_100"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="@dimen/font_size_20" />

    <ImageView
        android:id="@+id/connect_img"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginTop="60dp"
        android:layout_marginBottom="30dp"
        android:src="@mipmap/connectfailed"
        android:visibility="gone" />

    <TextView
        android:id="@+id/connect_status"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="视频连线中"
        android:textSize="@dimen/font_size_20" />

    <TextView
        android:id="@+id/connect_context"
        android:layout_width="wrap_content"
        android:layout_height="80dp"
        android:gravity="center"
        android:text="请耐心等待,不要离开本页"
        android:textSize="@dimen/font_size_16" />

    <Button
        android:id="@+id/send_cancel"
        android:layout_width="100dp"
        android:layout_height="40dp"
        android:layout_marginTop="50dp"
        android:background="@drawable/btn_login_bg"
        android:onClick="overNotarization"
        android:text="退出等待"
        android:textColor="@color/white"
        android:textSize="15sp"
        android:visibility="gone" />
</LinearLayout>