<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.RemoteEmpowermentActivity"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#2196F3">

        <ImageView
            android:id="@+id/backClicked"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical|left"
            android:layout_marginTop="5dp"
            android:layout_marginLeft="20dp"
            android:src="@drawable/back" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:padding="20dp"
            android:text="小额赋强"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="20sp"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="@dimen/space_20"
        android:layout_marginLeft="@dimen/space_40"
        android:layout_marginRight="@dimen/space_40">


        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never" />

            </androidx.core.widget.NestedScrollView>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>


    </LinearLayout>

</LinearLayout>