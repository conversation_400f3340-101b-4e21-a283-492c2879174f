<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/c_52b5fe">

    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/trtcMain"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <!--<ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center" />-->
    </com.tencent.rtmp.ui.TXCloudVideoView>

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <com.tencent.rtmp.ui.TXCloudVideoView
            android:id="@+id/trtcRemote"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!--<ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:scaleType="centerCrop" />-->
        </com.tencent.rtmp.ui.TXCloudVideoView>


        <TextView
            android:id="@+id/tvNotaryState"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black"
            android:gravity="center"
            android:text="等待公证员进入"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_18" />
    </RelativeLayout>
</LinearLayout>