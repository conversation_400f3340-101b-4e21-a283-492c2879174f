<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.RemoteModelActivity"
    android:background="@drawable/dialog_whit_circular_bg"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/space_20">
    <TextView
        android:id="@+id/dialog_title"
        style="@style/dialog_title_style"
        android:layout_marginBottom="@dimen/space_20"
        android:text="验证手机号码"
        android:textSize="@dimen/font_size_30" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/space_80"
            android:layout_height="wrap_content"
            android:text="手机号码"
            android:textSize="@dimen/font_size_16" />

        <EditText
            android:id="@+id/editMobile"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/border"
            android:hint="请输入手机号"
            android:inputType="phone"
            android:textCursorDrawable="@drawable/cursor_drawable"
            android:minEms="18"
            android:text=""
            android:textSize="@dimen/font_size_16" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_40"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/space_80"
            android:layout_height="wrap_content"
            android:text="验证码"
            android:textSize="@dimen/font_size_16" />

        <EditText
            android:id="@+id/editCode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/border"
            android:textCursorDrawable="@drawable/cursor_drawable"
            android:hint="验证码"
            android:inputType="number"
            android:minEms="10"
            android:text=""
            android:textSize="@dimen/font_size_16" />

        <com.example.framwork.widget.TimingButton
            android:id="@+id/buttonCode"
            android:layout_width="@dimen/space_120"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/space_23"
            android:background="@drawable/border"
            android:onClick="sendCodeClick"
            android:text="获取验证码"
            android:textColor="@color/black"
            app:tb_psText="获取验证码" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:layout_width="140dp"
        android:layout_height="@dimen/space_50"
        android:layout_marginTop="@dimen/dp_40"
        android:background="@drawable/btn_login_bg"
        android:onClick="submitClick"
        android:text="提交"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18" />
</LinearLayout>