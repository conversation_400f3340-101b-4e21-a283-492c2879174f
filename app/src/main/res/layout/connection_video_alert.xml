<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/connectionVideoAlert"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_corner10_white_all"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_gravity="center"
    android:visibility="visible">
    <TextView
        android:id="@+id/clock_text_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableLeft="@mipmap/clock_icon"
        android:drawablePadding="@dimen/space_11"
        android:textColor="@color/color2C98F0"
        android:textSize="@dimen/font_size16"
        android:background="@drawable/shape_corner5_f1f3f6_all"
        android:layout_gravity="right"
        android:paddingHorizontal="@dimen/space_10"
        android:paddingVertical="@dimen/space_7"
        android:layout_marginTop="@dimen/space_20"
        android:layout_marginRight="@dimen/space_20"
        android:text="00:20"
        android:gravity="center_vertical"/>
    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/space_10"
        android:layout_marginBottom="@dimen/space_30"
        android:layout_gravity="center_horizontal">

        <TextView
            android:id="@+id/connecting_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/space_15"
            android:drawableTop="@mipmap/connecting_icon"
            android:text="@string/connect_str"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="@dimen/font_size24"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/connectionTimeOut"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/connecting_text"
            android:layout_centerHorizontal="true"

            android:layout_marginTop="@dimen/space_15"
            android:text="@string/connect_time_out_str"
            android:textColor="@color/black"
            android:textSize="@dimen/font_size16" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/connectionTimeOut"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/space_40"
            android:layout_marginBottom="@dimen/space_30"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/close_transaction"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/space_24"
                android:background="@drawable/shape_corner5_3c6af4"
                android:onClick="CloseTransaction"
                android:paddingHorizontal="@dimen/space_38"
                android:paddingVertical="@dimen/space_10"
                android:text="@string/close_transaction"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/font_size16" />

<!--            <Button-->
<!--                android:id="@+id/make_appointment"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:background="@drawable/shape_corner5_3c6af4"-->
<!--                android:onClick="CloseTransaction"-->
<!--                android:paddingHorizontal="@dimen/space_38"-->
<!--                android:paddingVertical="@dimen/space_10"-->
<!--                android:text="@string/make_appointment"-->
<!--                android:textColor="@color/white"-->
<!--                android:textSize="@dimen/font_size16" />-->

        </LinearLayout>
    </RelativeLayout>

</LinearLayout>
