<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/notary_status_image"
        android:layout_width="70dp"
        android:layout_height="80dp"
        android:background="@mipmap/notaryonline" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/notary_name"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:gravity="center_vertical"
            android:text="公证员001号"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/notary_status"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:gravity="center_vertical"
            android:text="状态：空闲"
            android:textSize="15sp" />

    </LinearLayout>

    <Button
        android:id="@+id/notary_start_notarization"
        android:layout_width="150dp"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="80dp"
        android:background="@drawable/btn_login_bg"
        android:text="开始公证"
        android:textColor="@color/white"
        android:textSize="16sp" />
</LinearLayout>