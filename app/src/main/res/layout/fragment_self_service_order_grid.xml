<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">
        <data>
            <import type="com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderGridViewModel" />
            <variable
                name="viewModel"
                type="SelfServiceOrderGridViewModel" />

            <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

            <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />
        </data>

        <LinearLayout
            android:id="@+id/fragment_gridview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/color00000000"
            android:layout_marginLeft="@dimen/space_30"
            android:paddingVertical="@dimen/space_30"
            android:orientation="vertical">

            <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
                android:id="@+id/twinklingRefreshLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                binding:onLoadMoreCommand="@{viewModel.onLoadMoreCommand}"
                binding:onRefreshCommand="@{viewModel.onRefreshCommand}"
                binding:tr_head_height="80dp"
                android:visibility="visible"
                android:overScrollMode="never"
                binding:tr_wave_height="80dp">

                <androidx.recyclerview.widget.RecyclerView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    binding:itemBinding="@{viewModel.itemBinding}"
                    binding:items="@{viewModel.observableList}"
                    binding:layoutManager="@{LayoutManagers.grid(3)}" />

            </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>

            <RelativeLayout
                android:id="@+id/fragment_gridview_empty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:visibility="gone">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    binding:onClickCommand="@{viewModel.onRefreshCommand}"
                    binding:isThrottleFirst="@{Boolean.FALSE}"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/empty_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/empty_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_40"
                        android:text="@string/noOrderData"
                        android:textColor="@color/color3333"
                        android:textSize="@dimen/font_size24" />
                </LinearLayout>
            </RelativeLayout>

        </LinearLayout>
</layout>