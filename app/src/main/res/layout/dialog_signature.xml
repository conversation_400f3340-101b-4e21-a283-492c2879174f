<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/signatureView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_corner10_white_all"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_centerVertical="true"
        android:layout_gravity="right"
        android:layout_marginTop="@dimen/space_20"
        android:layout_marginRight="@dimen/space_20"
        android:padding="@dimen/space_5"
        android:src="@mipmap/icon_close" />

    <TextView
        android:id="@+id/sign_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:text="@string/sign_hint"
        android:textColor="@color/color3333"
        android:textSize="20sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/space_30"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/main_linlayout"
            android:layout_width="match_parent"
            android:layout_height="450dp"
            android:layout_gravity="center_horizontal"
            android:background="#f5f5f7"
            android:orientation="vertical" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/space_30"
            android:layout_marginBottom="@dimen/space_30"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_clear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="50dp"
                android:background="@drawable/shape_corner5_e8e8e8"
                android:gravity="center"
                android:paddingHorizontal="@dimen/space_30"
                android:paddingVertical="@dimen/space_8"
                android:text="@string/resign"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size18" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center"
                    android:text="@string/please_sign"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size18" />

                <TextView
                    android:id="@+id/name_pos"
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/shape_corner5_f5f5f7"
                    android:gravity="center"
                    android:padding="@dimen/space_5"
                    android:textColor="#3D99F5"
                    android:textSize="@dimen/font_size18"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="10dp"
                    android:gravity="center"
                    android:text="@string/word"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size18" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="50dp"
                android:background="@drawable/shape_corner5_3c6af4"
                android:gravity="center"
                android:paddingHorizontal="@dimen/space_30"
                android:paddingVertical="@dimen/space_8"
                android:text="@string/confirm_sign"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size18" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:visibility="gone" />
    </LinearLayout>
</LinearLayout>
