<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/stepSelf"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_20"
        android:src="@drawable/zz_05"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="10dp"
        android:minHeight="@dimen/space_300"
        android:padding="25dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stepSelf" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tvUpStep"
            style="@style/tv_black_wrap_18"
            android:layout_margin="10dp"
            android:layout_marginBottom="@dimen/space_20"
            android:background="@drawable/shape_tag"
            android:gravity="center"
            android:paddingLeft="15dp"
            android:paddingTop="5dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp"
            android:text="上一步"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_20" />

        <TextView
            android:id="@+id/tvWdzdStep"
            style="@style/tv_black_wrap_18"
            android:layout_margin="10dp"
            android:layout_marginBottom="@dimen/space_20"
            android:background="@drawable/shape_round_gray"
            android:gravity="center"
            android:paddingLeft="15dp"
            android:paddingTop="5dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp"
            android:text="未带资料入口"
            android:textColor="#333333"
            android:textSize="@dimen/font_size_20"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvSignStep"
            style="@style/tv_black_wrap_18"
            android:layout_margin="10dp"
            android:layout_marginBottom="@dimen/space_20"
            android:background="@drawable/shape_tag"
            android:gravity="center"
            android:paddingLeft="15dp"
            android:paddingTop="5dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp"
            android:text="签字确认"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_20" />

    </LinearLayout>

    <include
        android:id="@+id/signatureView"
        layout="@layout/canvs"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>