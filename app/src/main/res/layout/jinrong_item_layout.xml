<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#e0e0e0">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/white_shape"
        android:orientation="horizontal"
        android:padding="30dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="订单编号："
            android:textColor="@color/black"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/order_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:text="2022091254684254235"
            android:textColor="@color/black"
            android:textSize="20sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="50dp"
            android:text="服务机构："
            android:textColor="@color/black"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/order_mechanism"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_weight="1"
            android:text="江苏省南京市石城公证处"
            android:textColor="@color/black"
            android:textSize="20sp" />

        <Button
            android:id="@+id/start_notarization"
            android:layout_width="150dp"
            android:layout_height="40dp"
            android:layout_weight="0"
            android:background="@drawable/btn_start_n"
            android:text="开始公证"
            android:textColor="@color/white"
            android:textSize="20sp" />
    </LinearLayout>
</FrameLayout>