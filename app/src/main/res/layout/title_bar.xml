<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin_title"
    android:layout_width="match_parent"
    android:layout_height="@dimen/space_70"
    android:orientation="vertical">

    <View
        android:id="@+id/space"
        android:layout_width="match_parent"
        android:layout_height="25dp"
        android:background="@color/bar_transparent" />

    <RelativeLayout
        android:layout_below="@+id/space"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginLeft="@dimen/space_30"
        android:layout_centerVertical="true">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:drawableLeft="@mipmap/logo"
            android:drawablePadding="@dimen/space_5"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:text="@string/title"
            android:textColor="@color/color3333"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_shenban"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_toLeftOf="@+id/tv_linelayout"
            android:background="@drawable/shape_corner5_white"
            android:drawableLeft="@mipmap/explain"
            android:drawablePadding="@dimen/space_10"
            android:gravity="center"
            android:paddingHorizontal="@dimen/space_20"
            android:paddingVertical="@dimen/space_8"
            android:src="@mipmap/icon_home"
            android:text="@string/notarization_desc"
            android:textColor="@color/color3333"
            android:textSize="@dimen/space_16"
            android:visibility="gone" />
        <TextView
            android:id="@+id/tv_shutdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_toLeftOf="@+id/tv_restart"
            android:background="@drawable/shape_corner5_white"
            android:drawableLeft="@mipmap/icon_shutdown"
            android:drawablePadding="@dimen/space_10"
            android:gravity="center"
            android:paddingHorizontal="@dimen/space_20"
            android:paddingVertical="@dimen/space_8"
            android:text="@string/shutdown"
            android:textColor="@color/color3333"
            android:textSize="@dimen/space_16"
            android:visibility="gone" />
        <TextView
            android:id="@+id/tv_restart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:background="@drawable/shape_corner5_3c6af4"
            android:drawableLeft="@mipmap/icon_restart"
            android:drawablePadding="@dimen/space_10"
            android:gravity="center"
            android:paddingHorizontal="@dimen/space_20"
            android:paddingVertical="@dimen/space_8"
            android:text="@string/restart"
            android:layout_marginRight="@dimen/space_30"
            android:layout_marginLeft="@dimen/space_10"
            android:textColor="@color/white"
            android:textSize="@dimen/space_16"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/tv_linelayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true">
            <TextView
                android:id="@+id/tv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/space_10"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:background="@drawable/shape_corner5_white"
                android:drawableLeft="@mipmap/back_grey_icon"
                android:drawablePadding="@dimen/space_10"
                android:gravity="center"
                android:text="@string/back"
                android:textColor="@color/color3333"
                android:textSize="@dimen/space_16" />

            <TextView
                android:id="@+id/tv_home"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/space_10"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:background="@drawable/shape_corner5_white"
                android:drawableLeft="@mipmap/icon_home"
                android:drawablePadding="@dimen/space_10"
                android:gravity="center"
                android:text="@string/home"
                android:textColor="@color/color3333"
                android:textSize="@dimen/space_16" />
        </LinearLayout>


<!--        <TextView-->
<!--            android:id="@+id/tv_time"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_alignParentTop="true"-->
<!--            android:layout_alignParentRight="true"-->
<!--            android:layout_marginRight="@dimen/space_10"-->
<!--            android:gravity="center"-->
<!--            android:paddingLeft="@dimen/space_20"-->
<!--            android:paddingTop="@dimen/space_5"-->
<!--            android:paddingRight="@dimen/space_20"-->
<!--            android:paddingBottom="@dimen/space_5"-->
<!--            android:text=""-->
<!--            android:textColor="@color/color3333"-->
<!--            android:textSize="@dimen/font_size18" />-->

    </RelativeLayout>


</RelativeLayout>