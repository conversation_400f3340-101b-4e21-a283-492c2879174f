<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    >

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderDetailDocumentItemViewModel" />

    </data>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="@dimen/space_20"
        android:layout_marginLeft="@dimen/space_40"
        android:layout_marginBottom="@dimen/space_20"
        binding:onClickCommand="@{viewModel.documentPreView}"
        binding:isThrottleFirst="@{Boolean.FALSE}"
        android:orientation="vertical">
        <TextView
            android:id="@+id/order_detail_document_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            binding:text="@{viewModel.entity.typeName}"
            android:textSize="@dimen/font_size12"
            android:textColor="@color/colorBFBFBF"
            android:textAlignment="center"/>
        <ImageView
            android:id="@+id/order_detail_image_item_document"
            android:layout_width="@dimen/space_80"
            android:layout_height="@dimen/space_80"
            android:layout_marginTop="@dimen/space_10"
            android:src="@drawable/anim_loading_view"
            binding:imageUrl="@{viewModel.documentPath}"
            binding:placeholderRes="@mipmap/image_empty"
            android:scaleType="fitXY"/>
    </LinearLayout>
</layout>