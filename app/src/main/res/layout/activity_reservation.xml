<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.ReservationViewModel" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/space_30"
            android:background="@drawable/shape_corner10_white_all"
            android:orientation="vertical"
            android:padding="@dimen/space_30">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_15"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/title_bar"
                    android:drawableLeft="@mipmap/icon_must"
                    android:drawablePadding="@dimen/space_5"
                    android:text="@string/must"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size18"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="@dimen/space_20"
                    android:layout_weight="1"
                    android:background="#E8E8EB" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/rnl_search_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_must_hint"
                android:layout_marginTop="@dimen/space_5"
                android:gravity="center_vertical"
                android:paddingVertical="@dimen/space_8">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_must_hint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/phone_num"
                        android:textColor="@color/color6666"
                        android:textSize="@dimen/font_size14" />
                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/edt_phone"
                        style="@style/edit_spinner_edit_sty"
                        android:layout_width="0dp"
                        android:layout_weight="7"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_10"
                        android:layout_marginRight="@dimen/space_5"
                        android:background="@drawable/shape_corner4_f1f1f2"
                        android:drawablePadding="@dimen/space_8"
                        android:gravity="center_vertical"
                        android:hint="@string/phone_num_hint"
                        android:imeOptions="actionDone"
                        android:inputType="number"
                        android:maxLines="1"
                        android:maxLength="11"
                        android:paddingHorizontal="@dimen/space_20"
                        android:paddingVertical="@dimen/space_8"
                        android:singleLine="true" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.3"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_30"
                        android:text="@string/verifycode"
                        android:textColor="@color/color6666"
                        android:textSize="@dimen/font_size14"/>

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/edt_verifycode"
                        style="@style/edit_spinner_edit_sty"
                        android:layout_width="0dp"
                        android:layout_weight="7"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_8"
                        android:layout_marginRight="@dimen/space_5"
                        android:layout_toLeftOf="@+id/tv_search"
                        android:background="@drawable/shape_corner4_f1f1f2"
                        android:drawablePadding="@dimen/space_8"
                        android:gravity="center_vertical"
                        android:hint="@string/verifycode_hint"
                        android:imeOptions="actionDone"
                        android:inputType="number"
                        android:maxLines="1"
                        android:maxLength="6"
                        android:paddingHorizontal="@dimen/space_20"
                        android:paddingVertical="@dimen/space_8"
                        android:singleLine="true" />

                    <TextView
                        android:id="@+id/get_code_text_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_5"
                        android:background="@drawable/shape_corner5_3c6af4"
                        android:paddingHorizontal="@dimen/space_15"
                        android:paddingVertical="@dimen/space_5"
                        android:text="@string/send_code"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size14"
                        binding:onClickCommand="@{viewModel.messageCodeOnClickCommand}"
                        binding:isThrottleFirst="@{Boolean.FALSE}"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_30"
                        android:text="@string/notary_office"
                        android:textColor="@color/color6666"
                        android:textSize="@dimen/font_size14" />

                    <TextView
                        android:id="@+id/edt_notary_office"
                        style="@style/edit_spinner_edit_sty"
                        android:layout_width="0dp"
                        android:layout_weight="7"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_8"
                        android:layout_marginRight="@dimen/space_5"
                        android:layout_toLeftOf="@+id/tv_search"
                        android:background="@drawable/shape_corner4_f1f1f2"
                        android:drawablePadding="@dimen/space_10"
                        android:gravity="center_vertical"
                        android:hint="@string/notary_office_hint"
                        android:imeOptions="actionDone"
                        android:ellipsize="marquee"
                        android:text="@{viewModel.selectedNotaryOffice}"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/space_20"
                        android:paddingVertical="@dimen/space_8"
                        android:singleLine="true"
                        />
                    <ImageView
                        android:layout_width="@dimen/space_30"
                        android:layout_height="@dimen/space_30"
                        android:background="@mipmap/icon_arrow"
                        binding:onClickCommand="@{viewModel.chooseNotaryOffice}"
                        binding:isThrottleFirst="@{Boolean.FALSE}" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_45"
                        android:text="@string/date"
                        android:textColor="@color/color6666"
                        android:textSize="@dimen/font_size14"/>

                    <TextView
                        android:id="@+id/reservation_date"
                        style="@style/edit_spinner_edit_sty"
                        android:layout_width="wrap_content"
                        android:layout_weight="7"
                        android:lines="1"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_8"
                        android:layout_marginRight="@dimen/space_5"
                        android:layout_toLeftOf="@+id/tv_search"
                        android:background="@drawable/shape_corner4_f1f1f2"
                        android:drawablePadding="@dimen/space_10"
                        android:gravity="center_vertical"
                        android:hint="@string/date_hint"
                        android:imeOptions="actionDone"
                        android:ellipsize="marquee"
                        android:paddingHorizontal="@dimen/space_20"
                        android:paddingVertical="@dimen/space_8"
                        />
                    <ImageView
                        android:layout_width="@dimen/space_30"
                        android:layout_height="@dimen/space_30"
                        android:background="@mipmap/icon_arrow"
                        android:onClick="showTimePickDialog"/>
                </LinearLayout>
            </LinearLayout>
            <!--           <TextView-->
            <!--               android:layout_width="wrap_content"-->
            <!--               android:layout_height="wrap_content"-->
            <!--               android:text="@string/tips"-->
            <!--               android:textColor="@color/color3333"-->
            <!--               android:textSize="@dimen/font_size20"-->
            <!--               android:textStyle="bold"-->
            <!--               android:layout_marginLeft="@dimen/space_30"-->
            <!--               android:visibility="gone"/>-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_40"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:drawableLeft="@mipmap/icon_orditional"
                    android:drawablePadding="@dimen/space_5"
                    android:text="@string/optional"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size18"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="@dimen/space_20"
                    android:layout_weight="1"
                    android:background="#E8E8EB" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnl_optional"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_must_hint"
                android:layout_marginTop="@dimen/space_5"
                android:gravity="center_vertical"
                android:paddingVertical="@dimen/space_8">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_optional_hint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/title_bar"
                        android:text="@string/notarization_item"
                        android:textColor="@color/color6666"
                        android:textSize="@dimen/font_size14" />

                    <TextView
                        android:id="@+id/edt_notarization_item"
                        style="@style/edit_spinner_edit_sty"
                        android:layout_width="wrap_content"
                        android:layout_weight="7"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_8"
                        android:layout_marginRight="@dimen/space_5"
                        android:background="@drawable/shape_corner4_f1f1f2"
                        android:gravity="center_vertical"
                        android:hint="@string/notarization_item_hint"
                        android:imeOptions="actionDone"
                        android:maxLines="1"
                        android:lines="1"
                        android:ellipsize="marquee"
                        android:text="@{viewModel.notaryMatters}"
                        android:paddingHorizontal="@dimen/space_20"
                        android:paddingVertical="@dimen/space_8"
                        android:singleLine="true" />
                    <ImageView
                        android:layout_width="@dimen/space_30"
                        android:layout_height="@dimen/space_30"
                        android:background="@mipmap/icon_arrow"
                        binding:onClickCommand="@{viewModel.notaryMattersOnClickCommand}"
                        binding:isThrottleFirst="@{Boolean.FALSE}"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_30"
                        android:text="@string/notarization_address"
                        android:textColor="@color/color6666"
                        android:textSize="@dimen/font_size14"/>

                    <TextView
                        android:id="@+id/edt_notarization_address"
                        style="@style/edit_spinner_edit_sty"
                        android:layout_width="0dp"
                        android:layout_weight="7"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_8"
                        android:layout_marginRight="@dimen/space_5"
                        android:background="@drawable/shape_corner4_f1f1f2"
                        android:gravity="center_vertical"
                        android:hint="@string/notarization_address_hint"
                        android:imeOptions="actionDone"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/space_20"
                        android:paddingVertical="@dimen/space_8"
                        android:text="@{viewModel.selectedNotaryArea}"
                        android:singleLine="true" />
                    <ImageView
                        android:layout_width="@dimen/space_30"
                        android:layout_height="@dimen/space_30"
                        android:background="@mipmap/icon_arrow"
                        binding:onClickCommand="@{viewModel.areaOnClickCommand}"
                        binding:isThrottleFirst="@{Boolean.FALSE}" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_30"
                        android:text="@string/notarization_language"
                        android:textColor="@color/color6666"
                        android:textSize="@dimen/font_size14"/>

                    <TextView
                        android:id="@+id/edt_notarization_language"
                        style="@style/edit_spinner_edit_sty"
                        android:layout_width="0dp"
                        android:layout_weight="7"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/space_8"
                        android:layout_marginRight="@dimen/space_5"
                        android:layout_toLeftOf="@+id/tv_search"
                        android:background="@drawable/shape_corner4_f1f1f2"
                        android:gravity="center_vertical"
                        android:hint="@string/notarization_language_hint"
                        android:imeOptions="actionDone"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/space_20"
                        android:paddingVertical="@dimen/space_8"
                        android:text="@{viewModel.selectedNotaryLanguage}"
                        android:singleLine="true" />

                    <ImageView
                        android:layout_width="@dimen/space_30"
                        android:layout_height="@dimen/space_30"
                        android:background="@mipmap/icon_arrow"
                        binding:onClickCommand="@{viewModel.languageOnClickCommand}"
                        binding:isThrottleFirst="@{Boolean.FALSE}" />
                </LinearLayout>

<!--                <LinearLayout-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="horizontal"-->
<!--                    android:gravity="center">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_marginStart="@dimen/space_20"-->
<!--                        android:text="@string/notarization_book_num"-->
<!--                        android:textColor="@color/color6666"-->
<!--                        android:textSize="@dimen/font_size14" />-->
<!--                    <ImageView-->
<!--                        android:layout_width="@dimen/space_30"-->
<!--                        android:layout_height="@dimen/space_30"-->
<!--                        android:background="@drawable/shape_corner5_white"-->
<!--                        binding:onClickCommand="@{viewModel.reduceNotarizationCopiesCommand}"-->
<!--                        android:src="@mipmap/reduce"/>-->

<!--&lt;!&ndash;                    <TextView&ndash;&gt;-->
<!--&lt;!&ndash;                        android:layout_width="@dimen/space_28"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:layout_height="@dimen/space_28"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:layout_marginStart="@dimen/space_20"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:background="@drawable/shape_corner5_3c6af4"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:gravity="center"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:paddingBottom="@dimen/space_10"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:text="-"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:textColor="@color/white"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:textSize="22sp"&ndash;&gt;-->
<!--&lt;!&ndash;                        binding:onClickCommand="@{viewModel.reduceNotarizationCopiesCommand}"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:textStyle="bold" />&ndash;&gt;-->

<!--                    <androidx.appcompat.widget.AppCompatEditText-->
<!--                        android:id="@+id/edt_notarization_book_num"-->
<!--                        style="@style/edit_spinner_edit_sty"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_marginStart="@dimen/space_8"-->
<!--                        android:layout_marginEnd="@dimen/space_5"-->
<!--                        android:layout_toStartOf="@+id/tv_search"-->
<!--                        android:background="@drawable/shape_corner4_f1f1f2"-->
<!--                        android:drawablePadding="@dimen/space_8"-->
<!--                        android:gravity="center_vertical"-->
<!--                        android:imeOptions="actionDone"-->
<!--                        android:maxLines="1"-->
<!--                        android:paddingHorizontal="@dimen/space_20"-->
<!--                        android:paddingVertical="@dimen/space_8"-->
<!--                        android:singleLine="true"-->
<!--                        android:text="@{viewModel.notarizationCopies}"-->
<!--                        tools:ignore="ObsoleteLayoutParam" />-->

<!--&lt;!&ndash;                    <TextView&ndash;&gt;-->
<!--&lt;!&ndash;                        android:layout_width="@dimen/space_28"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:layout_height="@dimen/space_28"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:layout_marginStart="@dimen/space_5"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:background="@drawable/shape_corner5_3c6af4"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:gravity="center"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:text="+"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:paddingBottom="@dimen/space_5"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:textColor="@color/white"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:textSize="22sp"&ndash;&gt;-->
<!--&lt;!&ndash;                        binding:onClickCommand="@{viewModel.addNotarizationCopiesCommand}"&ndash;&gt;-->
<!--&lt;!&ndash;                        android:textStyle="bold" />&ndash;&gt;-->
<!--                    <ImageView-->
<!--                        android:layout_width="@dimen/space_30"-->
<!--                        android:layout_height="@dimen/space_30"-->
<!--                        android:background="@drawable/shape_corner5_white"-->
<!--                        binding:onClickCommand="@{viewModel.addNotarizationCopiesCommand}"-->
<!--                        android:src="@mipmap/add"/>-->
<!--                </LinearLayout>-->
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_marginTop="@dimen/space_15"
                android:text="@string/notarization_mark"
                android:textColor="@color/color6666"
                android:textSize="@dimen/font_size14"
                android:textStyle="bold" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_remark"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="match_parent"
                android:layout_height="135dp"
                android:layout_marginTop="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:drawablePadding="@dimen/space_8"
                android:gravity="left"
                android:maxLength="200"
                android:hint="@string/notarization_mark_hint"
                android:imeOptions="actionDone"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_15" />

            <TextView
                android:id="@+id/tv_shenban"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:drawableLeft="@mipmap/icon_alert_blue_cirle"
                android:text="@string/shenban_hint"
                android:drawablePadding="@dimen/space_8"
                android:layout_marginTop="@dimen/space_20"
                android:textColor="@color/date_picker_text_light"
                binding:onClickCommand="@{viewModel.showShenban}"
                binding:isThrottleFirst="@{Boolean.FALSE}"
                android:textSize="@dimen/font_size14" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_20"
                android:layout_marginRight="30dp"
                android:gravity="left"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/userAgreement"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/select_checkout"
                    android:button="@null" />


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="@string/read_agree"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/agreement_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/useRule"
                    android:textColor="@color/date_picker_text_light"
                    binding:onClickCommand="@{viewModel.lookOnLineRule}"
                    binding:isThrottleFirst="@{Boolean.FALSE}"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/dianziSignService_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/dianziSignService"
                    binding:onClickCommand="@{viewModel.lookElectronicRule}"
                    binding:isThrottleFirst="@{Boolean.FALSE}"
                    android:textColor="@color/date_picker_text_light"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/agree_to_use"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size16" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:layout_marginTop="@dimen/space_20"
                android:background="@drawable/shape_corner8_2568ff"
                android:paddingHorizontal="@dimen/space_60"
                android:paddingVertical="@dimen/space_15"
                android:text="@string/confirm_reservation"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size14"
                binding:onClickCommand="@{viewModel.confirmReservationOnClickCommand}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />
        </LinearLayout>


    </LinearLayout>
</layout>