<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.VideoNotarizationViewModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />

    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />



    </RelativeLayout>
</layout>