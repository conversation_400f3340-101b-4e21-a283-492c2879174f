<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/stepSelf"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_20"
        android:src="@drawable/zz_02"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/space_60"
        android:padding="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stepSelf">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/llCntentView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="@dimen/space_10"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@id/cc"
                app:layout_constraintTop_toBottomOf="parent">

                <TextView
                    android:id="@+id/hint1"
                    style="@style/tv_black_wrap_18"
                    android:textSize="@dimen/font_size_18"
                    android:layout_marginTop="@dimen/space_10"
                    android:layout_marginLeft="@dimen/space_10"
                    android:text="请选择公证书使用地区\\译文语言"
                    android:drawableLeft="@drawable/ac_title"
                    android:drawablePadding="5dp"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:padding="@dimen/space_10"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.gc.notarizationpc.widget.editspinner.AppCompatEditSpinner
                            android:id="@+id/addressChose"
                            android:layout_height="40dp"
                            android:layout_width="@dimen/space_250"
                            app:editInputType="text"
                            app:editMaxLength="20"
                            app:editMaxLines="1"
                            app:editTextColor="#000000"
                            app:editTextSize="@dimen/font_size_15"
                            app:hint="请选择公证书使用地区"
                            app:rightImageDropShowAllItem="true"
                            app:rightImageGone="false"
                            app:spinnerBackground="@drawable/bg_view_frame"
                            app:spinnerItemTextSize="@dimen/font_size_15"
                            app:spinnerItemMatchTextColor="#0A0EE1"
                            app:spinnerItemTextColor="#000000"
                            />
                        <com.gc.notarizationpc.widget.editspinner.AppCompatEditSpinner
                            android:id="@+id/ywyyChose"
                            android:layout_height="40dp"
                            android:layout_width="@dimen/space_250"
                            app:editInputType="text"
                            app:editMaxLength="20"
                            app:editMaxLines="1"
                            app:editTextColor="#000000"
                            app:editTextSize="@dimen/font_size_15"
                            android:layout_marginLeft="10dp"
                            app:hint="请选择公证书译文语言"
                            app:rightImageDropShowAllItem="true"
                            app:rightImageGone="false"
                            app:spinnerBackground="@drawable/bg_view_frame"
                            app:spinnerItemTextSize="@dimen/font_size_15"
                            app:spinnerItemMatchTextColor="#0A0EE1"
                            app:spinnerItemTextColor="#000000"
                            />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/gzcChose"
                        android:layout_height="40dp"
                        android:layout_width="@dimen/space_250"
                        android:gravity="center"
                        android:textSize="@dimen/font_size_15"
                        android:padding="5dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginLeft="20dp"
                        android:hint="请选择公证处"
                        android:background="@drawable/shape_line_grey"/>

                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_margin="10dp"
                    android:background="#eaeaea"/>

                <TextView
                    android:id="@+id/hint2"
                    style="@style/tv_black_wrap_18"
                    android:textSize="@dimen/font_size_18"
                    android:layout_marginLeft="@dimen/space_10"
                    android:text="申请人"
                    android:drawableLeft="@drawable/ac_04"
                    android:drawablePadding="5dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_height="40dp"
                            android:layout_width="100dp"
                            android:textSize="@dimen/font_size_15"
                            android:padding="5dp"
                            android:layout_marginLeft="10dp"
                            android:text="姓名"
                            android:gravity="center"/>

                        <TextView
                            android:id="@+id/realName"
                            android:layout_height="40dp"
                            android:layout_width="220dp"
                            android:textSize="@dimen/font_size_15"
                            android:padding="5dp"
                            android:text="张工小"
                            android:textColor="#333333"
                            android:gravity="center_vertical"
                            android:background="@drawable/shape_round_gray"/>

                        <TextView
                            android:layout_height="40dp"
                            android:layout_width="100dp"
                            android:textSize="@dimen/font_size_15"
                            android:padding="5dp"
                            android:text="性别"
                            android:gravity="center"/>

                        <TextView
                            android:id="@+id/sexs"
                            android:layout_height="40dp"
                            android:layout_width="220dp"
                            android:textSize="@dimen/font_size_15"
                            android:padding="5dp"
                            android:textColor="#333333"
                            android:text="男"
                            android:gravity="center_vertical"
                            android:background="@drawable/shape_round_gray"/>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_height="40dp"
                            android:layout_width="100dp"
                            android:textSize="@dimen/font_size_15"
                            android:padding="5dp"
                            android:gravity="center"
                            android:text="身份证号"/>

                        <TextView
                            android:id="@+id/cardId"
                            android:layout_height="40dp"
                            android:layout_width="350dp"
                            android:textSize="@dimen/font_size_15"
                            android:padding="5dp"
                            android:text="32099231911231334X"
                            android:gravity="center_vertical"
                            android:textColor="#333333"
                            android:background="@drawable/shape_round_gray"/>

                        <TextView
                            android:layout_height="40dp"
                            android:layout_width="100dp"
                            android:textSize="@dimen/font_size_15"
                            android:padding="5dp"
                            android:layout_marginLeft="10dp"
                            android:text="出生日期"
                            android:gravity="center"/>

                        <TextView
                            android:id="@+id/birthday"
                            android:layout_width="220dp"
                            android:layout_height="40dp"
                            android:background="@drawable/shape_round_gray"
                            android:padding="5dp"
                            android:text="2020-10-20"
                            android:textColor="#333333"
                            android:gravity="center_vertical"
                            android:textSize="@dimen/font_size_15" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_height="40dp"
                            android:layout_width="100dp"
                            android:textSize="@dimen/font_size_15"
                            android:padding="5dp"
                            android:gravity="center"
                            android:text="联系电话"/>

                        <TextView
                            android:id="@+id/mobilePhone"
                            android:layout_height="40dp"
                            android:layout_width="220dp"
                            android:textSize="@dimen/font_size_15"
                            android:padding="5dp"
                            android:text="15951283411"
                            android:gravity="center_vertical"
                            android:textColor="#333333"
                            android:background="@drawable/shape_round_gray"/>

                        <TextView
                            android:layout_height="40dp"
                            android:layout_width="100dp"
                            android:textSize="@dimen/font_size_15"
                            android:padding="5dp"
                            android:gravity="center"
                            android:text="地址"/>

                        <TextView
                            android:id="@+id/addressDetail"
                            android:layout_height="wrap_content"
                            android:layout_width="wrap_content"
                            android:textSize="@dimen/font_size_15"
                            android:paddingLeft="5dp"
                            android:paddingBottom="10dp"
                            android:gravity="center_vertical"
                            android:textColor="#333333"
                            android:background="@drawable/shape_round_gray"/>
                    </LinearLayout>

                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_margin="10dp"
                    android:background="#eaeaea"/>

                <TextView
                    android:id="@+id/hint3"
                    style="@style/tv_black_wrap_18"
                    android:textSize="@dimen/font_size_18"
                    android:layout_marginLeft="@dimen/space_10"
                    android:text="办理公证事项"
                    android:drawableLeft="@drawable/ac_title"
                    android:drawablePadding="5dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/shape_line_grey"/>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/cc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="15dp"
                android:paddingTop="5dp"
                android:paddingRight="15dp"
                android:paddingBottom="5dp"
                app:layout_constraintTop_toBottomOf="@+id/llCntentView"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvUpStep"
                    style="@style/tv_black_wrap_18"
                    android:layout_marginBottom="@dimen/space_20"
                    android:background="@drawable/shape_tag"
                    android:paddingLeft="15dp"
                    android:paddingTop="5dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="5dp"
                    android:text="上一步"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_20" />

                <TextView
                    android:id="@+id/tvNextStep"
                    style="@style/tv_black_wrap_18"
                    android:layout_marginBottom="@dimen/space_20"
                    android:background="@drawable/shape_tag"
                    android:paddingLeft="15dp"
                    android:paddingTop="5dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="5dp"
                    android:layout_marginLeft="20dp"
                    android:text="下一步"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_20" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>