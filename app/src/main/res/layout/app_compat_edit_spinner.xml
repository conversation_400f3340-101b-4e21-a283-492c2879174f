<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@drawable/bg_view_frame">

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/spinner_edit_text"
        style="@style/edit_spinner_edit_sty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:singleLine="true"
        android:imeOptions="actionDone"
        android:drawablePadding="@dimen/space_8"
        android:gravity="center_vertical"
        android:maxLines="1"/>

    <LinearLayout
        android:id="@+id/place"
        android:layout_width="30dp"
        android:layout_alignParentRight="true"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/spinner_expand"
            android:src="@drawable/zhcc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_centerVertical="true"
            />

    </LinearLayout>


    <View
        android:id="@+id/spinner_expand_above"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignStart="@id/place"
        android:layout_alignLeft="@id/place"
        android:layout_alignTop="@+id/spinner_edit_text"
        android:layout_alignEnd="@id/place"
        android:layout_alignRight="@id/place"
        android:layout_alignBottom="@+id/spinner_edit_text" />
</RelativeLayout>