<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.CounselingRoomViewModel" />
    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70"
            android:background="@drawable/shape_corner10_white_all" />

        <LinearLayout
            android:id="@+id/top_lay_out"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/title_bar"
            android:layout_marginHorizontal="@dimen/space_30"
            android:layout_marginTop="@dimen/space_26"
            android:layout_marginBottom="@dimen/space_10"
            android:background="@drawable/shape_corner5_white"
            android:gravity="center_vertical"
            android:padding="@dimen/space_7">

            <TextView
                android:id="@+id/tv_sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:drawableLeft="@mipmap/icon_video_room"
                android:gravity="center"
                android:text="@string/video_room"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size16"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:gravity="center">

                <View
                    android:layout_width="@dimen/space_10"
                    android:layout_height="@dimen/space_10"
                    android:layout_marginEnd="@dimen/space_5"
                    android:background="@drawable/shape_corner5_50c189_all" />

                <TextView
                    android:id="@+id/notary_office_text_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size16"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/shape_corner5_3c6af4"
                android:drawableStart="@mipmap/close_circle_white"
                android:drawablePadding="@dimen/space_4"
                android:marqueeRepeatLimit="1"
                android:paddingHorizontal="@dimen/space_10"
                android:paddingVertical="@dimen/space_5"
                android:text="@string/close_text"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size14"
                binding:onClickCommand="@{viewModel.closeVideo}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/top_lay_out"
            android:layout_marginHorizontal="@dimen/space_30"
            android:layout_marginBottom="@dimen/space_30"
            android:background="@drawable/shape_corner10_white_all"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/home_rl"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="4">

                <com.tencent.rtmp.ui.TXCloudVideoView
                    android:id="@+id/trtcRemote"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"></com.tencent.rtmp.ui.TXCloudVideoView>
                <TextView
                    android:id="@+id/tvNotaryStateOffVideo"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/space_5"
                    android:layout_marginTop="5dp"
                    android:background="@color/black"
                    android:gravity="center"
                    android:text="@string/natoryOffVideo"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size18"
                    android:visibility="gone" />

                <com.tencent.rtmp.ui.TXCloudVideoView
                    android:id="@+id/trtcMain"
                    android:layout_width="300dp"
                    android:layout_height="300dp"
                    android:layout_marginLeft="@dimen/space_5"
                    android:layout_marginTop="5dp" >
                </com.tencent.rtmp.ui.TXCloudVideoView>
                <TextView
                    android:id="@+id/tvNotaryState"
                    android:layout_width="300dp"
                    android:layout_height="300dp"
                    android:layout_marginLeft="@dimen/space_5"
                    android:layout_marginTop="5dp"
                    android:background="@color/black"
                    android:gravity="center"
                    android:text="等待公证员进入"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size18"
                    android:visibility="gone" />


            </RelativeLayout>

            <LinearLayout
                android:id="@+id/lnl_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_weight="1"
                android:orientation="vertical">

                <View
                    android:layout_width="140dp"
                    android:layout_height="1dp"
                    android:layout_gravity="center"
                    android:background="#E8E8EB" />

                <TextView
                    android:id="@+id/tvApplyInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/space_30"
                    android:layout_marginBottom="@dimen/space_30"
                    android:drawableLeft="@mipmap/icon_apply_info"
                    android:drawablePadding="@dimen/space_10"
                    android:gravity="center"
                    android:onClick="showApplyDialog"
                    android:text="@string/apply_info"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size14"
                    android:textStyle="bold" />

                <View
                    android:layout_width="140dp"
                    android:layout_height="1dp"
                    android:layout_gravity="center"
                    android:background="#E8E8EB" />

                <TextView
                    android:id="@+id/uploadMaterial_text_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/space_30"
                    android:layout_marginBottom="@dimen/space_30"
                    android:drawableLeft="@mipmap/upload_material_icon"
                    android:drawablePadding="@dimen/space_10"
                    android:gravity="center"
                    android:onClick="showMatterUploadDialog"
                    android:text="@string/uploadMaterialText"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size14"
                    android:textStyle="bold" />

                <View
                    android:layout_width="140dp"
                    android:layout_height="1dp"
                    android:layout_gravity="center"
                    android:background="#E8E8EB" />

                <TextView
                    android:id="@+id/tvLawPdf"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/space_30"
                    android:layout_marginBottom="@dimen/space_30"
                    android:drawableLeft="@mipmap/icon_law"
                    android:drawablePadding="@dimen/space_10"
                    android:gravity="center"
                    android:onClick="showLowDialog"
                    android:text="@string/readingInstrument"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size14"
                    android:textStyle="bold" />

                <View
                    android:layout_width="140dp"
                    android:layout_height="1dp"
                    android:layout_gravity="center"
                    android:background="#E8E8EB" />

                <TextView
                    android:id="@+id/tvFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/space_30"
                    android:layout_marginBottom="@dimen/space_30"
                    android:drawableLeft="@mipmap/icon_fee"
                    android:drawablePadding="@dimen/space_10"
                    android:gravity="center"
                    android:onClick="showFeeDialog"
                    android:text="@string/fee"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size14"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnl_line_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_weight="1"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="140dp"
                    android:layout_height="1dp"
                    android:layout_gravity="center"
                    android:background="#E8E8EB" />

                <TextView
                    android:id="@+id/tv_uploadMaterial"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/space_30"
                    android:layout_marginBottom="@dimen/space_30"
                    android:drawableLeft="@mipmap/upload_material_icon"
                    android:drawablePadding="@dimen/space_10"
                    android:gravity="center"
                    android:onClick="showMatterUploadDialog"
                    android:text="@string/uploadMaterialText"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size14"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>


    </RelativeLayout>
</layout>