<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="800dp"
    android:layout_height="600dp"
    android:layout_gravity="center"
    android:background="#F8F8F8">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="30dp"
        android:text="系统设置"
        android:textColor="@color/black"
        android:textSize="25sp" />

    <LinearLayout
        android:id="@+id/sys_into_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="#F8F8F8"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginLeft="15dp"
            android:gravity="center_vertical"
            android:text="请输入密码"
            android:textColor="@color/black"
            android:textSize="20sp" />

        <EditText
            android:id="@+id/sys_pwd"
            android:layout_width="300dp"
            android:layout_height="45dp"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="30dp"
            android:background="@drawable/shape_line_grey"
            android:hint="请输入6位数密码"
            android:inputType="numberPassword"
            android:maxLength="6"
            android:paddingLeft="10dp"
            android:singleLine="true"
            android:textSize="20sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="100dp"
            android:background="#F8F8F8"
            android:orientation="horizontal">

            <Button
                android:id="@+id/sys_cancel"
                android:layout_width="150dp"
                android:layout_height="45dp"
                android:background="@drawable/shape_line_grey"
                android:gravity="center"
                android:text="取消"
                android:textSize="20sp" />

            <Button
                android:id="@+id/sys_sure"
                android:layout_width="150dp"
                android:layout_height="45dp"
                android:layout_marginLeft="30dp"
                android:background="@drawable/btn_start_n"
                android:gravity="center"
                android:text="确认"
                android:textColor="@color/white"
                android:textSize="20sp" />

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/sys_camera_setting"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="#F8F8F8"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="#F8F8F8"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="环境摄像头"
                android:textColor="@color/black"
                android:textSize="20sp" />

            <CheckBox
                android:id="@+id/yinshiyun"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:button="@drawable/checkbox_state"
                android:checked="true"
                android:text="网络摄像头"
                android:textColor="@color/black"
                android:textSize="20sp" />

            <CheckBox
                android:id="@+id/localusb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:button="@drawable/checkbox_state"
                android:text="本地摄像头"
                android:textColor="@color/black"
                android:textSize="20sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/localusb_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:background="#F8F8F8"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="摄像头名称"
                android:textColor="@color/black"
                android:textSize="20sp" />

            <EditText
                android:id="@+id/sys_usbcamera"
                android:layout_width="350dp"
                android:layout_height="45dp"
                android:layout_marginLeft="10dp"
                android:background="@drawable/shape_line_grey"
                android:hint="请输入摄像头名称"
                android:maxLength="50"
                android:paddingLeft="10dp"
                android:singleLine="true"
                android:textSize="20sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="100dp"
            android:background="#F8F8F8"
            android:orientation="horizontal">

            <Button
                android:id="@+id/sys_cam_cancel"
                android:layout_width="150dp"
                android:layout_height="45dp"
                android:background="@drawable/shape_line_grey"
                android:text="取消"
                android:textSize="20sp" />

            <Button
                android:id="@+id/sys_cam_sure"
                android:layout_width="150dp"
                android:layout_height="45dp"
                android:layout_marginLeft="50dp"
                android:background="@drawable/btn_start_n"
                android:text="确认"
                android:textColor="@color/white"
                android:textSize="20sp" />

        </LinearLayout>
    </LinearLayout>
</RelativeLayout>