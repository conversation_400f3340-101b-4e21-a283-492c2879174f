<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.NotaryGridItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.NotaryGridItemViewModel" />
    </data>
    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        binding:onClickCommand="@{viewModel.itemClick}"
        binding:isThrottleFirst="@{Boolean.FALSE}" >
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            binding:imgSrc="@{viewModel.background}"
            android:scaleType="fitXY"
            android:layout_marginBottom="@dimen/space_20"
            android:layout_marginRight="@dimen/space_20" />
        <TextView
            android:id="@+id/title_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/font_size28"
            android:textStyle="bold"
            binding:text="@{viewModel.entity.label}"
            android:textAlignment="textStart"
            android:layout_marginHorizontal="@dimen/space_30"
            android:layout_marginTop="@dimen/space_30"/>

        <TextView
            android:id="@+id/subtitle_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/font_size14"
            android:textStyle="normal"
            binding:text="@{viewModel.entity.description}"
            android:lineSpacingExtra="@dimen/space_5"
            android:textAlignment="textStart"
            android:layout_below="@+id/title_text"
            android:layout_marginHorizontal="@dimen/space_30"
            android:layout_marginTop="@dimen/space_18"/>

    </RelativeLayout>


</layout>