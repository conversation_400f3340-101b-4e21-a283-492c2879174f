<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingDefaultResource">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/isProxyView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:onClick="proxyClick"
        android:src="@drawable/proxy_person_state"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/isPersonView"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/isPersonView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:onClick="personClick"
        android:src="@drawable/person_state"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/isProxyView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_50"
        android:gravity="center"
        android:text="选择角色"
        android:textSize="@dimen/font_size_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_20"
        android:gravity="center"
        android:text="为自己办理，请选择“我是本人”，为他人办理，请选择“我是代理人”"
        android:textSize="@dimen/font_size_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/backToHome"
        android:layout_width="140dp"
        android:layout_height="@dimen/space_50"
        android:layout_marginBottom="@dimen/dp_40"
        android:background="@drawable/btn_login_bg"
        android:onClick="backToMain"
        android:text="回到首页"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/next"
        app:layout_constraintStart_toStartOf="parent"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/next"
        android:layout_width="140dp"
        android:layout_height="@dimen/space_50"
        android:layout_marginBottom="@dimen/dp_40"
        android:background="@drawable/btn_login_bg"
        android:onClick="nextClick"
        android:text="下一步"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/backToHome"/>
</androidx.constraintlayout.widget.ConstraintLayout>