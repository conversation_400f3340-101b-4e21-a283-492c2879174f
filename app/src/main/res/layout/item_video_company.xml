<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <include layout="@layout/common_line" />

    <LinearLayout
        style="@style/ll_match_parent_horizontal_10_15">

        <TextView
            style="@style/tv_black_wrap_18"
            android:layout_width="@dimen/space_100"
            android:text="公司名称" />

        <TextView
            android:id="@+id/tvCompanyName"
            style="@style/tv_black_wrap_18"
            android:layout_width="match_parent"
            android:gravity="right"
            tools:text="姓名" />
    </LinearLayout>

    <include layout="@layout/common_line" />

    <LinearLayout
        style="@style/ll_match_parent_horizontal_10_15">

        <TextView
            style="@style/tv_black_wrap_18"
            android:layout_width="@dimen/space_100"
            android:text="公司法人" />

        <TextView
            android:id="@+id/tvContacts"
            style="@style/tv_black_wrap_18"
            android:layout_width="match_parent"
            android:gravity="right"
            tools:text="姓名" />
    </LinearLayout>

    <include layout="@layout/common_line" />

    <LinearLayout
        style="@style/ll_match_parent_horizontal_10_15">

        <TextView
            style="@style/tv_black_wrap_18"
            android:layout_width="@dimen/space_100"
            android:text="联系方式" />

        <TextView
            android:id="@+id/tvMobile"
            style="@style/tv_black_wrap_18"
            android:layout_width="match_parent"
            android:gravity="right"
            tools:text="姓名" />
    </LinearLayout>

    <include layout="@layout/common_line" />



    <LinearLayout
        style="@style/ll_match_parent_horizontal_10_15">

        <TextView
            style="@style/tv_black_wrap_18"
            android:layout_width="@dimen/space_100"
            android:text="公司地址" />

        <TextView
            android:id="@+id/tvAddress"
            style="@style/tv_black_wrap_18"
            android:layout_width="match_parent"
            android:gravity="right"
            tools:text="姓名" />
    </LinearLayout>

    <include layout="@layout/common_line_five" />
</LinearLayout>