<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:background="@color/colorF5F5F7"
    android:orientation="horizontal">

    <!-- 首字母行 -->
    <TextView
        android:id="@+id/tv_fistletters"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/space_30"
        android:layout_marginRight="@dimen/space_20"
        android:textColor="#000000"
        android:textStyle="bold"
        android:textAlignment="textStart"
        android:textSize="@dimen/font_size16" />

    <!--    <TextView-->
    <!--        android:id="@+id/tv_info"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:layout_gravity="center_vertical"-->
    <!--        android:layout_marginLeft="5dp"-->
    <!--        android:layout_weight="1.0"-->
    <!--        android:gravity="center_vertical"-->
    <!--        android:paddingTop="10dp"-->
    <!--        android:paddingBottom="10dp" />-->
    <com.gc.notarizationpc.myview.FlowLayout
        android:id="@+id/item_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/space_10" />
</LinearLayout>