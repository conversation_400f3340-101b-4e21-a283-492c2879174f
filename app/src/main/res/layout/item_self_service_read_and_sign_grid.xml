<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.SelfServiceReadAndSignGridItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelfServiceReadAndSignGridItemViewModel" />
    </data>
<LinearLayout
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/space_20"
    android:layout_marginRight="@dimen/space_50"
    android:orientation="vertical">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableLeft="@mipmap/split_vertical_icon"
        android:textStyle="bold"
        android:text="@{viewModel.entity.documentTypeName}"
        android:textColor="@color/color3333"
        android:singleLine="true"
        android:textSize="@dimen/font_size16"/>
    <ImageView
        android:layout_width="@dimen/space_90"
        android:layout_height="@dimen/space_90"
        android:layout_marginTop="@dimen/space_15"
        android:src="@mipmap/erroimage"
        android:scaleType="fitXY"
        app:imageUrl='@{viewModel.entity.firstDocUrl}'
        app:onClickCommand="@{viewModel.previewDoc}"
        app:placeholderRes="@{@drawable/erroimage}"
        />
</LinearLayout>

</layout>