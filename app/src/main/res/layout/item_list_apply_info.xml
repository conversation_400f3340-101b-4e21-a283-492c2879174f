<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/lnl_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_key"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_5"
            android:layout_weight="1.1"
            android:gravity="right"
            android:padding="@dimen/space_5"
            android:text="申请日期"
            android:textColor="@color/color6666"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/tv_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_5"
            android:layout_weight="4"
            android:background="@drawable/shape_corner4_f1f1f2"
            android:paddingHorizontal="@dimen/space_15"
            android:paddingVertical="@dimen/space_8"
            android:text="2024-03-20"
            android:textColor="#BFBFBF"
            android:textSize="@dimen/font_size14" />


    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_below="@+id/lnl_content"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="@dimen/space_30"
        android:layout_marginVertical="@dimen/space_10"
        android:background="#E8E8EB"
        android:visibility="gone"></View>
</RelativeLayout>

