<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mains"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/bg_shouye" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/space_10"
                android:onClick="certificateClick"
                android:scaleType="centerCrop"
                android:src="@drawable/bt_banzheng" />

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="advisoryClick"
                android:scaleType="centerCrop"
                android:src="@drawable/bt_legal" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_10">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/space_10"
                android:onClick="inquireClick"
                android:scaleType="centerCrop"
                android:src="@drawable/bt_chaxun" />

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="suggestClick"
                android:scaleType="centerCrop"
                android:src="@drawable/bt_jianyi" />
        </LinearLayout>
    </LinearLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="200dp"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="180dp"
            android:layout_height="180dp"
            android:onClick="vedioClick"
            android:background="@drawable/mian_two" />

        <ImageView
            android:layout_width="180dp"
            android:layout_height="180dp"
            android:onClick="certificateClick"
            android:layout_marginLeft="10dp"
            android:background="@drawable/main_one" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="380dp"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="180dp"
            android:layout_height="180dp"
            android:onClick="advisoryClick"
            android:background="@drawable/main_four" />

        <ImageView
            android:layout_width="180dp"
            android:layout_height="180dp"
            android:layout_marginLeft="10dp"
            android:onClick="suggestClick"
            android:background="@drawable/main_five" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_upgrade"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="@dimen/space_20"
        android:layout_toRightOf="@+id/tvVersion"
        android:padding="@dimen/space_15"
        android:text="版本检测" />

    <TextView
        android:id="@+id/tvVersion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:padding="@dimen/space_15"
        android:text="版本号" />


    <TextView
        android:id="@+id/tvRegister"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:onClick="registerClick"
        android:padding="@dimen/space_10"
        android:text="注册设备" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSplash"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:onClick="splashClick"
        android:scaleType="centerCrop"
        tools:visibility="gone" />
</RelativeLayout>
