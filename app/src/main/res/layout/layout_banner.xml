<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#90000000">

    <com.youth.banner.Banner
        android:id="@+id/banner"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/space_20"
        android:layout_marginTop="10dp"
        app:banner_auto_loop="false"
        app:banner_infinite_loop="false"
        app:banner_indicator_normal_color="@color/gary_82"
        app:banner_indicator_selected_color="@color/colorPrimary"
        app:layout_constraintBottom_toTopOf="@id/btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn"
        android:layout_width="140dp"
        android:layout_height="@dimen/space_50"
        android:layout_marginBottom="@dimen/space_20"
        android:background="@drawable/btn_login_bg"
        android:onClick="lookClick"
        android:text="已查阅"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>