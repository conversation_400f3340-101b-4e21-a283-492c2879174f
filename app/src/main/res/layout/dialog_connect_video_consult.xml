<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/vehiclemanager_pops_rl"
    android:layout_width="480dp"
    android:layout_height="wrap_content"
    android:padding="@dimen/space_20"
    android:background="@drawable/shape_corner_white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/second"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:text="0s"
            android:layout_marginTop="@dimen/space_15"
            android:textColor="@color/gray_4c4c4c"
            android:textSize="20sp"
            android:textStyle="bold"
            android:background="@drawable/shape_round_gray"
            android:paddingLeft="@dimen/space_15"
            android:paddingRight="@dimen/space_15"
            android:paddingTop="@dimen/space_6"
            android:paddingBottom="@dimen/space_6"/>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_connect_video_consult"
            android:layout_gravity="center"
            android:layout_marginTop="40dp"/>

        <TextView
            android:id="@+id/tv_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="正在连接..."
            android:layout_marginTop="@dimen/space_15"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"/>

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="40dp"
            android:text="连接时间过长可选择结束等待或者预约办理"
            android:textColor="@color/black"
            android:textSize="18sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_disconnect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="@dimen/space_30"
                android:paddingRight="@dimen/space_30"
                android:background="@drawable/shape_stoke_blue"
                android:gravity="center"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="结束等待"
                android:textColor="#6495ED"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>