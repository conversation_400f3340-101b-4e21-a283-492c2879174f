<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_corner8_white_left"
    android:orientation="vertical"
    android:padding="@dimen/space_20"
    android:paddingBottom="@dimen/space_8">

    <RelativeLayout
        android:id="@+id/rnl_search_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title_bar"
        android:padding="@dimen/space_15">

        <TextView
            android:id="@+id/tv_location"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:drawableLeft="@mipmap/icon_fee_title"
            android:drawablePadding="@dimen/space_5"
            android:text="@string/fee"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size18"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_refresh"
            android:layout_width="@dimen/space_40"
            android:layout_height="@dimen/space_40"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/space_40"
            android:layout_toLeftOf="@+id/iv_close"
            android:padding="@dimen/space_10"
            android:src="@mipmap/icon_refresh" />


        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/space_40"
            android:layout_height="@dimen/space_40"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/space_10"
            android:src="@mipmap/icon_close" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="@dimen/space_15"
        android:background="#E8E8EB" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/space_15"
        android:layout_marginTop="@dimen/space_15">

        <View
            android:layout_width="4dp"
            android:layout_height="12dp"
            android:layout_centerVertical="true"
            android:background="@drawable/shape_corner2_gradient" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/space_10"
            android:text="@string/notarization_fee"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14"
            android:textStyle="bold" />
    </RelativeLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/space_8"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_15"
            android:background="@drawable/shape_corner5_white_border_gray"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_corner5_f7f2fe"
                android:orientation="horizontal"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_notary_item"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:layout_weight="3"
                    android:gravity="left"
                    android:padding="@dimen/space_8"
                    android:text="@string/notarization_item"
                    android:textColor="#8A8E99"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_item"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:layout_weight="2"
                    android:gravity="center"
                    android:text="@string/project"
                    android:paddingHorizontal="@dimen/space_15"
                    android:paddingVertical="@dimen/space_8"
                    android:textColor="#8A8E99"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_price"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:layout_weight="1.5"
                    android:gravity="center"
                    android:text="@string/amount_receivable_fee"
                    android:paddingHorizontal="@dimen/space_15"
                    android:paddingVertical="@dimen/space_8"
                    android:textColor="#8A8E99"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginHorizontal="@dimen/space_15"
                android:background="#E8E8EB" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_fee"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/space_5" />

            <LinearLayout
                android:id="@+id/lnl_total"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_key"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:layout_weight="1.1"
                    android:gravity="left"
                    android:padding="@dimen/space_8"
                    android:text="@string/total_fee"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_total"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_5"
                    android:layout_weight="4"
                    android:gravity="right"
                    android:paddingHorizontal="@dimen/space_15"
                    android:paddingVertical="@dimen/space_8"
                    android:textColor="#E1594D"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tv_offline_pay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:layout_marginTop="@dimen/space_25"
        android:layout_marginRight="@dimen/space_15"
        android:background="@drawable/shape_corner5_3c6af4"
        android:paddingHorizontal="@dimen/space_30"
        android:paddingVertical="@dimen/space_10"
        android:text="@string/offline_fee"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size16"
        android:visibility="gone" />

</LinearLayout>