<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/bg_shouye"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1.0" />

    <com.gc.notarizationpc.widget.RoundTextureView
        android:id="@+id/previewView"
        android:layout_width="480dp"
        android:layout_height="480dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/backClicked"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        android:padding="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:textColor="@color/white"
        android:background="@drawable/new_back" />

    <com.example.framwork.widget.MaterialProgress
        android:layout_width="500dp"
        android:layout_height="500dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:mp_radius="250dp" />

    <LinearLayout
        android:layout_width="@dimen/space_500"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_10"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="@id/previewView"
        app:layout_constraintStart_toStartOf="@id/previewView"
        app:layout_constraintTop_toBottomOf="@id/previewView">

        <LinearLayout
            android:layout_width="@dimen/space_166"
            android:layout_height="@dimen/space_100"
            android:gravity="center"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="@dimen/space_60"
                android:layout_height="@dimen/space_60"
                android:scaleType="centerCrop"
                android:src="@drawable/sm1" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="请将人脸置于屏幕中央"></TextView>
        </LinearLayout>

        <LinearLayout
            android:layout_width="@dimen/space_166"
            android:layout_height="@dimen/space_100"
            android:gravity="center"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="@dimen/space_60"
                android:layout_height="@dimen/space_60"
                android:scaleType="centerCrop"
                android:src="@drawable/sm2" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="摘下眼镜面部无遮挡"></TextView>
        </LinearLayout>

        <LinearLayout
            android:layout_width="@dimen/space_166"
            android:layout_height="@dimen/space_100"
            android:gravity="center"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="@dimen/space_60"
                android:layout_height="@dimen/space_60"
                android:scaleType="centerCrop"
                android:src="@drawable/sm3" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="保持现场光线充足"></TextView>
        </LinearLayout>


    </LinearLayout>


    <LinearLayout
        android:id="@+id/l_result"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#50B4DCE7"
        android:gravity="top|center_horizontal"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/previewView"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_50"
            android:layout_marginTop="@dimen/space_60"
            android:layout_marginBottom="@dimen/space_30"
            android:gravity="center"
            android:text="识别结果"
            android:textSize="30dp"></TextView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/space_10">

                <ImageView
                    android:id="@+id/idImg"
                    android:layout_width="@dimen/space_120"
                    android:layout_height="@dimen/space_120"
                    android:scaleType="centerCrop" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="底库照片"></TextView>
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:background="@color/white"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/space_10">

                <ImageView
                    android:id="@+id/headerImg"
                    android:layout_width="@dimen/space_120"
                    android:layout_height="@dimen/space_120"
                    android:scaleType="centerCrop" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="当前照片"></TextView>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:orientation="horizontal"
            android:gravity="left|top">
            <TextView
                android:layout_width="@dimen/space_100"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:textSize="@dimen/font_size_16"
                android:gravity="right"
                android:text="姓&#12288;&#12288;名：" />
            <TextView
                android:id="@+id/cardInfoTv_name"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:layout_weight="1"
                android:gravity="left"
                android:textColor="#FF458AB0"
                android:textSize="@dimen/font_size_16"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:orientation="horizontal"
            android:gravity="left|top">
            <TextView
                android:layout_width="@dimen/space_100"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:textSize="@dimen/font_size_16"
                android:gravity="right"
                android:text="民&#12288;&#12288;族：" />
            <TextView
                android:id="@+id/cardInfoTv_nation"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:layout_weight="1"
                android:gravity="left"
                android:text=""
                android:textColor="#FF458AB0"
                android:textSize="@dimen/font_size_16"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:orientation="horizontal"
            android:gravity="left|top">
            <TextView
                android:layout_width="@dimen/space_100"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:textSize="@dimen/font_size_16"
                android:gravity="right"
                android:text="性&#12288;&#12288;别：" />
            <TextView
                android:id="@+id/cardInfoTv_sex"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:layout_weight="1"
                android:gravity="left"
                android:text=""
                android:textColor="#FF458AB0"
                android:textSize="@dimen/font_size_16"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:orientation="horizontal"
            android:gravity="left|top">
            <TextView
                android:layout_width="@dimen/space_100"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:textSize="@dimen/font_size_16"
                android:gravity="right"
                android:text="身份证号：" />
            <TextView
                android:id="@+id/cardInfoTv_idcard"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:layout_weight="1"
                android:gravity="left"
                android:text=""
                android:textColor="#FF458AB0"
                android:textSize="@dimen/font_size_16"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:orientation="horizontal"
            android:gravity="left|top">
            <TextView
                android:layout_width="@dimen/space_100"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:textSize="@dimen/font_size_16"
                android:gravity="right"
                android:text="地&#12288;&#12288;址：" />
            <TextView
                android:id="@+id/cardInfoTv_address"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:layout_weight="1"
                android:gravity="left"
                android:text=""
                android:textColor="#FF458AB0"
                android:textSize="@dimen/font_size_16"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:orientation="horizontal"
            android:gravity="left|top">
            <TextView
                android:layout_width="@dimen/space_100"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:textSize="@dimen/font_size_16"
                android:gravity="right"
                android:text="签发机关：" />
            <TextView
                android:id="@+id/cardInfoTv_depart"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:layout_weight="1"
                android:gravity="left"
                android:text=""
                android:textColor="#FF458AB0"
                android:textSize="@dimen/font_size_16"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:orientation="horizontal"
            android:gravity="left|top">
            <TextView
                android:layout_width="@dimen/space_100"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:textSize="@dimen/font_size_16"
                android:gravity="right"
                android:text="有效期限：" />
            <TextView
                android:id="@+id/cardInfoTv_validityTime"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:lineSpacingExtra="@dimen/space_10"
                android:padding="5dp"
                android:layout_weight="1"
                android:gravity="left"
                android:text=""
                android:textColor="#FF458AB0"
                android:textSize="@dimen/font_size_16"/>
        </LinearLayout>
    </LinearLayout>

    <com.serenegiant.arcface.widget.FaceRectView
        android:id="@+id/faceRectView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>