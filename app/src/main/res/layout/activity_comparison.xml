<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.CompariseViewModel" />
    </data>

    <RelativeLayout
        android:id="@+id/InquiryCertification_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:visibility="visible">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/title_bar"
            android:layout_margin="@dimen/space_30"
            android:background="@drawable/shape_corner10_white_all"
            android:gravity="center"
            android:padding="@dimen/space_30">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guidelinePercent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.75" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="centerCrop"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="1.0" />

                <com.gc.notarizationpc.widget.RoundTextureView
                    android:id="@+id/previewView"
                    android:layout_width="480dp"
                    android:layout_height="480dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@+id/guidelinePercent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/backClicked"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="20dp"
                    android:background="@mipmap/icon_back"
                    android:padding="10dp"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.gc.notarizationpc.widget.MaterialProgress
                    android:layout_width="500dp"
                    android:layout_height="500dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@+id/guidelinePercent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:mp_radius="250dp" />

                <LinearLayout
                    android:layout_width="500dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_15"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="@id/previewView"
                    app:layout_constraintStart_toStartOf="@id/previewView"
                    app:layout_constraintTop_toBottomOf="@id/previewView">

                    <TextView
                        android:layout_width="500dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/comparison_hint"
                        android:textColor="@color/color3333"></TextView>

                    <!--                    <LinearLayout-->
                    <!--                        android:layout_width="@dimen/space_166"-->
                    <!--                        android:layout_height="@dimen/space_100"-->
                    <!--                        android:gravity="center"-->
                    <!--                        android:orientation="vertical">-->

                    <!--                        <androidx.appcompat.widget.AppCompatImageView-->
                    <!--                            android:layout_width="@dimen/space_60"-->
                    <!--                            android:layout_height="@dimen/space_60"-->
                    <!--                            android:scaleType="centerCrop"-->
                    <!--                            android:src="@mipmap/sm1" />-->

                    <!--                        <TextView-->
                    <!--                            android:layout_width="match_parent"-->
                    <!--                            android:layout_height="match_parent"-->
                    <!--                            android:gravity="center"-->
                    <!--                            android:text="请将人脸置于屏幕中央"-->
                    <!--                            android:textColor="@color/color3333"></TextView>-->
                    <!--                    </LinearLayout>-->

                    <!--                    <LinearLayout-->
                    <!--                        android:layout_width="@dimen/space_166"-->
                    <!--                        android:layout_height="@dimen/space_100"-->
                    <!--                        android:gravity="center"-->
                    <!--                        android:orientation="vertical">-->

                    <!--                        <androidx.appcompat.widget.AppCompatImageView-->
                    <!--                            android:layout_width="@dimen/space_60"-->
                    <!--                            android:layout_height="@dimen/space_60"-->
                    <!--                            android:scaleType="centerCrop"-->
                    <!--                            android:src="@mipmap/sm2" />-->

                    <!--                        <TextView-->
                    <!--                            android:layout_width="match_parent"-->
                    <!--                            android:layout_height="match_parent"-->
                    <!--                            android:gravity="center"-->
                    <!--                            android:text="摘下眼镜面部无遮挡"></TextView>-->
                    <!--                    </LinearLayout>-->

                    <!--                    <LinearLayout-->
                    <!--                        android:layout_width="@dimen/space_166"-->
                    <!--                        android:layout_height="@dimen/space_100"-->
                    <!--                        android:gravity="center"-->
                    <!--                        android:orientation="vertical">-->

                    <!--                        <androidx.appcompat.widget.AppCompatImageView-->
                    <!--                            android:layout_width="@dimen/space_60"-->
                    <!--                            android:layout_height="@dimen/space_60"-->
                    <!--                            android:scaleType="centerCrop"-->
                    <!--                            android:src="@mipmap/sm3" />-->

                    <!--                        <TextView-->
                    <!--                            android:layout_width="match_parent"-->
                    <!--                            android:layout_height="match_parent"-->
                    <!--                            android:gravity="center"-->
                    <!--                            android:text="保持现场光线充足"></TextView>-->
                    <!--                    </LinearLayout>-->


                </LinearLayout>

                <!--右侧信息-->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:gravity="top|center_horizontal"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/previewView"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_20"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/checkInformation_text_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/space_20"
                            android:drawableStart="@mipmap/check_idcard"
                            android:text="@string/checkInformation"
                            android:textColor="@color/color3333"
                            android:textSize="@dimen/font_size18"
                            android:textStyle="bold" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_weight="1"
                            android:background="#E8E8EB" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_193"
                        android:layout_marginTop="@dimen/space_25"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/imgHead"
                            android:layout_width="@dimen/space_136"
                            android:layout_height="176dp"
                            android:layout_marginEnd="@dimen/space_20"
                            android:background="@drawable/shape_corner5_f5f5f7"
                            android:scaleType="fitXY" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="@dimen/space_193"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                tools:ignore="NestedWeights">

                                <TextView
                                    android:id="@+id/name_textView_id"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/nameString"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tv_name"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_5"
                                    android:background="@drawable/shape_corner4_f1f1f2"
                                    android:paddingHorizontal="@dimen/space_20"
                                    android:paddingVertical="@dimen/space_7"
                                    android:singleLine="true"
                                    android:textColor="@color/colorBFBFBF"
                                    android:textSize="@dimen/font_size14" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_marginTop="@dimen/space_15"
                                android:layout_weight="1"
                                android:gravity="top"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/sexString"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tv_sex"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_5"
                                    android:background="@drawable/shape_corner4_f1f1f2"
                                    android:paddingHorizontal="@dimen/space_20"
                                    android:paddingVertical="@dimen/space_7"
                                    android:singleLine="true"
                                    android:textColor="@color/colorBFBFBF"
                                    android:textSize="@dimen/font_size14" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_marginTop="@dimen/space_15"
                                android:layout_weight="1"
                                android:gravity="top"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/birthDay_textView_id"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/birthdayString"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tv_birthday"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_5"
                                    android:background="@drawable/shape_corner4_f1f1f2"
                                    android:paddingHorizontal="@dimen/space_20"
                                    android:paddingVertical="@dimen/space_7"
                                    android:singleLine="true"
                                    android:textColor="@color/colorBFBFBF"
                                    android:textSize="@dimen/font_size14" />
                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_15"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/space_23"
                            android:layout_weight="1"
                            android:gravity="top"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/nation_textView_id"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/nationString"
                                android:textColor="@color/color666666"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_nation"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_5"
                                android:background="@drawable/shape_corner4_f1f1f2"
                                android:paddingHorizontal="@dimen/space_20"
                                android:paddingVertical="@dimen/space_7"
                                android:singleLine="true"
                                android:textColor="@color/colorBFBFBF"
                                android:textSize="@dimen/font_size14" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="top"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/idCardNumber_textView_id"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/idCardNumber"
                                android:textColor="@color/color666666"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_idcard"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_5"
                                android:background="@drawable/shape_corner4_f1f1f2"
                                android:paddingHorizontal="@dimen/space_20"
                                android:paddingVertical="@dimen/space_7"
                                android:singleLine="true"
                                android:textColor="@color/colorBFBFBF"
                                android:textSize="@dimen/font_size14" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_15"
                        android:gravity="top"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/address_textView_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/addressString"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_address"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_7"
                            android:singleLine="true"
                            android:textColor="@color/colorBFBFBF"
                            android:textSize="@dimen/font_size14" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_15"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="top"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/issuingAuthority_textView_id"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/issuingAuthorityString"
                                android:textColor="@color/color666666"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_depart"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_5"
                                android:background="@drawable/shape_corner4_f1f1f2"
                                android:paddingHorizontal="@dimen/space_20"
                                android:paddingVertical="@dimen/space_7"
                                android:singleLine="true"
                                android:textColor="@color/colorBFBFBF"
                                android:textSize="@dimen/font_size14" />
                        </LinearLayout>


                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginTop="@dimen/space_15"
                        android:gravity="top"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/expireTime_textView_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/expireTimeString"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_expire"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_7"
                            android:singleLine="true"
                            android:textColor="@color/colorBFBFBF"
                            android:textSize="@dimen/font_size14" />
                    </LinearLayout>


<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:gravity="bottom"-->
<!--                        android:orientation="horizontal">-->

<!--                        <TextView-->
<!--                            android:id="@+id/backBtn_text_id"-->
<!--                            android:layout_width="0dp"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_marginEnd="@dimen/space_15"-->
<!--                            android:layout_weight="1"-->
<!--                            android:background="@drawable/shape_corner8_e8e8e8"-->
<!--                            android:onClick="goBackEvent"-->
<!--                            android:paddingVertical="@dimen/space_16"-->
<!--                            android:text="@string/back"-->
<!--                            android:textAlignment="center"-->
<!--                            android:textColor="@color/color3333"-->
<!--                            android:textSize="@dimen/font_size16"-->
<!--                            binding:onClickCommand="@{viewModel.goBackEvent}" />-->

<!--                        <TextView-->
<!--                            android:id="@+id/confirmBtn_text_id"-->
<!--                            android:layout_width="0dp"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            android:background="@drawable/shape_corner8_2568ff"-->
<!--                            android:paddingVertical="@dimen/space_16"-->
<!--                            android:text="@string/confirm"-->
<!--                            android:textAlignment="center"-->
<!--                            android:textColor="@color/white"-->
<!--                            android:textSize="@dimen/font_size16"-->
<!--                            binding:onClickCommand="@{viewModel.decideEvent}" />-->
<!--                    </LinearLayout>-->

                </LinearLayout>

                <com.serenegiant.arcface.widget.FaceRectView
                    android:id="@+id/faceRectView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </RelativeLayout>
</layout>