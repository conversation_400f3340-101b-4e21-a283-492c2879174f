<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">
<data>
    <variable
        name="viewModel"
        type="com.gc.notarizationpc.ui.viewmodel.InquiryCertificationHomeViewModel" />
</data>

    <RelativeLayout
        android:id="@+id/InquiryCertification_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:visibility="visible">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/title_bar"
            android:layout_marginStart="@dimen/space_30"
            android:layout_marginTop="@dimen/space_30"
            android:layout_marginEnd="@dimen/space_30"
            android:layout_marginBottom="@dimen/space_30"
            android:background="@drawable/shape_corner10_white_all"
            android:gravity="center"
            android:padding="@dimen/space_30">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/space_30"
                android:layout_weight="5"
                android:background="@drawable/shape_corner8_f5f5f7"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/idCard_read_image_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:src="@mipmap/read_idcard_infor" />

                <TextView
                    android:id="@+id/readIdCardInformationTitle_text_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/space_39"
                    android:text="@string/readIdCardInformationTitle"
                    android:textAlignment="center"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size28"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/readIdCardInformationSubTitle_text_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/space_23"
                    android:text="@string/readIdCardInformationSubTitle"
                    android:textAlignment="center"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size18" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_20"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/checkInformation_text_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/space_20"
                        android:drawableStart="@mipmap/check_idcard"
                        android:text="@string/checkInformation"
                        android:textColor="@color/color3333"
                        android:textSize="@dimen/font_size18"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1"
                        android:background="#E8E8EB" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_193"
                    android:layout_marginTop="@dimen/space_25"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/imgHead"
                        android:layout_width="@dimen/space_136"
                        android:layout_height="@dimen/space_193"
                        android:layout_marginEnd="@dimen/space_20"
                        android:background="@drawable/shape_corner5_f5f5f7"
                        android:scaleType="fitXY" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="@dimen/space_193"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            tools:ignore="NestedWeights">

                            <TextView
                                android:id="@+id/name_textView_id"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/nameString"
                                android:textColor="@color/color666666"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_5"
                                android:background="@drawable/shape_corner4_f1f1f2"
                                android:paddingHorizontal="@dimen/space_20"
                                android:paddingVertical="@dimen/space_7"
                                android:singleLine="true"
                                android:textColor="@color/colorBFBFBF"
                                android:textSize="@dimen/font_size14" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_marginTop="@dimen/space_15"
                            android:layout_weight="1"
                            android:gravity="top"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/sexString"
                                android:textColor="@color/color666666"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_sex"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_5"
                                android:background="@drawable/shape_corner4_f1f1f2"
                                android:paddingHorizontal="@dimen/space_20"
                                android:paddingVertical="@dimen/space_7"
                                android:singleLine="true"
                                android:textColor="@color/colorBFBFBF"
                                android:textSize="@dimen/font_size14" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_marginTop="@dimen/space_15"
                            android:layout_weight="1"
                            android:gravity="top"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/birthDay_textView_id"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/birthdayString"
                                android:textColor="@color/color666666"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_birthday"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_5"
                                android:background="@drawable/shape_corner4_f1f1f2"
                                android:paddingHorizontal="@dimen/space_20"
                                android:paddingVertical="@dimen/space_7"
                                android:singleLine="true"
                                android:textColor="@color/colorBFBFBF"
                                android:textSize="@dimen/font_size14" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_15"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/space_23"
                        android:layout_weight="1"
                        android:gravity="top"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/nation_textView_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/nationString"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_nation"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_7"
                            android:singleLine="true"
                            android:textColor="@color/colorBFBFBF"
                            android:textSize="@dimen/font_size14" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="top"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/idCardNumber_textView_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/idCardNumber"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_idcard"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_7"
                            android:singleLine="true"
                            android:textColor="@color/colorBFBFBF"
                            android:textSize="@dimen/font_size14" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_15"
                    android:gravity="top"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/address_textView_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/addressString"
                        android:textColor="@color/color666666"
                        android:textSize="@dimen/font_size14"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_5"
                        android:background="@drawable/shape_corner4_f1f1f2"
                        android:paddingHorizontal="@dimen/space_20"
                        android:paddingVertical="@dimen/space_7"
                        android:singleLine="true"
                        android:textColor="@color/colorBFBFBF"
                        android:textSize="@dimen/font_size14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_15"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/space_23"
                        android:layout_weight="1"
                        android:gravity="top"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/issuingAuthority_textView_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/issuingAuthorityString"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_depart"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_7"
                            android:singleLine="true"
                            android:textColor="@color/colorBFBFBF"
                            android:textSize="@dimen/font_size14" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="top"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/expireTime_textView_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/expireTimeString"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_expire"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_7"
                            android:singleLine="true"
                            android:textColor="@color/colorBFBFBF"
                            android:textSize="@dimen/font_size14" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="bottom"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/backBtn_text_id"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/space_15"
                        android:layout_weight="1"
                        android:background="@drawable/shape_corner8_e8e8e8"
                        android:onClick="goBackEvent"
                        android:paddingVertical="@dimen/space_16"
                        android:text="@string/back"
                        android:textAlignment="center"
                        android:textColor="@color/color3333"
                        android:textSize="@dimen/font_size16"
                        binding:onClickCommand="@{viewModel.goBackEvent}"
                        binding:isThrottleFirst="@{Boolean.FALSE}" />

                    <TextView
                        android:id="@+id/confirmBtn_text_id"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/shape_corner8_2568ff"
                        android:paddingVertical="@dimen/space_16"
                        android:text="@string/confirm"
                        android:textAlignment="center"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size16"
                        binding:onClickCommand="@{viewModel.decideEvent}"
                        binding:isThrottleFirst="@{Boolean.FALSE}" />
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </RelativeLayout>
</layout>
