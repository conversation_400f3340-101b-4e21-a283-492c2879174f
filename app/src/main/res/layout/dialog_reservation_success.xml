<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_corner10_white_all"
    android:gravity="center"
    android:layout_gravity="center"
    android:paddingVertical="@dimen/space_60"
    android:paddingHorizontal="@dimen/space_80"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/reservation_success"
        android:textColor="@drawable/bg_white_and_3333"
        android:textSize="@dimen/font_size18" />

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/space_60"
        android:background="@drawable/shape_corner8_2568ff"
        android:paddingHorizontal="@dimen/space_60"
        android:paddingVertical="@dimen/space_15"
        android:text="@string/confirm"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size14" />
</LinearLayout>
