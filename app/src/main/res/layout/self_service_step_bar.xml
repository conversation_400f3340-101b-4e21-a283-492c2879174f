<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin_title"
    android:layout_width="match_parent"
    android:layout_marginHorizontal="@dimen/space_30"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/rnl_progress_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/space_70"
        android:layout_marginTop="@dimen/space_30"
        android:background="@drawable/shape_corner10_white_all"
        android:gravity="center"
        android:paddingHorizontal="@dimen/space_40">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="15"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_first_num"
                android:layout_width="@dimen/space_30"
                android:layout_height="@dimen/space_30"
                android:background="@drawable/shape_circle_3c6af4"
                android:gravity="center"
                android:text="1"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size16"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_first"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_toRightOf="@+id/tv_first_num"
                android:gravity="center"
                android:text="@string/shenban_info_hint"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14"
                android:textStyle="bold" />
        </LinearLayout>

        <View
            android:id="@+id/view_first"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/space_10"
            android:layout_toRightOf="@+id/tv_first"
            android:layout_weight="19"
            android:background="#D0D2D6" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="15"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_second_num"
                android:layout_width="@dimen/space_30"
                android:layout_height="@dimen/space_30"
                android:layout_marginLeft="@dimen/space_5"
                android:layout_toRightOf="@+id/view_first"
                android:background="@drawable/shape_circle_cccccc"
                android:gravity="center"
                android:text="2"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size16"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_second"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_toRightOf="@+id/tv_second_num"
                android:gravity="center"
                android:text="@string/upload_material_hint"
                android:textColor="@color/color_A5A5A5"
                android:textSize="@dimen/font_size14"
                android:textStyle="bold" />
        </LinearLayout>


        <View
            android:id="@+id/view_second"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/space_5"
            android:layout_toRightOf="@+id/tv_second"
            android:layout_weight="19"
            android:background="#D0D2D6" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="15"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_three_num"
                android:layout_width="@dimen/space_30"
                android:layout_height="@dimen/space_30"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_toRightOf="@+id/view_second"
                android:background="@drawable/shape_circle_cccccc"
                android:gravity="center"
                android:text="3"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size16"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_three"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_toRightOf="@+id/tv_three_num"
                android:gravity="center"
                android:text="@string/doc_read_hint"
                android:textColor="@color/color_A5A5A5"
                android:textSize="@dimen/font_size14"
                android:textStyle="bold" />
        </LinearLayout>

        <View
            android:id="@+id/view_three"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/space_10"
            android:layout_toRightOf="@+id/tv_three"
            android:layout_weight="19"
            android:background="#D0D2D6"
            android:visibility="gone"/>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="15"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_four_num"
                android:layout_width="@dimen/space_30"
                android:layout_height="@dimen/space_30"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_toRightOf="@+id/view_three"
                android:background="@drawable/shape_circle_cccccc"
                android:gravity="center"
                android:text="4"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size16"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_four"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_toRightOf="@+id/tv_four_num"
                android:gravity="center"
                android:text="@string/complete_self_notarization_hint"
                android:textColor="@color/color_A5A5A5"
                android:textSize="@dimen/font_size14"
                android:textStyle="bold" />
        </LinearLayout>

    </LinearLayout>


</RelativeLayout>