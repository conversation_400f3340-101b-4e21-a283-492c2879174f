<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">
<data>
    <variable
        name="viewModel"
        type="com.gc.notarizationpc.ui.viewmodel.IDCardCompariseMobileViewModel" />
</data>

    <RelativeLayout
        android:id="@+id/InquiryCertification_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:visibility="visible">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/title_bar"
            android:layout_marginStart="@dimen/space_30"
            android:layout_marginTop="@dimen/space_30"
            android:layout_marginEnd="@dimen/space_30"
            android:layout_marginBottom="@dimen/space_30"
            android:background="@drawable/shape_corner10_white_all"
            android:gravity="center"
            android:padding="@dimen/space_30">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/space_30"
                android:layout_weight="5"
                android:background="@drawable/shape_corner8_f5f5f7"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/idCard_read_image_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:src="@mipmap/read_idcard_infor" />

                <TextView
                    android:id="@+id/readIdCardInformationTitle_text_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/space_39"
                    android:text="@string/readIdCardInformationTitle"
                    android:textAlignment="center"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size28"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/readIdCardInformationSubTitle_text_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/space_23"
                    android:text="@string/readIdCardInformationSubTitle"
                    android:textAlignment="center"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size18" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_20"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/checkInformation_text_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/space_20"
                        android:drawableStart="@mipmap/check_idcard"
                        android:text="@string/shenfen_yz"
                        android:textColor="@color/color3333"
                        android:textSize="@dimen/font_size18"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1"
                        android:background="#E8E8EB" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_25"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            tools:ignore="NestedWeights">

                            <TextView
                                android:id="@+id/name_textView_id"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/nameString"
                                android:textColor="@color/color666666"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_5"
                                android:background="@drawable/shape_corner4_f1f1f2"
                                android:paddingHorizontal="@dimen/space_20"
                                android:paddingVertical="@dimen/space_10"
                                android:singleLine="true"
                                android:textColor="@color/color3333"
                                android:textSize="@dimen/font_size14" />
                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_marginTop="@dimen/space_25"
                            android:layout_weight="1"
                            android:gravity="top"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/idCardNumber"
                                android:textColor="@color/color666666"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_idcard"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_5"
                                android:background="@drawable/shape_corner4_f1f1f2"
                                android:paddingHorizontal="@dimen/space_20"
                                android:paddingVertical="@dimen/space_10"
                                android:singleLine="true"
                                android:textColor="@color/color3333"
                                android:textSize="@dimen/font_size14" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_marginTop="@dimen/space_25"
                            android:layout_weight="1"
                            android:gravity="top"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/birthDay_textView_id"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/phone_num"
                                android:textColor="@color/color666666"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <EditText
                                android:id="@+id/edt_phone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_5"
                                android:background="@drawable/shape_corner5_3d99f5_border_white"
                                android:paddingHorizontal="@dimen/space_20"
                                android:paddingVertical="@dimen/space_10"
                                android:singleLine="true"
                                android:text="@={viewModel.phone}"
                                android:inputType="phone"
                                android:maxLength="11"
                                android:imeOptions="actionDone"
                                android:digits="0123456789"
                                android:textColor="@color/color3333"
                                android:textSize="@dimen/font_size14" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_25"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/space_23"
                        android:layout_weight="1.5"
                        android:gravity="top"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/issuingAuthority_textView_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/verifycode"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <EditText
                            android:id="@+id/edt_verifyCode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_10"
                            android:singleLine="true"
                            android:inputType="number"
                            android:digits="0123456789"
                            android:maxLength="6"
                            android:text="@={viewModel.verifyCode}"
                            android:hint="@string/verifycode_hint"
                            android:imeOptions="actionDone"
                            android:textColor="@color/color3333"
                            android:textSize="@dimen/font_size14" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="top"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/verifycode"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold"
                            android:visibility="invisible"/>

                        <TextView
                            android:id="@+id/tv_get_code"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner5_3c6af4"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_7"
                            android:singleLine="true"
                            android:gravity="center"
                            android:text="@string/getVerfyCode"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size14"
                            binding:onClickCommand="@{viewModel.getVerifyCode}"
                            binding:isThrottleFirst="@{Boolean.FALSE}" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                   android:layout_marginTop="@dimen/space_30"
                    android:orientation="horizontal">

                    <CheckBox
                        android:id="@+id/userAgreement"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/select_checkout"
                        android:checked="@={viewModel.isCheck}"
                        android:button="@null" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:text="@string/read_agree"
                        android:textColor="@color/color3333"
                        android:textSize="15sp" />

                    <TextView
                        android:id="@+id/agreement_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/useRule"
                        android:textColor="#3D99F5"
                        android:textSize="15sp"
                        binding:onClickCommand="@{viewModel.showRule}"
                        binding:isThrottleFirst="@{Boolean.FALSE}" />
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="bottom|center_horizontal"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_commit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="@dimen/space_60"
                        android:background="@drawable/shape_corner8_2568ff"
                        android:paddingVertical="@dimen/space_16"
                        android:text="@string/start_todo"
                        android:textColor="@color/white"
                        android:layout_gravity="bottom"
                        android:textSize="@dimen/font_size16"
                        binding:onClickCommand="@{viewModel.decideEvent}"
                        binding:isThrottleFirst="@{Boolean.FALSE}" />
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </RelativeLayout>
</layout>
