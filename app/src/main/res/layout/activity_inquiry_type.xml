<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    >

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.InquiryTypeViewModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/space_70"
            android:layout_marginBottom="@dimen/space_100"
            android:layout_marginRight="@dimen/space_170"
            android:layout_marginLeft="@dimen/space_170">
        <ImageView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginRight="@dimen/space_30"
            binding:onClickCommand="@{viewModel.identityRecognition}"
            binding:isThrottleFirst="@{Boolean.FALSE}"
            android:src="@mipmap/recog_idcard"/>

        <ImageView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@mipmap/input_phone_number"
            binding:onClickCommand="@{viewModel.phoneVerification}"
            binding:isThrottleFirst="@{Boolean.FALSE}"
            android:layout_marginRight="@dimen/space_30"/>

        <ImageView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent"
            binding:onClickCommand="@{viewModel.inputIdentityInformation}"
            android:src="@mipmap/input_idcard_infor"/>
    </LinearLayout>

    </LinearLayout>
</layout>