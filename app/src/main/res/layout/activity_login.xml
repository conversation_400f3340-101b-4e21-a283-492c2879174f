<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.login.LoginViewModel" />

<!--        <variable-->
<!--            name="click"-->
<!--            type="com.gc.notarizationpc.ui.login.LoginActivity.ProxyClick" />-->
    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

<!--        <com.hjq.bar.TitleBar-->
<!--            android:id="@+id/title"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:background="#1296db"-->
<!--            app:leftColor="@color/white"-->
<!--            app:leftSize="20sp"-->
<!--            app:leftTitle="智慧公证终端一体机"-->
<!--            app:rightBackground="@mipmap/ic_launcher"-->
<!--            app:rightTitle="首页"-->
<!--            app:titleColor="@color/white"-->
<!--            app:backButton="false"/>-->

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="20dp">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="34dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="16sp">


                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="8dp"
                    android:layout_marginRight="8dp"
                    android:background="#999999" />

                <EditText
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="请输入用户名"
                    android:text="@={viewModel.userName}"
                    android:textSize="16sp"
                    binding:onFocusChangeCommand="@{viewModel.onFocusChangeCommand}" />

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:padding="6dp"
                    android:src="@mipmap/ic_launcher"
                    android:visibility="@{viewModel.clearBtnVisibility}"
                    binding:onClickCommand="@{viewModel.clearUserNameOnClickCommand}" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="16sp">


                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="8dp"
                    android:layout_marginRight="8dp"
                    android:background="#999999" />

                <EditText
                    android:id="@+id/et_password"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="请输入密码"
                    android:inputType="textPassword"
                    android:text="@={viewModel.password}"
                    android:textSize="16sp" />

                <ImageView
                    android:id="@+id/iv_swich_passwrod"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:padding="6dp"
                    android:src="@mipmap/ic_launcher"
                    binding:onClickCommand="@{viewModel.passwordShowSwitchOnClickCommand}" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="8dp"
                    android:text="忘记密码"
                    android:textColor="#666666"
                    android:textSize="16sp" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="#2196F3"
                android:orientation="vertical"
                android:padding="6dp">
<!--使用这种形式的绑定，在原本事件绑定的基础之上，带有防重复点击的功能，
1秒内多次点击也只会执行一次操作。如果不需要防重复点击，可以加入这条属性 binding:isThrottleFirst="@{Boolean.TRUE}"-->
                <Button
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?selectableItemBackground"
                    android:text="登录"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    binding:onClickCommand="@{viewModel.loginOnClickCommand}" />
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="模拟一个登陆操作,随便输入账号密码点击登录即可进入"
                android:textColor="#EE1010" />

            <TextView
                android:id="@+id/tx_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:hint="请选择时间"
                android:onClick="showTimePickDialog"
                android:text="@={viewModel.time}"
                android:textSize="16sp" />

            <me.goldze.mvvmhabit.widget.AddReduceView
                android:id="@+id/add_reduce_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="弹出签名框"
                android:id="@+id/tv_show_sign"
                android:layout_marginTop="@dimen/space_20"
                android:onClick="showSignDialog"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="弹出多选"
                android:layout_marginTop="@dimen/space_20"
                android:onClick="showD"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="弹出列表dialog"
                android:layout_marginTop="@dimen/space_20"
                android:onClick="showListDialog"/>



        </LinearLayout>
    </RelativeLayout>
</layout>