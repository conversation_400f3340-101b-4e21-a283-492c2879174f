<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="100dp"
                    android:layout_height="40dp"
                    android:gravity="center"
                    android:padding="5dp"
                    android:text="*姓名"
                    android:textSize="@dimen/font_size_15" />

                <EditText
                    android:id="@+id/editRealName"
                    android:layout_width="220dp"
                    android:layout_height="45dp"
                    android:background="@drawable/shape_line_grey"
                    android:hint="请输入姓名"
                    android:inputType="text"
                    android:maxLength="30"
                    android:paddingLeft="@dimen/space_10"
                    android:text=""
                    android:imeOptions="actionDone"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_idcard"
                    android:layout_width="100dp"
                    android:layout_height="40dp"
                    android:gravity="center"
                    android:padding="5dp"
                    android:text="*身份证号"
                    android:textSize="@dimen/font_size_15" />

                <EditText
                    android:id="@+id/editIdCard"
                    android:layout_width="350dp"
                    android:layout_height="45dp"
                    android:background="@drawable/shape_line_grey"
                    android:hint="请输入身份证号"
                    android:inputType="text"
                    android:maxLength="18"
                    android:minEms="18"
                    android:paddingLeft="@dimen/space_10"
                    android:text=""
                    android:imeOptions="actionDone"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_sex"
                    android:layout_width="100dp"
                    android:layout_height="40dp"
                    android:gravity="center"
                    android:padding="5dp"
                    android:text="*性别"
                    android:textSize="@dimen/font_size_15" />


<!--                <EditText-->
<!--                    android:id="@+id/editSex"-->
<!--                    android:layout_width="220dp"-->
<!--                    android:layout_height="45dp"-->
<!--                    android:background="@drawable/shape_corner_white"-->
<!--                    android:hint="请选择性别"-->
<!--                    android:inputType="text"-->
<!--                    android:maxLength="20"-->
<!--                    android:minEms="20"-->
<!--                    android:paddingLeft="@dimen/space_10"-->
<!--                    android:text=""-->
<!--                    android:focusable="false"-->
<!--                    android:focusableInTouchMode="false"-->
<!--                    android:textSize="15sp"-->
<!--                    android:visibility="gone"/>-->

<!--                <com.gc.notarizationpc.widget.editspinner.AppCompatEditSpinner-->
<!--                    android:id="@+id/editSex"-->
<!--                    android:layout_height="40dp"-->
<!--                    android:layout_width="@dimen/space_250"-->
<!--                    app:editInputType="text"-->
<!--                    app:editMaxLength="20"-->
<!--                    app:editMaxLines="1"-->
<!--                    app:editTextColor="#000000"-->
<!--                    app:editTextSize="@dimen/font_size_15"-->
<!--                    app:hint="请选择性别"-->
<!--                    app:rightImageDropShowAllItem="true"-->
<!--                    app:rightImageGone="false"-->
<!--                    app:spinnerBackground="@drawable/bg_view_frame"-->
<!--                    app:spinnerItemTextSize="@dimen/font_size_15"-->
<!--                    app:spinnerItemMatchTextColor="#0A0EE1"-->
<!--                    app:spinnerItemTextColor="#000000"-->
<!--                    />-->

                <TextView
                    android:id="@+id/editSex"
                    android:layout_height="45dp"
                    android:layout_width="220dp"
                    android:gravity="left|center"
                    android:textSize="@dimen/font_size_15"
                    android:padding="5dp"
                    android:hint="请选择性别"
                    android:drawablePadding="8dp"
                    android:drawableRight="@drawable/zhcc"
                    android:background="@drawable/shape_line_grey"/>




            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_birth"
                    android:layout_width="100dp"
                    android:layout_height="40dp"
                    android:gravity="center"
                    android:padding="5dp"
                    android:text="*出生日期"
                    android:textSize="@dimen/font_size_15" />

                <EditText
                    android:id="@+id/editBirthday"
                    android:layout_width="220dp"
                    android:layout_height="45dp"
                    android:background="@drawable/shape_line_grey"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:hint="请输入日期"
                    android:inputType="text"
                    android:maxLength="20"
                    android:minEms="20"
                    android:paddingLeft="@dimen/space_10"
                    android:text="1990-05-15"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_phone"
                    android:layout_width="100dp"
                    android:layout_height="40dp"
                    android:gravity="center"
                    android:padding="5dp"
                    android:text="*联系电话"
                    android:textSize="@dimen/font_size_15" />

                <EditText
                    android:id="@+id/editPhone"
                    android:layout_width="220dp"
                    android:layout_height="45dp"
                    android:background="@drawable/shape_line_grey"
                    android:hint="请输入手机号码"
                    android:inputType="number"
                    android:maxLength="11"
                    android:minEms="11"
                    android:paddingLeft="@dimen/space_10"
                    android:text=""
                    android:imeOptions="actionDone"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_address"
                    android:layout_width="100dp"
                    android:layout_height="40dp"
                    android:gravity="center"
                    android:padding="5dp"
                    android:text="*地址"
                    android:textSize="@dimen/font_size_15" />

                <EditText
                    android:id="@+id/editAddress"
                    android:layout_width="350dp"
                    android:layout_height="45dp"
                    android:background="@drawable/shape_line_grey"
                    android:editable="false"
                    android:hint="请输入地址"
                    android:inputType="text"
                    android:maxLength="40"
                    android:singleLine="true"
                    android:imeOptions="actionDone"
                    android:paddingLeft="@dimen/space_10"
                    android:text=""
                    android:textSize="15sp" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_del"
            style="@style/tv_black_wrap_18"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="@dimen/space_5"
            android:layout_marginBottom="@dimen/space_5"
            android:background="@drawable/shape_tag"
            android:paddingLeft="25dp"
            android:paddingTop="5dp"
            android:paddingRight="25dp"
            android:paddingBottom="5dp"
            android:text="删除"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_20"
            android:visibility="gone"/>

    </LinearLayout>
</FrameLayout>