<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.IdentityCheckViewModel" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/space_30"
            android:background="@drawable/shape_corner10_white_all"
            android:orientation="vertical"
            android:padding="@dimen/space_30">

            <LinearLayout
                android:id="@+id/pay_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="visible">

                <TextView
                    android:id="@+id/commonPayType"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_horizontal"
                    android:text="@string/identity"
                    android:textColor="#43BBFF"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/numberPayType"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_marginLeft="30dp"
                    android:gravity="center_horizontal"
                    android:text="@string/identity_check"
                    android:textColor="@color/color3333"
                    android:textSize="20sp"
                    />
            </LinearLayout>

            <!--身份识别-->
            <LinearLayout
                android:id="@+id/lnl_idcard"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/shape_corner5_f5f5f7"
                android:layout_marginRight="@dimen/space_30"
                android:gravity="center"
                android:orientation="vertical">
                <ImageView
                    android:id="@+id/idCard_read_image_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/read_idcard_infor"
                    android:layout_gravity="center_horizontal"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:textStyle="bold"
                    android:textAlignment="center"
                    android:layout_marginTop="@dimen/space_39"
                    android:text="@string/readIdCardInformationTitle"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size28"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/space_23"
                    android:textAlignment="center"
                    android:text="@string/readIdCardInformationSubTitle"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size18"/>
            </LinearLayout>

            <!--身份验证-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_marginHorizontal="@dimen/space_60"
                android:layout_marginTop="@dimen/space_60"
                android:visibility="gone">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_50"
                    android:layout_marginHorizontal="@dimen/space_30"
                    android:gravity="center_vertical"
                    android:paddingVertical="@dimen/space_8">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/nameString"
                            android:textColor="@color/color6666"
                            android:textSize="@dimen/font_size14" />

                        <TextView
                            android:id="@+id/tv_name"
                            style="@style/edit_spinner_edit_sty"
                            android:layout_width="190dp"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/space_10"
                            android:layout_marginRight="@dimen/space_5"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:drawablePadding="@dimen/space_8"
                            android:gravity="center_vertical"
                            android:imeOptions="actionDone"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_8"
                            android:singleLine="true" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="left|center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/space_45"
                            android:text="@string/idCardNumber"
                            android:textColor="@color/color6666"
                            android:textSize="@dimen/font_size14"/>

                        <TextView
                            android:id="@+id/tv_idcard"
                            style="@style/edit_spinner_edit_sty"
                            android:layout_width="190dp"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/space_8"
                            android:layout_marginRight="@dimen/space_5"
                            android:layout_toLeftOf="@+id/tv_search"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:drawablePadding="@dimen/space_8"
                            android:gravity="center_vertical"
                            android:hint="@string/verifycode_hint"
                            android:imeOptions="actionDone"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_8"
                            android:singleLine="true" />

                    </LinearLayout>


                </LinearLayout>
                <LinearLayout
                    android:id="@+id/rnl_search_bar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_must_hint"
                    android:layout_marginTop="@dimen/space_20"
                    android:layout_marginHorizontal="@dimen/space_30"
                    android:gravity="center_vertical"
                    android:paddingVertical="@dimen/space_8">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_must_hint"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/phone_num"
                            android:textColor="@color/color6666"
                            android:textSize="@dimen/font_size14" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/edt_phone"
                            style="@style/edit_spinner_edit_sty"
                            android:layout_width="190dp"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/space_10"
                            android:layout_marginRight="@dimen/space_5"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:drawablePadding="@dimen/space_8"
                            android:gravity="center_vertical"
                            android:hint="@string/phone_num_hint"
                            android:imeOptions="actionDone"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_8"
                            android:singleLine="true" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="left|center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/space_45"
                            android:text="@string/verifycode"
                            android:textColor="@color/color6666"
                            android:textSize="@dimen/font_size14"/>

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/edt_verifycode"
                            style="@style/edit_spinner_edit_sty"
                            android:layout_width="190dp"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/space_8"
                            android:layout_marginRight="@dimen/space_5"
                            android:layout_toLeftOf="@+id/tv_search"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:drawablePadding="@dimen/space_8"
                            android:gravity="center_vertical"
                            android:hint="@string/verifycode_hint"
                            android:imeOptions="actionDone"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_8"
                            android:singleLine="true" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/space_5"
                            android:background="@drawable/shape_corner5_3c6af4"
                            android:paddingHorizontal="@dimen/space_10"
                            android:paddingVertical="@dimen/space_5"
                            android:text="@string/send_code"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />
                    </LinearLayout>


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_20"
                    android:layout_marginRight="30dp"
                    android:gravity="left"
                    android:layout_marginHorizontal="@dimen/space_30"
                    android:orientation="horizontal">

                    <CheckBox
                        android:id="@+id/userAgreement"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/select_checkout"
                        android:button="@null" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:text="@string/read_agree"
                        android:textColor="@color/color3333"
                        android:textSize="@dimen/font_size14" />

                    <TextView
                        android:id="@+id/agreement_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/privary"
                        android:textColor="@color/date_picker_text_light"
                        android:textSize="@dimen/font_size14" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/agree_to_use"
                        android:textColor="@color/color3333"
                        android:textSize="@dimen/font_size16" />

                </LinearLayout>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/space_30"
                    android:background="@drawable/shape_corner8_2568ff"
                    android:paddingHorizontal="@dimen/space_60"
                    android:paddingVertical="@dimen/space_15"
                    android:text="@string/start_to_notarization"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size14"
                    android:onClick="showReservationSuccess"/>
            </LinearLayout>
        </LinearLayout>


    </LinearLayout>
</layout>