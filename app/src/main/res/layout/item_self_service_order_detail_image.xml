<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    >

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderDetailImageItemViewModel" />

    </data>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="@dimen/space_20"
        android:layout_marginLeft="@dimen/space_40"
        binding:onClickCommand="@{viewModel.imagePreView}"
        binding:isThrottleFirst="@{Boolean.FALSE}"
        android:orientation="vertical">
        <TextView
            android:id="@+id/order_detail_image_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            binding:text="@{viewModel.entity.materialTypeName}"
            android:textSize="@dimen/font_size12"
            android:textColor="@color/colorBFBFBF"
            android:textAlignment="center"/>
        <ImageView
            android:id="@+id/order_detail_image_item_image"
            android:layout_width="@dimen/space_80"
            android:layout_height="@dimen/space_80"
            android:layout_marginTop="@dimen/space_10"
            binding:imageUrl="@{viewModel.entity.fileUrl}"
            binding:placeholderRes="@mipmap/image_empty"
            android:layout_marginBottom="@dimen/space_10"
            android:scaleType="fitXY"/>
    </LinearLayout>
</layout>