<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.FragmentMyAppointmentGridItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.FragmentMyAppointmentGridItemViewModel" />
    </data>
    <RelativeLayout
        android:id="@+id/item_grid_my_appointment_order"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/space_30"
        android:layout_marginRight="@dimen/space_30"
        android:padding="@dimen/space_20"
        android:background="@drawable/shape_corner8_f5f5f7">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    binding:imgSrc="@{viewModel.stateImg}"/>

                <TextView
                    android:id="@+id/item_grid_my_appointment_order_notary_type"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_alignParentLeft="true"
                    android:ellipsize="end"
                    android:maxWidth="@dimen/space_400"
                    android:singleLine="true"
                    binding:textColor="@{viewModel.stateColor}"
                    android:textSize="@dimen/font_size16"
                    android:textStyle="bold"
                    binding:text="@{viewModel.entity.statusString}" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_16"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_order_number_label"
                    android:layout_width="0dp"
                    android:layout_weight="2.3"
                    android:layout_height="wrap_content"
                    android:text="订单编号："
                    android:textColor="@color/color8888"
                    android:textSize="@dimen/font_size14" />
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_order_number"
                    android:layout_width="0dp"
                    android:layout_weight="8"
                    android:layout_height="wrap_content"
                    binding:text="@{viewModel.entity.id}"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_15"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_notary_name_label"
                    android:layout_width="0dp"
                    android:layout_weight="2.3"
                    android:layout_height="wrap_content"
                    android:text="公证处："
                    android:textColor="@color/color8888"
                    android:textSize="@dimen/font_size14" />
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_notary_name"
                    android:layout_width="0dp"
                    android:layout_weight="8"
                    android:layout_height="wrap_content"
                    binding:text="@{viewModel.entity.notarialName}"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_16"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_apply_user_name_label"
                    android:layout_width="0dp"
                    android:layout_weight="2.3"
                    android:layout_height="wrap_content"
                    android:text="申办人："
                    android:textColor="@color/color8888"
                    android:textSize="@dimen/font_size14" />
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_apply_user_name"
                    android:layout_width="0dp"
                    android:layout_weight="8"
                    android:layout_height="wrap_content"
                    binding:text="@{viewModel.entity.userName}"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_16"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_create_time_label"
                    android:layout_width="0dp"
                    android:layout_weight="2.3"
                    android:layout_height="wrap_content"
                    android:text="创建时间："
                    android:textColor="@color/color8888"
                    android:textSize="@dimen/font_size14" />
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_create_time"
                    android:layout_width="0dp"
                    android:layout_weight="8"
                    android:layout_height="wrap_content"
                    binding:text="@{viewModel.entity.createdTime}"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_16"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_create_appointment_time_label"
                    android:layout_width="0dp"
                    android:layout_weight="2.3"
                    android:layout_height="wrap_content"
                    android:text="预约时间："
                    android:textColor="@color/color8888"
                    android:textSize="@dimen/font_size14" />
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_create_appointment_time"
                    android:layout_width="0dp"
                    android:layout_weight="8"
                    android:layout_height="wrap_content"
                    binding:text="@{viewModel.appointmentTime}"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_16"
                android:layout_marginBottom="@dimen/space_30"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_remark_label"
                    android:layout_width="0dp"
                    android:layout_weight="2.3"
                    android:layout_height="wrap_content"
                    android:text="备注："
                    android:textColor="@color/color8888"
                    android:textSize="@dimen/font_size14" />
                <TextView
                    android:id="@+id/item_grid_my_appointment_order_remark"
                    android:layout_width="0dp"
                    android:layout_weight="8"
                    android:layout_height="wrap_content"
                    binding:text="@{viewModel.entity.caseRemarks}"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:layout_alignParentBottom="true"
            android:layout_alignParentRight="true"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/item_grid_my_appointment_order_enter_the_reception_room"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingVertical="@dimen/space_3"
                android:paddingHorizontal="@dimen/space_20"
                android:background="@drawable/shape_corner5_3c6af4"
                binding:onClickCommand="@{viewModel.enterRoomClick}"
                binding:isThrottleFirst="@{Boolean.FALSE}"
                android:textColor="@color/colorWhite"
                android:visibility="@{viewModel.isHide}"
                android:text="@string/enterTheReceptionRoom"/>
        </LinearLayout>
    </RelativeLayout>




</layout>