<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/shape_corner5_f7f2fe">


    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/space_5"
        android:layout_weight="1.5"
        android:gravity="left"
        android:padding="@dimen/space_8"
        android:text="农村房屋所有权"
        android:textColor="#8A8E99"
        android:textSize="@dimen/font_size14" />

    <TextView
        android:id="@+id/tv_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/space_5"
        android:layout_weight="3"
        android:gravity="right"
        android:paddingHorizontal="@dimen/space_15"
        android:paddingVertical="@dimen/space_8"
        android:text="0元"
        android:textColor="@color/color3333"
        android:textSize="@dimen/font_size14"
        android:visibility="visible"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_fee_child"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_10"/>
</LinearLayout>