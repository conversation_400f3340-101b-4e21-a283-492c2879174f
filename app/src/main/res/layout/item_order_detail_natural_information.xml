<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    >

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.OrderDetailNaturalInformationItemViewModel" />

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/space_40"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/order_detail_natural_information_role"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAlignment="center"
            binding:text="@{viewModel.personType}"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/order_detail_natural_information_user_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            binding:text="@{viewModel.entity.applicantName}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/order_detail_natural_information_phone_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.5"
            android:textAlignment="textStart"
            binding:text="@{viewModel.entity.contactNum}"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/order_detail_natural_information_certificate_type"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            binding:text="@{viewModel.entity.credentialTypeStr}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/order_detail_natural_information_certificate_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            binding:text="@{viewModel.entity.credentialNum}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/order_detail_natural_information_sex"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            binding:text="@{viewModel.sexString}"
            android:textAlignment="center"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/order_detail_natural_information_birthday"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            binding:text="@{viewModel.entity.birthday}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/order_detail_natural_information_address"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="4"
            binding:text="@{viewModel.entity.address}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />
    </LinearLayout>
</layout>