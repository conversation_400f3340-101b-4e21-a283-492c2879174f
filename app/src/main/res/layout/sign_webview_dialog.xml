<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="5dp"
        android:layout_marginTop="40dp"
        android:layout_marginBottom="40dp"
        android:orientation="vertical"
        android:background="@drawable/shape_corner_white">

        <WebView
            android:id="@+id/wvWebView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="5dp"
            android:layout_marginLeft="5dp"
            android:minHeight="@dimen/space_300"/>

    </LinearLayout>

</RelativeLayout>

