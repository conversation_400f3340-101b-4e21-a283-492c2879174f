<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.SelfApplyNorItemListItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelfApplyNorItemListItemViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/lnl_personal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/space_25"
        android:paddingVertical="@dimen/space_10">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2.5"
            android:text="@{viewModel.entity.mattersName}"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2.5"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/space_30"
                android:layout_height="@dimen/space_30"
                android:background="@drawable/shape_corner5_white"
                android:src="@mipmap/reduce"
                binding:onClickCommand="@{viewModel.subClick}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_notarization_book_num"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="wrap_content"
                android:layout_height="25dp"
                android:layout_marginStart="@dimen/space_5"
                android:layout_marginEnd="@dimen/space_5"
                android:layout_toStartOf="@+id/tv_search"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:digits="1234567890"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:imeOptions="actionDone"
                android:maxLines="1"
                android:layout_gravity="center"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_5"
                android:singleLine="true"
                android:text="@={viewModel.entity.notarizationNumber}"
                android:textColor="@color/color3333"
                binding:onFocusChangeCommand="@{viewModel.addNotarizationCopiesCommand}" />

            <ImageView
                android:layout_width="@dimen/space_30"
                android:layout_height="@dimen/space_30"
                android:background="@drawable/shape_corner5_white"
                android:src="@mipmap/add"
                binding:onClickCommand="@{viewModel.plusClick}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            binding:onClickCommand="@{viewModel.delClick}"
            binding:isThrottleFirst="@{Boolean.FALSE}" >

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="@dimen/space_25"
                android:layout_height="@dimen/space_25"
                android:layout_alignParentLeft="true"
                android:padding="@dimen/space_3"
                android:src="@mipmap/icon_del_gray" />
        </RelativeLayout>

    </LinearLayout>
</layout>