<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_grey_f5f5f5" >

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#2196F3">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="30dp"
                android:layout_gravity="center_vertical|left"
                android:layout_marginTop="5dp"
                android:layout_marginLeft="20dp"
                android:onClick="backClicked"
                android:src="@drawable/back" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:layout_marginRight="40dp"
                android:padding="20dp"
                android:text="在线咨询"
                android:textAlignment="center"
                android:textColor="#FFFFFF"
                android:textSize="20sp"/>

        </LinearLayout>



        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginTop="10dp"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bottom_background"
                android:visibility="gone">

                <EditText
                    android:id="@+id/editText"
                    style="@style/Widget.AppCompat.AutoCompleteTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:textCursorDrawable="@drawable/cursor_drawable"
                    android:layout_gravity="center"
                    android:enabled="false"
                    android:hint="请输入..."
                    android:maxLines="2" />

                <Button
                    android:id="@+id/send_button"
                    style="@style/Widget.AppCompat.Button.Borderless.Colored"
                    android:textColor="#42D363"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="发送" />
            </LinearLayout>


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>