<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingLeft="20dp"
    android:paddingRight="20dp"
    android:paddingTop="20dp">

    <RelativeLayout
        android:id="@+id/item_rl"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_centerInParent="true">

        <ImageView
            android:id="@+id/img_details"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginTop="20dp"
            android:contentDescription="@null"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:id="@+id/ll_del"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="10dp"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/img_back"
                android:layout_width="25dp"
                android:layout_height="25dp"

                android:contentDescription="@null"
                android:scaleType="fitXY"
                android:src="@drawable/delete"/>

        </LinearLayout>

    </RelativeLayout>

    <TextView
        android:id="@+id/detail_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/item_rl"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="15dp"
        android:text="上传名称"
        android:textColor="#666666"
        android:textSize="15sp" />
</RelativeLayout>