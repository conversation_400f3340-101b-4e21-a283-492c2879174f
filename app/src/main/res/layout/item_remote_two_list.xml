<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#F5F5F5"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="15dp"
        android:layout_marginBottom="@dimen/space_5"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/titleTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:text="借款人"
            android:textColor="#1f1f1f"
            android:textSize="22sp" />


        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F5F5F5" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="10dp">

            <RelativeLayout
                android:id="@+id/tv_remote_rela"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="姓名"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_remote"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:text=""
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#F5F5F5" />

            <RelativeLayout
                android:id="@+id/tv_id_rela"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="身份证号码"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/address_rela"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F5F5F5" />

                <TextView
                    android:id="@+id/addressTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:text="住所地"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_toRightOf="@id/addressTv"
                    android:gravity="right"
                    android:text=""
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/phone_rela"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F5F5F5" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:text="联系电话"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_phone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ybRl_One"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F5F5F5" />

                <TextView
                    android:id="@+id/ybRl_text_One"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_yb_One"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ybRl_Two"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F5F5F5" />

                <TextView
                    android:id="@+id/ybRl_text_Two"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_yb_Two"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ybRl_Three"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F5F5F5" />

                <TextView
                    android:id="@+id/ybRl_text_Three"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_yb_Three"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ybRl_Four"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F5F5F5" />

                <TextView
                    android:id="@+id/ybRl_text_Four"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_yb_Four"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ybRl_Five"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F5F5F5" />

                <TextView
                    android:id="@+id/ybRl_text_Five"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_yb_Five"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:textColor="#1f1f1f"
                    android:textSize="16sp" />

            </RelativeLayout>
        </LinearLayout>


        <ImageView
            android:id="@+id/itemLine"
            android:layout_width="match_parent"
            android:layout_height="5dp"
            android:layout_marginTop="10dp"
            android:background="#F5F5F5" />


    </LinearLayout>
</FrameLayout>