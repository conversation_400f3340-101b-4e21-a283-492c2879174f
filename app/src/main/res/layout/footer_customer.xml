<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="horizontal">
        <ProgressBar
            android:id="@+id/custom_footer_progress"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:padding="10dp"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/custom_footer_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/space_10"
            android:padding="10dp"
            android:text="@string/load_more_data"
            android:textColor="#777777" />
    </LinearLayout>

</RelativeLayout>