<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:id="@+id/remote_pop"
    tools:context=".ui.RemoteDetailsActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#2196F3">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical|left"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="5dp"
            android:onClick="backClicked"
            android:src="@drawable/back" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:padding="20dp"
            android:text="小额详情"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="20sp" />

    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/hint1"
                style="@style/tv_black_wrap_18"
                android:drawableLeft="@drawable/shape_tag"
                android:drawablePadding="@dimen/space_10"
                android:padding="@dimen/space_10"
                android:text="小额信息" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvApplicant"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_10"
                android:background="@color/bg_e1" />

            <LinearLayout
                android:id="@+id/propertyInformationLL"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/propertyInformationTv"
                    style="@style/tv_black_wrap_18"
                    android:drawableLeft="@drawable/shape_tag"
                    android:drawablePadding="@dimen/space_10"
                    android:padding="@dimen/space_10"
                    android:text="财产信息" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/propertyInformationRv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </LinearLayout>

            <TextView
                android:id="@+id/hint2"
                style="@style/tv_black_wrap_18"
                android:drawableLeft="@drawable/shape_tag"
                android:drawablePadding="@dimen/space_10"
                android:padding="@dimen/space_10"
                android:text="材料" />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/imgProxyRv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_file" />

            <Button
                android:id="@+id/bt_initiateNotarization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/space_40"
                android:layout_marginRight="@dimen/dp_10"
                android:layout_marginBottom="40dp"
                android:background="@drawable/shape_remote_details"
                android:onClick="btInitiateNotarization"
                android:text="发起公证"
                android:textColor="@color/white" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>