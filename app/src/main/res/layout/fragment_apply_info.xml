<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelfApplyInfoViewModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/transparent"
        android:orientation="vertical">

        <include
            android:id="@+id/self_service_step_bar"
            layout="@layout/self_service_step_bar"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_30"
            android:paddingBottom="@dimen/space_30"
            android:background="@drawable/shape_corner10_white_all"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/rnl_search_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/space_15">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:drawableLeft="@mipmap/icon_shenban"
                    android:paddingTop="@dimen/space_5"
                    android:text="@string/shenban_info"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size18"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_30"
                    android:layout_toLeftOf="@+id/edt_notarization_address"
                    android:text="@string/notarization_address"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/edt_notarization_address"
                    style="@style/edit_spinner_edit_sty"
                    android:layout_width="@dimen/space_140"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_8"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_toLeftOf="@+id/iv_address"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:gravity="center_vertical"
                    android:hint="@string/notarization_address_hint"
                    android:imeOptions="actionDone"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/space_20"
                    android:paddingVertical="@dimen/space_8"
                    android:singleLine="true"
                    android:text="@{viewModel.selectedNotaryArea.value}"
                    binding:onClickCommand="@{viewModel.areaOnClickCommand}"
                    binding:isThrottleFirst="@{Boolean.FALSE}" />

                <ImageView
                    android:id="@+id/iv_address"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_centerVertical="true"
                    android:layout_toLeftOf="@+id/tv_lan"
                    android:src="@mipmap/icon_arrow"
                    binding:onClickCommand="@{viewModel.areaOnClickCommand}"
                    binding:isThrottleFirst="@{Boolean.FALSE}" />


                <TextView
                    android:id="@+id/tv_lan"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_30"
                    android:layout_toLeftOf="@+id/edt_notarization_lan"
                    android:text="@string/notarization_language"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/edt_notarization_lan"
                    style="@style/edit_spinner_edit_sty"
                    android:layout_width="@dimen/space_140"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_8"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_toLeftOf="@+id/iv_lan"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:gravity="center_vertical"
                    android:hint="@string/notarization_language_hint"
                    android:imeOptions="actionDone"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/space_20"
                    android:paddingVertical="@dimen/space_8"
                    android:singleLine="true"
                    android:text="@{viewModel.selectedNotaryLanguage.value}"
                    binding:isThrottleFirst="@{Boolean.FALSE}"
                    binding:onClickCommand="@{viewModel.languageOnClickCommand}" />

                <ImageView
                    android:id="@+id/iv_lan"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_centerVertical="true"
                    android:layout_toLeftOf="@+id/tv_purpose"
                    android:src="@mipmap/icon_arrow"
                    binding:onClickCommand="@{viewModel.languageOnClickCommand}"
                    binding:isThrottleFirst="@{Boolean.FALSE}" />

                <TextView
                    android:id="@+id/tv_purpose"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_30"
                    android:layout_toLeftOf="@+id/edt_notarization_purpose"
                    android:text="@string/notaryDestination"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/edt_notarization_purpose"
                    style="@style/edit_spinner_edit_sty"
                    android:layout_width="@dimen/space_140"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_8"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_toLeftOf="@+id/iv_purpose"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:gravity="center_vertical"
                    android:hint="@string/notarization_purpose_hint"
                    android:imeOptions="actionDone"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/space_20"
                    android:paddingVertical="@dimen/space_8"
                    android:singleLine="true"
                    android:text="@{viewModel.selectedNotaryPurpose.value}"
                    binding:onClickCommand="@{viewModel.purPoseOnClickCommand}"
                    binding:isThrottleFirst="@{Boolean.FALSE}" />

                <ImageView
                    android:id="@+id/iv_purpose"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_centerVertical="true"
                    android:layout_toLeftOf="@+id/tv_notary_office"
                    android:src="@mipmap/icon_arrow"
                    binding:onClickCommand="@{viewModel.purPoseOnClickCommand}"
                    binding:isThrottleFirst="@{Boolean.FALSE}" />


                <TextView
                    android:id="@+id/tv_notary_office"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_30"
                    android:layout_toLeftOf="@+id/edt_notarization_office"
                    android:text="@string/notary_office"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/edt_notarization_office"
                    style="@style/edit_spinner_edit_sty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_8"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_toLeftOf="@+id/iv_notary_office"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:gravity="center_vertical"
                    android:hint="@string/notary_office_hint"
                    android:imeOptions="actionDone"
                    android:maxLines="1"
                    android:maxLength="50"
                    android:paddingHorizontal="@dimen/space_20"
                    android:paddingVertical="@dimen/space_8"
                    android:singleLine="true"
                    android:maxWidth="@dimen/space_300"
                    binding:onClickCommand="@{viewModel.selectNotaryOfficeClick}"
                    binding:isThrottleFirst="@{Boolean.FALSE}" />

                <ImageView
                    android:id="@+id/iv_notary_office"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:src="@mipmap/icon_arrow"
                    binding:onClickCommand="@{viewModel.selectNotaryOfficeClick}"
                    binding:isThrottleFirst="@{Boolean.FALSE}" />


            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_below="@+id/rnl_search_bar"
                android:layout_marginHorizontal="@dimen/space_30"
                android:background="#E8E8EB" />


            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/space_20"
                android:layout_weight="1"
                android:fillViewport="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/shape_corner8_f5f5f7"
                        android:paddingHorizontal="@dimen/space_10"
                        android:paddingVertical="@dimen/space_15">

                        <TextView
                            android:id="@+id/tv_personal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:text="@string/personal"
                            android:textColor="@color/color_bfbfbf"
                            android:textSize="@dimen/font_size12" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_alignParentTop="true"
                            android:drawableLeft="@mipmap/icon_add_blue"
                            android:text="@string/add"
                            android:paddingHorizontal="@dimen/space_10"
                            android:paddingBottom="@dimen/space_4"
                            android:paddingTop="@dimen/space_6"
                            android:textColor="@color/color_0c7ff2"
                            android:textSize="@dimen/font_size12"
                            binding:onClickCommand="@{viewModel.applicantClick}"
                            binding:isThrottleFirst="@{Boolean.FALSE}" />

                        <RelativeLayout
                            android:id="@+id/rnl_personal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/tv_personal"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner5_white"
                            android:padding="@dimen/space_3">

                            <LinearLayout
                                android:id="@+id/lnl_personal"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/shape_corner4_e7f2fe_top_right_left"
                                android:paddingHorizontal="@dimen/space_25"
                                android:paddingVertical="@dimen/space_10">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/role"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/nameString"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:text="@string/phone_num"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/operate"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />
                            </LinearLayout>

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_personal"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/lnl_personal"
                                binding:itemBinding="@{viewModel.itemBinding}"
                                binding:items="@{viewModel.observablePersonalList}"
                                binding:layoutManager="@{LayoutManagers.linear()}" />


                        </RelativeLayout>

                        <TextView
                            android:id="@+id/tv_company"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_below="@+id/rnl_personal"
                            android:layout_marginTop="@dimen/space_15"
                            android:text="@string/company"
                            android:textColor="@color/color_bfbfbf"
                            android:textSize="@dimen/font_size12" />
                        <!--公司开始-->
                        <RelativeLayout
                            android:id="@+id/rnl_company"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/tv_company"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner5_white"
                            android:padding="@dimen/space_3">

                            <LinearLayout
                                android:id="@+id/lnl_company"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/shape_corner4_e7f2fe_top_right_left"
                                android:paddingHorizontal="@dimen/space_25"
                                android:paddingVertical="@dimen/space_10">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/designation"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/role"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/legal_person_name"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />


                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="2"
                                    android:text="@string/legal_or_representative_phone_number"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/operate"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />
                            </LinearLayout>

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_company"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/lnl_company"
                                binding:itemBinding="@{viewModel.itemCompanyBinding}"
                                binding:items="@{viewModel.observableCompanyList}"
                                binding:layoutManager="@{LayoutManagers.linear()}" />
                        </RelativeLayout>
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_10"
                        android:background="@drawable/shape_corner8_f5f5f7"
                        android:padding="@dimen/space_10">

                        <TextView
                            android:id="@+id/tv_item"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:text="@string/notarization_item"
                            android:textColor="@color/color_bfbfbf"
                            android:textSize="@dimen/font_size12" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:drawableLeft="@mipmap/icon_add_blue"
                            android:paddingHorizontal="@dimen/space_10"
                            android:paddingTop="@dimen/space_6"
                            android:paddingBottom="@dimen/space_4"
                            android:text="@string/add"
                            android:textColor="@color/color_0c7ff2"
                            android:textSize="@dimen/font_size12"
                            binding:onClickCommand="@{viewModel.notaryItemAddClick}"
                            binding:isThrottleFirst="@{Boolean.FALSE}" />

                        <RelativeLayout
                            android:id="@+id/rnl_item"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/tv_item"
                            android:layout_marginTop="@dimen/space_5"
                            android:background="@drawable/shape_corner5_white"
                            android:padding="@dimen/space_3">

                            <LinearLayout
                                android:id="@+id/lnl_item"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/shape_corner4_e7f2fe_top_right_left"
                                android:paddingHorizontal="@dimen/space_25"
                                android:paddingVertical="@dimen/space_10">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="2.5"
                                    android:text="@string/notary_item_name"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="2.5"
                                    android:text="@string/notary_book_num"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />


                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/operate"
                                    android:textColor="@color/color_8A8E99"
                                    android:textSize="@dimen/font_size14" />
                            </LinearLayout>

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_item"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/lnl_item"
                                binding:itemBinding="@{viewModel.itemNorItemBinding}"
                                binding:items="@{viewModel.observableNorItemList}"
                                binding:layoutManager="@{LayoutManagers.linear()}"/>

                        </RelativeLayout>

                    </RelativeLayout>

                </LinearLayout>


            </ScrollView>

            <TextView
                android:id="@+id/tv_commit"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:layout_marginRight="@dimen/space_30"
                android:background="@drawable/shape_corner8_2568ff"
                android:gravity="center"
                android:paddingHorizontal="@dimen/space_60"
                android:paddingVertical="@dimen/space_16"
                android:text="@string/next_step"
                android:textColor="@color/white"
                binding:onClickCommand="@{viewModel.nextStep}"
                android:textSize="@dimen/font_size16" />


        </LinearLayout>


    </LinearLayout>
</layout>