<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="500dp"
    android:layout_height="400dp"
    android:layout_gravity="center"
    android:background="@drawable/white_shape">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="20dp"
        android:text="系统提示"
        android:textSize="20sp" />

    <TextView
        android:id="@+id/camera_erro_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="20dp"
        android:layout_marginRight="20dp"
        android:drawableRight="@mipmap/close"
        android:padding="10dp" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/camera_erro_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="当事人摄像头连接异常"
            android:textColor="@color/black"
            android:textSize="25sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginTop="20dp"
            android:text="请联系管理员协助检查处理"
            android:textSize="25sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginTop="10dp"
                android:text="或拨打 "
                android:textSize="25sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginTop="10dp"
                android:text="400-800-1820"
                android:textColor="@color/colorTheme"
                android:textSize="25sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginTop="10dp"
                android:text=" 咨询解决"
                android:textSize="25sp" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>