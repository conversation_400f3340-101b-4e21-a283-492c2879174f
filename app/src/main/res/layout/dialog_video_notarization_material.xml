<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:paddingBottom="@dimen/space_8">

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="#80060812">

        <RelativeLayout
            android:id="@+id/rnl_preview"
            android:layout_width="600dp"
            android:layout_height="800dp"
            android:layout_centerInParent="true"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_detail"
                android:layout_width="600dp"
                android:layout_height="800dp"
                android:layout_centerInParent="true"
                android:scaleType="fitXY" />
            <ImageView
                android:id="@+id/iv_close_img"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_alignParentRight="true"
                android:layout_alignParentTop="true"
                android:padding="@dimen/space_5"
                android:layout_marginTop="@dimen/space_8"
                android:layout_marginRight="@dimen/space_8"
                android:src="@mipmap/icon_close" />

        </RelativeLayout>
    </RelativeLayout>

    <LinearLayout
        android:layout_width="500dp"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:background="@drawable/shape_corner8_white_left"
        android:orientation="vertical"
        android:padding="@dimen/space_20"
        android:paddingBottom="@dimen/space_8">

        <RelativeLayout
            android:id="@+id/rnl_search_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/title_bar"
            android:padding="@dimen/space_15">

            <TextView
                android:id="@+id/tv_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:drawableLeft="@mipmap/icon_marital_title"
                android:drawablePadding="@dimen/space_5"
                android:text="@string/uploadMaterialText"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size18"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_refresh"
                android:layout_width="@dimen/space_40"
                android:layout_height="@dimen/space_40"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/space_40"
                android:layout_toLeftOf="@+id/iv_close"
                android:padding="@dimen/space_10"
                android:src="@mipmap/icon_refresh" />


            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="@dimen/space_40"
                android:layout_height="@dimen/space_40"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:padding="@dimen/space_10"
                android:src="@mipmap/icon_close" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="@dimen/space_15"
            android:background="#E8E8EB" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_parent"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/space_8"
            android:layout_weight="1" />

        <!--    <androidx.core.widget.NestedScrollView-->
        <!--        android:layout_width="match_parent"-->
        <!--        android:layout_height="0dp"-->
        <!--        android:layout_weight="1"-->
        <!--        android:layout_marginTop="@dimen/space_8">-->
        <!--        <LinearLayout-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:orientation="vertical"-->
        <!--            android:layout_marginHorizontal="@dimen/space_15">-->


        <!--            <RelativeLayout-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginTop="@dimen/space_15"-->
        <!--                android:layout_marginLeft="@dimen/space_15">-->

        <!--                <View-->
        <!--                    android:layout_width="4dp"-->
        <!--                    android:layout_height="12dp"-->
        <!--                    android:background="@drawable/shape_corner2_gradient"-->
        <!--                    android:layout_centerVertical="true"/>-->

        <!--                <TextView-->
        <!--                    android:layout_width="wrap_content"-->
        <!--                    android:layout_height="wrap_content"-->
        <!--                    android:layout_alignParentLeft="true"-->
        <!--                    android:layout_centerVertical="true"-->
        <!--                    android:layout_marginLeft="@dimen/space_10"-->
        <!--                    android:text="@string/booklet"-->
        <!--                    android:textColor="@color/color3333"-->
        <!--                    android:textSize="@dimen/font_size14"-->
        <!--                    android:textStyle="bold" />-->
        <!--            </RelativeLayout>-->
        <!--            <androidx.recyclerview.widget.RecyclerView-->
        <!--                android:id="@+id/rv_booklet"-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginTop="@dimen/space_8"-->
        <!--                android:layout_marginLeft="@dimen/space_15"-->
        <!--                android:padding="@dimen/space_5"/>-->


        <!--            <RelativeLayout-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginTop="@dimen/space_15"-->
        <!--                android:layout_marginLeft="@dimen/space_15">-->

        <!--                <View-->
        <!--                    android:layout_width="4dp"-->
        <!--                    android:layout_height="12dp"-->
        <!--                    android:background="@drawable/shape_corner2_gradient"-->
        <!--                    android:layout_centerVertical="true"/>-->

        <!--                <TextView-->
        <!--                    android:layout_width="wrap_content"-->
        <!--                    android:layout_height="wrap_content"-->
        <!--                    android:layout_alignParentLeft="true"-->
        <!--                    android:layout_centerVertical="true"-->
        <!--                    android:layout_marginLeft="@dimen/space_10"-->
        <!--                    android:text="@string/gz_content"-->
        <!--                    android:textColor="@color/color3333"-->
        <!--                    android:textSize="@dimen/font_size14"-->
        <!--                    android:textStyle="bold" />-->
        <!--            </RelativeLayout>-->
        <!--            <androidx.recyclerview.widget.RecyclerView-->
        <!--                android:id="@+id/rv_gzcontent"-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginTop="@dimen/space_8"-->
        <!--                android:layout_marginLeft="@dimen/space_15"-->
        <!--                android:padding="@dimen/space_5"/>-->


        <!--            <RelativeLayout-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginTop="@dimen/space_15"-->
        <!--                android:layout_marginLeft="@dimen/space_15">-->

        <!--                <View-->
        <!--                    android:layout_width="4dp"-->
        <!--                    android:layout_height="12dp"-->
        <!--                    android:background="@drawable/shape_corner2_gradient"-->
        <!--                    android:layout_centerVertical="true"/>-->

        <!--                <TextView-->
        <!--                    android:layout_width="wrap_content"-->
        <!--                    android:layout_height="wrap_content"-->
        <!--                    android:layout_alignParentLeft="true"-->
        <!--                    android:layout_centerVertical="true"-->
        <!--                    android:layout_marginLeft="@dimen/space_10"-->
        <!--                    android:text="@string/gz_degree"-->
        <!--                    android:textColor="@color/color3333"-->
        <!--                    android:textSize="@dimen/font_size14"-->
        <!--                    android:textStyle="bold" />-->
        <!--            </RelativeLayout>-->
        <!--            <androidx.recyclerview.widget.RecyclerView-->
        <!--                android:id="@+id/rv_degree"-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginTop="@dimen/space_8"-->
        <!--                android:layout_marginLeft="@dimen/space_15"-->
        <!--                android:padding="@dimen/space_5"/>-->

        <!--            <RelativeLayout-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginTop="@dimen/space_15"-->
        <!--                android:layout_marginLeft="@dimen/space_15">-->

        <!--                <View-->
        <!--                    android:layout_width="4dp"-->
        <!--                    android:layout_height="12dp"-->
        <!--                    android:background="@drawable/shape_corner2_gradient"-->
        <!--                    android:layout_centerVertical="true"/>-->

        <!--                <TextView-->
        <!--                    android:layout_width="wrap_content"-->
        <!--                    android:layout_height="wrap_content"-->
        <!--                    android:layout_alignParentLeft="true"-->
        <!--                    android:layout_centerVertical="true"-->
        <!--                    android:layout_marginLeft="@dimen/space_10"-->
        <!--                    android:text="@string/idcard"-->
        <!--                    android:textColor="@color/color3333"-->
        <!--                    android:textSize="@dimen/font_size14"-->
        <!--                    android:textStyle="bold" />-->
        <!--            </RelativeLayout>-->
        <!--            <androidx.recyclerview.widget.RecyclerView-->
        <!--                android:id="@+id/rv_idcard"-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginTop="@dimen/space_8"-->
        <!--                android:layout_marginLeft="@dimen/space_15"-->
        <!--                android:padding="@dimen/space_5"/>-->
        <!--        </LinearLayout>-->
        <!--    </androidx.core.widget.NestedScrollView>-->

        <TextView
            android:id="@+id/tv_scan_upload"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginTop="@dimen/space_25"
            android:layout_marginRight="@dimen/space_15"
            android:background="@drawable/shape_corner5_3c6af4"
            android:paddingHorizontal="@dimen/space_30"
            android:paddingVertical="@dimen/space_10"
            android:text="@string/scan_upload"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size16"
            android:visibility="gone"/>

    </LinearLayout>
</LinearLayout>