<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

<!--    <TextView-->
<!--        android:id="@+id/text"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_centerInParent="true"-->
<!--        android:text="0%"-->
<!--        android:textColor="#000000"-->
<!--        android:visibility="gone" />-->

    <RelativeLayout
        android:id="@+id/rnl_no_net"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:gravity="center"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableTop="@mipmap/empty"
            android:drawablePadding="30dp"
            android:gravity="center"
            android:text="请检查网络环境并重试"
            android:textColor="#999999"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/tv_refresh"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_hint"
            android:layout_centerInParent="true"
            android:layout_marginTop="10dp"
            android:background="@drawable/shape_corner5_3c6af4"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingTop="5dp"
            android:paddingRight="10dp"
            android:paddingBottom="5dp"
            android:text="刷新页面"
            android:textColor="@color/white"
            android:textSize="18sp" />
    </RelativeLayout>
</RelativeLayout>