<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceUploadMaterialViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceUploadMaterialViewModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.gc.notarizationpc.widget.SelfServiceStepBar
            android:id="@+id/self_service_step_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/space_30"
            android:layout_marginTop="@dimen/space_30"
            android:layout_marginEnd="@dimen/space_30"
            android:layout_marginBottom="@dimen/space_30"
            android:background="@drawable/shape_corner5_white"
            android:padding="@dimen/space_35">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableLeft="@mipmap/pickup_way"
                        android:text="@string/notaryDocumentPickUpWay"
                        android:textColor="@color/color3333"
                        android:textSize="@dimen/font_size18"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="wrap_content"
                        android:layout_height="1dp"
                        android:layout_marginLeft="@dimen/space_20"
                        android:background="@color/colorE8E8EB" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_30"
                    android:layout_marginBottom="@dimen/space_40"
                    android:paddingLeft="@dimen/space_30">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pickUpWay"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14" />

                        <RadioGroup
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_20"
                            android:orientation="horizontal">

                            <RadioButton
                                android:id="@+id/rb_pickup_way_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:button="@null"
                                android:checked="true"
                                android:drawableLeft="@drawable/select_radio"
                                android:drawablePadding="@dimen/space_10"
                                android:text="@string/toPickUp"
                                android:textColor="@color/color3333"
                                android:textSize="@dimen/font_size14" />

                            <RadioButton
                                android:id="@+id/rb_email_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/space_30"
                                android:button="@null"
                                android:checked="false"
                                android:drawableLeft="@drawable/select_radio"
                                android:drawablePadding="@dimen/space_10"
                                android:text="@string/toEmail"
                                android:textColor="@color/color3333"
                                android:textSize="@dimen/font_size14" />
                        </RadioGroup>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/lnl_lingqu"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_50"
                        android:layout_weight="5"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/space_15"
                            android:text="@string/pickUpNotaryOffice"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/self_service_pick_up_address"
                            style="@style/edit_spinner_edit_sty"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:drawablePadding="@dimen/space_8"
                            android:editable="false"
                            android:gravity="center_vertical"
                            android:imeOptions="actionDone"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_8"
                            android:singleLine="true" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/lnl_notray_addr"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/space_50"
                        android:layout_weight="5"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/space_15"
                            android:text="@string/addressString"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/self_service_address"
                            style="@style/edit_spinner_edit_sty"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:drawablePadding="@dimen/space_8"
                            android:editable="false"
                            android:gravity="center_vertical"
                            android:imeOptions="actionDone"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_8"
                            android:singleLine="true" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/lnl_lingqu_time"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/space_15"
                            android:text="@string/pickUpTime"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/self_service_pickup_time"
                            style="@style/edit_spinner_edit_sty"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:drawablePadding="@dimen/space_8"
                            android:editable="false"
                            android:gravity="center_vertical"
                            android:imeOptions="actionDone"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_8"
                            android:singleLine="true" />
                    </LinearLayout>


                    <LinearLayout
                        android:id="@+id/lnl_recipient"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_50"
                        android:layout_weight="5"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/space_15"
                            android:text="@string/recipient"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/edt_recipient"
                            style="@style/edit_spinner_edit_sty"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:drawablePadding="@dimen/space_8"
                            android:gravity="center_vertical"
                            android:imeOptions="actionDone"
                            android:maxLength="80"
                            android:maxLines="1"
                            android:text="@={viewModel.recipient}"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_8"
                            android:singleLine="true" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/lnl_phone"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/space_50"
                        android:layout_weight="5"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/space_15"
                            android:text="@string/phone_blank"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/edt_phone"
                            style="@style/edit_spinner_edit_sty"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:digits="0123456789"
                            android:drawablePadding="@dimen/space_8"
                            android:gravity="center_vertical"
                            android:imeOptions="actionDone"
                            android:inputType="number"
                            android:text="@={viewModel.phoneNum}"
                            android:maxLength="11"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_8"
                            android:singleLine="true" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/lnl_address"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/space_15"
                            android:text="@string/post_address"
                            android:textColor="@color/color666666"
                            android:textSize="@dimen/font_size14" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/edt_post_address"
                            style="@style/edit_spinner_edit_sty"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/shape_corner4_f1f1f2"
                            android:drawablePadding="@dimen/space_8"
                            android:text="@={viewModel.address}"
                            android:gravity="center_vertical"
                            android:imeOptions="actionDone"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/space_20"
                            android:paddingVertical="@dimen/space_8"
                            android:singleLine="true" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableLeft="@mipmap/icon_marital_title"
                        android:text="@string/uploadMaterialText"
                        android:textColor="@color/color3333"
                        android:textSize="@dimen/font_size18"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="wrap_content"
                        android:layout_height="1dp"
                        android:layout_marginLeft="@dimen/space_20"
                        android:background="@color/colorE8E8EB" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="@dimen/space_60"
                    binding:itemBinding="@{viewModel.itemBinding}"
                    binding:items="@{viewModel.observableList}"
                    binding:layoutManager="@{LayoutManagers.grid(2)}" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_lastStep"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_corner5_e8e8e8"
                    android:paddingHorizontal="@dimen/space_60"
                    android:paddingVertical="@dimen/space_10"
                    android:text="@string/lastStep"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size16"
                    android:textStyle="bold"
                    binding:onClickCommand="@{viewModel.lastStep}" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_20"
                    android:background="@drawable/shape_corner5_2568ff"
                    android:paddingHorizontal="@dimen/space_60"
                    android:paddingVertical="@dimen/space_10"
                    android:text="@string/nextStep"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size16"
                    android:textStyle="bold"
                    binding:onClickCommand="@{viewModel.nextStep}" />

            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>


</layout>