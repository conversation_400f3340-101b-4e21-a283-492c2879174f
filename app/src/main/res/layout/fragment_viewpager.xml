<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="me.goldze.mvvmhabit.base.BaseViewModel" />

        <import type="com.gc.notarizationpc.ui.adapter.DataViewPagerAdapter" />

        <variable
            name="viewModel"
            type="BaseViewModel" />

        <variable
            name="adapter"
            type="DataViewPagerAdapter" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:binding="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70"/>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            binding:tabGravity="fill"
            binding:tabBackground="@color/colorPrimary"
            binding:tabRippleColor="@android:color/transparent"
            binding:tabSelectedTextColor="@color/colorPrimary"
            binding:tabTextColor="@android:color/black" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F0F0F0" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
             />
    </LinearLayout>
</layout>