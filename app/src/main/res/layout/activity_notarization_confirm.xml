<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/details_pop"
    android:paddingLeft="@dimen/space_80"
    android:paddingRight="@dimen/space_80">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_50"
        android:text="申请信息"
        android:textSize="@dimen/font_size_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableLeft="@drawable/apply_02"
        android:drawablePadding="@dimen/space_10"
        android:text="申请人"
        android:textSize="@dimen/font_size_20"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <LinearLayout
        android:id="@+id/name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_30"
        android:orientation="horizontal"
        app:layout_constraintEnd_toStartOf="@id/gender"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/hint">

        <TextView
            android:layout_width="@dimen/space_100"
            android:layout_height="wrap_content"
            android:text="姓名"
            android:textSize="@dimen/font_size_20" />

        <TextView
            android:id="@+id/tvName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/border_no_fouse"
            android:textColor="@color/gary_82"
            android:textSize="@dimen/font_size_20"
            tools:text="姓名" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/gender"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/space_50"
        android:layout_marginTop="@dimen/space_30"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/name"
        app:layout_constraintTop_toBottomOf="@id/hint">

        <TextView
            android:layout_width="@dimen/space_100"
            android:layout_height="wrap_content"
            android:text="性别"
            android:textSize="@dimen/font_size_20" />

        <TextView
            android:id="@+id/tvGender"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/border_no_fouse"
            android:textColor="@color/gary_82"
            android:textSize="@dimen/font_size_20"
            tools:text="姓名" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/birth"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_30"
        android:orientation="horizontal"
        app:layout_constraintEnd_toStartOf="@id/idCard"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/name">

        <TextView
            android:layout_width="@dimen/space_100"
            android:layout_height="wrap_content"
            android:text="出生日期"
            android:textSize="@dimen/font_size_20" />

        <TextView
            android:id="@+id/tvBirth"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/border_no_fouse"
            android:textColor="@color/gary_82"
            android:textSize="@dimen/font_size_20"
            tools:text="姓名" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/idCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/space_50"
        android:layout_marginTop="@dimen/space_30"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/birth"
        app:layout_constraintTop_toBottomOf="@id/name">

        <TextView
            android:layout_width="@dimen/space_100"
            android:layout_height="wrap_content"
            android:text="身份证号"
            android:textSize="@dimen/font_size_20" />

        <TextView
            android:id="@+id/tvIdCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/border_no_fouse"
            android:textColor="@color/gary_82"
            android:textSize="@dimen/font_size_20"
            tools:text="姓名" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/mobile"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_30"
        android:orientation="horizontal"
        app:layout_constraintEnd_toStartOf="@id/address"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/birth">

        <TextView
            android:layout_width="@dimen/space_100"
            android:layout_height="wrap_content"
            android:text="联系电话"
            android:textSize="@dimen/font_size_20" />

        <TextView
            android:id="@+id/tvMobile"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/border_no_fouse"
            android:textColor="@color/gary_82"
            android:textSize="@dimen/font_size_20"
            tools:text="姓名" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/address"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/space_50"
        android:layout_marginTop="@dimen/space_30"
        android:gravity="right"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/mobile"
        app:layout_constraintTop_toBottomOf="@id/idCard">

        <TextView
            android:layout_width="@dimen/space_100"
            android:layout_height="wrap_content"
            android:text="地址"
            android:textSize="@dimen/font_size_20" />

        <TextView
            android:id="@+id/tvAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/border_no_fouse"
            android:textColor="@color/gary_82"
            android:textSize="@dimen/font_size_20"
            tools:text="姓名" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:layout_width="140dp"
        android:layout_height="@dimen/space_50"
        android:layout_marginBottom="@dimen/dp_40"
        android:background="@drawable/btn_login_bg"
        android:onClick="notarizationClick"
        android:text="下一步"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
