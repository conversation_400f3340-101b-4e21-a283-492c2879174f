<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.SelfApplyCompanylListItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelfApplyCompanylListItemViewModel" />

    </data>

    <LinearLayout
        android:id="@+id/lnl_personal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/space_25"
        android:paddingVertical="@dimen/space_10">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14"
            android:text="@{viewModel.entity.corporationName}"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14"
            android:text="@{viewModel.entity.role}"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14"
            android:text="@{viewModel.entity.legalRepresentative}"/>


        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14"
            android:text="@{viewModel.entity.contactNum}"/>


        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/iv_edit"
                android:layout_width="@dimen/space_25"
                android:layout_height="@dimen/space_25"
                android:layout_alignParentLeft="true"
                android:padding="@dimen/space_3"
                android:src="@mipmap/icon_edit"
                binding:onClickCommand="@{viewModel.editClick}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="@dimen/space_25"
                android:layout_height="@dimen/space_25"
                android:layout_marginLeft="@dimen/space_15"
                android:layout_toRightOf="@+id/iv_edit"
                android:padding="@dimen/space_3"
                android:src="@mipmap/icon_del_gray"
                binding:onClickCommand="@{viewModel.delClick}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />
        </RelativeLayout>

    </LinearLayout>
</layout>