<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="1000dp"
    android:layout_height="600dp"
    android:layout_gravity="center"
    android:background="@drawable/white_solid"
    android:paddingLeft="30dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/space_96"
        android:orientation="vertical">

        <!-- 上面的搜索框 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_96">

            <LinearLayout
                android:layout_width="@dimen/space_400"
                android:layout_height="40dp"
                android:layout_centerInParent="true"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_searchview"
                    android:layout_width="@dimen/space_310"
                    android:layout_height="40dp"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:gravity="center_vertical"
                    android:hint="@string/search_hint"
                    android:imeOptions="actionSearch"
                    android:paddingLeft="30dp"
                    android:singleLine="true"
                    android:textColorHint="@color/colorBFBFBF"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/tv_search"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/space_5"
                    android:background="@drawable/shape_corner5_3c6af4"
                    android:drawableLeft="@mipmap/icon_search"
                    android:drawablePadding="@dimen/space_10"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/space_10"
                    android:text="搜索"
                    android:textColor="@color/colorWhite"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <Button
                android:id="@+id/btn_close"
                android:layout_width="@dimen/space_20"
                android:layout_height="@dimen/space_20"
                android:layout_alignParentTop="true"
                android:layout_alignParentRight="true"
                android:layout_marginTop="@dimen/space_20"
                android:layout_marginRight="@dimen/space_20"
                android:background="@mipmap/icon_close" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignTop="@id/et_searchview"
            android:background="@color/colorF5F5F7"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ListView
                android:id="@+id/name_listview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:divider="@null"
                android:listSelector="#00000000" />
            <!-- 右侧供选择的列表 -->
            <com.gc.notarizationpc.myview.SideBarView
                android:id="@+id/sidrbar"
                android:layout_width="30dp"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:layout_marginRight="@dimen/space_30" />
        </LinearLayout>

    </LinearLayout>

    <!-- 界面中间显示的选中的大写首字母 -->
    <TextView
        android:id="@+id/tv_dialog"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_centerInParent="true"
        android:background="@drawable/alphabet_dialog_background"
        android:gravity="center"
        android:textColor="@color/colorText"
        android:textSize="20sp"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/make_sure_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="@dimen/space_30"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone">

        <Button
            android:id="@+id/search_cancel"
            android:layout_width="@dimen/space_100"
            android:layout_height="@dimen/space_40"
            android:background="@drawable/shape_corner5_e8e8e8"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size16" />

        <Button
            android:id="@+id/search_sure"
            android:layout_width="@dimen/space_100"
            android:layout_height="@dimen/space_40"
            android:layout_marginLeft="@dimen/space_30"
            android:background="@drawable/shape_corner5_3c6af4"
            android:gravity="center"
            android:text="@string/confirm"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/font_size16" />

    </LinearLayout>
</RelativeLayout>