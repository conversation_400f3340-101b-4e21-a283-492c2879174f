<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_shouye">


    <Button
        android:id="@+id/idcardBackClicked"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/new_back"
        android:padding="10dp"
        android:textColor="@color/white" />

    <ImageView
        android:layout_width="600dp"
        android:layout_height="600dp"
        android:layout_centerHorizontal="true"
        android:background="@drawable/read_idcard" />

    <TextView
        android:layout_width="600dp"
        android:layout_height="200dp"
        android:layout_centerHorizontal="true"
        android:background="@color/white"
        android:textColor="@color/black"
        android:textStyle="bold"
        android:textSize="40sp"
        android:text="请将身份证放置于阅读器上"
        android:gravity="center"
        android:layout_marginTop="400dp"/>

</RelativeLayout>