<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.NortaryListItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.NortaryListItemViewModel" />
        <import type="com.gc.mininotarization.R" />


    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/space_10"
        android:background="@drawable/bg_select_notary"
        android:padding="@dimen/space_10">

        <LinearLayout
            android:id="@+id/top_head_image"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/space_32"
            android:layout_marginTop="@dimen/space_20"
            android:layout_marginRight="@dimen/space_32"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_head"
                android:layout_width="@dimen/space_60"
                android:layout_height="@dimen/space_60"
                binding:url="@{viewModel.entity.headImg}"
                tools:ignore="ContentDescription" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_15"
                android:layout_gravity="start"
                android:gravity="center"
                android:orientation="vertical"
                tools:ignore="UseCompoundDrawables">

                <TextView
                    android:id="@+id/tv_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.entity.officeName}"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14"
                    android:textStyle="bold" />
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    binding:background="@{viewModel.stateDraw}"
                    android:orientation="horizontal"
                    android:layout_marginTop="@dimen/space_10"
                    android:gravity="center_vertical"
                    android:layout_gravity="start"
                    android:paddingVertical="@dimen/space_5">
                    <TextView
                        android:id="@+id/tv_state"
                        android:layout_width="@dimen/space_10"
                        android:layout_height="@dimen/space_10"
                        android:layout_marginStart="@dimen/space_10"
                        android:layout_marginEnd="@dimen/space_5"
                        binding:background="@{viewModel.dotDraw}"/>
                    <TextView
                        android:id="@+id/tv_state_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/space_10"
                        android:text="@{viewModel.stateStr}"
                        binding:textColor="@{viewModel.stateColor}"
                        android:textSize="@dimen/font_size10" />

                </LinearLayout>
            </LinearLayout>

        </LinearLayout>
        <LinearLayout
            android:id="@+id/phone_line"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/top_head_image"
            android:layout_centerVertical="true"
            android:layout_marginTop="@dimen/space_10"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/tv_phone_text_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/space_5"
                android:text="手机号："
                android:textColor="@color/color6666"
                android:textAlignment="center"
                android:textSize="@dimen/font_size12" />
            <TextView
                android:id="@+id/tv_phone_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/space_5"
                android:text="@{viewModel.entity.phoneNumber}"
                android:textColor="@color/color6666"
                android:textAlignment="center"
                android:textSize="@dimen/font_size12" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/phone_line"
            android:layout_marginTop="@dimen/space_10"
            android:layout_marginStart="@dimen/space_10"
            android:layout_marginEnd="@dimen/space_10"
            android:paddingVertical="@dimen/space_5"
            android:gravity="center"
            binding:background="@{viewModel.backgroundDraw}"
            binding:onClickCommand="@{viewModel.itemClick}"
            binding:isThrottleFirst="@{Boolean.FALSE}"
            android:clickable="@{viewModel.entity.clickable}"
            android:orientation="horizontal"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/icon_contact"
                android:paddingTop="@dimen/space_3"
                tools:ignore="ContentDescription" />
            <TextView
                android:id="@+id/tv_call"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_5"
                android:text="@string/call"
                android:textColor="@color/white"
                android:textAlignment="center"
                android:textSize="@dimen/font_size14" />
        </LinearLayout>

    </RelativeLayout>

</layout>