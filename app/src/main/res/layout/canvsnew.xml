<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/signatureView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F6F1"
    android:orientation="vertical"
    android:visibility="gone">

    <TextView
        android:id="@+id/sign_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:text="签名区域(请使用正楷签名)"
        android:textSize="20sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="600dp"
        android:layout_gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/main_linlayout"
            android:layout_width="400dp"
            android:layout_height="400dp"
            android:layout_gravity="center_horizontal"
            android:layout_margin="10dp"
            android:background="@drawable/kuang_blue"
            android:orientation="vertical" />


        <RelativeLayout
            android:layout_width="400dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="5dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/bt_clear"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="40dp"
                android:background="@drawable/shape_corner_white"
                android:gravity="center"
                android:text="重签"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="签"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/name_pos"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:background="@drawable/tianzige"
                    android:gravity="center"
                    android:textColor="#0F41A6"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="字"
                    android:textSize="16sp" />
            </LinearLayout>

            <Button
                android:id="@+id/bt"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="40dp"
                android:background="@drawable/shape_corner_blue"
                android:gravity="center"
                android:text="下一个"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:visibility="gone" />
    </LinearLayout>
</LinearLayout>
