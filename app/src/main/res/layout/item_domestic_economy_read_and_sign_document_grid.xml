<?xml version="1.0" encoding="utf-8"?>
<layout  xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">
    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.DomesticEconomyReadAndSignDocumentGridItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.DomesticEconomyReadAndSignDocumentGridItemViewModel" />
    </data>
    <ImageView
        android:id="@+id/pv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        android:layout_marginVertical="@dimen/space_10"
        android:src="@mipmap/erroimage"
        binding:imageUrl='@{String.valueOf(viewModel.entity)}'
        binding:onClickCommand="@{viewModel.amplifyDoc}"
        binding:isThrottleFirst="@{Boolean.FALSE}"
        binding:placeholderRes="@{@drawable/erroimage}"
        />
</layout>
