<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelectNotaryViewModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_corner10_white_all"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rnl_search_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_30"
            android:layout_marginTop="@dimen/space_20"
            android:background="@drawable/shape_corner5_white"
            android:padding="@dimen/space_8">

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="@dimen/space_40"
                android:layout_height="@dimen/space_40"
                android:layout_alignParentRight="true"
                android:layout_alignParentTop="true"
                android:padding="@dimen/space_10"
                android:src="@mipmap/icon_close" />


            <LinearLayout
                android:layout_width="330dp"
                android:layout_height="61dp"
                android:layout_centerInParent="true"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/edt_search"
                    style="@style/edit_spinner_edit_sty"
                    android:layout_width="313dp"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:background="@drawable/shape_corner4_f1f1f2"
                    android:drawablePadding="@dimen/space_8"
                    android:gravity="center_vertical"
                    android:hint="@string/search_hint"
                    android:imeOptions="actionDone"
                    android:maxLines="1"
                    android:padding="@dimen/space_11"
                    android:singleLine="true" />


                <TextView
                    android:id="@+id/tv_search"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toLeftOf="@+id/tv_refresh"
                    android:background="@drawable/shape_corner5_3c6af4"
                    android:drawableLeft="@mipmap/icon_search"
                    android:drawablePadding="@dimen/space_5"
                    android:padding="@dimen/space_9"
                    android:text="@string/search"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size16"
                    binding:onClickCommand="@{viewModel.searchClick}"
                    binding:isThrottleFirst="@{Boolean.FALSE}" />
            </LinearLayout>

        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_below="@+id/rnl_search_bar"
            android:layout_weight="1"
            android:background="#F5F5F7"
            android:gravity="center"
            android:layout_marginTop="@dimen/space_30"
            android:orientation="vertical"
            android:padding="@dimen/space_30">

            <!--            <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout-->
            <!--                android:id="@+id/twinklingRefreshLayout"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_weight="0.8"-->
            <!--                android:layout_height="match_parent"-->
            <!--                binding:onLoadMoreCommand="@{viewModel.onLoadMoreCommand}"-->
            <!--                binding:onRefreshCommand="@{viewModel.onRefreshCommand}"-->
            <!--                binding:tr_head_height="80dp"-->
            <!--                binding:tr_wave_height="80dp">-->
            <TextView
                android:id="@+id/tv_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:gravity="center"
                android:drawableLeft="@mipmap/icon_location"
                android:drawablePadding="@dimen/space_5"
                android:includeFontPadding="true"
                android:padding="0dp"
                android:text="江苏省南京市"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size18"
                android:textStyle="bold"
                binding:onClickCommand="@{viewModel.notaryAddressOnClickCommand}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/list_notarial_office"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/space_20"
                android:layout_marginBottom="@dimen/space_20"
                android:padding="@dimen/space_10"
                binding:itemBinding="@{viewModel.notaryOfficeItemBinding}"
                binding:items="@{viewModel.notaryOfficeList}"
                binding:layoutManager="@{LayoutManagers.linear()}" />
            <!--            </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>-->

        </LinearLayout>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/space_28"
            android:layout_marginBottom="@dimen/space_28"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@+id/tv_confirm"
                android:background="@drawable/shape_corner5_f5f5f7"
                android:paddingHorizontal="@dimen/space_35"
                android:paddingVertical="@dimen/space_8"
                android:text="@string/cancel"
                android:textColor="@color/color6666"
                android:textSize="@dimen/font_size16" />

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_marginRight="@dimen/space_20"
                android:background="@drawable/shape_corner5_3c6af4"
                android:paddingHorizontal="@dimen/space_35"
                android:paddingVertical="@dimen/space_8"
                android:text="@string/confirm"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size16" />
        </RelativeLayout>


    </LinearLayout>
</layout>