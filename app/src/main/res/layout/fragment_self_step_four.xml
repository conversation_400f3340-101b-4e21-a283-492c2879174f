<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/stepSelf"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_20"
        android:src="@drawable/zz_04"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="@dimen/space_80"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="@dimen/space_100"
        android:padding="25dp"
        android:scrollbars="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stepSelf" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginBottom="@dimen/space_70"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tvUpStep"
            style="@style/tv_black_wrap_18"
            android:layout_margin="10dp"
            android:layout_marginBottom="@dimen/space_20"
            android:background="@drawable/shape_tag"
            android:gravity="center"
            android:onClick="nextStepClick"
            android:paddingLeft="15dp"
            android:paddingTop="5dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp"
            android:text="上一步"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_20" />

        <TextView
            android:id="@+id/tvWdzdStep"
            style="@style/tv_black_wrap_18"
            android:layout_margin="10dp"
            android:layout_marginBottom="@dimen/space_20"
            android:background="@drawable/shape_round_gray"
            android:gravity="center"
            android:onClick="nextNoZlStepClick"
            android:paddingLeft="15dp"
            android:paddingTop="5dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp"
            android:text="未带资料入口"
            android:textColor="#333333"
            android:textSize="@dimen/font_size_20"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvNextStep"
            style="@style/tv_black_wrap_18"
            android:layout_margin="10dp"
            android:layout_marginBottom="@dimen/space_20"
            android:background="@drawable/shape_tag"
            android:gravity="center"
            android:onClick="nextStepClick"
            android:paddingLeft="15dp"
            android:paddingTop="5dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp"
            android:text="下一步"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_20" />
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginBottom="@dimen/space_20"
        android:gravity="center"
        android:orientation="horizontal"
        android:text="提示:若未带齐相关材料，可以稍后联系公证员通过其他方式补齐"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>