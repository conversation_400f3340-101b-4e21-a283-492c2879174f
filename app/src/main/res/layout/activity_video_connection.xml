<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.VideoConnectionViewModel" />
    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/title_bar"
            android:layout_marginLeft="@dimen/space_20"
            android:layout_marginRight="@dimen/space_20"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="20dp">

            <ImageView
                android:id="@+id/iv_splx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scaleType="fitXY"
                android:src="@mipmap/choose_notary"
                android:onClick="selectNotary"
                tools:ignore="ContentDescription" />

            <ImageView
                android:id="@+id/iv_zzbz"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_70"
                android:scaleType="fitXY"
                android:src="@mipmap/match_notary"
                android:onClick="matchNotary"
                tools:ignore="ContentDescription" />

        </LinearLayout>


    </RelativeLayout>
</layout>