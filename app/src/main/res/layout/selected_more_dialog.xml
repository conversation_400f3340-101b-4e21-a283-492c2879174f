<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="5dp"
        android:layout_marginTop="40dp"
        android:orientation="vertical"
        android:background="@drawable/shape_corner_white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/titleTop"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:textSize="@dimen/font_size_18"
                android:text="点击选择公证事项(可多选)"
                android:padding="5dp"
                android:layout_margin="@dimen/space_10" />

            <TextView
                android:id="@+id/closeDialog"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:textSize="@dimen/font_size_18"
                android:text="X"
                android:gravity="center"
                android:layout_alignParentRight="true"
                android:padding="5dp"
                android:layout_margin="@dimen/space_10" />

        </RelativeLayout>

        <EditText
            android:id="@+id/searchText"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:textCursorDrawable="@drawable/cursor_drawable"
            android:padding="3dp"
            android:visibility="gone"
            android:background="@drawable/shape_line_grey"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="5dp"
            android:layout_marginLeft="5dp"
            android:minHeight="@dimen/space_300"/>

        <TextView
            android:id="@+id/sureItemsText"
            android:layout_width="@dimen/space_150"
            android:layout_height="50dp"
            android:textSize="@dimen/font_size_18"
            android:text="确认"
            android:gravity="center"
            android:textColor="@color/white"
            android:paddingTop="5dp"
            android:paddingRight="25dp"
            android:paddingLeft="25dp"
            android:paddingBottom="5dp"
            android:layout_margin="@dimen/space_10"
            android:layout_gravity="right|center_vertical"
            android:background="@drawable/shape_tag" />

    </LinearLayout>

</RelativeLayout>

