<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_corner10_white_all"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingHorizontal="@dimen/space_100"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size20"
            android:text="@string/video_exit_alert"
            android:layout_marginTop="@dimen/space_60"
            android:layout_marginBottom="@dimen/space_40"
            android:layout_gravity="center"
            android:textAlignment="center"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/space_40"
            android:layout_marginBottom="@dimen/space_60"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/dialog_video_exit_alert_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/cancel"
                android:textSize="@dimen/font_size16"
                android:textColor="@color/white"
                android:background="@drawable/shape_corner5_e8e8e8"
                android:layout_marginRight="@dimen/space_80"
                android:paddingVertical="@dimen/space_10"
                android:paddingHorizontal="@dimen/space_40"/>

            <TextView
                android:id="@+id/dialog_video_exit_alert_decide"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/confirm"
                android:textSize="@dimen/font_size16"
                android:textColor="@color/white"
                android:background="@drawable/shape_corner5_3c6af4"
                android:paddingVertical="@dimen/space_10"
                android:paddingHorizontal="@dimen/space_40"/>
        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>