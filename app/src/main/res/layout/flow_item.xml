<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/label_name"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:layout_marginBottom="10dp"
    android:background="@drawable/shape_corner4_white_all"
    android:gravity="center"
    android:paddingLeft="@dimen/space_30"
    android:paddingRight="@dimen/space_30"
    android:paddingTop="@dimen/space_5"
    android:paddingBottom="@dimen/space_5"
    android:textColor="@color/color666666"
    android:textSize="@dimen/font_size14">

</TextView>