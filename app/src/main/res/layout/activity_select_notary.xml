<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelectNotaryViewModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />

    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <RelativeLayout
            android:id="@+id/rnl_search_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/title_bar"
            android:layout_marginHorizontal="@dimen/space_30"
            android:layout_marginTop="@dimen/space_30"
            android:background="@drawable/shape_corner5_white"
            android:padding="@dimen/space_15">

            <TextView
                android:id="@+id/tv_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:drawableLeft="@mipmap/icon_location"
                android:drawablePadding="@dimen/space_5"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size20"
                android:textStyle="bold"
                android:gravity="center"
                binding:onClickCommand="@{viewModel.notaryAddressOnClickCommand}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/spinner_edit_text"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/space_5"
                android:layout_toLeftOf="@+id/tv_search"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/notary_hint"
                android:imeOptions="actionDone"
                android:maxLines="1"
                android:padding="@dimen/space_11"
                android:singleLine="true"
                android:text="@={viewModel.searchEdit}" />

            <TextView
                android:id="@+id/tv_search"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@+id/tv_refresh"
                android:background="@drawable/shape_corner2_3c6af4"
                android:drawableLeft="@mipmap/icon_search"
                android:drawablePadding="@dimen/space_5"
                android:padding="@dimen/space_9"
                android:text="@string/search"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size16"
                binding:onClickCommand="@{viewModel.searchClick}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />

            <TextView
                android:id="@+id/tv_refresh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/space_40"
                android:layout_marginRight="@dimen/space_5"
                android:layout_toLeftOf="@+id/tv_back"
                android:background="@drawable/shape_corner2_e8e8e8"
                android:drawableLeft="@mipmap/icon_refresh"
                android:drawablePadding="@dimen/space_5"
                android:padding="@dimen/space_10"
                android:text="@string/refresh"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size16"
                binding:onClickCommand="@{viewModel.refreshClick}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />

            <TextView
                android:id="@+id/tv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@drawable/shape_corner2_e8e8e8"
                android:drawableLeft="@mipmap/icon_back"
                android:drawablePadding="@dimen/space_5"
                android:onClick="finishActivity"
                android:padding="@dimen/space_10"
                android:text="@string/back"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size16"
                binding:onClickCommand="@{viewModel.backClick}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rnl_empty"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/rnl_search_bar"
            android:layout_margin="@dimen/space_30"
            android:background="@drawable/shape_corner5_white"
            android:gravity="center"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="暂无数据"
                android:textSize="@dimen/font_size14"
                android:textColor="@color/color6666" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/lnl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/rnl_search_bar"
            android:layout_margin="@dimen/space_30"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="visible">

<!--            <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout-->
<!--                android:id="@+id/twinklingRefreshLayout"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_weight="0.8"-->
<!--                android:layout_height="match_parent"-->
<!--                binding:onLoadMoreCommand="@{viewModel.onLoadMoreCommand}"-->
<!--                binding:onRefreshCommand="@{viewModel.onRefreshCommand}"-->
<!--                binding:tr_head_height="80dp"-->
<!--                binding:tr_wave_height="80dp">-->

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/list_notarial_office"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.8"
                android:background="@drawable/shape_corner5_white"
                android:padding="@dimen/space_10"

                binding:layoutManager="@{LayoutManagers.linear()}" />
<!--            </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>-->

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="vertical"
            android:paddingTop="@dimen/space_2"
            android:background="@drawable/shape_corner5_white"
            android:paddingBottom="@dimen/space_2"
            android:layout_marginLeft="@dimen/space_20">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/space_20">

                <TextView
                    android:id="@+id/tv_notaryname"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="@dimen/space_20"
                    android:layout_marginTop="@dimen/space_20"
                    android:layout_marginBottom="@dimen/space_10"
                    android:drawableLeft="@mipmap/icon_office_normal"
                    android:drawablePadding="@dimen/space_5"
                    android:text=""
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size16"
                    android:textStyle="bold"
                    android:visibility="invisible" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_8"
                    android:layout_marginTop="@dimen/space_20"
                    android:layout_marginBottom="@dimen/space_10"
                    android:layout_toRightOf="@+id/tv_notaryname"
                    android:onClick="getVersion"
                    android:text="@string/inOrderToEnsure"
                    android:textColor="@color/color8888"
                    android:textSize="@dimen/font_size14"
                    android:textStyle="bold" />
            </RelativeLayout>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/list_notary"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_marginRight="@dimen/space_10"
                android:layout_marginTop="@dimen/space_5"
                android:layout_marginBottom="@dimen/space_20"
                android:layout_weight="2"
                android:scrollbarStyle="outsideInset"
                android:paddingHorizontal="@dimen/space_15"
                binding:itemBinding="@{viewModel.notaryItemBinding}"
                binding:items="@{viewModel.notaryList}"
                binding:layoutManager="@{LayoutManagers.grid(3)}"
                android:visibility="visible"/>



        </LinearLayout>

        </LinearLayout>


    </RelativeLayout>
</layout>