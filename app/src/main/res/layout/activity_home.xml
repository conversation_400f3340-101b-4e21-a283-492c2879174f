<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.HomeViewModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <LinearLayout
            android:id="@+id/lnl_content"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_below="@+id/title_bar"
            android:layout_marginLeft="@dimen/space_30"
            android:layout_marginTop="@dimen/space_25"
            android:layout_marginRight="@dimen/space_30"
            android:layout_weight="1"
            android:gravity="center">
            <!--left-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.8"
                android:background="@mipmap/bg_home_left"
                android:orientation="vertical"
                android:padding="@dimen/space_10">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <View
                        android:id="@+id/view_divider"
                        android:layout_width="2dp"
                        android:layout_height="@dimen/space_15"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:background="@color/white"
                        android:visibility="gone" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="@dimen/space_6"
                        android:layout_toRightOf="@+id/view_divider"
                        android:drawableLeft="@mipmap/icon_enter"
                        android:drawablePadding="@dimen/space_5"
                        android:text="快捷入口"
                        android:textColor="@color/color3333"
                        android:textSize="@dimen/font_size20" />
                </RelativeLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/list_quick_enter"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/space_20"
                    android:layout_marginBottom="@dimen/space_20"
                    android:layout_weight="1"
                    binding:itemBinding="@{viewModel.itemBinding}"
                    binding:items="@{viewModel.observableList}"
                    binding:layoutManager="@{LayoutManagers.linear()}" />


            </LinearLayout>

            <!--right-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_weight="2"
                android:orientation="horizontal"
                android:paddingTop="@dimen/space_2"
                android:paddingBottom="@dimen/space_2">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="@dimen/space_10"
                    android:layout_marginRight="@dimen/space_10"
                    android:layout_weight="1.5"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_splx"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentTop="true"
                        android:layout_marginBottom="@dimen/space_10"
                        android:layout_weight="1"
                        android:onClick="goToSPLX"
                        android:scaleType="fitXY"
                        android:src="@mipmap/module_splx" />

                    <ImageView
                        android:id="@+id/iv_zzbz"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_marginTop="@dimen/space_20"
                        android:layout_weight="1"
                        android:onClick="goToBZCX"
                        android:scaleType="fitXY"
                        android:src="@mipmap/module_zzbz" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="@dimen/space_20"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_yydj"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginBottom="@dimen/space_6"
                        android:layout_weight="1"
                        android:onClick="goToYYDJ"
                        android:scaleType="fitXY"
                        android:src="@mipmap/module_yydj" />

                    <ImageView
                        android:id="@+id/iv_spbz"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginTop="@dimen/space_20"
                        android:layout_marginBottom="@dimen/space_6"
                        android:layout_weight="1"
                        android:onClick="goToSPBZ"
                        android:scaleType="fitXY"
                        android:src="@mipmap/module_spbz" />

                    <ImageView
                        android:id="@+id/iv_bzcx"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginTop="@dimen/space_20"
                        android:layout_weight="1"
                        android:onClick="goToZZBZ"
                        android:scaleType="fitXY"
                        android:src="@mipmap/module_bzcx" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="@dimen/space_15"
            android:layout_marginBottom="@dimen/space_30"
            android:src="@mipmap/icon_chat"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/lnl_lan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:padding="@dimen/space_10">

            <TextView
                android:id="@+id/tv_Version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="@dimen/space_30"
                android:layout_marginTop="@dimen/space_10"
                android:layout_marginBottom="@dimen/space_30"
                android:onClick="getVersion"
                android:text="版本号"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/tv_bo"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@+id/tv_zh"
                android:background="@drawable/shape_corner5_white"
                android:gravity="center"
                android:paddingHorizontal="@dimen/space_15"
                android:paddingVertical="@dimen/space_8"
                android:text="བོད་ཡིག"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size18" />

            <TextView
                android:id="@+id/tv_zh"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_marginRight="@dimen/space_20"
                android:background="@drawable/shape_corner5_3c6af4"
                android:gravity="center"
                android:paddingVertical="@dimen/space_8"
                android:text="中文"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size18" />

        </RelativeLayout>

    </LinearLayout>
</layout>