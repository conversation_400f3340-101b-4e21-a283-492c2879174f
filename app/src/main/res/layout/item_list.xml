<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.FragmentListItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.FragmentListItemViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/linear_order"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/space_5"
        android:orientation="vertical"
        android:padding="@dimen/space_5"
        binding:onClickCommand="@{viewModel.itemClick}">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/order_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginStart="@dimen/space_5"
                android:layout_marginTop="@dimen/space_5"
                android:layout_marginEnd="@dimen/space_5"
                android:layout_marginBottom="@dimen/space_5"
                android:text="受理状态"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/font_size16"
                binding:onClickCommand="@{viewModel.stateClick}" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#FFF5F6F6" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_5"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="订单编号:"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size16" />

            <TextView
                android:id="@+id/order_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2022061313552010"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size16" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_5"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.2"
                android:text="借款人:"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/borrow_man"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.2"
                android:text="借款金额:"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/borrow_money"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size14" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_5"
            android:layout_marginTop="@dimen/space_10"
            android:orientation="horizontal">


            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:text="申请时间:"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/apply_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5.2"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size14" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_5"
            android:layout_marginTop="@dimen/space_10"
            android:orientation="horizontal">


            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:text="受理时间:"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/accept_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5.2"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size14" />

        </LinearLayout>


    </LinearLayout>
</layout>