<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    >

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderDetailUnitInformationItemViewModel" />

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/space_40"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/item_order_detail_unit_information_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            binding:text="@{viewModel.entity.corporationName}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:paddingHorizontal="@dimen/space_30"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/item_order_detail_unit_information_certificate_type"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            binding:text="@{viewModel.entity.corporationCredentialTypeStr}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/item_order_detail_unit_information_certificate_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            binding:text="@{viewModel.entity.corporationCredentialNum}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/item_order_detail_unit_information_legal_person_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.5"
            binding:text="@{viewModel.entity.legalRepresentative}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/item_order_detail_unit_information_legal_or_representative_phone_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            binding:text="@{viewModel.entity.contactNum}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <TextView
            android:id="@+id/item_order_detail_unit_information_address"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="4"
            binding:text="@{viewModel.entity.corporationAddress}"
            android:textAlignment="textStart"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

    </LinearLayout>
</layout>