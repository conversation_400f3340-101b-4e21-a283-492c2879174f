<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceMaterialToUploadGridItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceMaterialToUploadGridItemViewModel" />
    </data>
<!--<RelativeLayout-->
<!--    android:layout_width="@dimen/space_100"-->
<!--    android:layout_height="@dimen/space_100"-->
<!--    android:layout_marginTop="@dimen/space_10"-->
<!--    android:layout_marginRight="@dimen/space_10">-->
<!--    <RelativeLayout-->
<!--        android:layout_width="@dimen/space_100"-->
<!--        android:layout_height="@dimen/space_100"-->
<!--        android:layout_marginTop="@dimen/space_10"-->
<!--        android:layout_marginRight="@dimen/space_10">-->

<!--        <ImageView-->
<!--            android:layout_width="@dimen/space_90"-->
<!--            android:layout_height="@dimen/space_90"-->
<!--            android:scaleType="fitXY"-->
<!--            binding:onClickCommand="@{viewModel.uploadMaterial}"-->
<!--            binding:placeholderRes="@mipmap/icon_calendar"-->
<!--            binding:imageUrl='@{viewModel.entity.fileUrl}'-->
<!--            />-->
<!--    </RelativeLayout>-->
<!--    <ImageView-->
<!--        android:id="@+id/materila_close"-->
<!--        android:layout_width="@dimen/space_20"-->
<!--        android:layout_height="@dimen/space_20"-->
<!--        android:background="@mipmap/icon_round_del"-->
<!--        binding:isVisible="@{viewModel.hideClose}"-->
<!--        binding:onClickCommand="@{viewModel.deleteMaterial}"-->
<!--        android:layout_alignParentRight="true"-->
<!--        android:layout_alignParentTop="true"/>-->
<!--</RelativeLayout>-->

    <RelativeLayout
        android:layout_width="110dp"
        android:layout_height="110dp">

        <ImageView
            android:id="@+id/work_iv_wip_picture"
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:layout_centerInParent="true"
            android:scaleType="fitXY"
            android:src="@mipmap/erroimage"
            binding:imageUrl='@{viewModel.entity.fileUrl}'
            binding:onClickCommand="@{viewModel.uploadMaterial}"
            binding:placeholderRes="@{@drawable/icon_add}" />

        <ImageView
            android:layout_width="@dimen/space_20"
            android:layout_height="@dimen/space_20"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            binding:isVisible="@{viewModel.entity.showDel}"
            binding:onClickCommand="@{viewModel.deleteMaterial}"
            android:src="@mipmap/icon_round_del"
            android:background="@mipmap/icon_round_del"/>
    </RelativeLayout>



</layout>