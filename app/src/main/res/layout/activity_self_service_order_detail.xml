<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    >

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderDetailViewModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />

    </data>


    <LinearLayout
        android:id="@+id/order_detail"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:id="@+id/activity_order_detail_main_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_corner10_white_all"
                android:orientation="vertical"
                android:layout_marginHorizontal="@dimen/space_30"
                android:layout_marginTop="@dimen/space_30"
                android:layout_marginBottom="@dimen/space_110"
                android:padding="@dimen/space_15">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_50"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableLeft="@mipmap/order_icon"
                        android:drawablePadding="@dimen/space_10"
                        binding:text="@{viewModel.orderStatus}"
                        android:textColor="@color/color3333"
                        android:textSize="@dimen/font_size18"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:drawableLeft="@mipmap/back_grey_icon"
                        android:drawablePadding="@dimen/space_10"
                        android:text="@string/back"
                        android:textColor="@color/color3333"
                        binding:onClickCommand="@{viewModel.back}"
                        android:textSize="@dimen/font_size14"
                        binding:isThrottleFirst="@{Boolean.FALSE}" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_1"
                    android:background="@color/colorE8E8EB" />

                <androidx.core.widget.NestedScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/space_15"
                    android:layout_marginTop="@dimen/space_20"
                    android:layout_marginBottom="@dimen/space_30">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableLeft="@mipmap/split_vertical_icon"
                            android:drawablePadding="@dimen/space_10"
                            android:paddingLeft="@dimen/space_2"
                            android:text="@string/apply_info"
                            android:textColor="@color/color3333"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_10"
                            android:background="@drawable/shape_corner8_f5f5f7"
                            android:orientation="vertical"
                            android:padding="@dimen/space_30">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/order_detail_apply_order_number_label"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/applyOrderNumber"
                                    android:textAlignment="textEnd"
                                    android:textColor="@color/color999999"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_order_number"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/space_10"
                                    android:layout_weight="6"
                                    binding:text="@{viewModel.selfServiceOrderDetailModel.recordId}"
                                    android:textAlignment="textStart"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_notary_destination_label"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/notaryDestination"
                                    android:textAlignment="textEnd"
                                    android:textColor="@color/color999999"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_notary_destination"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/space_10"
                                    android:layout_weight="4"
                                    binding:text="@{viewModel.selfServiceOrderDetailModel.usedPurposeStr}"
                                    android:textAlignment="textStart"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_order_time_label"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/orderTime"
                                    android:textAlignment="textEnd"
                                    android:textColor="@color/color999999"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_order_time"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/space_10"
                                    android:layout_weight="6"
                                    binding:text="@{viewModel.selfServiceOrderDetailModel.createdTime}"
                                    android:textAlignment="textStart"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginVertical="@dimen/space_10"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/order_detail_apply_notary_office_label"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/notary_office"
                                    android:textAlignment="textEnd"
                                    android:textColor="@color/color999999"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_notary_office"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/space_10"
                                    android:layout_weight="6"
                                    binding:text="@{viewModel.selfServiceOrderDetailModel.createdOrganName}"
                                    android:textAlignment="textStart"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_notarization_item_label"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/notarization_item"
                                    android:textAlignment="textEnd"
                                    android:textColor="@color/color999999"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_notarization_item"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/space_10"
                                    android:layout_weight="4"
                                    binding:text="@{viewModel.selfServiceOrderDetailModel.applicationItemStr}"
                                    android:textAlignment="textStart"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_apply_lan_label"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/apply_lan"
                                    android:textAlignment="textEnd"
                                    android:textColor="@color/color999999"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_apply_lan"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/space_10"
                                    android:layout_weight="6"
                                    binding:text="@{viewModel.selfServiceOrderDetailModel.translateLanguageStr}"
                                    android:textAlignment="textStart"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/order_detail_apply_pickup_address_label"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/pickUpAddress"
                                    android:textAlignment="textEnd"
                                    android:textColor="@color/color999999"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_pickup_address"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/space_10"
                                    android:layout_weight="6"
                                    binding:text="@{viewModel.selfServiceOrderDetailModel.postAddress}"
                                    android:textAlignment="textStart"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_pickup_way_label"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/pickUpWay"
                                    android:textAlignment="textEnd"
                                    android:textColor="@color/color999999"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_pickup_way"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/space_10"
                                    android:layout_weight="4"
                                    binding:text="@{viewModel.receiveWay}"
                                    android:textAlignment="textStart"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_notarization_address_label"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/notarization_address"
                                    android:textAlignment="textEnd"
                                    android:textColor="@color/color999999"
                                    android:textSize="@dimen/font_size14" />

                                <TextView
                                    android:id="@+id/order_detail_apply_notarization_address"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/space_10"
                                    android:layout_weight="6"
                                    binding:text="@{viewModel.selfServiceOrderDetailModel.usedPlaceStr}"
                                    android:textAlignment="textStart"
                                    android:textColor="@color/color666666"
                                    android:textSize="@dimen/font_size14" />
                            </LinearLayout>

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_20"
                            android:drawableLeft="@mipmap/split_vertical_icon"
                            android:drawablePadding="@dimen/space_10"
                            android:paddingLeft="@dimen/space_2"
                            android:text="@string/partyInformation"
                            android:textColor="@color/color3333"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_10"
                            android:background="@drawable/shape_corner8_f5f5f7"
                            android:orientation="vertical"
                            android:padding="@dimen/space_10">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/natural_person_information"
                                android:textColor="@color/colorBFBFBF"
                                android:textSize="@dimen/font_size12" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_10"
                                android:background="@drawable/shape_corner4_white_all"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="34dp"
                                    android:background="@drawable/shape_corner4_e7f2fe_top_right_left"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="@string/role"
                                        android:textAlignment="center"
                                        android:textColor="@color/color8A8E99"
                                        android:textSize="@dimen/font_size14" />

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="@string/nameString"
                                        android:textAlignment="textStart"
                                        android:textColor="@color/color8A8E99"
                                        android:textSize="@dimen/font_size14" />

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1.5"
                                        android:text="@string/phone_num"
                                        android:textAlignment="textStart"
                                        android:textColor="@color/color8A8E99"
                                        android:textSize="@dimen/font_size14" />

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="@string/certificate_type"
                                        android:textAlignment="textStart"
                                        android:textColor="@color/color8A8E99"
                                        android:textSize="@dimen/font_size14" />

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="2"
                                        android:text="@string/certificate_number"
                                        android:textAlignment="textStart"
                                        android:textColor="@color/color8A8E99"
                                        android:textSize="@dimen/font_size14" />

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="@string/sexString"
                                        android:textAlignment="center"
                                        android:textColor="@color/color8A8E99"
                                        android:textSize="@dimen/font_size14" />

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="2"
                                        android:text="@string/birthdayString"
                                        android:textAlignment="textStart"
                                        android:textColor="@color/color8A8E99"
                                        android:textSize="@dimen/font_size14" />

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="4"
                                        android:text="@string/addressString"
                                        android:textAlignment="textStart"
                                        android:textColor="@color/color8A8E99"
                                        android:textSize="@dimen/font_size14" />

                                </LinearLayout>

                                <androidx.recyclerview.widget.RecyclerView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    binding:itemBinding="@{viewModel.naturalInformationItemBinding}"
                                    binding:items="@{viewModel.observableNaturalInformationList}"
                                    binding:lineManager="@{LineManagers.horizontal()}"
                                    binding:layoutManager="@{LayoutManagers.linear()}"/>
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/unit_information_list"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_20"
                                    android:text="@string/unit_information"
                                    android:textColor="@color/colorBFBFBF"
                                    android:textSize="@dimen/font_size12" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/space_10"
                                    android:background="@drawable/shape_corner4_white_all"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="@dimen/space_40"
                                        android:background="@drawable/shape_corner4_e7f2fe_top_right_left"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="2"
                                            android:text="@string/designation"
                                            android:textAlignment="textStart"
                                            android:paddingHorizontal="@dimen/space_30"
                                            android:textColor="@color/color8A8E99"
                                            android:textSize="@dimen/font_size14" />

                                        <TextView
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="2"
                                            android:text="@string/certificate_type"
                                            android:textAlignment="textStart"
                                            android:textColor="@color/color8A8E99"
                                            android:textSize="@dimen/font_size14" />

                                        <TextView
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="2"
                                            android:text="@string/certificate_number"
                                            android:textAlignment="textStart"
                                            android:textColor="@color/color8A8E99"
                                            android:textSize="@dimen/font_size14" />

                                        <TextView
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1.5"
                                            android:text="@string/legal_person_name"
                                            android:textAlignment="textStart"
                                            android:textColor="@color/color8A8E99"
                                            android:textSize="@dimen/font_size14" />

                                        <TextView
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="2"
                                            android:text="@string/legal_or_representative_phone_number"
                                            android:textAlignment="textStart"
                                            android:textColor="@color/color8A8E99"
                                            android:textSize="@dimen/font_size14" />

                                        <TextView
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="4"
                                            android:text="@string/addressString"
                                            android:textAlignment="textStart"
                                            android:textColor="@color/color8A8E99"
                                            android:textSize="@dimen/font_size14" />

                                    </LinearLayout>

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        binding:itemBinding="@{viewModel.unitInformationItemBinding}"
                                        binding:items="@{viewModel.observableUnitInformationList}"
                                        binding:lineManager="@{LineManagers.horizontal()}"
                                        binding:layoutManager="@{LayoutManagers.linear()}"/>
                                </LinearLayout>
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/fee_information_list"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_20"
                                android:layout_marginBottom="@dimen/space_10"
                                android:drawableLeft="@mipmap/split_vertical_icon"
                                android:drawablePadding="@dimen/space_10"
                                android:paddingLeft="@dimen/space_2"
                                android:text="@string/fee_information"
                                android:textColor="@color/color3333"
                                android:textSize="@dimen/font_size14"
                                android:textStyle="bold" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/shape_corner8_f5f5f7"
                                android:padding="@dimen/space_12"
                                android:orientation="vertical">
                                <androidx.recyclerview.widget.RecyclerView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    binding:itemBinding="@{viewModel.feeInformationItemBinding}"
                                    binding:items="@{viewModel.observableFeeInformationList}"
                                    binding:layoutManager="@{LayoutManagers.linear()}" />
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/space_40"
                                    android:gravity="center_vertical"
                                    android:background="@drawable/shape_corner4_white_all"
                                    android:paddingHorizontal="@dimen/space_30"
                                    android:layout_marginBottom="@dimen/space_5"
                                    android:orientation="horizontal">
                                    <View
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:background="@color/colorWhite"
                                        android:layout_weight="1">
                                    </View>

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:orientation="horizontal">
                                        <TextView
                                            android:id="@+id/item_order_detail_unit_information_notary_fee"
                                            android:layout_width="@dimen/space_100"
                                            android:layout_height="wrap_content"
                                            android:text="@string/other_fee"
                                            android:textAlignment="textEnd"
                                            android:textColor="@color/color8A8E99"
                                            android:textSize="@dimen/font_size14" />

                                        <TextView
                                            android:id="@+id/item_order_detail_unit_information_legal_person_name"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginLeft="@dimen/space_15"
                                            binding:text="@{viewModel.selfServiceOrderDetailModel.paymentListVO.otherTotalFee.toString()}"
                                            android:textAlignment="textStart"
                                            android:textColor="@color/color3333"
                                            android:textSize="@dimen/font_size14" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/chineseUnit"
                                            android:textSize="@dimen/space_14"
                                            android:textColor="@color/color3333"/>
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:orientation="horizontal">
                                        <TextView
                                            android:id="@+id/item_order_detail_unit_information_legal_or_representative_phone_number"
                                            android:layout_width="@dimen/space_100"
                                            android:layout_height="wrap_content"
                                            android:text="@string/reduce_fee"
                                            android:textAlignment="textEnd"
                                            android:textColor="@color/color8A8E99"
                                            android:textSize="@dimen/font_size14" />

                                        <TextView
                                            android:id="@+id/item_order_detail_unit_information_address"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginLeft="@dimen/space_15"
                                            binding:text="@{viewModel.selfServiceOrderDetailModel.paymentListVO.reductionFee.toString()}"
                                            android:textAlignment="textStart"
                                            android:textColor="@color/color3333"
                                            android:textSize="@dimen/font_size14" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/chineseUnit"
                                            android:textSize="@dimen/space_14"
                                            android:textColor="@color/color3333"/>
                                    </LinearLayout>


                                </LinearLayout>
                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginVertical="@dimen/space_10">
                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentRight="true"
                                        android:orientation="horizontal">
                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/total_fee"
                                            android:textSize="@dimen/space_14"
                                            android:textColor="@color/color3333"/>
                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginLeft="@dimen/space_15"
                                            android:layout_marginRight="@dimen/space_5"
                                            binding:text="@{viewModel.selfServiceOrderDetailModel.paymentListVO.totalFee.toString()}"
                                            android:textSize="@dimen/space_14"
                                            android:textColor="@color/color27E314"/>
                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/chineseUnit"
                                            android:textSize="@dimen/space_14"
                                            android:textColor="@color/color3333"/>

                                    </LinearLayout>
                                </RelativeLayout>
                            </LinearLayout>
                        </LinearLayout>


                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_20"
                            android:layout_marginBottom="@dimen/space_10"
                            android:drawableLeft="@mipmap/split_vertical_icon"
                            android:drawablePadding="@dimen/space_10"
                            android:paddingLeft="@dimen/space_2"
                            android:text="@string/material"
                            android:textColor="@color/color3333"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/shape_corner8_f5f5f7"
                            binding:itemBinding="@{viewModel.imageItemBinding}"
                            binding:items="@{viewModel.observableImageList}"
                            binding:layoutManager="@{LayoutManagers.grid(8)}" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_20"
                            android:layout_marginBottom="@dimen/space_10"
                            android:drawableLeft="@mipmap/split_vertical_icon"
                             android:drawablePadding="@dimen/space_10"
                            android:paddingLeft="@dimen/space_2"
                            android:text="@string/documnets"
                            android:textColor="@color/color3333"
                            android:textSize="@dimen/font_size14"
                            android:textStyle="bold" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/shape_corner8_f5f5f7"
                            binding:itemBinding="@{viewModel.documentsItemBinding}"
                            binding:items="@{viewModel.observableDocumentList}"
                            binding:layoutManager="@{LayoutManagers.grid(8)}"
                            />
                    </LinearLayout>
                </androidx.core.widget.NestedScrollView>
            </LinearLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_64"
                android:layout_alignParentBottom="true"
                android:layout_marginStart="@dimen/space_30"
                android:layout_marginEnd="@dimen/space_30"
                android:layout_marginBottom="@dimen/space_30"
                binding:isVisible="@{viewModel.bottomStatusVisibility}"
                android:background="@drawable/shape_corner10_white_all">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/space_14"
                    android:background="@drawable/shape_corner5_e8e8e8"
                    android:drawableLeft="@mipmap/forbid_icon"
                    android:drawablePadding="@dimen/space_10"
                    android:text="@string/terminate"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/space_20"
                    android:textColor="@color/color3333"
                    binding:onClickCommand="@{viewModel.stopCase}"
                    binding:isThrottleFirst="@{Boolean.FALSE}"
                    android:textSize="@dimen/font_size14" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_margin="@dimen/space_14"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/read_document"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginRight="@dimen/space_14"
                        android:background="@drawable/shape_corner5_3c6af4"
                        android:drawableLeft="@mipmap/book_white_icon"
                        android:drawablePadding="@dimen/space_10"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/space_20"
                        android:text="@string/readingInstrument"
                        android:textColor="@color/colorWhite"
                        binding:onClickCommand="@{viewModel.readDocuments}"
                        binding:isThrottleFirst="@{Boolean.FALSE}"
                        android:textSize="@dimen/font_size14" />

                    <TextView
                        android:id="@+id/apply_material"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginRight="@dimen/space_14"
                        android:background="@drawable/shape_corner5_3c6af4"
                        android:drawableLeft="@mipmap/add_white_icon"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/space_20"
                        android:drawablePadding="@dimen/space_10"
                        android:text="@string/supplementaryMaterials"
                        android:textColor="@color/colorWhite"
                        binding:onClickCommand="@{viewModel.applyMaterial}"
                        binding:isThrottleFirst="@{Boolean.FALSE}"
                        android:textSize="@dimen/font_size14" />

                </LinearLayout>
            </RelativeLayout>
        </RelativeLayout>
    </LinearLayout>
</layout>