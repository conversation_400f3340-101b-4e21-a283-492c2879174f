<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_15"
        android:layout_marginLeft="@dimen/space_15">

        <View
            android:layout_width="4dp"
            android:layout_height="12dp"
            android:background="@drawable/shape_corner2_gradient"
            android:layout_centerVertical="true"/>

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/space_10"
            android:text="@string/booklet"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14"
            android:textStyle="bold" />
    </RelativeLayout>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_child"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_8"
        android:layout_marginLeft="@dimen/space_15"
        android:padding="@dimen/space_5"/>
</LinearLayout>