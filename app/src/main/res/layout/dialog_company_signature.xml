<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="550dp"
    android:layout_height="450dp"
    android:layout_gravity="center"
    android:background="@drawable/shape_corner10_white_all"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_corner10_white_all"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/space_175">


        <ImageView
            android:id="@+id/iv_img"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/space_67"
            android:scaleType="fitXY"
            android:src="@mipmap/erroimage" />

        <Button
            android:id="@+id/btn_sign"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/iv_img"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/space_64"
            android:layout_marginBottom="@dimen/space_30"
            android:background="@drawable/shape_corner5_3c6af4"
            android:onClick="CloseTransaction"
            android:paddingHorizontal="@dimen/space_38"
            android:paddingVertical="@dimen/space_15"
            android:text="@string/company_signature"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/font_size16" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/space_8"
        android:layout_marginRight="@dimen/space_8"
        android:padding="@dimen/space_8"
        android:src="@mipmap/icon_close" />
</RelativeLayout>
