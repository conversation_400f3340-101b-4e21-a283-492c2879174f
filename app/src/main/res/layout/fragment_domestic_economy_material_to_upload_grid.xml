<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.FragmentDomesticEconomyMaterialToUploadGridViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.FragmentDomesticEconomyMaterialToUploadGridViewModel" />
        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />
    </data>
    <LinearLayout
        android:id="@+id/item_grid_my_appointment_order"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@mipmap/split_vertical_icon"
            android:textColor="@color/color3333"
            android:textStyle="bold"
            android:layout_marginVertical="@dimen/space_20"
            android:textSize="@dimen/font_size14"
            android:text="@{viewModel.entity.materialTypeName}"/>
        <androidx.recyclerview.widget.RecyclerView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            binding:itemBinding="@{viewModel.childItemBinding}"
            binding:items="@{viewModel.childObservableList}"
            binding:layoutManager="@{LayoutManagers.grid(4)}"
             />

    </LinearLayout>



</layout>