<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal"
        android:padding="5dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_type"
            android:layout_width="0dp"
            android:layout_weight="0.8"
            android:layout_height="40dp"
            android:textSize="@dimen/font_size_15"
            android:text="代理人"
            android:layout_marginRight="@dimen/space_15"
            android:gravity="center"
            android:background="@drawable/shape_corner_blue"
            android:textColor="@color/white"
            android:padding="5dp"
            android:visibility="invisible"/>

        <TextView
            android:id="@+id/realName"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="40dp"
            android:textSize="@dimen/font_size_15"
            android:text="姓名"
            android:ellipsize="end"
            android:maxLines="1"
            android:gravity="center_vertical"
            android:padding="5dp"/>

        <TextView
            android:id="@+id/cardType"
            android:layout_width="0dp"
            android:layout_weight="1.5"
            android:layout_height="40dp"
            android:textSize="@dimen/font_size_15"
            android:text="证件类型"
            android:gravity="center_vertical"
            android:padding="5dp"/>

        <TextView
            android:id="@+id/cardNumber"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_height="40dp"
            android:textSize="@dimen/font_size_15"
            android:text="证件号码"
            android:gravity="center_vertical"
            android:padding="5dp"/>

        <TextView
            android:id="@+id/sexs"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="40dp"
            android:textSize="@dimen/font_size_15"
            android:text="男"
            android:gravity="center_vertical"
            android:padding="5dp" />

        <TextView
            android:id="@+id/birthday"
            android:layout_width="0dp"
            android:layout_weight="1.5"
            android:layout_height="40dp"
            android:textSize="@dimen/font_size_15"
            android:text="出生日期"
            android:gravity="center_vertical"
            android:padding="5dp" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>