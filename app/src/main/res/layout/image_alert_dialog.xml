<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="5dp"
        android:background="@color/white"
        android:orientation="vertical">

        <com.gc.notarizationpc.utils.ZoomImageView
            android:id="@+id/uploadPathImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:src="@drawable/ic_add_photo"/>

        <TextView
            android:id="@+id/closeDialog"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:text="X"
            android:textStyle="bold"
            android:gravity="center"
            android:textSize="50dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"/>

    </RelativeLayout>

</RelativeLayout>