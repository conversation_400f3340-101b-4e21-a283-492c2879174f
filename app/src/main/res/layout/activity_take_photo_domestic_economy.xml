<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.DomesticEconomyTakePhotoViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/rnl_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rnl_title"
            android:layout_width="match_parent"
            android:layout_height="80dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginHorizontal="@dimen/space_20"
                android:layout_marginLeft="@dimen/space_10"
                android:text="取景区"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size18" />

            <!--        <TextView-->
            <!--            android:layout_width="wrap_content"-->
            <!--            android:layout_height="wrap_content"-->
            <!--            android:layout_centerVertical="true"-->
            <!--            android:layout_marginLeft="@dimen/space_5"-->
            <!--            android:layout_toRightOf="@+id/tv_title"-->
            <!--            android:text="（请将材料放在高拍仪面板上，点击拍摄按钮）"-->
            <!--            android:textColor="#999999"-->
            <!--            android:textSize="@dimen/font_size_18" />-->

            <TextView
                android:id="@+id/tv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/space_20"
                android:background="@drawable/shape_corner2_e8e8e8"
                android:drawableLeft="@mipmap/icon_back"
                android:drawablePadding="@dimen/space_5"
                android:onClick="finishActivity"
                android:padding="@dimen/space_10"
                android:text="@string/back"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size16" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_1"
            android:layout_marginHorizontal="@dimen/space_20"
            android:background="#E8E8EB" />

        <LinearLayout
            android:id="@+id/lnl_center"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_below="@+id/rnl_title"
            android:layout_marginTop="@dimen/space_10"
            android:layout_weight="1">

            <TextureView
                android:id="@+id/informationPhoto"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/space_20"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_weight="3.5" />

            <!--右侧 -->
            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginRight="@dimen/space_20"
                android:layout_weight="1"
                android:background="@drawable/shape_corner5_f5f5f7"
                android:padding="@dimen/space_20">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rnl_imgs"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </RelativeLayout>

        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/lnl_center"
            android:layout_centerHorizontal="true"
            android:layout_marginVertical="@dimen/space_15"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_upload"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:background="@drawable/shape_corner5_e8e8e8"
                android:paddingHorizontal="@dimen/space_30"
                android:paddingVertical="@dimen/space_8"
                android:text="@string/done"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size16" />

            <TextView
                android:id="@+id/tv_camera"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_toRightOf="@+id/tv_upload"
                android:background="@drawable/shape_corner_blue"
                android:paddingHorizontal="@dimen/space_30"
                android:paddingVertical="@dimen/space_8"
                android:text="@string/camera"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size16" />
        </RelativeLayout>


    </LinearLayout>
</layout>