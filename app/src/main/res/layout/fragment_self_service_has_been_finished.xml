<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceHasBeenFinishedViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.FragmentSelfServiceHasBeenFinishedViewModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.gc.notarizationpc.widget.SelfServiceStepBar
            android:id="@+id/self_service_step_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/space_30"
            android:background="@drawable/shape_corner5_white"
            android:padding="@dimen/space_30">

            <LinearLayout
                android:id="@+id/fee_information_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="@dimen/space_90"
                android:background="@color/white"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_20"
                    android:layout_marginBottom="@dimen/space_10"
                    android:drawableLeft="@mipmap/split_vertical_icon"
                    android:drawablePadding="@dimen/space_10"
                    android:paddingLeft="@dimen/space_2"
                    android:text="@string/fee_information"
                    android:textColor="@color/color3333"
                    android:textSize="@dimen/font_size14"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_corner8_f5f5f7"
                    android:orientation="vertical"
                    android:padding="@dimen/space_12">

                    <ScrollView
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:fillViewport="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <androidx.recyclerview.widget.RecyclerView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                binding:itemBinding="@{viewModel.feeInformationItemBinding}"
                                binding:items="@{viewModel.feeInformationList}"
                                binding:layoutManager="@{LayoutManagers.linear()}" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/space_40"
                                android:layout_marginBottom="@dimen/space_5"
                                android:background="@drawable/shape_corner4_white_all"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:paddingHorizontal="@dimen/space_30"
                                android:visibility="gone">

                                <View
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:background="@color/colorWhite"></View>

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/item_order_detail_unit_information_notary_fee"
                                        android:layout_width="@dimen/space_100"
                                        android:layout_height="wrap_content"
                                        android:text="@string/other_fee"
                                        android:textAlignment="textEnd"
                                        android:textColor="@color/color8A8E99"
                                        android:textSize="@dimen/font_size14" />


                                    <TextView
                                        android:id="@+id/tvOtherFee"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="@dimen/space_15"
                                        android:textColor="@color/color3333"
                                        android:textSize="@dimen/space_14" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="@dimen/space_15"
                                        android:text="元"
                                        android:textAlignment="textStart"
                                        android:textColor="@color/color3333"
                                        android:textSize="@dimen/font_size14" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/item_order_detail_unit_information_legal_or_representative_phone_number"
                                        android:layout_width="@dimen/space_100"
                                        android:layout_height="wrap_content"
                                        android:text="@string/reduce_fee"
                                        android:textAlignment="textEnd"
                                        android:textColor="@color/color8A8E99"
                                        android:textSize="@dimen/font_size14" />

                                    <TextView
                                        android:id="@+id/tvReduceFee"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="@dimen/space_15"
                                        android:textColor="@color/color3333"
                                        android:textSize="@dimen/space_14" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="@dimen/space_15"
                                        android:text="元"
                                        android:textAlignment="textStart"
                                        android:textColor="@color/color3333"
                                        android:textSize="@dimen/font_size14" />
                                </LinearLayout>


                            </LinearLayout>
                        </LinearLayout>
                    </ScrollView>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/space_10">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_fee"
                                android:textColor="@color/color3333"
                                android:textSize="@dimen/space_14" />

                            <TextView
                                android:id="@+id/tvTotalFee"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/space_15"
                                android:layout_marginRight="@dimen/space_5"
                                android:textColor="@color/color27E314"
                                android:textSize="@dimen/space_14" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/chineseUnit"
                                android:textColor="@color/color3333"
                                android:textSize="@dimen/space_14" />

                        </LinearLayout>
                    </RelativeLayout>
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_10"
                    android:background="@drawable/shape_corner5_fef6f7"
                    android:paddingVertical="@dimen/space_15"
                    android:paddingLeft="@dimen/space_20"
                    android:text="@string/explainAboutFee"
                    android:textColor="@color/colorF78716"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:background="@drawable/shape_corner5_2568ff"
                android:paddingHorizontal="@dimen/space_60"
                android:paddingVertical="@dimen/space_10"
                android:text="@string/done"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size16"
                android:textStyle="bold"
                binding:onClickCommand="@{viewModel.finish}"
                binding:isThrottleFirst="@{Boolean.FALSE}" />

        </RelativeLayout>
    </LinearLayout>


</layout>