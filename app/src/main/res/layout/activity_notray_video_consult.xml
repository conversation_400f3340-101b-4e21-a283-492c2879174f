<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rnl_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/rnl_title"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:background="#2196F3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center_vertical|left"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="5dp"
            android:onClick="back"
            android:paddingLeft="10dp"
            android:paddingTop="5dp"
            android:paddingRight="10dp"
            android:paddingBottom="5dp"
            android:src="@drawable/back" />


        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:layout_marginRight="40dp"
            android:padding="20dp"
            android:text="公证咨询"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="20sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/rnl_title">

        <RelativeLayout
            android:id="@+id/home_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_below="@+id/tv_title"
            android:layout_weight="2">

            <com.tencent.rtmp.ui.TXCloudVideoView
                android:id="@+id/trtcMain"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

            </com.tencent.rtmp.ui.TXCloudVideoView>

            <com.tencent.rtmp.ui.TXCloudVideoView
                android:id="@+id/trtcRemote"
                android:layout_width="300dp"
                android:layout_height="300dp"
                android:layout_marginLeft="@dimen/space_5"
                android:layout_marginTop="5dp" />
            <!--            <include-->
            <!--                android:id="@+id/videoRoom"-->
            <!--                layout="@layout/layout_video_room"-->
            <!--                android:layout_width="400dp"-->
            <!--                android:layout_height="match_parent"-->
            <!--                android:layout_alignParentTop="true"-->
            <!--                android:layout_marginLeft="10dp" />-->

            <TextView
                android:id="@+id/tvNotaryState"
                android:layout_width="300dp"
                android:layout_height="300dp"
                android:layout_marginLeft="@dimen/space_5"
                android:layout_marginTop="5dp"
                android:background="@color/black"
                android:gravity="center"
                android:text="等待公证员进入"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_18"
                android:visibility="gone" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_alignParentBottom="true"
                android:background="@drawable/shape_transparency_blue"
                android:visibility="visible">

                <TextView
                    android:id="@+id/tv_video_hint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/space_6"
                    android:layout_marginRight="@dimen/space_15"
                    android:background="@drawable/shape_corner_blue"
                    android:onClick="overConsult"
                    android:paddingLeft="@dimen/space_10"
                    android:paddingTop="@dimen/space_8"
                    android:paddingRight="@dimen/space_15"
                    android:paddingBottom="@dimen/space_8"
                    android:text="结束咨询"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_14" />

            </RelativeLayout>
        </RelativeLayout>


        <!--右侧 -->
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1.5">

            <TextView
                android:id="@+id/tv_upload_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_15"
                android:layout_marginTop="@dimen/space_10"
                android:layout_marginBottom="@dimen/space_10"
                android:text="材料上传"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size_16"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_upload"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_upload_txt"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_15"
                android:background="@drawable/shape_corner_blue"
                android:paddingLeft="@dimen/space_10"
                android:paddingTop="@dimen/space_8"
                android:paddingRight="@dimen/space_15"
                android:paddingBottom="@dimen/space_8"
                android:text="材料上传"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_14" />

            <!--            <com.gc.notarizationpc.widget.MultiImageView-->
            <!--                android:id="@+id/multi_imgs"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="match_parent"-->
            <!--                android:layout_below="@+id/tv_upload"-->
            <!--                android:layout_marginLeft="@dimen/space_10"-->
            <!--                android:layout_marginTop="@dimen/space_10"-->
            <!--                android:layout_marginRight="@dimen/space_10" />-->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rnl_imgs"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/tv_upload"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:layout_marginRight="@dimen/space_10" />

        </RelativeLayout>

    </LinearLayout>


</RelativeLayout>