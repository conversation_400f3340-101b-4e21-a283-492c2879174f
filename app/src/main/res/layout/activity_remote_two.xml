<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="900dp"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/ll_recycler"
            android:layout_width="900dp"
            android:layout_height="match_parent"
            android:orientation="vertical">


            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tb_remote"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/title_color"
                app:tabIndicatorColor="@color/black_60"
                app:tabIndicatorHeight="3dp"
                app:tabMinWidth="150dp"
                app:tabMode="scrollable"
                app:tabSelectedTextColor="@color/white"
                app:tabTextColor="@color/white_30" />


            <androidx.viewpager.widget.ViewPager
                android:id="@+id/viewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1" />
        </LinearLayout>

        <include
            android:id="@+id/layoutBanner"
            layout="@layout/layout_bannernew"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none"
            android:visibility="gone" />

        <include
            layout="@layout/canvs_with_close"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </RelativeLayout>

    <include
        android:id="@+id/videoRoom"
        layout="@layout/layout_video_room"
        android:layout_width="400dp"
        android:layout_height="match_parent"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:layout_toRightOf="@+id/stepView" />
</RelativeLayout>