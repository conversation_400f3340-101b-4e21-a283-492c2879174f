<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_corner10_white_all"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center_horizontal"
        android:paddingHorizontal="@dimen/space_100"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size20"
            android:text="@string/video_connet_has_finish"
            android:layout_marginTop="@dimen/space_60"
            android:layout_marginBottom="@dimen/space_20"
            android:textAlignment="center"/>

        <TextView
            android:id="@+id/dialog_video_exit_time_decrease_alert_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size16"
            android:text="@string/back"
            android:layout_marginTop="@dimen/space_60"
            android:background="@drawable/shape_corner5_3c6af4"
            android:layout_marginBottom="@dimen/space_60"
            android:paddingVertical="@dimen/space_10"
            android:paddingHorizontal="@dimen/space_40"
            android:textAlignment="center"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>