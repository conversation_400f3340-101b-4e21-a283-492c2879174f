<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rnl_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <RelativeLayout
        android:id="@+id/rnl_title"
        android:layout_width="match_parent"
        android:layout_height="80dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取景区"
            android:textSize="@dimen/font_size_18"
            android:textColor="@color/black"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/space_10"/>

        <TextView
            android:layout_toRightOf="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="（请将材料放在高拍仪面板上，点击拍摄按钮）"
            android:textSize="@dimen/font_size_18"
            android:textColor="#999999"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/space_5"/>

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/space_15"
            android:scaleType="fitXY"
            android:src="@drawable/icon_close" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/rnl_title"
        android:layout_marginTop="@dimen/space_10">

        <RelativeLayout
            android:id="@+id/home_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_below="@+id/tv_title"
            android:layout_marginLeft="@dimen/space_10"
            android:background="@color/black"
            android:layout_marginBottom="@dimen/space_8"
            android:layout_weight="2.5">

            <TextureView
                android:id="@+id/informationPhoto"
                android:layout_width="match_parent"
                android:layout_height="600dp"
                android:layout_marginBottom="@dimen/space_55"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <!--            <RelativeLayout-->

            <!--            android:background="@drawable/icon_bg_photo"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="80dp"-->
            <!--                android:layout_alignParentBottom="true"-->
            <!--                android:background="@drawable/shape_transparency_blue"-->
            <!--                android:visibility="visible">-->

            <ImageView
                android:id="@+id/tv_capture"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_alignParentBottom="true"
                android:layout_centerInParent="true"
                android:src="@drawable/icon_take_photo" />

            <!--            </RelativeLayout>-->
        </RelativeLayout>

        <!--右侧 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rnl_imgs"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_centerHorizontal="true" />

            <TextView
                android:id="@+id/tv_upload"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="@dimen/space_6"
                android:layout_marginRight="@dimen/space_15"
                android:layout_marginBottom="@dimen/space_10"
                android:background="@drawable/shape_corner_blue"
                android:gravity="center"
                android:paddingLeft="@dimen/space_15"
                android:paddingTop="@dimen/space_8"
                android:paddingRight="@dimen/space_15"
                android:paddingBottom="@dimen/space_8"
                android:text="上传"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/space_10"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_14" />
        </LinearLayout>

    </LinearLayout>


</RelativeLayout>