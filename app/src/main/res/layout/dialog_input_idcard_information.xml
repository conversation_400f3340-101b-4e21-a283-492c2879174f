<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/dialog_input_id_card_information"
    android:layout_width="@dimen/space_568"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_corner10_white_all"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_gravity="center"
    android:visibility="visible">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginLeft="@dimen/space_46"
        android:layout_marginRight="@dimen/space_60"
        android:layout_gravity="center">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/space_110"
            android:orientation="vertical">
            <TextView
                android:id="@+id/dialog_input_id_card_information_id_card"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/idCardNumber"
                android:drawablePadding="@dimen/space_26"
                android:textColor="@color/color6666"
                android:textSize="@dimen/font_size16" />
            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/dialog_input_id_card_information_id_card_edit_text"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginTop="@dimen/space_10"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/idCardNumber_hint"
                android:imeOptions="actionDone"
                android:digits="1234567890X"
                android:maxLines="1"
                android:maxLength="18"
                android:padding="@dimen/space_11"
                android:singleLine="true" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_35"
            android:layout_marginBottom="@dimen/space_90"
            android:orientation="horizontal">
            <CheckBox
                android:id="@+id/dialog_input_id_card_checkBox"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:background="@drawable/select_checkout"
                android:button="@null" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="@string/readAndAgree"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size14" />

            <TextView
                android:id="@+id/dialog_input_id_card_agreement_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/useRule"
                android:layout_marginRight="@dimen/space_110"
                android:textColor="@color/date_picker_text_light"
                android:textSize="@dimen/font_size14" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:layout_marginBottom="@dimen/space_50"
            android:orientation="horizontal">
            <Button
                android:id="@+id/dialog_input_id_card_information_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/back"
                android:background="@drawable/shape_corner5_e8e8e8"
                android:textSize="@dimen/font_size16"
                android:paddingVertical="@dimen/space_10"
                android:paddingHorizontal="@dimen/space_54"
                android:layout_marginRight="@dimen/space_25"
                />
            <Button
                android:id="@+id/dialog_input_id_card_information_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/confirm"
                android:background="@drawable/shape_corner5_2568ff"
                android:textSize="@dimen/font_size16"
                android:textColor="@color/colorWhite"
                android:paddingVertical="@dimen/space_10"
                android:paddingHorizontal="@dimen/space_54"

                />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
