<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.OnLineRulesViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/title_bar">
            <LinearLayout
                android:id="@+id/on_line_rule_linearlayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_marginRight="@dimen/space_20"
                android:gravity="top"
                android:orientation="vertical"
                android:padding="20dp">
                <TextView
                    android:id="@+id/on_line_rule_title_text_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/useRuleNoBracket"
                    android:textAlignment="center"
                    android:textStyle="bold"
                    android:textSize="@dimen/font_size20"
                    android:textColor="@color/color3333"/>

                <TextView
                    android:id="@+id/on_line_rule_text_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_20"
                    android:textSize="@dimen/font_size16"
                    android:textColor="@color/color3333"
                    android:textAlignment="textStart"
                    android:singleLine="false"
                    android:lineSpacingExtra="@dimen/space_5"
                    android:textStyle="bold"/>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </RelativeLayout>
</layout>