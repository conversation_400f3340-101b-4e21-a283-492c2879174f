<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.HomeQuickListItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.HomeQuickListItemViewModel" />


    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/space_8"
        android:background="@drawable/bg_home_quick_enter"
        android:layout_marginBottom="@dimen/space_10"
        android:layout_marginLeft="@dimen/space_20"
        android:layout_marginRight="@dimen/space_20"
        binding:isSelected="@{viewModel.entity.choose}"
        binding:onClickCommand="@{viewModel.itemClick}">

<!--        <ImageView-->
<!--            android:layout_width="@dimen/space_24"-->
<!--            android:layout_height="@dimen/space_24"-->
<!--            android:layout_centerVertical="true"-->
<!--            android:layout_toLeftOf="@+id/tv_name"-->
<!--            android:src="@{viewModel.entity.drawImg}"-->
<!--            />-->

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginRight="@dimen/space_5"
            android:paddingLeft="@dimen/space_10"
            android:paddingTop="@dimen/space_9"
            android:paddingRight="@dimen/space_15"
            android:paddingBottom="@dimen/space_9"
            android:text="@{viewModel.entity.funcName}"
            android:textColor="@color/white"
            android:layout_marginLeft="@dimen/space_3"
            android:textSize="@dimen/font_size14"
            tools:text="电子打印" />
    </RelativeLayout>

</layout>