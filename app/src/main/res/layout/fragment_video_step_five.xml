<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/space_10"
            android:background="@drawable/border_table"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1.5"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="申请人"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvPayName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16"
                    tools:text="王二麻子" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="承办人员"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvStaff"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_1"
                android:background="@color/black" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1.5"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="受理单号"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvNo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16"
                    tools:text="202101291030591966" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="受理日期"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvDate"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_1"
                android:background="@color/black" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="2"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="公证事项"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvMatter"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:gravity="center"
                    android:minLines="2"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16"
                    tools:text="公共钢结构就感觉" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="发证地点"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvAddress"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:padding="@dimen/space_5"
                    android:textSize="@dimen/font_size_13" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_1"
                android:background="@color/black" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1.5"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="公证费"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvGZF"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16"
                    tools:text="1" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="法律服务费"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvFLFWF"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_1"
                android:background="@color/black" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1.5"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="上门服务费"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvSMFWF"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16"
                    tools:text="王二麻子" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="摄像、拍照费"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvPZF"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_1"
                android:background="@color/black" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1.5"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="其他服务费"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvQTFWF"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16"
                    tools:text="王二麻子" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:layout_width="@dimen/space_160"
                    android:layout_height="match_parent"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:text="缴费日期"
                    android:textSize="@dimen/font_size_16" />

                <View
                    android:layout_width="@dimen/space_1"
                    android:layout_height="match_parent"
                    android:background="@color/black" />

                <TextView
                    android:id="@+id/tvPayDate"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:gravity="center"
                    android:padding="@dimen/space_10"
                    android:textSize="@dimen/font_size_16" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_1"
                android:background="@color/black" />

            <TextView
                android:id="@+id/tvTotal"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="3"
                android:gravity="center"
                android:padding="@dimen/space_20"
                android:text="合计："
                android:textSize="@dimen/font_size_26" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/payView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/shape_white"
            android:elevation="@dimen/space_10"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/space_20"
            android:visibility="invisible"
            tools:visibility="gone">

            <TextView
                android:id="@+id/hintPay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请打开支付宝扫码付款(元)" />

            <TextView
                android:id="@+id/payTotal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_10"
                android:text="0"
                android:textColor="@color/red"
                android:textSize="@dimen/space_30"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/payQrCode"
                android:layout_width="@dimen/space_250"
                android:layout_height="@dimen/space_250" />

            <!--            <LinearLayout-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content">-->

            <!--                <androidx.appcompat.widget.AppCompatButton-->
            <!--                    android:id="@+id/btnPayFail"-->
            <!--                    android:layout_width="100dp"-->
            <!--                    android:layout_height="@dimen/space_50"-->
            <!--                    android:layout_marginRight="@dimen/space_100"-->
            <!--                    android:background="@drawable/btn_login_bg"-->
            <!--                    android:text="支付失败"-->
            <!--                    android:textColor="@color/white"-->
            <!--                    android:textSize="@dimen/font_size_18" />-->

            <!--                <androidx.appcompat.widget.AppCompatButton-->
            <!--                    android:id="@+id/btnPaySuccess"-->
            <!--                    android:layout_width="100dp"-->
            <!--                    android:layout_height="@dimen/space_50"-->
            <!--                    android:background="@drawable/btn_login_bg"-->
            <!--                    android:text="完成支付"-->
            <!--                    android:textColor="@color/white"-->
            <!--                    android:textSize="@dimen/font_size_18" />-->
            <!--            </LinearLayout>-->
            <TextView
                android:id="@+id/PayStatu"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/space_50"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="待支付！"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size_18" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/payTypeView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/shape_white"
            android:gravity="center"
            android:padding="@dimen/space_20"
            android:visibility="gone">

            <TextView
                android:id="@+id/wxPay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/space_100"
                android:drawableTop="@drawable/ic_wx_pay"
                android:drawablePadding="@dimen/space_10"
                android:gravity="center"
                android:text="微信支付"
                android:textSize="@dimen/space_20" />

            <TextView
                android:id="@+id/aliPay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableTop="@drawable/ic_ali_pay"
                android:drawablePadding="@dimen/space_10"
                android:gravity="center"
                android:text="支付宝支付"
                android:textSize="@dimen/space_20" />
        </LinearLayout>
    </FrameLayout>
</ScrollView>