<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.RemoteNotarizationActivity">

    <include
        android:id="@+id/videoRoom"
        layout="@layout/layout_video_room"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/videoRoom"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:id="@+id/ll_recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_40"
                android:background="@color/title_color"/>

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tb_remote"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/title_color"
                app:tabIndicatorColor="@color/white"
                app:tabSelectedTextColor="@color/white"
                app:tabTextColor="@color/white_30" />


            <androidx.viewpager.widget.ViewPager
                android:id="@+id/viewPager"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"/>


            <WebView
                android:id="@+id/materialHtml"
                style="@style/tv_black_wrap_18"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:padding="@dimen/space_20" />

        </LinearLayout>

        <WebView
            android:id="@+id/signatureView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <include
            android:id="@+id/layoutBanner"
            layout="@layout/layout_banner"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none"
            android:visibility="gone" />

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>