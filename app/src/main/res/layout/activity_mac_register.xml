<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.DeviceRegisterViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main">

        <include
            android:id="@+id/title_bar"
            layout="@layout/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_70"
            android:background="@drawable/shape_corner10_white_all" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/title_bar"
            android:layout_marginHorizontal="@dimen/space_30"
            android:layout_marginBottom="@dimen/space_30"
            android:layout_marginTop="@dimen/space_20"
            android:background="@drawable/shape_corner10_white_all"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/mac_address_register_fail"
                android:textSize="24sp"
                android:textColor="@color/color3333"
                android:layout_gravity="center"
                android:textStyle="bold"
                android:gravity="center"
                android:drawableTop="@mipmap/icon_no_register"
                android:drawablePadding="@dimen/space_30"
                />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/customer_phone"
                android:textSize="20sp"
                android:textColor="@color/color3333"
                android:gravity="center_horizontal"
                android:layout_marginTop="@dimen/space_20"
                />

            <TextView
                android:id="@+id/tv_mac_address"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="20sp"
                android:textColor="@color/color3333"
                android:gravity="center_horizontal"
                android:layout_marginTop="@dimen/space_20"
                android:visibility="invisible"
                />


            <TextView
                android:id="@+id/get_mac_address"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/get_mac_address"
                android:textSize="16sp"
                android:background="@drawable/shape_corner5_3c6af4"
                android:textColor="@color/white"
                android:gravity="center_horizontal"
                android:layout_gravity="center"
                android:paddingHorizontal="@dimen/space_35"
                android:paddingVertical="@dimen/space_12"
                android:layout_marginTop="@dimen/space_20"
                android:onClick="getMacAddress"
                />


        </LinearLayout>


    </RelativeLayout>
</layout>