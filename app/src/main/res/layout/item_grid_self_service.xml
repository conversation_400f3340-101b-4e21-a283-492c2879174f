<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderGridItemViewModel" />

        <variable
            name="viewModel"
            type="com.gc.notarizationpc.ui.viewmodel.SelfServiceOrderGridItemViewModel" />
    </data>
    <LinearLayout
        android:id="@+id/item_grid_notary_order"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/space_30"
        android:layout_marginRight="@dimen/space_30"
        android:orientation="vertical"
        android:paddingLeft="@dimen/space_20"
        android:paddingTop="@dimen/space_20"
        android:paddingBottom="@dimen/space_20"
        android:background="@drawable/shape_corner8_f5f5f7"
        binding:onClickCommand="@{viewModel.itemClick}"
        binding:isThrottleFirst="@{Boolean.FALSE}" >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/item_grid_notary_order_notary_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableLeft="@mipmap/order_icon"
                    android:drawablePadding="@dimen/space_8"
                    android:layout_alignParentLeft="true"
                    android:layout_marginRight="@dimen/space_120"
                    android:text="@{viewModel.entity.orderName}"
                    android:textColor="@color/color3333"
                    android:textStyle="bold"
                    android:singleLine="true"
                    android:ellipsize="end"
                    android:maxWidth="@dimen/space_400"
                    android:textSize="@dimen/font_size16" />

                <TextView
                    android:id="@+id/order_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"
                    binding:background="@{viewModel.itemGridNotaryOrderNotaryStatusColor}"
                    android:paddingVertical="@dimen/space_4"
                    android:paddingLeft="@dimen/space_25"
                    android:paddingRight="@dimen/space_20"
                    binding:text="@{viewModel.itemGridNotaryOrderNotaryStatusString}"
                    binding:textColor="@{viewModel.itemGridNotaryOrderNotaryStatusTextColor}"
                    android:textAlignment="textEnd"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_15"
                android:layout_marginBottom="@dimen/space_10"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/item_grid_notary_order_user_name_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="姓名："
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
                <TextView
                    android:id="@+id/item_grid_notary_order_user_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.entity.applicantName}"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/space_20"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/item_grid_notary_order_time_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="时间："
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
                <TextView
                    android:id="@+id/item_grid_notary_order_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.entity.createTime}"
                    android:textColor="@color/color666666"
                    android:textSize="@dimen/font_size14" />
            </LinearLayout>


        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/item_grid_notary_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_corner5_3c6af4"
                android:textColor="@color/colorWhite"
                android:paddingVertical="@dimen/space_5"
                android:paddingHorizontal="@dimen/space_20"
                binding:onClickCommand="@{viewModel.delete}"
                binding:visibility="@{viewModel.itemGridNotaryDelete}"
                android:layout_marginRight="@dimen/space_10"
                android:text="@string/delete"/>

            <TextView
                android:id="@+id/item_grid_notary_read_instrument"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_corner5_3c6af4"
                android:textColor="@color/colorWhite"
                android:paddingVertical="@dimen/space_5"
                android:paddingHorizontal="@dimen/space_20"
                binding:onClickCommand="@{viewModel.continueWrite}"
                android:layout_marginRight="@dimen/space_10"
                binding:visibility="@{viewModel.itemGridNotaryContinueWrite}"
                android:text="@string/continueWrite"/>

            <TextView
                android:id="@+id/item_grid_notary_supply_materials"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/space_10"
                android:background="@drawable/shape_corner5_3c6af4"
                android:paddingVertical="@dimen/space_5"
                android:paddingHorizontal="@dimen/space_20"
                binding:onClickCommand="@{viewModel.supplementaryMaterialsClick}"
                android:textColor="@color/colorWhite"
                binding:visibility="@{viewModel.itemGridNotarySupplyMaterialsStatus}"
                android:text="@string/supplementaryMaterials"/>

            <TextView
                android:id="@+id/item_grid_notary_enter_room"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingVertical="@dimen/space_5"
                android:paddingHorizontal="@dimen/space_20"
                android:layout_marginLeft="@dimen/space_10"
                android:background="@drawable/shape_corner5_3c6af4"
                binding:onClickCommand="@{viewModel.readDocument}"
                android:textColor="@color/colorWhite"
                binding:visibility="@{viewModel.itemGridNotaryReadDocument}"
                android:layout_marginRight="@dimen/space_10"
                android:text="@string/readingInstrument"/>


        </LinearLayout>

    </LinearLayout>



</layout>