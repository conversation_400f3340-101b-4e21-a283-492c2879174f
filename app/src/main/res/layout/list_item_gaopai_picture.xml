<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/space_10"
    android:layout_gravity="center"
    android:gravity="center">

    <RelativeLayout
        android:layout_width="190dp"
        android:layout_height="130dp"
        android:gravity="center"
        >

        <ImageView
            android:id="@+id/work_iv_wip_picture"
            android:layout_width="180dp"
            android:layout_height="120dp"
            android:layout_centerInParent="true"
            android:scaleType="fitXY"
            android:src="@mipmap/icon_add" />

        <ImageView
            android:id="@+id/work_iv_wip_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:paddingLeft="30dp"
            android:paddingBottom="30dp"
            android:src="@mipmap/icon_round_del" />
    </RelativeLayout>
</LinearLayout>