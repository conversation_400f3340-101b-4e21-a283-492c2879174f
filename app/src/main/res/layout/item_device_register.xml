<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:layout_marginBottom="@dimen/space_5"
    android:padding="@dimen/space_10">

    <TextView
        android:id="@+id/tvNotary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="@dimen/font_size_18"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="测试公证处" />

    <TextView
        android:id="@+id/tvAddress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_5"
        app:layout_constraintTop_toBottomOf="@id/tvNotary"
        tools:text="测试公证处"
        android:visibility="gone"/>


</androidx.constraintlayout.widget.ConstraintLayout>