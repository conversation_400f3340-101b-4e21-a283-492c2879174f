<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:paddingBottom="@dimen/space_8">

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="#80060812">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/viewPager"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:visibility="gone"/>

<!--        <com.github.barteksc.pdfviewer.PDFView-->
<!--            android:id="@+id/pdfView"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="match_parent"-->
<!--            android:layout_centerInParent="true" />-->

<!--        <ProgressBar-->
<!--            android:id="@+id/progressBar"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_centerInParent="true"-->
<!--            android:visibility="gone" />-->



    </RelativeLayout>

    <LinearLayout
        android:layout_width="500dp"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:background="@drawable/shape_corner8_white_left"
        android:orientation="vertical"
        android:padding="@dimen/space_20"
        android:paddingBottom="@dimen/space_8">

        <RelativeLayout
            android:id="@+id/rnl_search_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/title_bar"
            android:padding="@dimen/space_20">

            <TextView
                android:id="@+id/tv_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:drawableLeft="@mipmap/icon_law"
                android:drawablePadding="@dimen/space_5"
                android:text="@string/readingInstrument"
                android:textColor="@color/color3333"
                android:textSize="@dimen/font_size18"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_refresh"
                android:layout_width="@dimen/space_40"
                android:layout_height="@dimen/space_40"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/space_40"
                android:layout_toLeftOf="@+id/iv_close"
                android:padding="@dimen/space_10"
                android:src="@mipmap/icon_refresh" />


            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="@dimen/space_40"
                android:layout_height="@dimen/space_40"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:padding="@dimen/space_10"
                android:src="@mipmap/icon_close" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="@dimen/space_15"
            android:background="#E8E8EB" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/space_8"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/space_15"
                android:orientation="vertical">

                <com.gc.notarizationpc.ui.view.TextFlowLayout
                    android:id="@+id/flow_names"
                    android:layout_marginTop="@dimen/space_25"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_parent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <TextView
            android:id="@+id/tv_sign"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginTop="@dimen/space_25"
            android:layout_marginRight="@dimen/space_25"
            android:background="@drawable/shape_corner5_3c6af4"
            android:paddingHorizontal="@dimen/space_30"
            android:paddingVertical="@dimen/space_10"
            android:text="@string/read_and_sign"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size16" />

    </LinearLayout>
</LinearLayout>