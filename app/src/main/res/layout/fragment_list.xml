<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.gc.notarizationpc.ui.viewmodel.FragmentListModel" />

        <variable
            name="viewModel"
            type="FragmentListModel" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="me.goldze.mvvmhabit.binding.viewadapter.recyclerview.LineManagers" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
            android:id="@+id/twinklingRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            binding:onLoadMoreCommand="@{viewModel.onLoadMoreCommand}"
            binding:onRefreshCommand="@{viewModel.onRefreshCommand}"
            binding:tr_head_height="80dp"
            binding:tr_wave_height="80dp">

            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                binding:itemBinding="@{viewModel.itemBinding}"
                binding:items="@{viewModel.observableList}"
                binding:layoutManager="@{LayoutManagers.linear()}"
                binding:lineManager="@{LineManagers.horizontal()}" />

        </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>

    </LinearLayout>
</layout>