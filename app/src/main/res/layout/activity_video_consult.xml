<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="65dp"
        android:background="#5087E2">

        <ImageView
            android:layout_width="60dp"
            android:layout_height="70dp"
            android:layout_centerVertical="true"
            android:onClick="finish"
            android:scaleType="fitXY"
            android:src="@drawable/icon_back" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_corner6_left_white"
            android:orientation="vertical"
            android:padding="@dimen/space_15">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/icon_chat_ask"
                android:drawablePadding="@dimen/space_6"
                android:text="常见问题"
                android:textColor="#001534"
                android:textSize="@dimen/font_size_20"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_faq"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/space_25"
                android:layout_weight="1" />


            <TextView
                android:id="@+id/tv_change"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_30"
                android:background="@drawable/blue_fillcolor"
                android:gravity="center"
                android:onClick="changeQuestion"
                android:padding="@dimen/space_6"
                android:text="更换问题"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20" />
        </LinearLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="#DEDEDE" />

        <!--right-->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:background="@drawable/shape_corner6_right_top_gray"
            android:orientation="vertical">


            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <LinearLayout
                    android:id="@+id/rnl_answer"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_margin="@dimen/space_15"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:id="@+id/msg_owner_rl_commonmsg"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="10dp"
                        android:visibility="visible">

                        <ImageView
                            android:id="@+id/msg_owner_iv_head"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_alignParentRight="true"
                            android:src="@drawable/msg_icon_head" />

                        <TextView
                            android:id="@+id/msg_owner_tv_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="8dp"
                            android:layout_toLeftOf="@+id/msg_owner_iv_head"
                            android:gravity="center_vertical"
                            android:text="我"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/msg_owner_tv_time"
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_marginRight="6dp"
                            android:layout_toLeftOf="@id/msg_owner_tv_name"
                            android:gravity="center_vertical"
                            android:textColor="#C5C5C5"
                            android:textSize="14sp"
                            android:visibility="gone"
                            tools:text="2022.8.13  16:56" />

                        <LinearLayout
                            android:id="@+id/msg_owner_ll_msgcontentview"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/msg_owner_tv_name"
                            android:layout_marginTop="8dp"
                            android:layout_marginRight="8dp"
                            android:layout_toLeftOf="@+id/msg_owner_iv_head"
                            android:gravity="right"
                            android:orientation="vertical">

                            <LinearLayout
                                android:id="@+id/msg_owner_ll_contentview"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/msg_owner_tv_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:textColor="#333333"
                                    android:textSize="15sp"
                                    android:visibility="visible"
                                    tools:text="售房委托公证怎么办理？" />

                            </LinearLayout>

                        </LinearLayout>

                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/msg_others_rl_commonmsg"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/msg_owner_rl_commonmsg"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="10dp"
                        android:visibility="visible">

                        <ImageView
                            android:id="@+id/msg_others_iv_head"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginLeft="10dp"
                            android:src="@drawable/icon_chat" />

                        <TextView
                            android:id="@+id/msg_others_tv_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_toRightOf="@+id/msg_others_iv_head"
                            android:text="电子公证员"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:id="@+id/msg_others_ll_contentview"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/msg_others_tv_name"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="8dp"
                            android:layout_toRightOf="@+id/msg_others_iv_head"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/msg_others_tv_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:textColor="#333333"
                                android:textSize="16sp"
                                android:visibility="visible"
                                tools:text="contentcontent" />

                        </LinearLayout>

                    </RelativeLayout>

                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

            <LinearLayout
                android:id="@+id/rn_input"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_below="@+id/rv_answer"
                android:layout_marginRight="@dimen/space_10"
                android:background="@drawable/shape_corner6_right_bottom_white"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_line"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/shape_corner_green"
                    android:gravity="center"
                    android:onClick="gotoVideoConsult"
                    android:padding="@dimen/space_12"
                    android:text="视频连线公证员"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_20" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</LinearLayout>