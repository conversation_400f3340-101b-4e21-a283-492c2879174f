<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/signatureView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_corner10_white_all"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/lnl_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_personal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/shape_3c6af4_top_left"
            android:gravity="center"
            android:padding="@dimen/space_18"
            android:text="@string/personal"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size16" />

        <TextView
            android:id="@+id/tv_company"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/shape_f5f5f7_top_right"
            android:gravity="center"
            android:padding="@dimen/space_18"
            android:text="@string/company_unit"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size16" />
    </LinearLayout>

    <!--个人-->
    <LinearLayout
        android:id="@+id/lnl_personal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/lnl_title"
        android:orientation="vertical"
        android:padding="@dimen/space_40"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_role_must"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_role"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tv_role_must"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/role_blank"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <RadioGroup
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="2"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rb_applicant"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:checked="true"
                    android:drawableLeft="@drawable/select_radio"
                    android:drawablePadding="@dimen/space_10"
                    android:text="@string/applicant"
                    android:textColor="@color/color3333" />

                <RadioButton
                    android:id="@+id/rb_proxy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_40"
                    android:button="@null"
                    android:drawableLeft="@drawable/select_radio"
                    android:drawablePadding="@dimen/space_10"
                    android:text="@string/proxy"
                    android:textColor="@color/color3333" />
            </RadioGroup>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_20">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_username_must"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_username"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_toRightOf="@+id/tv_username_must"
                    android:text="@string/username_blank"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_username"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="2"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/username_hint"
                android:imeOptions="actionDone"
                android:maxLength="15"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true" />

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_phone_must"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_phone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_toRightOf="@+id/tv_phone_must"
                    android:text="@string/phone_blank"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_phone"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="2"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:digits="1234567890"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/phone_hint"
                android:imeOptions="actionDone"
                android:maxLength="11"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true"
                android:text="15850669988" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_30">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_card_must"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_card"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tv_card_must"
                    android:text="@string/certificate_number"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_card"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="1.95"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:digits="1234567890X"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/card_hint"
                android:imeOptions="actionDone"
                android:maxLength="18"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true" />

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_type_must"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_certificate_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tv_type_must"
                    android:text="@string/certificate_type"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

<!--            <RelativeLayout-->
<!--                android:id="@+id/rnl_personal_certificate_type_value"-->
<!--                android:layout_height="match_parent"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_weight="1.3"-->
<!--                android:background="@drawable/shape_corner4_f1f1f2"-->
<!--                android:layout_marginLeft="@dimen/space_10"-->
<!--                android:layout_marginRight="@dimen/space_5">-->

            <Spinner
                android:id="@+id/sp_certificate_type_value"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1.3"
                android:gravity="center_vertical"
                android:imeOptions="actionDone"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:layout_marginRight="@dimen/space_5"
                android:layout_marginLeft="@dimen/space_10"
                android:singleLine="true"
                android:focusable="true"
                android:spinnerMode="dialog"
                android:text="@string/idcard" />
<!--                <ImageView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:src="@mipmap/icon_down"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_marginRight="@dimen/space_20"-->
<!--                    android:layout_centerVertical="true"/>-->
<!--            </RelativeLayout>-->

            <TextView
                android:id="@+id/tv_idcard_identy"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_weight="0.7"
                android:background="@drawable/shape_corner5_3c6af4"
                android:gravity="center"
                android:paddingHorizontal="@dimen/space_8"
                android:paddingVertical="@dimen/space_8"
                android:text="@string/card_identy"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size16" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_30">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_birth_must"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_birth"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tv_birth_must"
                    android:text="@string/birthdayString"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_birth"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="1.68"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:drawablePadding="@dimen/space_8"
                android:editable="false"
                android:gravity="center_vertical"
                android:hint="@string/birth_hint"
                android:imeOptions="actionDone"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true" />

            <ImageView
                android:id="@+id/iv_birth"
                android:layout_width="@dimen/space_30"
                android:layout_height="@dimen/space_30"
                android:layout_gravity="center"
                android:src="@mipmap/icon_calendar" />

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_sex_must"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_sex"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tv_sex_must"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/sex_blank"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <RadioGroup
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="2"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rb_male"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:checked="true"
                    android:drawableLeft="@drawable/select_radio"
                    android:drawablePadding="@dimen/space_10"
                    android:text="@string/male"
                    android:textColor="@color/color3333" />

                <RadioButton
                    android:id="@+id/rb_female"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/space_40"
                    android:button="@null"
                    android:drawableLeft="@drawable/select_radio"
                    android:drawablePadding="@dimen/space_10"
                    android:text="@string/female"
                    android:textColor="@color/color3333" />
            </RadioGroup>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_30">

            <TextView
                android:id="@+id/tv_address"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_weight="0.45"
                android:text="@string/address_blank"
                android:textColor="@color/color6666"
                android:textSize="@dimen/font_size14" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_address"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_5"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="4"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/address_hint"
                android:imeOptions="actionDone"
                android:maxLength="155"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/space_40"
            android:layout_marginTop="@dimen/space_40"
            android:drawableLeft="@mipmap/explain"
            android:drawablePadding="@dimen/space_8"
            android:text="@string/add_apply_hint"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

    </LinearLayout>

    <!--单位-->
    <LinearLayout
        android:id="@+id/lnl_company"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/lnl_title"
        android:orientation="vertical"
        android:padding="@dimen/space_40"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_role_c_must"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_role_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tv_role_c_must"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/role_blank"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <RadioGroup
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="2"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rb_applicant_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:checked="true"
                    android:drawableLeft="@drawable/select_radio"
                    android:drawablePadding="@dimen/space_10"
                    android:text="@string/applicant"
                    android:textColor="@color/color3333" />

            </RadioGroup>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_28"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_name_must"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_name_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_toRightOf="@+id/tv_name_must"
                    android:text="@string/companyname_blank"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_name_c"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="2"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/companyname_hint"
                android:imeOptions="actionDone"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_30">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_type_must_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_type_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tv_type_must_c"
                    android:text="@string/certificate_type"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <Spinner
                android:id="@+id/sp_certificate_type_c"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="2"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:drawableRight="@mipmap/icon_down"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/company_type_hint"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:spinnerMode="dialog" />

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_card_must_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_card_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tv_card_must_c"
                    android:text="@string/certificate_number"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_card_c"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="2"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"

                android:hint="@string/legal_card_num_hint"
                android:imeOptions="actionDone"
                android:maxLength="50"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_20"
            android:text="@string/legal_boss"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/space_5"
            android:background="#F1F1F2" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_20">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_identy_type_must_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_identy_type_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tv_identy_type_must_c"
                    android:text="@string/certificate_type"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <Spinner
                android:id="@+id/sp_identy_type_c"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="1.3"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:gravity="center_vertical"
                android:imeOptions="actionDone"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true"
                android:spinnerMode="dialog"
                android:text="@string/idcard" />

            <TextView
                android:id="@+id/tv_idcard_identy_c"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_weight="0.7"
                android:background="@drawable/shape_corner5_3c6af4"
                android:gravity="center"
                android:paddingHorizontal="@dimen/space_8"
                android:paddingVertical="@dimen/space_8"
                android:text="@string/card_identy"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size16" />

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_legal_card_must"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_legal_card"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tv_legal_card_must"
                    android:text="@string/certificate_number"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_legal_card"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="2"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:digits="1234567890X"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/card_hint"
                android:imeOptions="actionDone"
                android:maxLength="18"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_30">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_companyname_must_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_companyname"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_toRightOf="@+id/tv_companyname_must_c"
                    android:text="@string/username_blank"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_company"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="2"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/username_hint"
                android:imeOptions="actionDone"
                android:maxLength="50"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true" />

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_20"
                android:layout_weight="0.5">

                <TextView
                    android:id="@+id/tv_phone_must_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_marginRight="@dimen/space_5"
                    android:layout_weight="1"
                    android:text="*"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="@dimen/font_size14" />

                <TextView
                    android:id="@+id/tv_phone_c"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_toRightOf="@+id/tv_phone_must_c"
                    android:text="@string/phone_blank"
                    android:textColor="@color/color6666"
                    android:textSize="@dimen/font_size14" />
            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edt_phone_c"
                style="@style/edit_spinner_edit_sty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/space_10"
                android:layout_marginRight="@dimen/space_5"
                android:layout_weight="2"
                android:background="@drawable/shape_corner4_f1f1f2"
                android:digits="1234567890"
                android:drawablePadding="@dimen/space_8"
                android:gravity="center_vertical"
                android:hint="@string/legal_phone_hint"
                android:imeOptions="actionDone"
                android:maxLength="11"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/space_20"
                android:paddingVertical="@dimen/space_8"
                android:singleLine="true" />
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginTop="@dimen/space_40"
            android:drawableLeft="@mipmap/explain"
            android:drawablePadding="@dimen/space_8"
            android:text="@string/add_apply_hint"
            android:textColor="@color/color3333"
            android:textSize="@dimen/font_size14" />
    </LinearLayout>



    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginVertical="@dimen/space_28"
        android:layout_marginBottom="@dimen/space_28"
        android:gravity="center">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/tv_confirm"
            android:background="@drawable/shape_corner5_f5f5f7"
            android:paddingHorizontal="@dimen/space_35"
            android:paddingVertical="@dimen/space_8"
            android:text="@string/cancel"
            android:textColor="@color/color6666"
            android:textSize="@dimen/font_size16" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/space_20"
            android:layout_marginRight="@dimen/space_20"
            android:background="@drawable/shape_corner5_3c6af4"
            android:paddingHorizontal="@dimen/space_35"
            android:paddingVertical="@dimen/space_8"
            android:text="@string/confirm"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size16" />
    </RelativeLayout>

</RelativeLayout>
