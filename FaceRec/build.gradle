apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'

android {
    splits {
        abi {
            enable true
            reset()
            include 'armeabi-v7a'
            universalApk false
        }
    }
    compileSdkVersion 29
    buildToolsVersion "29.0.3"
    ndkVersion "20.0.5594570"
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 29
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'

    }



    sourceSets {
        main {
            assets.srcDirs = ['assets']
            jniLibs.srcDirs = ['libs']
        }
    }

    // solving com.android.tools.r8.CompilationFailedException: Compilation failed to complete
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
//    ndkVersion '21.4.7075529'

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.core:core-ktx:1.2.0'
    implementation "com.serenegiant:common:2.12.4"
    api files('libs\\arcsoft_face.jar')
    api files('libs\\arcsoft_image_util.jar')
    api files('libs\\autobanh.jar')
}
