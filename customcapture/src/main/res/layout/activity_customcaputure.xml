<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/custom_main_bg">


    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/trtc_tc_cloud_view_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/ll_trtc_mute_video_default"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black"
        android:visibility="gone">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="center"
            android:src="@mipmap/custom_user_portrait" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp">


        <ImageView
            android:id="@+id/trtc_ic_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="15dp"
            android:background="@mipmap/custom_ic_back" />

        <TextView
            android:id="@+id/trtc_tv_room_number"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="15dp"
            android:gravity="center_horizontal"
            android:text="1111"
            android:textColor="@android:color/white"
            android:textSize="20sp" />

    </LinearLayout>


    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/trtc_tc_cloud_view_1"
        android:layout_width="90dp"
        android:layout_height="160dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="80dp" />

    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/trtc_tc_cloud_view_2"
        android:layout_width="90dp"
        android:layout_height="160dp"
        android:layout_above="@id/trtc_tc_cloud_view_1"
        android:layout_alignParentRight="true"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="10dp" />

    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/trtc_tc_cloud_view_3"
        android:layout_width="90dp"
        android:layout_height="160dp"
        android:layout_above="@id/trtc_tc_cloud_view_2"
        android:layout_alignParentRight="true"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="10dp" />


    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/trtc_tc_cloud_view_4"
        android:layout_width="90dp"
        android:layout_height="160dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="15dp"
        android:layout_marginBottom="80dp" />


    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/trtc_tc_cloud_view_5"
        android:layout_width="90dp"
        android:layout_height="160dp"
        android:layout_above="@id/trtc_tc_cloud_view_4"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="15dp"
        android:layout_marginBottom="10dp" />


    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/trtc_tc_cloud_view_6"
        android:layout_width="90dp"
        android:layout_height="160dp"
        android:layout_above="@id/trtc_tc_cloud_view_5"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="15dp"
        android:layout_marginBottom="10dp" />

</RelativeLayout>