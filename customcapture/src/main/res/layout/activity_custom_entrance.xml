<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rtc_entrance_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/custom_main_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/entrance_ic_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="15dp"
            android:background="@mipmap/custom_ic_back" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="15dp"
            android:gravity="center_horizontal"
            android:text="TRTC示例教程"
            android:textColor="@android:color/white"
            android:textSize="20sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_room_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="100dp"
        android:layout_marginRight="60dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请输入房间号："
            android:textColor="@color/custom_green_bg" />

        <EditText
            android:id="@+id/et_input_room_id"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:layout_marginTop="5dp"
            android:background="@drawable/custom_edit_bg"
            android:inputType="number" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_room_info"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="60dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请输入用户名："
            android:textColor="@color/custom_green_bg" />

        <EditText
            android:id="@+id/et_input_username"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:layout_marginTop="5dp"
            android:background="@drawable/custom_edit_bg" />

    </LinearLayout>

    <Button
        android:id="@+id/bt_enter_room"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_margin="60dp"
        android:background="@drawable/custom_button_bg"
        android:text="进入房间"
        android:textColor="@android:color/white" />

</RelativeLayout>