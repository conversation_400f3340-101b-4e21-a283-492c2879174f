import 'dart:async';
import 'dart:io';
import 'package:amap_location_fluttify/amap_location_fluttify.dart';
import 'package:dio/dio.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:notarization_station_app/config.dart';
import 'package:notarization_station_app/routes/router.dart' as Routers;
import 'package:notarization_station_app/utils/ServiceLocator.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:package_info/package_info.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:umeng_apm_sdk/umeng_apm_sdk.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

import 'base_framework/config/app_config.dart';
import 'global/global_provider_manager.dart';
import 'utils/global.dart';

void main() {

  final UmengApmSdk umengApmSdk = UmengApmSdk(
    name: '',

    bver: '',

    // 是否开启SDK运行时日志输出
    enableLog: true,

    // 您使用的flutter版本，默认为空，为方便定位访问，建议配置
    flutterVersion: '2.0.4',

    engineVersion: '2dce47073a',

    // 开启监测页面帧率（默认关闭) 版本 v2.1.3 可支持
    enableTrackingPageFps: true,

    // 开启监测页面性能（默认关闭）版本 v2.1.3 可支持
    enableTrackingPagePerf: true,

    // 带入继承ApmWidgetsFlutterBinding的覆写和初始化方法, 可用于自定义监听应用生命周期
    // 确保去掉原有的WidgetsFlutterBinding.ensureInitialized() ，以免出现重复初始化绑定的异常造成无法正常初始化，SDK内部已通过initFlutterBinding入参带入继承的WidgetsFlutterBinding实现初始化操作
    initFlutterBinding: MyApmWidgetsFlutterBinding.ensureInitialized,

  );

  umengApmSdk.init(appRunner: (observer) async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String packageName = packageInfo.packageName;
    String buildNumber = packageInfo.buildNumber;
    String version = packageInfo.version;
    umengApmSdk.name = packageName;
    umengApmSdk.bver = '$version+$buildNumber';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isOne = prefs.getBool("isOne");
    try {
      Response response = await Dio().get(Config.systemSetting);
      wjPrint("response-----$response");
      if (response.data != null &&
          response.data["country_state"] != null &&
          response.data["country_state"]['code'] == 200001) {
        wjPrint('getData:success');
        G.isColorFiltered = true;
      }
    } catch (e) {
      ExceptionTrace.captureException(exception: Exception(e.toString()));
    }
    await AmapLocation.instance.init(
        iosKey:
        "11b7bf8dfc4f12d78fecb4f2f8f72885");
    ///横竖屏
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
    await AppConfig.init();
    setupLocator();
    // /// 动态申请定位权限
    // requestPermission();
    if (Platform.isAndroid) {
      await FlutterDownloader.initialize(debug: true);
    }
    EasyLoading.instance
      ..displayDuration = Duration(minutes: 3)
      ..maskType = EasyLoadingMaskType.black
      ..userInteractions = false;
    return MyApp(
      navigatorObserver: observer,
    );
  });
  FlutterError.onError = (FlutterErrorDetails details) async {
    wjPrint("进入FlutterError.onError");
    bool isDebugMode = false;
    assert(() {
      isDebugMode = true;
      return true;
    }());
    if (isDebugMode) {
      FlutterError.dumpErrorToConsole(details);
    } else {
      ExceptionTrace.captureException(exception: Exception("进入FlutterError.onError" + details.toString()));
    }
  };
}

/// 动态申请定位权限
void requestPermission() async {
  // 申请权限
  bool hasLocationPermission = await requestLocationPermission();
  if (hasLocationPermission) {
    wjPrint("定位权限申请通过");
  } else {
    wjPrint("定位权限申请不通过");
  }
}

/// 申请定位权限
/// 授予定位权限返回true， 否则返回false
Future<bool> requestLocationPermission() async {
  //获取当前的权限
  var status = await Permission.location.status;
  if (status == PermissionStatus.granted) {
    //已经授权
    return true;
  } else {
    //未授权则发起一次申请
    status = await Permission.location.request();
    if (status == PermissionStatus.granted) {
      return true;
    } else {
      return false;
    }
  }
}

class MyApmWidgetsFlutterBinding extends ApmWidgetsFlutterBinding {
  static WidgetsBinding ensureInitialized() {
    MyApmWidgetsFlutterBinding();
    return WidgetsBinding.instance;
  }
}

class MyApp extends StatefulWidget {
  final NavigatorObserver navigatorObserver;
  const MyApp({Key key, this.navigatorObserver}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    String channelString = "";
    if (Platform.isAndroid) {
      channelString = "江苏省远程公证/android";
    } else if (Platform.isIOS) {
      channelString = "江苏省远程公证/ios";
    }
    getProjectEncryptSetting();
    UmengCommonSdk.initCommon("668b4091940d5a4c49853a1b",
        "668b41c6940d5a4c49853add", channelString);
    UmengCommonSdk.setPageCollectionModeAuto();
  }


  @override
  Widget build(BuildContext context) {
    setDesignWHD(750, 1334, density: 1.0);
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: MultiProvider(
          providers: providers,
          child: RefreshConfiguration(
            hideFooterWhenNotFull: false, //列表数据不满一页,不触发加载更多
            child: MaterialApp(
                navigatorKey: G.navigatorKey,
                navigatorObservers: <NavigatorObserver>[
                  widget.navigatorObserver ??
                      ApmNavigatorObserver.singleInstance
                ],
                debugShowCheckedModeBanner: false,
                locale: Locale("zh"),
                //国际化工厂代理
                localizationsDelegates: [
                  RefreshLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate
                ],
                supportedLocales: [
                  const Locale('en'),
                  const Locale('zh'),
                ],
                localeResolutionCallback:
                    (Locale locale, Iterable<Locale> supportedLocales) {
                  //wjPrint("change language");
                  return locale;
                },
                onGenerateRoute: Routers.Router.getRoutes,
                builder: (BuildContext context, Widget child) {
                  // Widget myChild;
                  // if(!kReleaseMode){
                  //   myChild = Stack(
                  //     children: [
                  //       child,
                  //       Positioned(
                  //         top: 50,
                  //         right: 20,
                  //         child: NetworkSpeedWidget(),
                  //       )
                  //     ],
                  //   );
                  // }else{
                  //   myChild = child;
                  // }
                  return MediaQuery(
                    data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
                    child: Material(
                      type: MaterialType.transparency,
                      child: FlutterEasyLoading(child: child),
                    ),
                  );
                },
                initialRoute: Routers.RoutePaths.HomeIndex
              // home: NavigationHomeScreen(),
            ),
          )),
    );
  }
}


