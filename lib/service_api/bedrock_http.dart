
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:umeng_apm_sdk/umeng_apm_sdk.dart';

import '../config.dart';
import 'intercept.dart';

typedef NetSuccessCallback<T> = Function(T data);
typedef NetErrorCallback = Function(int code, String msg);

class HttpManager {
  String baseUrl = Config.hostUrl;
  int connectTimeout = 60000;
  int receiveTimeout = 60000;
  factory HttpManager() => _singleton;

  static final HttpManager _singleton = HttpManager._();

  static HttpManager get instance => HttpManager();

  static Dio _dio;

  Dio get dio => _dio;

  HttpManager._() {
    final _options = new BaseOptions(
      baseUrl: baseUrl,
      //连接时间为5秒
      connectTimeout: connectTimeout,
      //响应时间为3秒
      receiveTimeout: receiveTimeout,
      //设置请求头
      headers: {},
      //默认值是"application/json; charset=utf-8",Headers.formUrlEncodedContentType会自动编码请求体.
      contentType: Headers.jsonContentType,
      //共有三种方式json,bytes(响应字节),stream（响应流）,plain
      responseType: ResponseType.plain,
    );
    _dio = Dio(_options);

    // /// Fiddler抓包代理配置 https://www.jianshu.com/p/d831b1f7c45b
    // (_dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //     (HttpClient client) {
    //   client.findProxy = (uri) {
    //     //proxy all request to localhost:8888
    //     return 'PROXY ************:8888';
    //   };
    //   client.badCertificateCallback =
    //       (X509Certificate cert, String host, int port) => true;
    // };

    // 设置Cookie
    //  _dio.interceptors.add(CookieManager(CookieJar()));
    /// 添加拦截器
    _dio.interceptors..add(BaseInterceptor());
    if (kReleaseMode) {
      _dio.interceptors.add(LogInterceptor(
          responseBody: false,
          request: false,
          requestBody: false,
          requestHeader: false,
          responseHeader: false));
    } else {
      _dio.interceptors.add(LogInterceptor(
          responseBody: true,
          request: true,
          requestBody: true,
          requestHeader: true,
          responseHeader: true));
    }
  }

  //get请求方法
  Future<Response> get(url,
      {queryParameters,
      Options options,
      CancelToken cancelToken,
      Function errorCallBack,
      bool checkNet = true}) async {
    Response response;
    wjPrint('打印请求的方式：get,请求的数据参数：$queryParameters');
    try {
      if (checkNet) {
        NetWorkTempConnectivityTools().initConnectivity();
      }
      Options tempOptions = options;
      String sm4PublicKey = getPublicKey();
      String sm4EncryptKey = encryptSM4Key(sm4PublicKey);
      tempOptions ??= Options();
      tempOptions.headers['encrypt'] = sm4EncryptKey;
      if (queryParameters != null) {
        String encryptData = wjEncrypt(queryParameters, sm4PublicKey);
        wjPrint("加密后的数据：$encryptData");
        response = await _dio.get(url,
            queryParameters: {
              "encryptStr": encryptData,
            },
            options: tempOptions,
            cancelToken: cancelToken);
      } else {
        wjPrint("加密后的数据：无加密数据");
        response =
            await _dio.get(url, options: tempOptions, cancelToken: cancelToken);
      }
    } catch (e) {
      if (errorCallBack != null) {
        errorCallBack.call(e);
      }
      ExceptionTrace.captureException(
          exception: Exception('url:$url \n error: ${e.toString()}'));
      wjPrint("...请求出错了$e");
    }
    return response;
  }

  //post请求
  Future<Response> post(url,
      {data,
      Map<String, dynamic> queryParameters,
      Options options,
      CancelToken cancelToken,
      Function errorCallBack,
      bool checkNet = true}) async {
    wjPrint('打印请求的方式：post,请求的数据参数：${queryParameters ?? data}');
    Response response;
    try {
      if (checkNet) {
        NetWorkTempConnectivityTools().initConnectivity();
      }
      Options tempOptions = options;
      String sm4PublicKey = getPublicKey();
      String sm4EncryptKey = encryptSM4Key(sm4PublicKey);
      if (tempOptions == null) {
        tempOptions ??= Options();
        tempOptions.headers['encrypt'] = sm4EncryptKey;
      } else {
        if (!tempOptions.headers.keys.contains("encrypt")) {
          tempOptions.headers['encrypt'] = sm4EncryptKey;
        }
      }
      if (data != null) {
        String encryptData;
        if (data is FormData) {
          wjPrint("加密后的数据：直传无加密: $data");
        } else {
          encryptData = wjEncrypt(data, sm4PublicKey);
          wjPrint("加密后的数据：$encryptData");
        }
        response = await _dio.post(url,
            data: (data is FormData)
                ? data
                : {
                    "encryptStr": encryptData,
                  },
            options: tempOptions,
            cancelToken: cancelToken);
      } else if (queryParameters != null) {
        String encryptData = wjEncrypt(queryParameters, sm4PublicKey);
        wjPrint("加密后的数据：$encryptData");
        response = await _dio.post(url,
            queryParameters: {
              "encryptStr": encryptData,
            },
            options: tempOptions,
            cancelToken: cancelToken);
      } else {
        response = await _dio.post(url,
            data: (data is FormData)
                ? data
                : {
                    "encryptStr": wjEncrypt(data, sm4PublicKey),
                  },
            queryParameters: {
              "encryptStr": wjEncrypt(queryParameters, sm4PublicKey),
            },
            options: tempOptions,
            cancelToken: cancelToken);
      }
    } catch (e) {
      if (errorCallBack != null) {
        errorCallBack.call(e);
      }
      ExceptionTrace.captureException(
          exception: Exception('url:$url \n error: ${e.toString()}'));
      wjPrint("...请求出错了$e");
    }
    return response;
  }
}

enum Method { get, post, put, patch, delete, head }

/// 使用拓展枚举替代 switch判断取值
extension MethodExtension on Method {
  String get value => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD'][index];
}
