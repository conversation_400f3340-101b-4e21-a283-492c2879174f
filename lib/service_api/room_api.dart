import 'package:notarization_station_app/service_api/bedrock_http.dart';

import '../config.dart';

class RoomApi {
  static RoomApi _instance;

  RoomApi get instance => getSingleton();

  factory RoomApi() => getSingleton();

  static RoomApi getSingleton() {
    if (_instance == null) {
      _instance = RoomApi._internal();
    }
    return _instance;
  }

  RoomApi._internal();

  // 用户进入直播间验证
  static Future checkUserWhenEnterLiveRoom(map) async {
    final response = await HttpManager.instance
        .post('${Config.room}/yxroom/buyer-info/enterDirectSeeding', data: map);
    return response.data;
  }

  // 选房记录

  static Future roomSelectionRecord(map) async {
    final response = await HttpManager.instance.post(
        '${Config.room}/yxroom/buyer-info/roomSelectionRecord',
        data: map);
    return response.data;
  }
}
