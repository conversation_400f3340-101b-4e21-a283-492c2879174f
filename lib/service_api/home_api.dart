import 'dart:math';

import 'package:dio/dio.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/config.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';

import 'bedrock_http.dart';

class HomeApi {
  static HomeApi _singleton;

  factory HomeApi() => getSingleton();

  static HomeApi getSingleton() {
    if (_singleton == null) {
      _singleton = HomeApi._internal();
    }
    return _singleton;
  }

  HomeApi._internal() {
    //do stuff
  }

//获取公证处列表的
  Future getNotarialOffice(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notarialOffice/selectAll",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //获取公证处列表的
  Future getNotarialOfficeTwo(map) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notarialOffice/selectAllUp",
        queryParameters: map);
    return response.data;
  }

  //创建订单
  Future addOrder(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notaryorder/machineAdd",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //取消订单
  Future shutVideoOrder(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/notaryorder/doUserStop", data: map);
    return response.data;
  }

  // 获取金融公证列表页
  Future getTaskList(map, {Function errorCallBack}) async {
    wjPrint("获取金融公证列表页---参数-------$map");
    final response = await HttpManager.instance.post(
        "${Config.enforcement}/block-order/taskList",
        queryParameters: map,
        errorCallBack: errorCallBack);
    wjPrint("获取金融公证列表页---返回结果-------$response");
    return response.data;
  }

  // 获取金融公证简易列表页
  Future getCommonTaskList(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.easyEnforcement}/easy-block-order/taskList",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  // 获取受理室简易模版自然人
  Future getCommonUser(map) async {
    final response = await HttpManager.instance.post(
        "${Config.easyEnforcement}/easy-block-order/getPartyDetails",
        data: map);
    return response.data;
  }

  // 受理室简易模版上传材料
  Future uploadCommonFile(map) async {
    final response = await HttpManager.instance.get(
        "${Config.easyEnforcement}/easy-block-order/addMaterial",
        queryParameters: map);
    return response.data;
  }

  // 金融公证发起公证
  Future launchNotarization(map) async {
    final response = await HttpManager.instance.post(
        "${Config.enforcement}/block-order/launchNotarization",
        data: map);
    return response.data;
  }

  // 简易模版金融公证发起公证
  Future launchCommonNotarization(map) async {
    final response = await HttpManager.instance.post(
        "${Config.easyEnforcement}/easy-block-order/launchNotarization",
        data: map);
    return response.data;
  }

  // 金融公证-受理室获取顶部列表
  Future selectPage(map) async {
    final response = await HttpManager.instance
        .post("${Config.userModule}/sys/dict/selectPage", data: map);
    return response.data;
  }

  // 获取主键
  Future getSelectAllTop(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.userModule}/sys/dict/selectAllTop",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  // 根据主键ID获取数据
  Future getByParentId(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.userModule}/sys/dict/getByParentId",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  // 查询公证员状态列表
  Future getListNotaryStatus(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.get(
        "${Config.notaryModule}/cgz/notaryPublic/listNotaryStatus",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  // 受理室订单详情
  Future blockOrderGetPartyDetails(map) async {
    final response = await HttpManager.instance
        .post("${Config.enforcement}/block-order/getPartyDetails", data: map);
    return response.data;
  }

  //上传图片
  Future uploadImg(Map map) async {
    final response = await HttpManager.instance
        .post("${Config.annexModule}/sys/annex/uploadfiles", data: map);
    return response.data;
  }

  Future uploadPictures(MultipartFile file, {Function errorCallBack}) async {
    FormData formData = FormData.fromMap({
      "file": file,
    });
    Options options = Options(
        receiveTimeout: 5000,
        sendTimeout: 5000,
        contentType: "multipart/form-data");
    final response = await HttpManager.instance.post(
        "${Config.annexModule}/sys/annex/fastDFSUpload",
        data: formData,
        options: options,
        errorCallBack: errorCallBack);
    wjPrint("uploadPictures---------$response");
    return response.data;
  }

  //活体检测
  Future getBiopsy(Map map) async {
    final response = await HttpManager.instance.post(
        "${Config.identificationModule}/biopsyController/bioassay",
        data: map);
    return response.data;
  }

  //人脸比对
  Future faceComparison(map) async {
    final response = await HttpManager.instance
        .post("${Config.identificationModule}/face/faceComparison", data: map);
    return response.data;
  }

  //支付宝支付
  Future aliPay(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/alipay/doSign",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //自助存证   支付宝支付
  Future autoAliPay(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/alipay/selfHelpDoSign",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  // 支付宝支付  /alipay/doAppPagePay
  Future doAppPagePay(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/alipay/doAppPagePay",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //微信支付 selfHelpMWEBParamFixedAppId     MWEBParamFixedAppId
  // Future wxPay(map) async {
  //   final response = await HttpManager.instance.post("${Config.notaryModule}/cgz/wxpay/MWEBParamFixedAppId", queryParameters: map);
  //   return response.data;
  // }
  //
  // //自助存证  微信支付
  // Future autoWxPay(map) async {
  //   final response = await HttpManager.instance.post("${Config.notaryModule}/cgz/wxpay/selfHelpMWEBParamFixedAppId", queryParameters: map);
  //   return response.data;
  // }

  Future wxPay(map) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/wxpay/MWEBParam",
        queryParameters: map);
    return response.data;
  }

  //自助存证  微信支付
  Future autoWxPay(map) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/wxpay/selfHelpWXParam",
        queryParameters: map);
    return response.data;
  }

  //公证用途分类
  Future getNotarizationPurpose(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notarypurpose/selectTree",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //公证使用地列表
  Future getCountryArea(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/sys/countryArea/selectAll",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //公证语言列表
  Future getCountryLanguage(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/sys/countryLanguage/selectAll",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //自助公证提交订单
  Future addAutoHelpList(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notaryorder/add",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  /// 提交定位信息
  ///  "busiId": "busiId_a2b5ff776766",
  ///   "busiType": 0,
  ///   "idCard": "idCard_e83c95f4b5ce",
  ///   "ip": "ip_9a2c4287acc2",
  ///   "longitude": "longitude_90a2d99056fe",
  ///   "latitude": "latitude_20cde4883185"
  /// busiType： 1自助、2视频、3合同
  Future uploadLocationInformation(map, {Function errorCallBack})async{
    final response = await HttpManager.instance.post("${Config.notaryModule}/cgz/geo/updBusiGeoInfo",data:map,errorCallBack: errorCallBack);
    return response.data;
  }

  // 视频公证待处理、已取消提醒 /sys/ordervideo/app/videoNotarizationWaitHandleRemind
  /*
   greffierIdCard	公证员身份证号		true	 string
   notaryId	公证处Id		false	 string
   type	提醒类型 1-待受理 2-待受理取消		true
integer(int32)
  */
  Future getVideoNotarizationWaitHandleRemind(map,
      {Function errorCallBack}) async {
    wjPrint("获取金融公证列表页---参数-------$map");
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/sys/ordervideo/app/videoNotarizationWaitHandleRemind",
        data: map,
        errorCallBack: errorCallBack);
    wjPrint("获取金融公证列表页---返回结果-------$response");
    return response.data;
  }

  //设置邮寄
  Future setTakeType(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notaryorder/doSetState",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //自助公证 上传材料保存接口
  Future upLoadMaterial(map) async {
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/material/add", data: map);
    return response.data;
  }

  //自助公证  获取公证事项材料
  Future getUpLoadMaterialName(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notaryitemmodel/selectNotaryItem",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //自助公证  预览
  Future previewFile(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/material/addApp",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //签名
  Future doSetSignNameJSCA(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notaryorder/doSetSignNameJSCA",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //自助公证  签字
  Future doSetSignName(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notaryorder/doSetSignName",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //在线咨询  留言
  Future askMessage(map) async {
    final response = await HttpManager.instance
        .post("${Config.voteModule}/cgz/consultation/add", data: map);
    return response.data;
  }

  //在线咨询 人工服务
  Future askPeople(map) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/roomnumber/getArtificial",
        queryParameters: map);
    return response.data;
  }

  //在线咨询 消息记录
  Future getChattingList(map) async {
    final response = await HttpManager.instance.post(
        "${Config.voteModule}/cgz/consultation/selectPage",
        queryParameters: map);
    return response.data;
  }

  //查询是否有合同公证
  Future getContract(map, {Function errorCallBack}) async {
    wjPrint(map);
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notaryorder/queryContract",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //查询支付结果
  Future getPay(map) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/wxpay/orderQuery",
        queryParameters: map);
    return response.data;
  }

  //消息通知 获取所有消息
  Future getAllInfoList(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/articleupload/selectPage",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //自助存证 新增
  Future addAutoOrder(map) async {
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/sys/smarthome/add", data: map);
    return response.data;
  }

//新增预约
  Future addAppointment(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/makeappointment/add",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //预约列表
  Future appointmentList(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/makeappointment/selectPage",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

//预约发起
  Future appointmentOrder(map) async {
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/notaryorder/orderAdd", data: map);
    return response.data;
  }

  //预约取消
  Future cancelAppointmentOrder(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/makeappointment/cancel",
        data: map,
        errorCallBack: errorCallBack,
        options: Options(contentType: 'application/x-www-form-urlencoded'));
    return response.data;
  }

  ///进入会议
  Future postConference(Map<String, dynamic> params) async {
    final response = await HttpManager.instance.post(
        '${Config.notaryModule}/cgz/netmeeting/enterRoom',
        queryParameters: params);
    return response.data;
  }

  ///提交表决
  Future postVote(map) async {
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/netmeeting/voteToPdf", data: map);
    return response.data;
  }

  ///签字包
  Future postSignature(map) async {
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/meetinguser/userSign", data: map);
    // final response = await HttpManager.instance.post("${Config.notaryModule}/cgz/videolog/txYunGetSign", data: map);
    // wjPrint("===================="+response.data);
    return response.data;
  }

  //对公列表
  Future protocolList(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/publicnotaryuser/queryPublicNotary",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  ///
  /// 对公及合同列表
  Future multipartyNotaryList(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.get(
        "${Config.notaryModule}/cgz/notaryorder/multipartyNotaryList",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //对公列表
  Future getProtocol(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/signingsubject/queryNotaryTime",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  Future getProtocolInfo(map) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/signingsubject/querySignUser",
        data: map);
    return response.data;
  }

  Future historyNotarial(map) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/historynotarialoffice/add",
        data: map);
    return response.data;
  }

  Future queryHistoryOffice(map) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/historynotarialoffice/queryHistoryOffice",
        data: map);
    return response.data;
  }

  Future getContractRecord(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.bankModule}/cgz/bankorder/selectPage",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //更新小额NEW
  Future updateSmall(map) async {
    final response = await HttpManager.instance.post(
        "${Config.bankModule}/cgz/bankmaterialrelation/addList",
        queryParameters: map);
    return response.data;
  }

  //删除小额NEW
  Future delSmallImg(map) async {
    // final response = await bedRock.post("/cgz/bankmaterial/delete", queryParameters: map);
    final response = await HttpManager.instance.post(
        "${Config.bankModule}/cgz/bankmaterialrelation/delete",
        queryParameters: map);
    return response.data;
  }

  ///人脸
  Future postFace(Map<String, dynamic> params) async {
    final response = await HttpManager.instance.post(
        '${Config.notaryModule}/cgz/meetinguser/userIdentification',
        data: params);
    return response.data;
  }

  Future getBank(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.bankModule}/cgz/creditbankorder/selectPage",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  Future getSmallDetail(map) async {
    final response = await HttpManager.instance.post(
        "${Config.bankModule}/cgz/creditbankorder/getByUnitGuid",
        data: map);
    return response.data;
  }

  Future updateBank(map) async {
    final response = await HttpManager.instance.post(
        "${Config.bankModule}/cgz/creditbankorder/materialAdd",
        data: map);
    return response.data;
  }

  Future delBankImg(map) async {
    final response = await HttpManager.instance.post(
        "${Config.bankModule}/cgz/creditbankorder/materialDelete",
        data: map);
    return response.data;
  }

  Future uploadPart(map) async {
    Options options = Options(contentType: "multipart/form-data");
    final response = await HttpManager.instance.post(
        "${Config.annexModule}/sys/annex/uploadPart",
        data: map,
        options: options);
    return response.data;
  }

  Future uploadPageId(Map<String, dynamic> data) async {
    final response = await HttpManager.instance.get(
        "${Config.annexModule}/sys/annex/initiateMultipartUpload",
        queryParameters: data);
    return response.data;
  }

  Future openLog(Map<String, dynamic> data) async {
    final response = await HttpManager.instance
        .post("${Config.userModule}/operLog/add", data: data);
    return response.data;
  }

  /// 版本更新
  Future updateVersion(map, {Function errorCallBack}) async {
    try {
      BaseOptions _options = BaseOptions(
        baseUrl: Config.hostUrl,
        //连接时间为5秒
        connectTimeout: 60000,
        //响应时间为3秒
        receiveTimeout: 60000,
        //设置请求头
        headers: {},
        //默认值是"application/json; charset=utf-8",Headers.formUrlEncodedContentType会自动编码请求体.
        contentType: Headers.jsonContentType,
        //共有三种方式json,bytes(响应字节),stream（响应流）,plain
        responseType: ResponseType.json,
      );
      wjPrint("版本更新接口传参打印：$map");
      final response = await Dio(_options).post("${Config.userModule}/cgz/appedition/selectNewBy",data: map);
      wjPrint("版本更新接口返回数据打印：${response.toString()}");
      return response.data;
    } catch (e) {
      errorCallBack(e);
      return null;
    }
  }

  // 获取区号
  Future getPhoneAreaCode(map) async {
    final response = await HttpManager.instance.get(
        "${Config.userModule}/sys/dict/getAreaCode",
        queryParameters: map);
    return response.data;
  }

  //境外用户注册时发送邮箱验证码  /cgz/userApp/sendEmailCode

  Future getSendEmailCode(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        '${Config.userModule}/cgz/userApp/sendEmailCode',
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

// 境外发送短信
  Future getAbordMsgSend(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.get(
        '${Config.userModule}/sys/Sms/abroadSend',
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

// 联系公证员 /cgz/userApp/appContactAdmin
  Future appContactAdmin(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        '${Config.userModule}/cgz/userApp/appContactAdmin',
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  /// 政融支付
  /// appId	小程序AppId 小程序必传		false
  // string
  // callBackUrl	回调地址		true
  // string
  // payLogo	支付类型 1、公证费用 2、补缴费用		true
  // integer(int32)
  // pyChnlCd	渠道 1网站 2 APP 3 H5 4大厅收银台 5自助终端 6系统发起 7POS 8微信小程序 d支付宝小程序		false
  // string
  // unitGuid	订单主键		true
  // string

  Future placeOrder(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        '${Config.notaryModule}/cgz/notaryorder/placeOrder',
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  // 获取省市区信息接口
  Future getProvinceAndCiteMessage(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.get(
        '${Config.userModule}/sysregion/get/$map',
        errorCallBack: errorCallBack);
    return response.data;
  }

  // 数币支付 /cgz/notaryorder/ccBisPlaceOrder
  Future ccBisPlaceOrder(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        '${Config.notaryModule}/cgz/notaryorder/ccBisPlaceOrder',
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  // 查询数币支付的订单详情  /ccbis/queyOrder
  Future queryShuBiOrder(map, {Function errorCallBack, bool checkNet}) async {
    final response = await HttpManager.instance.get('/pay-api/ccbis/queyOrder',
        queryParameters: map, errorCallBack: errorCallBack, checkNet: checkNet);
    return response.data;
  }

// 查询公证书是否配置数币支付
  Future getPayConfig(map, {Function errorCallBack, bool checkNet}) async {
    final response = await HttpManager.instance.get(
        '/pay-api/zrzf/getPayConfig',
        queryParameters: map,
        errorCallBack: errorCallBack,
        checkNet: checkNet);
    return response.data;
  }

// 查询公证书是否配置数币支付
  Future getOnlyOfficeConfig(map,
      {Function errorCallBack, bool checkNet = false}) async {
    final response = await HttpManager.instance.get('/office/config/info',
        queryParameters: map,
        errorCallBack: errorCallBack,
        checkNet: checkNet);
    return response.data;
  }

  // 人脸识别报告生成人脸识别报告
  Future similarityForSC(map,
      {Function errorCallBack, bool checkNet = false}) async {
    final response = await HttpManager.instance.post(
        '/identification/face/similarityForSC',
        data: map,
        errorCallBack: errorCallBack,
        checkNet: checkNet);
    return response.data;
  }

  // 人脸识别报告生成人脸识别报告
  Future saveFaveReport(map,
      {Function errorCallBack, bool checkNet = false}) async {
    final response = await HttpManager.instance.post(
        '${Config.notaryModule}/cgz/notaryorder/saveFaceReport',
        data: map,
        errorCallBack: errorCallBack,
        checkNet: checkNet);
    return response.data;
  }
}
