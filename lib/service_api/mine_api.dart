import 'package:dio/dio.dart';
import 'package:notarization_station_app/config.dart';

import 'bedrock_http.dart';

class MineApi {
  static MineApi _singleton;

  factory MineApi() => getSingleton();

  static MineApi getSingleton() {
    if (_singleton == null) {
      _singleton = MineApi._internal();
    }
    return _singleton;
  }

  MineApi._internal() {
    //do stuff
  }

  Future getAddressList(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/receivingaddress/selectPage",
        queryParameters: map,errorCallBack: errorCallBack);
    return response.data;
  }

  Future addAddress(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/receivingaddress/add", data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  Future updateAddress(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/receivingaddress/update", data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  Future delAddress(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/receivingaddress/delete",
        queryParameters: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  Future upLoadImg(map) async {
    final response = await HttpManager.instance
        .post("${Config.userModule}/cgz/user/appUserHeadUpdate", data: map);
    return response.data;
  }

  Future uploadPictures(String path) async {
    var name = path.substring(path.lastIndexOf("/") + 1, path.length) + ".png";
    var image = await MultipartFile.fromFile(
      path,
      filename: name,
    );
    FormData formData = FormData.fromMap({
      "file": image,
    });
    final response = await HttpManager.instance
        .post("${Config.annexModule}/sys/annex/fastDFSUpload", data: formData);
    return response.data;
  }

  Future upLoadVersion(map) async {
    final response = await HttpManager.instance
        .post("${Config.userModule}/cgz/appedition/selectNewBy", data: map);
    return response.data;
  }

  //获取声网ID
  Future getToken(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/agora/token", data: map,errorCallBack: errorCallBack);
    return response.data;
  }

  Future userToCancellation(map) async {
    final response = await HttpManager.instance.post(
        "${Config.userModule}/cgz/user/userToCancellation",
        queryParameters: map);
    return response.data;
  }

  // 保存境外用户实名信息  /cgz/user/certification
  Future saveAbroadUserInformation(map)async{
    final response = await HttpManager.instance.post(
        "${Config.userModule}/cgz/user/certification",
        data: map);
    return response.data;
  }

   // 人脸是被
  Future getFaceCompare(map,{Function errorCallBack})async{
    final response = await HttpManager.instance.post(
        "${Config.identificationModule}/face/faceComparison",
        data: map,
    errorCallBack: errorCallBack);
    return response.data;
  }

  // 境外人脸校验
  Future getAbroadFaceCompare(map,{Function errorCallBack})async{
    final response = await HttpManager.instance.post(
        "${Config.identificationModule}/overIdAuth/idAuth",
        data: map,
    errorCallBack: errorCallBack);
    return response.data;
  }

  // /cgz/user/disableByLoginName  app账号禁用账号
  Future disableByLoginName(map, {Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.userModule}/cgz/user/disableByLoginName",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }
}
