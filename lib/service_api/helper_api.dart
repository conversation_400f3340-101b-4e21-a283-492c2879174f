import 'package:dio/dio.dart';
import 'package:notarization_station_app/config.dart';
import 'package:notarization_station_app/utils/common_tools.dart';

import 'bedrock_http.dart';

class HelperApi {
  static HelperApi _singleton;

  factory HelperApi() => getSingleton();

  static HelperApi getSingleton() {
    if (_singleton == null) {
      _singleton = HelperApi._internal();
    }
    return _singleton;
  }

  HelperApi._internal() {
    //do stuff
  }

  Future getNotarizationList(map) async {
    //获取公证查询分页接口
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notaryorder/selectPage",
        queryParameters: map);
    return response.data;
  }

  Future getOneNotarizationData(map,{Function errorCallBack}) async {
    //获取单条公证订单详情
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/notaryorder/getByOrderId", data: map,
    errorCallBack: errorCallBack);
    return response.data;
  }

  Future getOneListNotarizationData(map,{Function errorCallBack}) async {
    //新获取公证查询分页接口
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notaryorder/selectNewPage",
        queryParameters: map,
    errorCallBack: errorCallBack);
    return response.data;
  }

  // 自助和视频列表 /cgz/notaryorder/queryPage
  Future queryPageData(map,{Function errorCallBack}) async {
    //新获取公证查询分页接口
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notaryorder/appQueryPage",
        data: map,
        errorCallBack: errorCallBack);
    return response.data;
  }

  //自助存证 查询列表
  Future getAutoOrderList(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/sys/smarthome/selectPage",
        queryParameters: map,
    errorCallBack: errorCallBack);
    return response.data;
  }

  // 视频文件解密
  Future generatePresignedUr(map) async {
    final response = await HttpManager.instance.get(
        "${Config.annexModule}/sys/annex/generatePresignedUr",
        queryParameters: map);
    return response.data;
  }

//对公公证 查询列表
  Future getDuiOrderList(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/signingsubject/selectPage",
        queryParameters: map,
    errorCallBack: errorCallBack);
    return response.data;
  }

  //对公公证 单个
  Future getDuiOrder(map,{Function errorCallBack,bool checkNet = true}) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/signingsubject/getById",
        queryParameters: map,errorCallBack: errorCallBack,checkNet: checkNet);
    return response.data;
  }

  //公证评分详情
  Future getById(map,{Function errorCallBack}) async {
    wjPrint(
        '=============================检查公证评分详情接口地址=============================');
    final response = await HttpManager.instance.get(
        "${Config.notaryModule}/cgz/videoscore/getById",
        queryParameters: map,
    errorCallBack: errorCallBack);
    wjPrint('#####################response.data: ' + response.toString());
    return response.data;
  }

  //公证评分添加
  Future getEvaluateAdd(map) async {
    wjPrint(
        '=============================公证评分添加接口地址=============================');
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/videoscore/insert", data: map);
    wjPrint('#####################response.data: ' + response.toString());
    return response.data;
  }

  //查询支付详情
  Future queryOrderInfo(map,{Function errorCallBack,bool checkNet}) async {
    wjPrint(
        '=============================查询支付详情接口地址=============================');
    final response = await HttpManager.instance
        .post("/pay-api/zrzf/queyOrderInfo", queryParameters: map,options: Options(contentType: "Headers.application/x-www-form-urlencoded"),checkNet: checkNet);
    wjPrint('#####################response.data: ' + response.toString());
    return response.data;
  }

  // 订单评价 /cgz/notaryorder/evaluate
  Future evaluateOrder(map,{Function errorCallBack}) async {
    wjPrint(
        '=============================检查公证评分添加接口地址=============================');
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/notaryorder/evaluate", data: map,);
    wjPrint('#####################response.data: ' + response.toString());
    return response.data;
  }

// 订单列表 自助和shi'oing
  Future equeryPageOrder(map,{Function errorCallBack,bool checkNet}) async {
    wjPrint(
        '=============================查询订单列表接口地址=============================');
    final response = await HttpManager.instance
        .post("${Config.notaryModule}/cgz/notaryorder/appQueryPage", data: map,checkNet: checkNet);
    wjPrint('#####################response.data: ' + response.toString());
    return response.data;
  }
}
