
import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';
import 'package:notarization_station_app/service_api/home_api.dart';

class NotarizedMatterViewModel extends SingleViewStateModel{


  List treeList = [];

  getTree() async {
    var res = await HomeApi.getSingleton()
        .getNotarizationPurpose({}, errorCallBack: (e) {});
    if (res['code'] == 200) {
      treeList = res['items'];
      notifyListeners();
    }
  }


  @override
  Future loadData() {
    // TODO: implement loadData
    throw UnimplementedError();
  }

  @override
  onCompleted(data) {
    // TODO: implement onCompleted
    throw UnimplementedError();
  }

}