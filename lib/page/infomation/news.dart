import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/infomation/vm/news_vm.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:provider/provider.dart';

class NewsPage extends StatefulWidget {
  final arguments;
  NewsPage({Key key, this.arguments}) : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return NewsPageState();
  }
}

class NewsPageState extends BaseState<NewsPage>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  bool isClick = false;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Consumer<UserViewModel>(builder: (ctx, userModel, child) {
      return ProviderWidget<NewsModel>(
        model: NewsModel(userModel, widget.arguments),
        onModelReady: (model) {
          model.getComment();
        },
        builder: (ctx, vm, child) {
          return Scaffold(
            appBar: commonAppBar(title: "详情"),
            backgroundColor: AppTheme.chipBackground,
            body:SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                      padding: EdgeInsets.fromLTRB(10, 8, 10, 8),
                      color: Colors.white,
                      width: getWidthPx(750),
                      child: Html(
                        data: widget.arguments['content'],
                        defaultTextStyle: TextStyle(
                            fontSize: 14, color: Color(0xff4B4B4B)),
                      )),
                  Container(
                    padding: EdgeInsets.fromLTRB(10, 8, 10, 8),
                    color: Colors.white,
                    margin: EdgeInsets.only(bottom: 15),
                    child: Row(
                      children: [
                        // Container(
                        //   constraints: BoxConstraints.expand(
                        //     width: 30.0,
                        //     height: 30.0,
                        //   ),
                        //   decoration: BoxDecoration(
                        //     image: DecorationImage(image: NetworkImage(Config.splicingImageUrl(widget.arguments['headIcon']))),
                        //     borderRadius: BorderRadius.circular(15.0),
                        //   ),
                        // ),
                        // SizedBox(width: 10,),
                        Expanded(
                            child: Text(
                                "${widget.arguments['notarialName']}",
                                style: TextStyle(
                                    fontSize: 16,
                                    color: Color(0xff606060)))),
                        Text("${widget.arguments['createDate']}",
                            style: TextStyle(color: Color(0xff8D8D8D))),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    });
  }

  @override
  bool get wantKeepAlive => true;
}

class NewsPersistensHeaderDelegate extends SliverPersistentHeaderDelegate {
  final int amount;

  NewsPersistensHeaderDelegate(this.amount);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    // TODO: implement build
    return Container(
      height: 45,
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.all(10),
      // margin: EdgeInsets.fromLTRB(0, 15, 0, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(width: 1, color: Color(0xffEFEFEF))),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text("评论 ${amount.toString()}",
              style: TextStyle(fontWeight: FontWeight.w500, fontSize: 18)),
          //Text("赞 ${widget.arguments['likesNumber']}",style: TextStyle(color:Color(0xff8C8C8C),fontSize: 16)),
        ],
      ),
    );
  }

  @override
  // TODO: implement maxExtent
  double get maxExtent => 45;

  @override
  // TODO: implement minExtent
  double get minExtent => 45;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    // TODO: implement shouldRebuild
    return true;
  }
}
