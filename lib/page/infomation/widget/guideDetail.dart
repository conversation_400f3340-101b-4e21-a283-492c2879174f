import 'package:flutter/material.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';

class GuideDetailPage extends StatefulWidget {
  int arguments;
  GuideDetailPage({Key key, this.arguments}) : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return GuidePageState();
  }
}

class GuidePageState extends BaseState<GuideDetailPage> {
  String img1 = 'lib/assets/images/shipingongzheng.png';
  String img2 = 'lib/assets/images/zizhugongzheng.png';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: commonAppBar(title: "办证指南"),
      backgroundColor: AppTheme.chipBackground,
      body: SingleChildScrollView(
        child: Image.asset(
          widget.arguments == 1 ? img2 : img1,
          // loadingBuilder: (context, child, loadingProgress) {
          //   EasyLoading.show(status: "正在加载中...");
          //   if (loadingProgress == null) {
          //     EasyLoading.dismiss();
          //     return child;
          //   }
          //   return SizedBox();
          // },
        ),
      ),
    );
  }
}
