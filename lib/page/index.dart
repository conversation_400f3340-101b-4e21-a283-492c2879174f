import 'dart:io';
import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/helper/index.dart';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/socket/mqtt_client.dart';
import 'package:notarization_station_app/utils/alert_view.dart';
import 'package:notarization_station_app/utils/check_update.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:notarization_station_app/utils/updateEntity.dart';
import 'package:package_info/package_info.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import 'login/vm/user_view_model.dart';
import 'mine/mine_page.dart';

typedef TransportScrollController = Function(ScrollController controller);

class MainIndexPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return MainIndexPageState();
  }
}

class MainIndexPageState extends BaseState<MainIndexPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  PageController _pageController;
  DateTime lastPress;
  DateTime _lastPressedAt;
  final List<Widget> pages = [
    // HomeIndexWdiget(),
    IndexNew(),
    // HomeIndexPage(),
    HelperPage(),
    // InformationIndexPage(),
    // CommunityPage(),
    // MessagePage(),
    MinePage()
  ];
  SharedPreferences prefs;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0);
    Future.delayed(Duration.zero, () async {
      prefs = await SharedPreferences.getInstance();
      bool isOne = prefs.getBool("isOne");
      wjPrint("------------$isOne");
      if (isOne == null || isOne) {
        showDeleteConfirmDialog();
      }
    });

    Future.delayed(Duration(microseconds: 200), () async {
      prefs = await SharedPreferences.getInstance();
      bool isOne = prefs.getBool("isOne");
      if (!isOne) {
        getRemoteVersionData(context);
      }
    });
  }

  // 弹出对话框
  Future<bool> showDeleteConfirmDialog() {
    return showDialog<bool>(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return WillPopScope(
          onWillPop: () async {
            return Future.value(false);
          },
          child: AlertDialog(
            title: Text("隐私政策"),
            content: RichText(
              text: TextSpan(
                text: '欢迎你使用江苏省远程公证APP，请你仔细阅读并充分理解 ',
                style: TextStyle(color: Colors.black, fontSize: 18.0),
                children: <TextSpan>[
                  TextSpan(
                    text: '《隐私政策》 ',
                    style: TextStyle(
                      color: AppTheme.themeBlue,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () async {
                        G.pushNamed(RoutePaths.Privacy);
                      },
                  ),
                  TextSpan(text: '如你同意'),
                  TextSpan(
                    text: '《隐私政策》 ',
                    style: TextStyle(
                      color: AppTheme.themeBlue,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () async {
                        G.pushNamed(RoutePaths.Privacy);
                      },
                  ),
                  TextSpan(
                    text: '的全部内容，请点击“同意”开始使用我们的服务 ',
                  ),
                ],
              ),
            ),
            actions: <Widget>[
              TextButton(
                child: Text("退出"),
                onPressed: () => exit(0),
              ),
              TextButton(
                child: Text("同意"),
                onPressed: () {
                  //关闭对话框并返回true
                  prefs.setBool("isOne", false);
                  Navigator.of(context).pop(true);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  /// 获取后台的当前版本
  void getRemoteVersionData(BuildContext context) async {
    var params = {"editionType": 1};
    HomeApi.getSingleton().updateVersion(params, errorCallBack: (e) {
    }).then((value) async {
      if (value == null) return;
      AppUpdate appInfo = AppUpdate.fromJson(value);
      bool hasUpdate = false;
      int versionCode = 0000;
      if (appInfo.code == 200) {
        PackageInfo packageInfo = await PackageInfo.fromPlatform();
        if (appInfo.item != null && appInfo.item.appTitle != null) {
          int appVersion = compareAppVersions(appInfo.item.appTitle, packageInfo.version);
          hasUpdate = appVersion > 0 ? true : false;
        }
      }
      if (hasUpdate) {
        compareVersion(context, appInfo.item.appContent);
      }
    });
  }

  /// 判断当前版本大小
  void compareVersion(BuildContext context, String content) {
    showCupertinoDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return WillPopScope(
            onWillPop: () async {
              if (_lastPressedAt == null ||
                  DateTime.now().difference(_lastPressedAt) >
                      Duration(seconds: 2)) {
                _lastPressedAt = DateTime.now();
                ToastUtil.showOtherToast("再按一次，退出");
                return Future.value(false);
              } else {
                exit(0);
              }
            },
            child: Material(
              color: Colors.transparent,
              child: Container(
                margin:
                    const EdgeInsets.symmetric(vertical: 200, horizontal: 50),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                      "lib/assets/images/bg_update_top.png",
                      fit: BoxFit.cover,
                    ),
                    Expanded(
                      child: Container(
                        width: double.maxFinite,
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(10.0),
                                bottomRight: Radius.circular(10.0))),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                                child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 15),
                              child: Text(
                                '$content',
                                // "1、优化api接口。\n 2、添加使用demo演示。\n 3、新增自定义更新服务API接口。\n 4、优化更新提示界面。",
                                style: TextStyle(
                                  fontSize: 16.0,
                                ),
                              ),
                            )),
                            GestureDetector(
                              child: Center(
                                child: Container(
                                  width: 120,
                                  height: 40,
                                  alignment: Alignment.center,
                                  margin:
                                      const EdgeInsets.symmetric(vertical: 15),
                                  decoration: BoxDecoration(
                                      color: Colors.redAccent,
                                      borderRadius:
                                          BorderRadius.circular(10.0)),
                                  child: Text(
                                    "更新",
                                    style: TextStyle(
                                        fontSize: 16.0,
                                        color: Colors.white,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ),
                              ),
                              onTap: () async {
                                if (Platform.isIOS) {
                                  String urlString =
                                      'https://apps.apple.com/cn/app/id6443843689';
                                  if (await canLaunch(urlString)) {
                                    await launch(urlString);
                                  }
                                } else if (Platform.isAndroid) {
                                  G.pop();
                                  showDownloadApkProgress(context);
                                  CheckUpdate checkUpdate = CheckUpdate();
                                  checkUpdate.check(context, false);
                                }
                              },
                            )
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return switchStatusBar2Dark(
        isSetDark: true,
        edgeInsets: EdgeInsets.all(0),
        child: Container(
          color: AppTheme.white,
          width: getWidthPx(750),
          height: getHeightPx(1334),
          child: Consumer<UserViewModel>(
            builder: (ctx, vm, child) {
              if (vm.idCard != null && vm.hasUser) {
                MqttClientMsg.instance.connect(vm.idCard);
              }
              return Scaffold(
                  body: WillPopScope(
                    onWillPop: () async {
                      if (_lastPressedAt == null ||
                          DateTime.now().difference(_lastPressedAt) >
                              Duration(seconds: 2)) {
                        _lastPressedAt = DateTime.now();
                        ToastUtil.showOtherToast("再按一次，退出");
                        return Future.value(false);
                      } else {
                        exit(0);
                      }
                    },
                    child: Container(
                        color: Colors.white, child: pages[vm.currentIndex]),
                  ),
                  bottomNavigationBar: Theme(
                      data: ThemeData(
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent),
                      child: BottomNavigationBar(
                        type: BottomNavigationBarType.fixed,
                        items: [
                          BottomNavigationBarItem(
                            icon: Image.asset('lib/assets/images/icon_首页默认@3x.png',width: 24,height: 24,),
                            activeIcon:Image.asset('lib/assets/images/icon_首页选中@3x.png',width: 24,height: 24,),
                            // ignore: deprecated_member_use
                            label: '首页',
                          ),
                          BottomNavigationBarItem(
                            icon: Image.asset('lib/assets/images/icon_搜索默认@3x.png',width: 24,height: 24,),
                            activeIcon:Image.asset('lib/assets/images/icon_搜索选中@3x.png',width: 24,height: 24,),
                            // ignore: deprecated_member_use
                            label: '查询',
                          ),
                          // BottomNavigationBarItem(
                          //   icon: communityIco(size: 22),
                          //   // ignore: deprecated_member_use
                          //   label: '消息通知',
                          // ),
                          BottomNavigationBarItem(
                            icon: Image.asset('lib/assets/images/icon_我的默认@3x.png',width: 24,height: 24,),
                            activeIcon:Image.asset('lib/assets/images/icon_我的选中@3x.png',width: 24,height: 24,),
                            label: '我的',
                          ),
                        ],
                        unselectedFontSize: 12,
                        // 未选中字体大小
                        selectedFontSize: 12,
                        // 选中字体大小
                        selectedItemColor: AppTheme.themeBlue,
                        // 选中字体颜色
                        currentIndex: vm.currentIndex,
                        onTap: (index) {
                          // if(index == 1 && !vm.hasUser){
                          //   ToastUtil.showErrorToast("请先登录！");
                          //   G.pushNamed(RoutePaths.LOGIN);
                          // }
                          vm.currentIndex = index;
                        },
                      )));
            },
          ),
        ));
  }

  @override
  bool get wantKeepAlive => true;
}
