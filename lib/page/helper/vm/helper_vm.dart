import 'dart:async';
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/helper_api.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:notarization_station_app/utils/throttle_anti-shake.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class HelpViewModel extends SingleViewStateModel {
  List shipingLists = []; //公证列表
  RefreshController refreshController;
  UserViewModel userModel;
  int pageNum = 1;
  String pageSize = "10";
  String orderNumber; //单条订单编号
  double totalMoney = 0.0; //需要付款的金额
  Timer timerPay;
  int numPay = 1;
  Timer timerPay1;
  int numPay1 = 1;

  /// 1: 自助办证  2: 视屏办证  3：多方公证 4：对公公证 5：自助存证
  int orderNum = 2;

  TextEditingController textEditingController = TextEditingController();
  String orderId = '';
  String remarks = '';
  String score = '';
  bool isBool = true;
  String evaluateContent = '';
  int value = 0;

  HelpViewModel(UserViewModel userModel) {
    this.userModel = userModel;
    refreshController = RefreshController();
  }

  //评价
  addScore() {
    if (!isBool) {
      return;
    }
    // ToastUtil.showWarningToast(" orderId:"+orderId+
    //     " remarks:"+textEditingController.text+
    //     " score:"+score);

    if (textEditingController.text.isNotEmpty) {
      isBool = false;

      Map<String, dynamic> map = {
        "orderId": orderId, //订单id
        "remarks": textEditingController.text, //备注
        "score": score, //评分
        // "updateUserId":'', //修改用户Id
      };
      HelperApi.getSingleton().getEvaluateAdd(map).then((res) {
        isBool = true;
        wjPrint('===============================');
        if (res["code"] == 200) {
          ToastUtil.showErrorToast("评价已提交");
          // G.getCurrentState().pop("refresh");
        } else if (res["code"] == 4001) {
          ToastUtil.showErrorToast(res["message"]);
        } else {
          ToastUtil.showErrorToast(res["data"]);
        }
      });
    } else {
      ToastUtil.showWarningToast("请填写评价" + textEditingController.text);
    }
  }

  /// 获取支付配置信息
  getPayConfig(String notaryId, BuildContext context, String unitGuid,
      int payLogo, double fee, String orderNo) {
    Map<String, dynamic> data = {"notaryId": notaryId};
    HomeApi.getSingleton()
        .getPayConfig(data, errorCallBack: (e) {}, checkNet: true)
        .then((value) {
      wjPrint("获取支付配置信息------$value");
      if (value['code'] == 200) {
        // isSupportOnlinePay = true;
        goPay(context, unitGuid, payLogo, fee, orderNo);
        // notifyListeners();
      } else if (value['code'] == 11047) {
        // isSupportOnlinePay = false;
        ToastUtil.showErrorToast("公证处未开通在线支付功能，请联系公证员通过线下完成支付");
        // notifyListeners();
        // EasyLoading.show();
        // Future.delayed(const Duration(seconds: 1), () {
        //   setTakeType(unitGuid);
        // });
      }
    });
  }

  goPay(BuildContext context, String unitGuid, int payLogo, double fee,
      String orderNo) {
    if (fee != null && fee > 0) {
      Navigator.pushNamed(context, RoutePaths.payWidget, arguments: {
        "unitGuid": unitGuid,
        'callBackUrl': "/index",
        'payLogo': payLogo,
        'fee': fee,
        "orderNo": orderNo
      }).then((value) {
        getShiPingList();
      });
    } else {
      setTakeType(unitGuid);
    }
  }

  setTakeType(String unitGuid){
    EasyLoading.show();
    var map = {"unitGuid": unitGuid, "notaryState": 101, "payType": "pt006"};
    HomeApi.getSingleton().setTakeType(map, errorCallBack: (r) {
      EasyLoading.dismiss();
      ToastUtil.showErrorToast(r.toString());
    }).then((value) {
      EasyLoading.dismiss();
      if (value['code'] == 200) {
        ToastUtil.showSuccessToast("支付成功");
        getShiPingList();
      } else {
        ToastUtil.showNormalToast(value['message']);
      }
    });
  }

  getShiPingList() {
    //视频公证列表
    pageNum = 1;
    shipingLists?.clear();
    EasyLoading.show();
    if (orderNum == 4) {
      if (userModel.idCard != null && userModel.idCard.isNotEmpty) {
        var map = {
          "currentPage": pageNum.toString(),
          "pageSize": pageSize,
          "idCard": userModel.idCard,
        };
        HelperApi.getSingleton().getDuiOrderList(map, errorCallBack: (e) {
          EasyLoading.dismiss();
          setBusy(false);
        }).then((data) {
          EasyLoading.dismiss();
          setBusy(false);
          wjPrint("........$data");
          if (data["code"] == 200) {
            shipingLists = data["items"];
          }
          notifyListeners();
        });
      } else {
        EasyLoading.dismiss();
        return ToastUtil.showWarningToast("请先实名认证！");
      }
    } else if(orderNum ==3){
      var map = {
        "currentPage": pageNum.toString(),
        "pageSize": pageSize,
        //"userId": userModel.unitGuid,
        "notaryForm": orderNum
      };
      wjPrint(".......$map");
      HelperApi.getSingleton().getOneListNotarizationData(map,
          errorCallBack: (e) {
        setBusy(false);
        EasyLoading.dismiss();
      }).then((data) {
        setBusy(false);
        EasyLoading.dismiss();
        if (data["code"] == 200) {
          shipingLists = data["items"];
        }
        notifyListeners();
      });
    }else{
      var mapShiPin = {
        "currentPage": pageNum.toString(),
        "pageSize": pageSize,
        //"userId": userModel.unitGuid,
        "notaryForm": orderNum
      };
      HelperApi.getSingleton().queryPageData(mapShiPin,
          errorCallBack: (e) {
            EasyLoading.dismiss();
            pageNum -= 1;
          }).then((data) {
        EasyLoading.dismiss();
        if(data!=null){
          if(data['code']==200){
            if(data['data']!=null&&data['data']['appNotaryOrderVoList']!=null){
              data['data']["appNotaryOrderVoList"].forEach((i) {
                shipingLists.add(i);
              });
              notifyListeners();
            }else{
              ToastUtil.showToast("暂无查询到数据");
            }
          }else{
            ToastUtil.showErrorToast(data['message']??data['data']??data['msg']);
          }

        }else{
          ToastUtil.showErrorToast("网络出错了，请稍后再试");
        }

      });
    }
  }

  Future loadData() async {
    setBusy(true);
  }

  refresh() async {
    pageNum = 1;
    EasyLoading.show();
    shipingLists.clear();
    refreshController.resetNoData();
    wjPrint("+++++refresh(++++++++++++++$orderNum");
    if (orderNum == 4) {
      if (userModel.idCard != null && userModel.idCard.isNotEmpty) {
        var map = {
          "currentPage": pageNum.toString(),
          "pageSize": pageSize,
          "idCard": userModel.idCard,
        };
        HelperApi.getSingleton().getDuiOrderList(map, errorCallBack: (e) {
          EasyLoading.dismiss();
          setBusy(false);
          refreshController.refreshFailed();
        }).then((data) {
          EasyLoading.dismiss();
          if (data["items"] != null) {
            shipingLists = data["items"];
          }
          refreshController.refreshCompleted();
          notifyListeners();
        }).whenComplete(() {
          refreshController.refreshCompleted();
          EasyLoading.dismiss();
          setBusy(false);
        });
      } else {
        EasyLoading.dismiss();
        return ToastUtil.showWarningToast("请先实名认证！");
      }
    } else if(orderNum ==3){
      var map1 = {
        "currentPage": pageNum.toString(),
        "pageSize": pageSize,
        //"userId":userModel.unitGuid,
        "notaryForm": orderNum
      };
      log("++++map----$map1");
      HelperApi.getSingleton().getOneListNotarizationData(map1,
          errorCallBack: (e) {
        setBusy(false);
        refreshController.refreshFailed();
        EasyLoading.dismiss();
      }).then((data) {
        EasyLoading.dismiss();
        shipingLists = data["items"];
        refreshController.refreshCompleted();
        notifyListeners();
      }).whenComplete(() {
        refreshController.refreshCompleted();
        EasyLoading.dismiss();
        setBusy(false);
      });
    }else{
      var mapShiPin = {
        "currentPage": pageNum.toString(),
        "pageSize": pageSize,
        //"userId": userModel.unitGuid,
        "notaryForm": orderNum
      };
      HelperApi.getSingleton().queryPageData(mapShiPin,
          errorCallBack: (e) {
            EasyLoading.dismiss();
            refreshController.refreshFailed();
            pageNum -= 1;
          }).then((data) {
        EasyLoading.dismiss();
        if(data!=null){
          if(data['code']==200){
            if(data['data']!=null&&data['data']['appNotaryOrderVoList']!=null){
              data['data']["appNotaryOrderVoList"].forEach((i) {
                shipingLists.add(i);
              });
              refreshController.refreshCompleted();
              notifyListeners();
            }else{
              ToastUtil.showToast("暂无查询到数据");
            }
          }else{
            ToastUtil.showErrorToast(data['message']??data['data']??data['msg']);
          }

        }else{
          ToastUtil.showErrorToast("网络出错了，请稍后再试");
        }
      });
    }
  }

  loadMore() async {
    pageNum += 1;

    if (orderNum == 4) {
      wjPrint("+++++refresh++++++++++++++$orderNum");
      if (userModel.idCard != null && userModel.idCard.isNotEmpty) {
        var map = {
          "currentPage": pageNum.toString(),
          "pageSize": pageSize,
          "idCard": userModel.idCard,
        };
        HelperApi.getSingleton().getDuiOrderList(map, errorCallBack: (e) {
          EasyLoading.dismiss();
          refreshController.loadFailed();
          pageNum -= 1;
        }).then((data) {
          EasyLoading.dismiss();
          data["items"].forEach((i) {
            shipingLists.add(i);
          });
          if (data["items"].isEmpty) {
            refreshController.loadNoData();
          } else {
            refreshController.loadComplete();
            notifyListeners();
          }
        });
      } else {
        EasyLoading.dismiss();
        return ToastUtil.showWarningToast("请先实名认证！");
      }
    } else if(orderNum ==3){
      var mapShiPin = {
        "currentPage": pageNum.toString(),
        "pageSize": pageSize,
        //"userId": userModel.unitGuid,
        "notaryForm": orderNum
      };
      HelperApi.getSingleton().getOneListNotarizationData(mapShiPin,
          errorCallBack: (e) {
        EasyLoading.dismiss();
        refreshController.loadFailed();
        pageNum -= 1;
      }).then((data) {
        EasyLoading.dismiss();
        data["items"].forEach((i) {
          shipingLists.add(i);
        });
        if (data["items"].isEmpty) {
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
          notifyListeners();
        }
      });
    }else{
      var mapShiPin = {
        "currentPage": pageNum.toString(),
        "pageSize": pageSize,
        //"userId": userModel.unitGuid,
        "notaryForm": orderNum
      };
      HelperApi.getSingleton().queryPageData(mapShiPin,
          errorCallBack: (e) {
            EasyLoading.dismiss();
            refreshController.loadFailed();
            pageNum -= 1;
          }).then((data) {
        EasyLoading.dismiss();
        if(data!=null){
          if(data['code']==200){
            if(data['data']!=null&&data['data']['appNotaryOrderVoList']!=null){
              data['data']["appNotaryOrderVoList"].forEach((i) {
                shipingLists.add(i);
              });
              if (data['data']["appNotaryOrderVoList"].isEmpty) {
                refreshController.loadNoData();
              } else {
                refreshController.loadComplete();
                notifyListeners();
              }
            }else{
              ToastUtil.showToast("暂无查询到数据");
            }
          }else{
            ToastUtil.showErrorToast(data['message']??data['data']??data['msg']);
          }

        }else{
          ToastUtil.showErrorToast("网络出错了，请稍后再试");
        }
      });
    }
  }

  // 评价弹框
  showCommentDialog(BuildContext context, String unitGuid,String evaluateContent, String grade) {
    textEditingController.text = evaluateContent ?? '';
    value = grade == null ? 0 : int.parse(grade);
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return MediaQuery.removePadding(
            removeBottom: true,
            context: context,
            child: StatefulBuilder(
              builder: (context, myState) {
                return Column(
                  children: [
                    const Spacer(),
                    Container(
                      height: 360,
                      padding: EdgeInsets.symmetric(horizontal: 15),
                      margin: EdgeInsets.symmetric(horizontal: 30),
                      decoration: BoxDecoration(
                          color: AppTheme.white,
                          borderRadius: BorderRadius.all(Radius.circular(10))),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Padding(
                                padding: EdgeInsets.only(top: 20, left: 20),
                                child: RichText(
                                    text: TextSpan(children: [
                                  TextSpan(
                                      text: "*",
                                      style: TextStyle(
                                          fontSize: 12, color: Colors.red)),
                                  TextSpan(
                                      text: " 服务评价",
                                      style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black))
                                ])),
                              ),
                              const Spacer(),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 20),
                            child: Container(
                              child: Column(
                                children: [
                                  Container(
                                    width: MediaQuery.of(context).size.width,
                                    height: 50,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        SizedBox(
                                          width: 20,
                                        ),
                                        Expanded(
                                          child: Center(
                                            child: ListView.builder(
                                                scrollDirection:
                                                    Axis.horizontal,
                                                shrinkWrap: true,
                                                itemCount: 5,
                                                itemBuilder: (ctx, a) {
                                                  return InkWell(
                                                    onTap: () {
                                                      myState(() {
                                                        value = a + 1;
                                                      });
                                                    },
                                                    child: a < value
                                                        ? Icon(
                                                            Icons.star,
                                                            size: 30,
                                                            color: AppTheme
                                                                .themeBlue,
                                                          )
                                                        : Icon(
                                                            Icons.star_border,
                                                            size: 30,
                                                            color:
                                                                AppTheme.bg_f,
                                                          ),
                                                  );
                                                }),
                                          ),
                                        ),
                                        SizedBox(
                                          width: 20,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        "评价内容(不超过200个字)",
                                        style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.black,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const Spacer(),
                                    ],
                                  ),
                                  Container(
                                    margin: const EdgeInsets.symmetric(
                                        vertical: 10),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10),
                                    height: 150,
                                    decoration: new BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(4.0)),
                                      border: new Border.all(
                                          width: 1, color: AppTheme.Text_min),
                                    ),
                                    child: Scrollbar(
                                      child: TextField(
                                        decoration: InputDecoration(
                                          hintText: "",
                                          border: InputBorder.none,
                                        ),
                                        style: TextStyle(
                                            fontSize: 16.0,
                                            color: Color(0xff606266)),
                                        controller: textEditingController,
                                        // cursorColor: Color(0xff00c295),
                                        scrollPadding: EdgeInsets.only(
                                            top: 0.0, bottom: 6.0),
                                        inputFormatters: [
                                          FilteringTextInputFormatter.deny(RegExp(
                                              "[^\\u0020-\\u007E\\u00A0-\\u00BE\\u2E80-\\uA4CF\\uF900-\\uFAFF\\uFE30-\\uFE4F\\uFF00-\\uFFEF\\u0080-\\u009F\\u2000-\\u201f\r\n]")),
                                          LengthLimitingTextInputFormatter(200)
                                        ],
                                        maxLines: 20,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                const Spacer(),
                                InkWell(
                                  onTap: () {
                                    G.pop();
                                  },
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 30, vertical: 8),
                                    child: Text(
                                      '取消',
                                      style: TextStyle(
                                          fontSize: 15,
                                          color: AppTheme.themeBlue),
                                    ),
                                    decoration: ShapeDecoration(
                                        color: AppTheme.white,
                                        shape: StadiumBorder(
                                            side: BorderSide(
                                                color: AppTheme.themeBlue,
                                                width: 1.0))),
                                  ),
                                ),
                                SizedBox(
                                  width: 20,
                                ),
                                InkWell(
                                  onTap: debounce(() {
                                    if(value == 0){
                                      ToastUtil.showWarningToast("请选择服务评价等级");
                                      return;
                                    }
                                    EasyLoading.show();
                                    HelperApi.getSingleton().evaluateOrder({
                                      "unitGuid": unitGuid,
                                      "evaluateContent":
                                      textEditingController.text,
                                      "grade": value
                                    }, errorCallBack: (e) {
                                      EasyLoading.dismiss();
                                      ToastUtil.showErrorToast("评价发布失败，稍后再试");
                                    }).then((value) {
                                      EasyLoading.dismiss();
                                      if (value != null) {
                                        if (value['code'] == 200) {
                                          G.pop();
                                          refresh();
                                          ToastUtil.showSuccessToast("评价发布成功");
                                        } else {
                                          ToastUtil.showErrorToast(
                                              value['message']);
                                        }
                                      } else {
                                        ToastUtil.showErrorToast("评价发布失败，稍后再试");
                                      }
                                    });
                                  }),
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 30, vertical: 8),
                                    child: Text(
                                      '提交',
                                      style: TextStyle(
                                          fontSize: 15, color: AppTheme.white),
                                    ),
                                    decoration: ShapeDecoration(
                                        color: AppTheme.themeBlue,
                                        shape: StadiumBorder(
                                            side: BorderSide(
                                                color: AppTheme.themeBlue,
                                                width: 1.0))),
                                  ),
                                ),
                                const Spacer(),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer()
                  ],
                );
              },
            ),
          );
        });
  }

  @override
  onCompleted(data) {
    if (data.items == null || data.items.length == 0) {
      setEmpty();
    } else {
      data["items"].forEach((i) {
        shipingLists.add(i);
      });
    }
  }
}
