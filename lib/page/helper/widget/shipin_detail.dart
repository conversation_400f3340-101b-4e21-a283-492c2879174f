import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/widget/photo_view/fade_route.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/components/photo_view_gallery.dart';
import 'package:notarization_station_app/page/helper/vm/helper_vm.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/helper_api.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:provider/provider.dart';

import '../../../config.dart';

class ShiPinDetailPage extends StatefulWidget {
  final arguments;
  const ShiPinDetailPage({Key key, this.arguments}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ShiPinDetailState();
  }
}

class ShiPinDetailState extends BaseState<ShiPinDetailPage>
    with AutomaticKeepAliveClientMixin {
  Map oneData = {};
  List materialLists = [];
  List orderLogs = [];
  List imgLists = [];
  List addPayImgLists = [];
  List applicantList = [];
  List payInfor = [];
  double supplementFee = 0.0;
  bool isInit = false;
  bool isInput = true;
  int score = 0;
  String remarks = '';
  @override
  void initState() {
    super.initState();
    getData();
    getById();
  }

  getData() async {
    orderLogs.clear();
    setState(() {});
    var map = {
      "unitGuid": widget.arguments["data"]["unitGuid"],
    };
    HelperApi.getSingleton()
        .getOneNotarizationData(map, errorCallBack: (e) {})
        .then((value) {
      if (value["code"] == 200) {
        oneData = value["item"];
        materialLists = oneData["order"]["materUrlList"];
        payInfor = oneData['payInfo'];
        applicantList = oneData["applyuser"];
        addPayImgLists = oneData["orderSupplements"];
        oneData["orderLogs"].forEach((item) {
          orderLogs.add(item);
        });
        if (oneData["orderSupplements"] != null) {
          oneData["orderSupplements"].forEach((item) {
            supplementFee += item["supplementFee"];
          });
        }
        isInit = true;
        setState(() {});
      }
    });
  }

  //公证评分详情
  getById() async {
    var map = {
      "orderId": widget.arguments["data"]["unitGuid"],
    };
    HelperApi.getSingleton().getById(map, errorCallBack: (e) {}).then((value) {
      // wjPrint('=====================================value========================================= '+value.toString());
      // wjPrint('======value["code"]====== '+value["code"].toString());
      // wjPrint('======value["data"]["score"]====== '+value["data"]["score"].toString());
      // wjPrint('======value["data"]["remarks"]====== '+value["data"]["remarks"].toString());
      if (value["data"] != null) {
        // wjPrint('获取评价详情');
        isInput = false;
        score = int.parse(value["data"]["score"]);
        remarks = value["data"]["remarks"];
        setState(() {});
      } else {
        // wjPrint('添加评价详情');
        isInput = true;
        setState(() {});
      }
    });
  }

  HelpViewModel quizVm;
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      child: Scaffold(
        appBar: commonAppBar(title: "订单详情"),
        body: Consumer<UserViewModel>(
          builder: (ctx, userModel, child) {
            return ProviderWidget<HelpViewModel>(
                model: HelpViewModel(userModel),
                onModelReady: (model) {
                  quizVm = model;
                },
                builder: (ctx, vm, child) {
                  return isInit
                      ? SingleChildScrollView(
                          child: Column(
                            children: <Widget>[
                              Container(
                                margin: EdgeInsets.only(top: getHeightPx(20)),
                                width: getWidthPx(750),
                                decoration: BoxDecoration(color: Colors.white),
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                  itemCount: orderLogs.length,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    var item = orderLogs[index];
                                    return Row(
                                      children: <Widget>[
                                        Container(
                                          margin: EdgeInsets.only(
                                            left: getWidthPx(30),
                                          ),
                                          width: getWidthPx(180),
                                          child: Text(
                                            item["createDate"]??'',
                                            style: TextStyle(
                                                fontSize: 12,
                                                color: index == 0
                                                    ? Colors.black
                                                    : Colors.black45),
                                          ),
                                        ),
                                        Container(
                                          width: getWidthPx(25),
                                          alignment: Alignment.center,
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            mainAxisSize: MainAxisSize.min,
                                            children: <Widget>[
                                              index == 0
                                                  ? Image.asset(
                                                      "lib/assets/images/stepImg.png",
                                                      width: getWidthPx(25),
                                                    )
                                                  : Container(
                                                      width: getWidthPx(10),
                                                      height: getHeightPx(10),
                                                      decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(10),
                                                          color:
                                                              Colors.black12),
                                                    ),
                                              Container(
                                                width: getWidthPx(2),
                                                height: getHeightPx(88),
                                                decoration: BoxDecoration(
                                                    border: Border.all(
                                                        width: 1,
                                                        color: Colors.black12)),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Expanded(
                                          child: Column(
                                            children: <Widget>[
                                              Container(
                                                margin: EdgeInsets.only(
                                                    left: getWidthPx(30),
                                                    top: getWidthPx(20)),
                                                child: Text(
                                                  item["notaryStateName"]??'',
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      color: index == 0
                                                          ? Colors.black
                                                          : Colors.black45,
                                                      fontWeight:
                                                          FontWeight.w700),
                                                ),
                                              ),
                                              Container(
                                                margin: EdgeInsets.only(
                                                    left: getWidthPx(30),
                                                    top: getWidthPx(5)),
                                                child: Text(
                                                  item["reason"]??'',
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      color: index == 0
                                                          ? Colors.black
                                                          : Colors.black45,
                                                      fontWeight:
                                                          FontWeight.w700),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                              SizedBox(
                                height: getHeightPx(20),
                              ),
                              Container(
                                width: getWidthPx(750),
                                decoration: BoxDecoration(color: Colors.white),
                                child: Column(
                                  children: <Widget>[
                                    Container(
                                      margin: EdgeInsets.only(
                                        left: getWidthPx(40),
                                        top: getWidthPx(30),
                                      ),
                                      child: Row(
                                        children: <Widget>[
                                          Text(
                                            "基本信息",
                                            style: TextStyle(
                                                fontWeight: FontWeight.w700),
                                          )
                                        ],
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: getWidthPx(40),
                                          top: getWidthPx(15),
                                          right: getWidthPx(40)),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: <Widget>[
                                          Container(
                                            child: Text(
                                              "申请号",
                                              style: TextStyle(
                                                  color: Colors.black45),
                                            ),
                                          ),
                                          Text(
                                              "${oneData["order"]["orderNo"] ?? ""}",
                                              style: TextStyle(
                                                  color: Colors.black))
                                        ],
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: getWidthPx(40),
                                          top: getWidthPx(15),
                                          right: getWidthPx(40)),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: <Widget>[
                                          Container(
                                            child: Text(
                                              "服务机构",
                                              style: TextStyle(
                                                  color: Colors.black45),
                                            ),
                                          ),
                                          Text(oneData["order"]["notaryName"]??'',
                                              style: TextStyle(
                                                  color: Colors.black))
                                        ],
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: getWidthPx(40),
                                          top: getWidthPx(15),
                                          right: getWidthPx(40)),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: <Widget>[
                                          Text(
                                            "公证事项",
                                            style: TextStyle(
                                                color: Colors.black45),
                                          ),
                                          const SizedBox(width: 5,),
                                          Expanded(
                                            child: Text(
                                              oneData["order"]
                                                  ["notaryItemNames"]??'',
                                              style: TextStyle(
                                                  color: Colors.black),
                                              textAlign: TextAlign.right,
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: getWidthPx(40),
                                          top: getWidthPx(15),
                                          right: getWidthPx(40)),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: <Widget>[
                                          Container(
                                            child: Text(
                                              "申请时间",
                                              style: TextStyle(
                                                  color: Colors.black45),
                                            ),
                                          ),
                                          Text(oneData["order"]["createDate"]??'',
                                              style: TextStyle(
                                                  color: Colors.black))
                                        ],
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: getWidthPx(40),
                                          top: getWidthPx(15),
                                          bottom: getWidthPx(15),
                                          right: getWidthPx(40)),
                                      child: Column(
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              Container(
                                                child: Text(
                                                  "订单状态",
                                                  style: TextStyle(
                                                      color: Colors.black45),
                                                ),
                                              ),
                                              Text(
                                                  oneData["order"]
                                                      ["notaryStateName"]??'',
                                                  style: TextStyle(
                                                      color: Colors.black))
                                            ],
                                          ),
                                          Offstage(
                                            offstage: oneData["order"]
                                                        ["stopReason"] ==
                                                    null ||
                                                oneData["order"]["stopReason"]
                                                    .isEmpty,
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 10),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Text("终止原因",
                                                          style: TextStyle(
                                                              color: Colors
                                                                  .black45)),
                                                      const Spacer(),
                                                    ],
                                                  ),
                                                  SizedBox(
                                                    height: 10,
                                                  ),
                                                  Row(
                                                    children: [
                                                      SizedBox(
                                                        width: 75,
                                                      ),
                                                      Expanded(
                                                          child: Text(oneData["order"]
                                                                          [
                                                                          "stopReason"] ==
                                                                      null ||
                                                                  oneData["order"]
                                                                          [
                                                                          "stopReason"]
                                                                      .isEmpty
                                                              ? ""
                                                              : oneData["order"]
                                                                  [
                                                                  "stopReason"])),
                                                    ],
                                                  )
                                                ],
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: getHeightPx(20),
                              ),
                              Container(
                                color: Colors.white,
                                padding: EdgeInsets.only(
                                    left: getWidthPx(40), top: getWidthPx(10)),
                                child: Row(
                                  children: <Widget>[
                                    Text(
                                      "申请人信息",
                                      style: TextStyle(
                                          fontWeight: FontWeight.w700),
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                color: Colors.white,
                                child: ListView.builder(
                                    shrinkWrap: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    itemCount: applicantList.length,
                                    itemBuilder: (ctx, a) {
                                      return Column(
                                        children: <Widget>[
                                          Container(
                                            margin: EdgeInsets.only(
                                                left: getWidthPx(40),
                                                top: getWidthPx(15),
                                                right: getWidthPx(40)),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: <Widget>[
                                                Text(
                                                  "姓名",
                                                  style: TextStyle(
                                                      color: Colors.black45),
                                                ),
                                                Text(applicantList[a]["name"]??"",
                                                    style: TextStyle(
                                                        color: Colors.black))
                                              ],
                                            ),
                                          ),
                                          Container(
                                            margin: EdgeInsets.only(
                                                left: getWidthPx(40),
                                                top: getWidthPx(15),
                                                right: getWidthPx(40)),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: <Widget>[
                                                Text(
                                                  "身份证号",
                                                  style: TextStyle(
                                                      color: Colors.black45),
                                                ),
                                                Text(applicantList[a]["idCard"]??'',
                                                    style: TextStyle(
                                                        color: Colors.black))
                                              ],
                                            ),
                                          ),
                                          Container(
                                            margin: EdgeInsets.only(
                                                left: getWidthPx(40),
                                                top: getWidthPx(15),
                                                right: getWidthPx(40)),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: <Widget>[
                                                Text(
                                                  "电话",
                                                  style: TextStyle(
                                                      color: Colors.black45),
                                                ),
                                                Text(
                                                    "${applicantList[a]["mobile"] ?? ""}",
                                                    style: TextStyle(
                                                        color: Colors.black))
                                              ],
                                            ),
                                          ),
                                          applicantList[a]["principal"] == null
                                              ? Container(
                                                  margin: EdgeInsets.only(
                                                    left: getWidthPx(40),
                                                    top: getWidthPx(15),
                                                    right: getWidthPx(40),
                                                    bottom: getWidthPx(15),
                                                  ),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: <Widget>[
                                                      Text(
                                                        "地址",
                                                        style: TextStyle(
                                                            color:
                                                                Colors.black45),
                                                      ),
                                                      SizedBox(
                                                        width: getWidthPx(20),
                                                      ),
                                                      Expanded(
                                                        child: Text(
                                                          applicantList[a][
                                                                      "address"] ==
                                                                  null
                                                              ? ""
                                                              : applicantList[a]
                                                                  ["address"],
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.black),
                                                          textAlign:
                                                              TextAlign.right,
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                )
                                              : SizedBox(),
                                          applicantList[a]["principal"] != null
                                              ? Container(
                                                  margin: EdgeInsets.only(
                                                    left: getWidthPx(40),
                                                    top: getWidthPx(15),
                                                    right: getWidthPx(40),
                                                    bottom: getWidthPx(15),
                                                  ),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: <Widget>[
                                                      Text(
                                                        "代理人",
                                                        style: TextStyle(
                                                            color:
                                                                Colors.black45),
                                                      ),
                                                      SizedBox(
                                                        width: getWidthPx(20),
                                                      ),
                                                      Expanded(
                                                        child: Text(
                                                          applicantList[a]
                                                              ["principal"]??"",
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.black),
                                                          textAlign:
                                                              TextAlign.right,
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                )
                                              : SizedBox(),
                                          Container(
                                            height: getHeightPx(20),
                                            color: AppTheme.bg_d,
                                          ),
                                        ],
                                      );
                                    }),
                              ),
                              Container(
                                height: getHeightPx(20),
                                color: AppTheme.bg_d,
                              ),
                              Container(
                                width: getWidthPx(750),
                                color: Colors.white,
                                child: Column(
                                  children: <Widget>[
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: getWidthPx(40),
                                          top: getWidthPx(30)),
                                      child: Row(
                                        children: <Widget>[
                                          Text(
                                            "公证费用",
                                            style: TextStyle(
                                                fontWeight: FontWeight.w700),
                                          )
                                        ],
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: getWidthPx(40),
                                          top: getWidthPx(15),
                                          bottom: getWidthPx(15),
                                          right: getWidthPx(40)),
                                      child: Column(
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              Text(
                                                "总费用",
                                                style: TextStyle(
                                                    color: Colors.black45),
                                              ),
                                              Text(
                                                  oneData["order"]["fee"] !=
                                                          null
                                                      ? "￥${oneData["order"]["fee"]}元"
                                                      : "￥0.0元",
                                                  style: TextStyle(
                                                      color: Colors.black))
                                            ],
                                          ),
                                          Padding(
                                            padding: EdgeInsets.symmetric(
                                                vertical: 10),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: <Widget>[
                                                Text(
                                                  "支付状态",
                                                  style: TextStyle(
                                                      color: Colors.black45),
                                                ),
                                                Text(
                                                    payInfor == null ||
                                                            payInfor.isEmpty
                                                        ? "线下支付"
                                                        : "线上支付",
                                                    style: TextStyle(
                                                        color: Colors.black))
                                              ],
                                            ),
                                          ),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              Text(
                                                "订单金额",
                                                style: TextStyle(
                                                    color: Colors.black45),
                                              ),
                                              Text(
                                                  oneData["order"]["itemFee"] !=
                                                          null
                                                      ? "￥${oneData["order"]["itemFee"]}元"
                                                      : "￥0.0元",
                                                  style: TextStyle(
                                                      color: Colors.black))
                                            ],
                                          ),
                                          Offstage(
                                            offstage: !(payInfor != null &&
                                                payInfor.isNotEmpty),
                                            child: Container(
                                              color: Colors.white,
                                              margin: EdgeInsets.only(
                                                  bottom: getWidthPx(20)),
                                              padding: EdgeInsets.only(
                                                  bottom: getHeightPx(20)),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            vertical:
                                                                getWidthPx(20)),
                                                    child: Text(
                                                      "支付信息",
                                                      style: TextStyle(
                                                          fontWeight:
                                                              FontWeight.w700),
                                                    ),
                                                  ),
                                                  ListView.builder(
                                                      shrinkWrap: true,
                                                      physics:
                                                          NeverScrollableScrollPhysics(),
                                                      itemCount:
                                                          payInfor.length,
                                                      itemBuilder:
                                                          (context, index) {
                                                        return Column(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              children: [
                                                                Text("支付时间",
                                                                    style: TextStyle(
                                                                        color: Colors
                                                                            .black45)),
                                                                Text(payInfor[0]
                                                                        [
                                                                        'payDate'] ??
                                                                    ''),
                                                              ],
                                                            ),
                                                            SizedBox(
                                                              height:
                                                                  getWidthPx(
                                                                      20),
                                                            ),
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              children: [
                                                                Text("流水号",
                                                                    style: TextStyle(
                                                                        color: Colors
                                                                            .black45)),
                                                                Text(payInfor[0]
                                                                        [
                                                                        'payOrderNo'] ??
                                                                    ''),
                                                              ],
                                                            ),
                                                            // 'payType' 支付类型 1、通常支付 2、数币支付'，
                                                            SizedBox(
                                                              height:
                                                                  getWidthPx(
                                                                      20),
                                                            ),

                                                            Container(
                                                              width: double
                                                                  .infinity,
                                                              child: Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  Text("支付渠道",
                                                                      style: TextStyle(
                                                                          color:
                                                                              Colors.black45)),
                                                                  Text(payInfor[0]
                                                                              [
                                                                              'payType'] ==
                                                                          1
                                                                      ? '通常支付'
                                                                      : "数币支付"),
                                                                ],
                                                              ),
                                                            ),
                                                          ],
                                                        );
                                                      }),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              SizedBox(
                                height: getHeightPx(20),
                              ),
                              Container(
                                width: getWidthPx(750),
                                decoration: BoxDecoration(color: Colors.white),
                                child: Column(
                                  children: <Widget>[
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: getWidthPx(40),
                                          top: getWidthPx(30)),
                                      child: Row(
                                        children: <Widget>[
                                          Text(
                                            "材料",
                                            style: TextStyle(
                                                fontWeight: FontWeight.w700),
                                          )
                                        ],
                                      ),
                                    ),
                                    materialLists != null &&
                                            materialLists.isNotEmpty
                                        ? Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: GridView.builder(
                                                shrinkWrap: true,
                                                physics:
                                                    NeverScrollableScrollPhysics(),
                                                gridDelegate:
                                                    SliverGridDelegateWithFixedCrossAxisCount(
                                                  crossAxisCount: 3, //每行三列
                                                  childAspectRatio:
                                                      1.0, //显示区域宽高相等
                                                  mainAxisSpacing: 8,
                                                  crossAxisSpacing: 8,
                                                ),
                                                itemCount: materialLists.length,
                                                itemBuilder: (context, index) {
                                                  return InkWell(
                                                    onTap: () {
                                                      List imgArr = [];
                                                      materialLists
                                                          .forEach((element) {
                                                        imgArr.add(Config
                                                            .splicingImageUrl(
                                                                element));
                                                      });
                                                      Navigator.of(context)
                                                          .push(new FadeRoute(
                                                              page:
                                                                  PhotoViewGalleryScreen(
                                                                      images:
                                                                          imgArr, //传入图片list
                                                                      index:
                                                                          index, //传入当前点击的图片的index
                                                                      heroTag:
                                                                          "1")));
                                                    },
                                                    child: FadeInImage
                                                        .assetNetwork(
                                                      imageCacheWidth: 800,
                                                      imageCacheHeight: 800,
                                                      placeholder:
                                                          'lib/assets/images/empty.png',
                                                      image: Config
                                                          .splicingImageUrl(
                                                              materialLists[
                                                                      index]
                                                                  .toString()),
                                                    ),
                                                  );
                                                }),
                                          )
                                        : Container(
                                            margin: EdgeInsets.only(
                                                left: getWidthPx(40),
                                                top: getWidthPx(30)),
                                            child: Row(
                                              children: <Widget>[
                                                Text("暂无数据"),
                                              ],
                                            ),
                                          ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: getWidthPx(40),
                                          top: getWidthPx(30)),
                                      child: Row(
                                        children: <Widget>[
                                          Text(
                                            "费用补缴",
                                            style: TextStyle(
                                                fontWeight: FontWeight.w700),
                                          )
                                        ],
                                      ),
                                    ),
                                    addPayImgLists != null &&
                                            addPayImgLists[0]
                                                    ["imagePathBack"] !=
                                                null &&
                                            addPayImgLists[0]["imagePathBack"]
                                                .isNotEmpty
                                        ? Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: GridView.builder(
                                                shrinkWrap: true,
                                                physics:
                                                    NeverScrollableScrollPhysics(),
                                                gridDelegate:
                                                    SliverGridDelegateWithFixedCrossAxisCount(
                                                  crossAxisCount: 3, //每行三列
                                                  childAspectRatio:
                                                      1.0, //显示区域宽高相等
                                                  mainAxisSpacing: 8,
                                                  crossAxisSpacing: 8,
                                                ),
                                                itemCount: addPayImgLists[0]
                                                        ["imagePathBack"]
                                                    .length,
                                                itemBuilder: (context, index) {
                                                  return InkWell(
                                                    onTap: () {
                                                      Navigator.of(context)
                                                          .push(new FadeRoute(
                                                              page:
                                                                  PhotoViewGalleryScreen(
                                                                      images: addPayImgLists[
                                                                              0]
                                                                          [
                                                                          "imagePathBack"], //传入图片list
                                                                      index:
                                                                          index, //传入当前点击的图片的index
                                                                      heroTag:
                                                                          "1")));
                                                    },
                                                    child: FadeInImage
                                                        .assetNetwork(
                                                      imageCacheWidth: 800,
                                                      imageCacheHeight: 800,
                                                      placeholder:
                                                          'lib/assets/images/empty.png',
                                                      image: Config
                                                          .splicingImageUrl(
                                                              addPayImgLists[0][
                                                                      "imagePathBack"]
                                                                  [index]),
                                                    ),
                                                  );
                                                }),
                                          )
                                        : Container(
                                            margin: EdgeInsets.only(
                                                left: getWidthPx(40),
                                                top: getWidthPx(30),
                                                bottom: getWidthPx(30)),
                                            child: Row(
                                              children: <Widget>[
                                                Text("暂无数据"),
                                              ],
                                            ),
                                          ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: getHeightPx(20),
                              ),
                            ],
                          ),
                        )
                      : loadingWidget();
                });
          },
        ),
      ),
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
    );
  }

  /// 获取支付配置信息
  getPayConfig(String notaryId, BuildContext context, String unitGuid,
      double fee, String orderNo) {
    Map<String, dynamic> data = {"notaryId": notaryId};
    HomeApi.getSingleton()
        .getPayConfig(data, errorCallBack: (e) {}, checkNet: true)
        .then((value) {
      wjPrint("获取支付配置信息------$value");
      if (value['code'] == 200) {
        // isSupportOnlinePay = true;
        goPay(context);
        // notifyListeners();
      } else if (value['code'] == 11047) {
        // isSupportOnlinePay = false;
        ToastUtil.showErrorToast("公证处未开通在线支付功能，请联系公证员通过线下完成支付");
        // notifyListeners();
        // EasyLoading.show();
        Future.delayed(const Duration(seconds: 1), () {
          var map = {
            "unitGuid": unitGuid,
            "notaryState": 101,
            "payType": "pt006"
          };
          HomeApi.getSingleton().setTakeType(map, errorCallBack: (r) {
            // EasyLoading.dismiss();
            ToastUtil.showErrorToast(r.toString());
          }).then((value) {
            if (value['code'] == 200) {
              getData();
            } else {
              ToastUtil.showNormalToast(value['message']);
            }
          });
        });
      }
    });
  }

  goPay(BuildContext context) {
    if (oneData["order"] != null &&
        oneData["order"]["fee"] != null &&
        oneData["order"]["fee"] > 0) {
      int payLogo = 0;
      if (oneData['order']['notaryStateName'] == '待支付') {
        payLogo = 1;
      } else if (oneData['order']['notaryStateName'] == '待补缴费用') {
        payLogo = 2;
      }
      Navigator.pushNamed(context, RoutePaths.payWidget, arguments: {
        "unitGuid": oneData['order']['unitGuid'],
        'callBackUrl': "/index",
        'payLogo': payLogo,
        'fee': oneData['order']['fee'],
        "orderNo": oneData['order']['orderNo']
      });
    } else {
      EasyLoading.show();
      var map = {
        "unitGuid": oneData['order']['unitGuid'],
        "notaryState": 101,
        "payType": "pt006"
      };
      HomeApi.getSingleton().setTakeType(map, errorCallBack: (r) {
        EasyLoading.dismiss();
        ToastUtil.showErrorToast(r.toString());
      }).then((value) {
        EasyLoading.dismiss();
        if (value['code'] == 200) {
          ToastUtil.showSuccessToast("支付成功");
          getData();
        } else {
          ToastUtil.showErrorToast(value['message']);
        }
      });
    }
  }

  @override
  bool get wantKeepAlive => false;
}
