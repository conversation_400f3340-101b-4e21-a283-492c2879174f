import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/widget/photo_view/fade_route.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/components/photo_view_gallery.dart';
import 'package:notarization_station_app/components/star/span_rating_widget.dart';
import 'package:notarization_station_app/page/helper/vm/helper_vm.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/helper_api.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:provider/provider.dart';

class DuiGongDetailPage extends StatefulWidget {
  final arguments;
  const DuiGongDetailPage({Key key, this.arguments}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return DuiGongDetailState();
  }
}

class DuiGongDetailState extends BaseState<DuiGongDetailPage>
    with AutomaticKeepAliveClientMixin {
  bool isInput = true;
  int score = 0;
  String remarks = '';

  @override
  void initState() {
    super.initState();
    getData();
    getById();
  }

  Map orderInfo = {};
  getData() async {
    var map = {
      "unitGuid": widget.arguments["data"]["unitGuid"],
    };
    HelperApi.getSingleton()
        .getDuiOrder(map, errorCallBack: (e) {})
        .then((value) {
      wjPrint("33333333333333$value");
      if (value["code"] == 200) {
        orderInfo = value["item"];
        setState(() {});
      }
    });
  }

  //公证评分详情
  getById() async {
    var map = {
      "orderId": widget.arguments["data"]["unitGuid"],
    };
    HelperApi.getSingleton().getById(map, errorCallBack: (e) {}).then((value) {
      // wjPrint('=====================================value========================================= '+value.toString());
      // wjPrint('======value["code"]====== '+value["code"].toString());
      // wjPrint('======value["data"]["score"]====== '+value["data"]["score"].toString());
      // wjPrint('======value["data"]["remarks"]====== '+value["data"]["remarks"].toString());
      if (value["data"] != null) {
        // wjPrint('获取评价详情');
        isInput = false;
        score = int.parse(value["data"]["score"]);
        remarks = value["data"]["remarks"];
        setState(() {});
      } else {
        // wjPrint('添加评价详情');
        isInput = true;
        setState(() {});
      }
    });
  }

  HelpViewModel quizVm;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      child: Scaffold(
        appBar: commonAppBar(title: "订单详情"),
        body: Consumer<UserViewModel>(
          builder: (ctx, userModel, child) {
            return ProviderWidget<HelpViewModel>(
                model: HelpViewModel(userModel),
                onModelReady: (model) {
                  quizVm = model;
                },
                builder: (ctx, vm, child) {
                  return SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          width: getWidthPx(750),
                          color: Colors.white,
                          child: Column(
                            children: <Widget>[
                              Container(
                                margin: EdgeInsets.only(
                                  left: getWidthPx(40),
                                  top: getWidthPx(30),
                                ),
                                child: Row(
                                  children: <Widget>[
                                    Text(
                                      "基本信息",
                                      style: TextStyle(
                                          fontWeight: FontWeight.w700),
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    top: getWidthPx(15),
                                    right: getWidthPx(40)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Text(
                                      "申请号",
                                      style: TextStyle(color: Colors.black45),
                                    ),
                                    Text("${orderInfo['orderNo'] ?? ""}",
                                        style: TextStyle(color: Colors.black))
                                  ],
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    top: getWidthPx(15),
                                    right: getWidthPx(40)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Text(
                                      "服务机构",
                                      style: TextStyle(color: Colors.black45),
                                    ),
                                    Text("${orderInfo['notarialName'] ?? ""}",
                                        style: TextStyle(color: Colors.black))
                                  ],
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    top: getWidthPx(15),
                                    right: getWidthPx(40)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Text(
                                      "项目名称",
                                      style: TextStyle(color: Colors.black45),
                                    ),
                                    Text("${orderInfo['projectName'] ?? ""}",
                                        style: TextStyle(color: Colors.black))
                                  ],
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    top: getWidthPx(15),
                                    right: getWidthPx(40)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Text(
                                      "公证时间",
                                      style: TextStyle(color: Colors.black45),
                                    ),
                                    Text("${orderInfo['notaryTime'] ?? ""}",
                                        style: TextStyle(color: Colors.black))
                                  ],
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    top: getWidthPx(15),
                                    bottom: getWidthPx(15),
                                    right: getWidthPx(40)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Text(
                                      "公证状态",
                                      style: TextStyle(color: Colors.black45),
                                    ),
                                    Text(
                                        "${orderInfo["notaryState"] == 10 ? "待受理" : orderInfo["notaryState"] == 20 ? "受理中" : orderInfo["notaryState"] == 31 ? "终止（未完成申办）" : "完成"}",
                                        style: TextStyle(color: Colors.black))
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: getHeightPx(20),
                        ),
                        Container(
                          color: Colors.white,
                          width: getWidthPx(750),
                          padding: EdgeInsets.only(
                              left: getWidthPx(40), top: getWidthPx(20)),
                          child: Text(
                            "经办信息",
                            style: TextStyle(fontWeight: FontWeight.w700),
                          ),
                        ),

                        orderInfo['notaryenterprises'] == null
                            ? SizedBox()
                            : Container(
                                color: Colors.white,
                                child: ListView.builder(
                                    shrinkWrap: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    itemCount:
                                        orderInfo['notaryenterprises'].length,
                                    itemBuilder: (ctx, a) {
                                      var item =
                                          orderInfo['notaryenterprises'][a];
                                      return Column(
                                        children: <Widget>[
                                          item['unitGuid'] == null
                                              ? SizedBox()
                                              : Container(
                                                  margin: EdgeInsets.only(
                                                      left: getWidthPx(40),
                                                      top: getWidthPx(15),
                                                      right: getWidthPx(40)),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: <Widget>[
                                                      Container(
                                                        child: Text(
                                                          "企业名称",
                                                          style: TextStyle(
                                                              color: Colors
                                                                  .black45),
                                                        ),
                                                      ),
                                                      Text(
                                                          "${item['enterpriseName'] ?? ""}",
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.black))
                                                    ],
                                                  ),
                                                ),
                                          item['unitGuid'] == null
                                              ? SizedBox()
                                              : Container(
                                                  margin: EdgeInsets.only(
                                                      left: getWidthPx(40),
                                                      top: getWidthPx(15),
                                                      right: getWidthPx(40)),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: <Widget>[
                                                      Container(
                                                        child: Text(
                                                          "企业地址",
                                                          style: TextStyle(
                                                              color: Colors
                                                                  .black45),
                                                        ),
                                                      ),
                                                      Text(
                                                          "${item['enterpriseAddress'] ?? ""}",
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.black))
                                                    ],
                                                  ),
                                                ),
                                          ListView.builder(
                                              shrinkWrap: true,
                                              physics:
                                                  NeverScrollableScrollPhysics(),
                                              itemCount:
                                                  item['publicnotaryusers']
                                                      .length,
                                              itemBuilder: (ctx, b) {
                                                var itemA =
                                                    item['publicnotaryusers']
                                                        [b];
                                                return Column(
                                                  children: <Widget>[
                                                    Container(
                                                      margin: EdgeInsets.only(
                                                          left: getWidthPx(40),
                                                          top: getWidthPx(15),
                                                          right:
                                                              getWidthPx(40)),
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: <Widget>[
                                                          Container(
                                                            child: Text(
                                                              "名字",
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .black45),
                                                            ),
                                                          ),
                                                          Text(
                                                              "${itemA['userName'] ?? ""}",
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .black))
                                                        ],
                                                      ),
                                                    ),
                                                    Container(
                                                      margin: EdgeInsets.only(
                                                          left: getWidthPx(40),
                                                          top: getWidthPx(15),
                                                          right:
                                                              getWidthPx(40)),
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: <Widget>[
                                                          Container(
                                                            child: Text(
                                                              "身份证号",
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .black45),
                                                            ),
                                                          ),
                                                          Text(
                                                              "${itemA['idCard'] ?? ""}",
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .black))
                                                        ],
                                                      ),
                                                    ),
                                                    Container(
                                                      margin: EdgeInsets.only(
                                                          left: getWidthPx(40),
                                                          top: getWidthPx(15),
                                                          right:
                                                              getWidthPx(40)),
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: <Widget>[
                                                          Container(
                                                            child: Text(
                                                              "手机号",
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .black45),
                                                            ),
                                                          ),
                                                          Text(
                                                              "${itemA['mobile'] ?? ""}",
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .black))
                                                        ],
                                                      ),
                                                    ),
                                                    Container(
                                                      margin: EdgeInsets.only(
                                                          left: getWidthPx(40),
                                                          top: getWidthPx(15),
                                                          right:
                                                              getWidthPx(40)),
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: <Widget>[
                                                          Container(
                                                            child: Text(
                                                              "类型",
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .black45),
                                                            ),
                                                          ),
                                                          Text(
                                                              "${itemA['userType'] == 0 ? "法定代表人" : itemA['userType'] == 0 ? "经办人" : "个人"}",
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .black))
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 10,
                                                    )
                                                  ],
                                                );
                                              }),
                                        ],
                                      );
                                    }),
                              ),
                        Container(
                          height: getHeightPx(20),
                          color: AppTheme.bg_d,
                        ),
                        // Container(
                        //   width: getWidthPx(750),
                        //   color: Colors.white,
                        //   child: Column(
                        //     children: <Widget>[
                        //       Container(
                        //         margin: EdgeInsets.only(left: getWidthPx(40),top: getWidthPx(30)),
                        //         child: Row(
                        //           children: <Widget>[
                        //             Text("公证书签收",style: TextStyle(fontWeight: FontWeight.w700),)
                        //           ],
                        //         ),
                        //       ),
                        //       Container(
                        //         margin: EdgeInsets.only(left: getWidthPx(40),top: getWidthPx(15),bottom: getWidthPx(15),right: getWidthPx(40)),
                        //         child: Row(
                        //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        //           children: <Widget>[
                        //             Text("公证书.pdf",style: TextStyle(color: Colors.black45),),
                        //             Container(
                        //               width: 60,
                        //               height: 25,
                        //               child: FLFlatButton(
                        //                 expanded: true,
                        //                 color: Colors.green,
                        //                 textColor: Colors.white,
                        //                 child: Text('签收', textAlign: TextAlign.center,style: TextStyle(fontSize: 12),),
                        //                 onPressed: () => wjPrint('on click'),
                        //               ),
                        //             )
                        //
                        //           ],
                        //         ),
                        //       )
                        //     ],
                        //   ),
                        // ),
                        SizedBox(
                          height: getHeightPx(20),
                        ),

                        Container(
                          width: getWidthPx(750),
                          color: Colors.white,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Container(
                                margin: EdgeInsets.only(
                                    left: getWidthPx(40), top: getWidthPx(30)),
                                child: Text(
                                  "材料",
                                  style: TextStyle(fontWeight: FontWeight.w700),
                                ),
                              ),
                              orderInfo['rstMaterial'] != null
                                  ? Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: GridView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              NeverScrollableScrollPhysics(),
                                          gridDelegate:
                                              SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 3, //每行三列
                                            childAspectRatio: 1.0, //显示区域宽高相等
                                            mainAxisSpacing: 8,
                                            crossAxisSpacing: 8,
                                          ),
                                          itemCount:
                                              orderInfo['rstMaterial'].length,
                                          itemBuilder: (context, index) {
                                            var item =
                                                orderInfo['rstMaterial'][index];
                                            return InkWell(
                                              onTap: () {
                                                List imgArr = [];
                                                orderInfo['rstMaterial']
                                                    .forEach((element) {
                                                  imgArr
                                                      .add(element['filePath']);
                                                });
                                                Navigator.of(context).push(new FadeRoute(
                                                    page: PhotoViewGalleryScreen(
                                                        images: imgArr, //传入图片list
                                                        index: index, //传入当前点击的图片的index
                                                        heroTag: "1")));
                                              },
                                              child: FadeInImage.assetNetwork(
                                                imageCacheWidth: 800,
                                                imageCacheHeight: 800,
                                                placeholder:
                                                    'lib/assets/images/empty.png',
                                                image:
                                                    item['filePath'].toString(),
                                              ),
                                            );
                                          }),
                                    )
                                  : SizedBox(),
                              orderInfo['rstScreenshot'] != null
                                  ? Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: GridView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              NeverScrollableScrollPhysics(),
                                          gridDelegate:
                                              SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 3, //每行三列
                                            childAspectRatio: 1.0, //显示区域宽高相等
                                            mainAxisSpacing: 8,
                                            crossAxisSpacing: 8,
                                          ),
                                          itemCount:
                                              orderInfo['rstScreenshot'].length,
                                          itemBuilder: (context, index) {
                                            var item =
                                                orderInfo['rstScreenshot']
                                                    [index];
                                            return InkWell(
                                              onTap: () {
                                                Navigator.of(context).push(
                                                    new FadeRoute(
                                                        page:
                                                            PhotoViewGalleryScreen(
                                                                images: orderInfo[
                                                                    'rstScreenshot'], //传入图片list
                                                                index:
                                                                    index, //传入当前点击的图片的index
                                                                heroTag: "1")));
                                              },
                                              child: FadeInImage.assetNetwork(
                                                imageCacheWidth: 800,
                                                imageCacheHeight: 800,
                                                placeholder:
                                                    'lib/assets/images/empty.png',
                                                image: item.toString(),
                                              ),
                                            );
                                          }),
                                    )
                                  : SizedBox(),
                            ],
                          ),
                        ),

                        SizedBox(
                          height: getHeightPx(20),
                        ),
                        Container(
                            width: getWidthPx(750),
                            decoration: BoxDecoration(color: Colors.white),
                            child: Column(children: <Widget>[
                              Container(
                                margin: EdgeInsets.only(
                                  left: getWidthPx(40),
                                  top: getWidthPx(30),
                                ),
                                child: Row(
                                  children: <Widget>[
                                    Text(
                                      "服务评价",
                                      style: TextStyle(
                                          fontWeight: FontWeight.w700),
                                    )
                                  ],
                                ),
                              ),
                              SponRatingWidget(
                                value: score,
                                evaluate: remarks,
                                isInput: isInput,
                                onRatingUpdate: (e, str) async {
                                  // wjPrint("服务评价: $e   $str");
                                  vm.textEditingController.text = str;
                                  vm.orderId =
                                      widget.arguments["data"]["unitGuid"];
                                  vm.score = e.toString();
                                  vm.addScore();
                                  setState(() {
                                    score = int.parse(vm.score);
                                    isInput = vm.isBool;
                                  });
                                },
                              ),
                            ])),

                        Offstage(
                          offstage: !(orderInfo["notaryState"] == 15),
                          child: Container(
                            margin: EdgeInsets.only(top: getWidthPx(20)),
                            child: InkWell(
                              onTap: () {
                                if (orderInfo['order'] != null &&
                                    orderInfo['order']["fee"] != null &&
                                    orderInfo['order']["fee"] > 0) {
                                  Navigator.pushNamed(
                                      context, RoutePaths.payWidget,
                                      arguments: {
                                        "unitGuid": orderInfo['order']
                                            ['unitGuid'],
                                        'callBackUrl': "/index",
                                        'payLogo': 1,
                                        'fee': orderInfo['order']['fee'],
                                        "orderNo": orderInfo['order']['orderNo']
                                      });
                                } else {
                                  EasyLoading.show();
                                  var map = {
                                    "unitGuid": orderInfo['order']['unitGuid'],
                                    "notaryState": 30,
                                    "payType": "pt006"
                                  };
                                  HomeApi.getSingleton().setTakeType(map,
                                      errorCallBack: (r) {
                                    EasyLoading.dismiss();
                                    ToastUtil.showErrorToast(r.toString());
                                  }).then((value) {
                                    EasyLoading.dismiss();
                                    if (value['code'] == 200) {
                                      ToastUtil.showSuccessToast("支付成功");
                                      getData();
                                    } else {
                                      ToastUtil.showErrorToast(
                                          value['message']);
                                    }
                                  });
                                }
                              },
                              child: Container(
                                width: getWidthPx(200),
                                height: getWidthPx(80),
                                alignment: Alignment.center,
                                padding: EdgeInsets.symmetric(
                                    horizontal: getWidthPx(40),
                                    vertical: getWidthPx(10)),
                                decoration: BoxDecoration(
                                  color: AppTheme.themeBlue,
                                  borderRadius: BorderRadius.circular(
                                    getWidthPx(10),
                                  ),
                                ),
                                child: Text(
                                  '立即支付',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      fontSize: getSp(28), color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                });
          },
        ),
      ),
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
    );
  }

  @override
  bool get wantKeepAlive => false;
}
