import 'dart:async';
import 'dart:io';

import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';
import 'package:notarization_station_app/page/login/entity/userInfo.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/account_api.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/utils/ServiceLocator.dart';
import 'package:notarization_station_app/utils/TelAndSmsService.dart';
import 'package:notarization_station_app/utils/alert_view.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';

import '../../../appTheme.dart';
import '../../../config.dart';

class CodeViewModel extends SingleViewStateModel {
  UserViewModel userViewModel;
  TextEditingController phoneController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  CodeViewModel(this.userViewModel);

  TelAndSmsService service = locator<TelAndSmsService>();

  bool loading = false;
  int countDownTime = 0;
  Timer countdownTimer;
  String codeCountdownStr = '获取验证码';
  bool codeCountDownIsEnable = true;
  int _countdownNum = 119;

  // 选中展示的区号
  String dropDownValue = '86';

  String selectData = "1";

  void changeDropDownValue(String value) {
    dropDownValue = value;
    G.phoneArea.forEach((element) {
      if (element['dictCode'] == value) {
        selectData = element['dictValue'];
      }
    });
    notifyListeners();
  }

  /// 倒计时
  countDown() {
    // Timer的第一秒倒计时是有一点延迟的，为了立刻显示效果可以添加下一行。
    countdownTimer?.cancel();
    codeCountdownStr = '${_countdownNum--}s后重新获取';
    codeCountDownIsEnable = false;
    notifyListeners();
    countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_countdownNum > 0) {
        codeCountdownStr = '${_countdownNum--}s后重新获取';
        wjPrint('123');
      } else {
        codeCountDownIsEnable = true;
        codeCountdownStr = '获取验证码';
        _countdownNum = 119;
        countdownTimer.cancel();
        countdownTimer = null;
      }
      notifyListeners();
    });
  }

  //sendType:获取短信类型（1、注册 2、登录 3、忘记密码）
  // type: 用户类型(1 机构用户,2 普通用户),示例值(2)
  getImageCode(BuildContext myContext) {
    if (selectData == "1") {
      if (phoneController.text.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
        return;
      }
      if (phoneController.text.length != 11) {
        ToastUtil.showWarningToast('手机号格式错误');
        return;
      }

      Map<String, dynamic> data = {
        'type': '2',
        'identy': phoneController.text,
      };
      codeCountDownIsEnable = false;
      notifyListeners();
      showCaptchaCodeAlert(
          context: myContext,
          imageData: data,
          isForgetPassword: false,
          confirmEvent: (String code) {
            if (code != null && code.isNotEmpty) {
              getCode(code);
            } else {
              codeCountDownIsEnable = true;
              notifyListeners();
              ToastUtil.showWarningToast('请输入图形验证码');
            }
          },
          cancelEvent: () {
            codeCountDownIsEnable = true;
            notifyListeners();
          },
          successCallBack: (value) {
            if (value["code"] != 200) {
              codeCountDownIsEnable = true;
              notifyListeners();
            }
          },
          failCallBack: () {
            codeCountDownIsEnable = true;
            notifyListeners();
          },
          errorCallBack: (e) {
            codeCountDownIsEnable = true;
            notifyListeners();
          });
    }
  }

  getCode(String code) {
    if (selectData == '1') {
      if (phoneController.text.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
        codeCountDownIsEnable = true;
        notifyListeners();
        return;
      }
      if (phoneController.text.length != 11) {
        ToastUtil.showWarningToast('手机号格式错误');
        codeCountDownIsEnable = true;
        notifyListeners();
        return;
      }
      if (code.isEmpty) {
        ToastUtil.showWarningToast("请输入图形验证码");
        codeCountDownIsEnable = true;
        notifyListeners();
        return;
      }

      codeCountDownIsEnable = false;
      notifyListeners();
      Map<String, String> map = {
        "areaCode": dropDownValue,
        "mobile": phoneController.text,
        "sendType": "2",
        'type': "2",
        "graphicCode": code,
      };
      AccountApi.getSingleton().getCode(map, errorCallBack: (e) {
        codeCountDownIsEnable = true;
        notifyListeners();
        ToastUtil.showWarningToast("获取失败，稍后再试！");
      }).then((data) {
        if (data != null) {
          if (data["code"] != 200) {
            codeCountDownIsEnable = true;
            notifyListeners();
            ToastUtil.showWarningToast("${data["data"] ?? data["message"]}");
            return;
          }
          ToastUtil.showSuccessToast("验证码获取成功！");
          countDown();
        } else {
          codeCountDownIsEnable = true;
          notifyListeners();
          ToastUtil.showWarningToast("获取失败，稍后再试！");
        }
      });
      return;
    }
    Map<String, dynamic> map = {
      'areaCode': dropDownValue,
      "mobile": phoneController.text,
      'type': 3, // 1: register 2: resetPsd 3: login
    };
    HomeApi.getSingleton().getAbordMsgSend(map).then((data) {
      if (data != null) {
        if (data["code"] != 200) {
          ToastUtil.showWarningToast("${data["data"] ?? data["message"]}");
          return;
        }
        countDown();
        ToastUtil.showSuccessToast("验证码获取成功！");
      } else {
        ToastUtil.showWarningToast("获取失败，稍后再试！");
      }
    });
  }

  login(BuildContext context) {
    if (phoneController.text.isEmpty || codeController.text.isEmpty) {
      ToastUtil.showWarningToast("用户名或验证码不能为空！");
    } else {
      loading = true;
      notifyListeners();
      Map<String, Object> map = {
        "smsCode": codeController.text,
        "source": "1",
        'areaCode': dropDownValue,
        'mobile': phoneController.text,
        "type": "2"
      };
      AccountApi.getSingleton().getCodeLogin(map, errorCallBack: (e) {
        loading = false;
        notifyListeners();
      }).then((data) {
        loading = false;
        if (data != null) {
          wjPrint("参数：$data");
          if (data["code"] != 200) {
            if (data['code'] == 11012) {
              showAccountForbiddenAlert(context);
            } else {
              ToastUtil.showWarningToast(data["data"] ?? data["message"]);
              return;
            }
          }
          SpUtil.remove("lastUserId");
          AccountApi.getSingleton().getUserInfo(
              {'roleId': data["data"]['role'][0]['roleId']},
              data["data"]['token']).then((res) {
            if (res["code"] == 200) {
              res['data']['token'] = data["data"]['token'];
              UserInfoEntity user = UserInfoEntity.fromJson(res['data']);
              if (user.restPwStatus) {
                ToastUtil.showSuccessToast("当前密码过于简单，请修改密码后再次登录");
                bool isAbroad = false;
                if (SpUtil.getString(Config.lastUserPhoneNumber) != null &&
                    SpUtil.getString(Config.lastUserPhoneNumber).isNotEmpty) {
                  if (RegExp("^1[3456789]\\d{9}\$")
                      .hasMatch(SpUtil.getString(Config.lastUserPhoneNumber))) {
                    isAbroad = false;
                  } else {
                    isAbroad = true;
                  }
                  Navigator.pushNamed(context, RoutePaths.ModifyPsd,
                      arguments: {
                        'isAbroad': isAbroad,
                        'phoneNumber':
                            SpUtil.getString(Config.lastUserPhoneNumber),
                        'title': "找回密码"
                      });
                } else {
                  Navigator.pushNamed(context, RoutePaths.ModifyPsd,
                      arguments: {
                        'isAbroad': isAbroad,
                        'phoneNumber': '',
                        'title': " 找回密码"
                      });
                }
              } else {
                ToastUtil.showSuccessToast("登录成功！");
                userViewModel.saveUser(user);
                G.getCurrentState().pushNamedAndRemoveUntil(
                    RoutePaths.HomeIndex, (Route route) => false);
              }
            } else {
              ToastUtil.showWarningToast("登录失败，请稍后再试");
            }
          });
        } else {
          ToastUtil.showWarningToast("登录失败，请稍后再试");
        }
        notifyListeners();
      });
    }
  }

  void showAccountForbiddenAlert(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return Material(
            color: Colors.black.withAlpha(100),
            child: Align(
              alignment: Alignment.center,
              child: Container(
                width: 300,
                height: 200,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10)),
                child: Column(
                  children: [
                    const Spacer(),
                    Text(
                      '该账号为禁用状态',
                      style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontWeight: FontWeight.bold),
                    ),
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Text(
                        '      可联系公证员协助处理，或使用大陆手机号联系4008001820进行咨询处理。',
                        style: TextStyle(
                            fontSize: 15,
                            color: Colors.black,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                            exit(1);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(
                                vertical: 10, horizontal: 20),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: AppTheme.themeBlue, width: 1.0),
                                borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              '退出',
                              style: TextStyle(
                                  fontSize: 15, color: AppTheme.themeBlue),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 20,
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                            service.call("4008001820");
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(
                                vertical: 10, horizontal: 20),
                            decoration: BoxDecoration(
                                color: AppTheme.themeBlue,
                                borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              '立即拨打',
                              style:
                                  TextStyle(fontSize: 15, color: Colors.white),
                            ),
                          ),
                        )
                      ],
                    ),
                    const Spacer(),
                  ],
                ),
              ),
            ),
          );
        });
  }

  @override
  Future loadData() {}

  @override
  onCompleted(data) {}
}
