import 'dart:async';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';
import 'package:notarization_station_app/page/login/entity/userInfo.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/account_api.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/utils/ServiceLocator.dart';
import 'package:notarization_station_app/utils/TelAndSmsService.dart';
import 'package:notarization_station_app/utils/alert_view.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';

class RegisterViewModel extends SingleViewStateModel {
  TextEditingController phoneC = TextEditingController();
  TextEditingController codeC = TextEditingController();
  TextEditingController psdC = TextEditingController();
  TextEditingController psdAgainC = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController emailCodeController = TextEditingController();
  TextEditingController abroadPswC = TextEditingController();
  TextEditingController abroadPswAgainC = TextEditingController();

  bool phoneBool = false;

  int countDownTime = 0;
  Timer countdownTimer;
  String codeCountdownStr = '获取验证码';
  bool codeCountDownIsEnable = true;
  int _countdownNum = 119;

  Timer abroadCountdownTimer;
  String abroadCodeCountdownStr = '获取验证码';
  bool abroadCodeCountDownIsEnable = true;
  int _abroadCountdownNum = 119;

  // 选中展示的区号
  String dropDownValue = '86';

  String dropDownValueFirst = '86';

  String selectData = "大陆用户";

  TelAndSmsService service = locator<TelAndSmsService>();

  bool loading = false;

  void setSelectData(String value) {
    // codeCountdownStr = '获取验证码';
    // _countdownNum = 119;
    // isCodeTap = false;
    // countdownTimer?.cancel();
    selectData = value;
    // phoneC.clear();
    // codeC.clear();
    // psdC.clear();
    // psdAgainC.clear();
    // emailController.clear();
    // emailCondeController.clear();
    notifyListeners();
  }

  final UserViewModel userViewModel;

  RegisterViewModel(this.userViewModel);

  /// 国内密码脱密展示
  bool passwordShow = false;
  bool password2Show = false;

  /// 国外密码脱密展示
  bool abroadPasswordShow = false;
  bool abroadPassword2Show = false;
  bool isConsent = false;

  void phoneBoolShow(bool v) {
    phoneBool = v;
    notifyListeners();
  }

  /// 倒计时
  countDown() {
    // Timer的第一秒倒计时是有一点延迟的，为了立刻显示效果可以添加下一行。
    countdownTimer?.cancel();
    codeCountdownStr = '${_countdownNum--}s后重新获取';
    notifyListeners();
    countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_countdownNum > 0) {
        codeCountdownStr = '${_countdownNum--}s后重新获取';
      } else {
        codeCountDownIsEnable = true;
        codeCountdownStr = '获取验证码';
        _countdownNum = 119;
        countdownTimer.cancel();
        countdownTimer = null;
      }
      notifyListeners();
    });
  }

  /// 倒计时
  abroadCountDown() {
    // Timer的第一秒倒计时是有一点延迟的，为了立刻显示效果可以添加下一行。
    abroadCountdownTimer?.cancel();
    abroadCodeCountdownStr = '${_abroadCountdownNum--}s后重新获取';
    notifyListeners();
    abroadCountdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_abroadCountdownNum > 0) {
        abroadCodeCountdownStr = '${_abroadCountdownNum--}s后重新获取';
      } else {
        abroadCodeCountdownStr = '获取验证码';
        abroadCodeCountDownIsEnable = true;
        _abroadCountdownNum = 119;
        abroadCountdownTimer.cancel();
        abroadCountdownTimer = null;
      }
      notifyListeners();
    });
  }

// identy	邮箱地址 or 手机号码	query	true	string
// type	验证码类型（1.注册 2.登录 3.忘记密码）	query	true
  // 获取图像验证码接口
  void getImgCode({BuildContext myContext}) {
    if (selectData == "大陆用户") {
      if (phoneC.text.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
        return;
      }
      if (phoneC.text.length != 11) {
        ToastUtil.showWarningToast('手机号格式错误');
        return;
      }
    }
    if (selectData == "境外用户") {
      if (phoneC.text.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
        return;
      }
      if (emailController.text.isEmpty) {
        ToastUtil.showWarningToast("请输入邮箱");
        return;
      }
      if (!RegexUtil.isEmail(emailController.text)) {
        ToastUtil.showWarningToast("请输入正确的邮箱格式");
        return;
      }
    }
    if (selectData == "境外用户") {
      abroadCodeCountDownIsEnable = false;
      notifyListeners();
    } else {
      codeCountDownIsEnable = false;
      notifyListeners();
    }

    Map<String, dynamic> data = {
      'type': '1',
      'identy': selectData == "境外用户" ? emailController.text : phoneC.text,
    };
    showCaptchaCodeAlert(
        context: myContext,
        imageData: data,
        isForgetPassword: false,
        confirmEvent: (String code) {
          if (code != null && code.isNotEmpty) {
            if (selectData == "境外用户") {
              getAbroadEmailCode(code);
            } else {
              getChinaCountrySmsCode(code);
            }
          } else {
            if (selectData == "境外用户") {
              abroadCodeCountDownIsEnable = true;
              notifyListeners();
            } else {
              codeCountDownIsEnable = true;
              notifyListeners();
            }
            ToastUtil.showWarningToast('请输入图形验证码');
          }
        },
        cancelEvent: () {
          if (selectData == "境外用户") {
            abroadCodeCountDownIsEnable = true;
            notifyListeners();
          } else {
            codeCountDownIsEnable = true;
            notifyListeners();
          }
        },
        successCallBack: (value) {
          if (value["code"] != 200) {
            if (selectData == "境外用户") {
              abroadCodeCountDownIsEnable = true;
              notifyListeners();
            } else {
              codeCountDownIsEnable = true;
              notifyListeners();
            }
          }
        },
        failCallBack: () {
          if (selectData == "境外用户") {
            abroadCodeCountDownIsEnable = true;
            notifyListeners();
          } else {
            codeCountDownIsEnable = true;
            notifyListeners();
          }
        },
        errorCallBack: (e) {
          if (selectData == "境外用户") {
            abroadCodeCountDownIsEnable = true;
            notifyListeners();
          } else {
            codeCountDownIsEnable = true;
            notifyListeners();
          }
        });
  }

  // 国内用户获取短信验证码
  getChinaCountrySmsCode(String graphicCode) {
    if (phoneC.text.isEmpty) {
      ToastUtil.showWarningToast("请输入手机号");
      codeCountDownIsEnable = true;
      notifyListeners();
    } else if (phoneC.text.length != 11) {
      ToastUtil.showWarningToast('手机号格式错误');
      codeCountDownIsEnable = true;
      notifyListeners();
    } else if (graphicCode.isEmpty) {
      ToastUtil.showWarningToast('请输入图形验证码');
      codeCountDownIsEnable = true;
      notifyListeners();
    } else {
      codeCountDownIsEnable = false;
      setBusy(true);
      notifyListeners();
      // if (selectData == "1") {
      Map<String, String> map = {
        "areaCode": dropDownValue,
        "mobile": phoneC.text,
        "sendType": "1",
        'type': "2",
        "graphicCode": graphicCode,
      };
      AccountApi.getSingleton().getCode(map, errorCallBack: (e) {
        codeCountdownStr = '获取验证码';
        codeCountDownIsEnable = true;
        notifyListeners();
      }).then((data) {
        if (data != null && data["code"] == 200) {
          ToastUtil.showSuccessToast("验证码获取成功！");
          countDown();
        } else {
          ToastUtil.showWarningToast(data['data']??data["message"]);
          codeCountdownStr = '获取验证码';
          codeCountDownIsEnable = true;
          notifyListeners();
        }
      });
    }
  }

  // 获取国外用户的邮箱验证码
  getAbroadEmailCode(String captchaCode) {
    if (phoneC.text.isEmpty) {
      ToastUtil.showWarningToast("请输入手机号");
      abroadCodeCountDownIsEnable = true;
      notifyListeners();
    } else if (emailController.text.isEmpty) {
      ToastUtil.showWarningToast("请输入邮箱");
      abroadCodeCountDownIsEnable = true;
      notifyListeners();
    } else if (!RegexUtil.isEmail(emailController.text)) {
      ToastUtil.showWarningToast("请输入正确的邮箱格式");
      abroadCodeCountDownIsEnable = true;
      notifyListeners();
    } else if (captchaCode.isEmpty) {
      ToastUtil.showWarningToast("请输入图形验证码");
      abroadCodeCountDownIsEnable = true;
      notifyListeners();
    } else {
      Map<String, dynamic> map = {
        'email': emailController.text,
        "code": captchaCode,
        // "mobile": phoneC.text,
        // 'type': 1, // 1: register 2: resetPsd 3: login
      };
      HomeApi.getSingleton().getSendEmailCode(map, errorCallBack: (e) {
        abroadCodeCountdownStr = '获取验证码';
        abroadCodeCountDownIsEnable = true;
        notifyListeners();
      }).then((data) {
        if (data != null && data["code"] == 200) {
          ToastUtil.showSuccessToast("验证码获取成功！");
          abroadCountDown();
        } else {
          abroadCodeCountdownStr = '获取验证码';
          abroadCodeCountDownIsEnable = true;
          notifyListeners();
          ToastUtil.showWarningToast(data['data'] ?? data['message']);
        }
      });
    }
  }

  amendPsw() {
    if (selectData == '大陆用户') {
      if (phoneC.text.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
      } else if (phoneC.text.length != 11) {
        ToastUtil.showWarningToast("手机号格式错误");
      } else if (codeC.text.isEmpty) {
        ToastUtil.showWarningToast("请输入验证码");
      } else if (codeC.text.length != 6) {
        ToastUtil.showWarningToast("验证码格式错误");
      } else if (psdC.text.length < 8 || psdC.text.length > 16) {
        ToastUtil.showWarningToast("密码长度为8-16位");
      } else if (!G.checkPassWord(psdC.text)) {
        ToastUtil.showWarningToast("密码必须包含大写字母、小写字母、数字、特殊字符三种及三种以上");
      } else if (psdC.text != psdAgainC.text) {
        ToastUtil.showWarningToast("两次输入的密码不一致");
      } else {
        if (!isConsent) {
          ToastUtil.showWarningToast("请阅读并同意《江苏省远程公证隐私政策》和《服务协议》");
        } else {
          loading = true;
          notifyListeners();
          Map<String, String> map1 = {
            "password": G.generateMd5(psdC.text),
            "mobileType": "1",
            'areaCode': dropDownValue,
            "mobile": "${phoneC.text}",
            "smsCode": codeC.text,
            // "plainPassword": sm4Encrypt(psdC.text),
          };
          EasyLoading.show();
          AccountApi.getSingleton().register(map1, errorCallBack: (e) {
            EasyLoading.dismiss();
            loading = false;
            notifyListeners();
          }).then((res) {
            EasyLoading.dismiss();
            loading = false;
            notifyListeners();
            if (res != null) {
              wjPrint("参数1：$res");
              if (res["code"] != 200) {
                ToastUtil.showWarningToast(res["data"]??res['message']);
                return;
              }
              // G.pushNamed(RoutePaths.LOGIN);
              login(psdC.text);
            } else {
              ToastUtil.showWarningToast("注册失败，稍后再试！");
            }
          });
        }
      }
    } else {
      wjPrint("phoneC.text.length ------${phoneC.text.length}");
      if (phoneC.text.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
      } else if (emailController.text.isEmpty) {
        ToastUtil.showWarningToast("请输入邮箱");
      } else if (!RegexUtil.isEmail(emailController.text)) {
        ToastUtil.showWarningToast("邮箱格式错误");
      } else if (emailCodeController.text.isEmpty) {
        ToastUtil.showWarningToast("请输入验证码");
      } else if (emailCodeController.text.length != 6) {
        ToastUtil.showWarningToast("验证码格式错误");
      } else if (abroadPswC.text.length < 8 || abroadPswC.text.length > 16) {
        ToastUtil.showWarningToast("密码长度为8-16位");
      } else if (abroadPswC.text != abroadPswAgainC.text) {
        ToastUtil.showWarningToast("两次输入的密码不一致");
      } else if (!G.checkPassWord(abroadPswC.text)) {
        ToastUtil.showWarningToast("密码必须包含大写字母、小写字母、数字、特殊字符三种及三种以上");
      } else {
        if (!isConsent) {
          ToastUtil.showWarningToast("请阅读并同意《江苏省远程公证隐私政策》和《服务协议》");
        } else {
          loading = true;
          notifyListeners();
          Map<String, dynamic> params = {
            'mobile': phoneC.text,
            'mobileType': 2,
            'password': G.generateMd5(abroadPswC.text),
            'email': emailController.text,
            'emailCode': emailCodeController.text,
            // "plainPassword": sm4Encrypt(abroadPswC.text),
          };

          EasyLoading.show();
          HomeApi.getSingleton().appContactAdmin(params, errorCallBack: (e) {
            loading = false;
            notifyListeners();
            EasyLoading.dismiss();
          }).then((value) {
            loading = false;
            notifyListeners();
            EasyLoading.dismiss();
            if (value != null) {
              if (value['code'] == 200) {
                login(abroadPswC.text);
              } else {
                ToastUtil.showWarningToast(
                    value['data'] ?? value['message'] ?? '');
              }
            } else {
              ToastUtil.showErrorToast('注册失败，请稍后再试');
            }
          });
        }
      }
    }
  }

  login(String password) {
    EasyLoading.show();
    Map<String, String> map = {
      "loginName": phoneC.text,
      "password": G.generateMd5(password),
      "source": "1",
      "type": "2",
    };
    AccountApi.getSingleton().getLogin(map, errorCallBack: (e) {
      EasyLoading.dismiss();
      ToastUtil.showErrorToast('注册失败，请稍后再试');
    }).then((data) {
      if (data != null) {
        if (data["code"] == 200 && data["items"]['role'].length != 0) {
          SpUtil.remove("lastUserId");
          AccountApi.getSingleton().getUserInfo(
              {'roleId': data["items"]['role'][0]['roleId'], 'device': 'app'},
              data["items"]['token'], errorCallBack: (e) {
            EasyLoading.dismiss();
            ToastUtil.showErrorToast('注册失败，请稍后再试');
          }).then((res) {
            if (res != null) {
              if (res["code"] == 200) {
                EasyLoading.dismiss();
                res['data']['token'] = data["items"]['token'];
                UserInfoEntity user = UserInfoEntity.fromJson(res['data']);
                userViewModel.saveUser(user);
                ToastUtil.showSuccessToast("注册成功");
                G.pushNamed(RoutePaths.RealName,
                    arguments: {'comeFrom': 'isLogin'});
              } else {
                EasyLoading.dismiss();
                ToastUtil.showWarningToast(res['message'] ?? res['data'] ?? "");
              }
            } else {
              EasyLoading.dismiss();
              ToastUtil.showWarningToast('注册失败，请稍后再试');
            }
          });
        } else {
          EasyLoading.dismiss();
          ToastUtil.showWarningToast(data['message'] ?? data['data'] ?? '');
        }
      } else {
        EasyLoading.dismiss();
        ToastUtil.showWarningToast('注册失败，请稍后再试');
      }
    });
  }

  setPasswordShow() {
    selectData == '大陆用户'
        ? passwordShow = !passwordShow
        : abroadPasswordShow = !abroadPasswordShow;
    notifyListeners();
  }

  setPasswordAgainShow() {
    selectData == '大陆用户'
        ? password2Show = !password2Show
        : abroadPassword2Show = !abroadPassword2Show;
    notifyListeners();
  }

  @override
  Future loadData() {}

  @override
  onCompleted(data) {}
}
