import 'dart:ui';

import 'package:color_dart/RgbaColor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/services.dart';
import 'package:notarization_station_app/base_framework/widget/a_button/index.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/iconfont/Icon.dart';
import 'package:notarization_station_app/page/login/vm/register_vm.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:provider/provider.dart';

import '../../appTheme.dart';

class RegisterPage extends StatefulWidget {
  final String phone;
  final bool isAbroad;

  RegisterPage({Key key, this.phone, this.isAbroad = false}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return RegisterPageState();
  }
}

class RegisterPageState extends BaseState<RegisterPage>
    with SingleTickerProviderStateMixin {
  RegisterViewModel registerViewModel;

  TabController _tabController;

  String isAbroad = '';

  String phoneNumber = '';

  @override
  void initState() {
    super.initState();
    isAbroad = widget.isAbroad ? "境外用户" : "大陆用户";
    phoneNumber = widget.phone;
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    super.dispose();
    _tabController.dispose();
    registerViewModel.countdownTimer?.cancel();
    registerViewModel.abroadCountdownTimer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: commonAppBar(title: '注册账号', actions: <Widget>[
        Padding(
          padding: EdgeInsets.only(right: getWidthPx(30)),
          child: Column(children: [
            const Spacer(),
            InkWell(
              onTap: () {
                G.pushNamed(RoutePaths.registerExplain);
              },
              child: Text(
                "注册说明",
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            const Spacer()
          ]),
        )
      ]),
      body: Consumer<UserViewModel>(
        builder: (ctx, userModel, child) {
          return ProviderWidget<RegisterViewModel>(
            model: RegisterViewModel(userModel),
            onModelReady: (model) {
              model.initData();
              registerViewModel = model;
              _tabController.animateTo(widget.isAbroad ? 1 : 0);
              registerViewModel.setSelectData(isAbroad);
            },
            builder: (ctx, vm, child) {
              return Container(
                color: Colors.white,
                width: getWidthPx(750),
                height: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: getWidthPx(40)),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Row(
                        children: [
                          const Spacer(),
                          Container(
                            margin: EdgeInsets.only(
                                top: getHeightPx(10), bottom: getHeightPx(10)),
                            alignment: Alignment.center,
                            width: 250,
                            height: 250,
                            child: Image.asset('lib/assets/images/logoimg.png',
                                fit: BoxFit.contain),
                          ),
                          const Spacer(),
                        ],
                      ),
                      Container(
                        color: Colors.white,
                        padding: EdgeInsets.symmetric(horizontal: 30),
                        child: TabBar(
                          labelColor: Colors.blue,
                          unselectedLabelColor: Colors.grey,
                          indicatorColor: Colors.blue,
                          indicatorSize: TabBarIndicatorSize.label,
                          indicatorWeight: 3,
                          labelStyle: TextStyle(
                              fontSize: 15, fontWeight: FontWeight.w500),
                          unselectedLabelStyle: TextStyle(fontSize: 15),
                          indicatorPadding: EdgeInsets.only(
                              bottom: getHeightPx(10), top: getHeightPx(10)),
                          labelPadding: EdgeInsets.only(
                              bottom: getHeightPx(10), top: getHeightPx(10)),
                          controller: _tabController,
                          tabs: [
                            Tab(
                              text: "大陆用户",
                            ),
                            Tab(
                              text: "境外用户",
                            ),
                          ],
                          onTap: (index) {
                            index == 0
                                ? vm.setSelectData('大陆用户')
                                : vm.setSelectData('境外用户');
                          },
                        ),
                      ),
                      mainWidget(),
                      Container(
                        padding: EdgeInsets.only(top: 10, right: 20),
                        child: Text(
                          '''密码必须包含大写字母、小写字母、数字、特殊字符其中三种''',
                          style: TextStyle(
                            fontSize: 12,
                          ),
                        ),
                      ),

                      /// 注册
                      Padding(
                        padding: EdgeInsets.only(
                            top: getWidthPx(50),
                            left: getWidthPx(40),
                            right: getWidthPx(40)),
                        child: InkWell(
                          onTap: () {
                            vm.amendPsw();
                          },
                          child: Container(
                              alignment: Alignment.center,
                              padding: EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(5)),
                                color: AppTheme.themeBlue,
                              ),
                              child: Text('注册',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: AppTheme.nearlyWhite))),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 15),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Checkbox(
                              value: vm.isConsent,
                              activeColor: AppTheme.themeBlue, //选中时的颜色
                              onChanged: (value) {
                                vm.isConsent = !vm.isConsent;
                                vm.notifyListeners();
                              },
                            ),
                            Text(
                              '已阅读和同意',
                              style: TextStyle(fontSize: 14),
                            ),
                            InkWell(
                              child: Text(
                                '《隐私政策》',
                                style: TextStyle(
                                    color: AppTheme.themeBlue, fontSize: 14),
                              ),
                              onTap: () {
                                G.pushNamed(RoutePaths.Privacy);
                              },
                            ),
                            Text(
                              '和',
                              style: TextStyle(fontSize: 14),
                            ),
                            InkWell(
                              child: Text(
                                '《服务协议》',
                                style: TextStyle(
                                    color: AppTheme.themeBlue, fontSize: 14),
                              ),
                              onTap: () {
                                G.pushNamed(RoutePaths.UserAgreement);
                              },
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: getHeightPx(20),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  /// center widget
  Widget mainWidget() {
    return Column(
      children: <Widget>[
        //手机号码
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            RichText(
                text: TextSpan(
                    text: '*',
                    style: TextStyle(color: Colors.red, fontSize: 14),
                    children: [
                  TextSpan(
                      text: '手机号码',
                      style: TextStyle(fontSize: 14, color: Colors.black))
                ])),
            Expanded(
              child: Container(
                margin: EdgeInsets.only(
                    left: getWidthPx(30), right: getWidthPx(30)),
                height: 45,
                decoration: BoxDecoration(border: borderBottom()),
                child: TextField(
                  keyboardType: TextInputType.number,
                  maxLength: registerViewModel.selectData == '大陆用户' ? 11 : 20,
                  // ignore: deprecated_member_use
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly
                  ], //只能输入数字
                  controller: registerViewModel.phoneC,
                  decoration: InputDecoration(
                    counterText: "",
                    border: InputBorder.none,
                    hintText: '请输入手机号码',
                    hintStyle: TextStyle(
                        fontSize: getSp(26), color: Color(0xFF999999)),
                  ),
                  onChanged: (v) {
                    // registerViewModel.phoneBool =
                    //     registerViewModel.phoneC.text.length < 11
                    //         ? false
                    //         : true;
                    // registerViewModel.notifyListeners();
                  },
                ),
              ),
            ),
          ],
        ),
        registerViewModel.selectData == "大陆用户"
            ? Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      RichText(
                          text: TextSpan(
                              text: '*',
                              style: TextStyle(color: Colors.red, fontSize: 14),
                              children: [
                            TextSpan(
                                text: '验  证  码',
                                style: TextStyle(
                                    fontSize: 14, color: Colors.black))
                          ])),
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.only(
                              left: getWidthPx(30), right: getWidthPx(30)),
                          height: 45,
                          decoration: BoxDecoration(border: borderBottom()),
                          child: Row(
                            children: <Widget>[
                              Expanded(
                                child: TextField(
                                  controller: registerViewModel.codeC,
                                  inputFormatters: [
                                    // ignore: deprecated_member_use
                                    FilteringTextInputFormatter.allow(
                                        RegExp("[0-9]")),
                                  ],
                                  maxLength: 6,
                                  keyboardType: TextInputType.number,
                                  decoration: InputDecoration(
                                      counterText: '',
                                      border: InputBorder.none,
                                      hintText: '请输入短信验证码',
                                      hintStyle: TextStyle(
                                          fontSize: getSp(26),
                                          color: Color(0xFF999999))),
                                ),
                              ),
                              Container(
                                height: 25,
                                decoration: BoxDecoration(
                                    border: Border(
                                        left: BorderSide(
                                            color: rgba(242, 242, 242, 1)))),
                              ),
                              buildGetEmailCode(),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              )
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      RichText(
                          text: TextSpan(
                              text: '*',
                              style: TextStyle(color: Colors.red, fontSize: 14),
                              children: [
                            TextSpan(
                                text: '邮       箱',
                                style: TextStyle(
                                    fontSize: 14, color: Colors.black))
                          ])),
                      Expanded(
                        child: Container(
                            margin: EdgeInsets.only(
                                left: getWidthPx(30), right: getWidthPx(30)),
                            height: 45,
                            alignment: Alignment.bottomCenter,
                            decoration: BoxDecoration(
                              border: borderBottom(),
                            ),
                            child: TextField(
                              controller: registerViewModel.emailController,
                              keyboardType: TextInputType.visiblePassword,
                              maxLength: 50,
                              decoration: InputDecoration(
                                counterText: '',
                                border: InputBorder.none,
                                hintText: '请输入邮箱',
                                hintStyle: TextStyle(
                                    fontSize: getSp(26),
                                    color: Color(0xFF999999)),
                              ),
                            )),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      RichText(
                          text: TextSpan(
                              text: '*',
                              style: TextStyle(color: Colors.red, fontSize: 14),
                              children: [
                            TextSpan(
                                text: '验  证  码',
                                style: TextStyle(
                                    fontSize: 14, color: Colors.black))
                          ])),
                      Expanded(
                        child: Container(
                            margin: EdgeInsets.only(
                                left: getWidthPx(30), right: getWidthPx(30)),
                            height: 45,
                            decoration: BoxDecoration(border: borderBottom()),
                            child: Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    maxLength: 6,
                                    controller:
                                        registerViewModel.emailCodeController,
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      // ignore: deprecated_member_use
                                      FilteringTextInputFormatter.allow(
                                          RegExp("[0-9]")),
                                    ],
                                    decoration: InputDecoration(
                                      counterText: '',
                                      border: InputBorder.none,
                                      hintText: '请输入邮箱验证码',
                                      hintStyle: TextStyle(
                                          fontSize: getSp(26),
                                          color: Color(0xFF999999)),
                                    ),
                                  ),
                                ),
                                Container(
                                  height: 25,
                                  decoration: BoxDecoration(
                                      border: Border(
                                          left: BorderSide(
                                              color: rgba(242, 242, 242, 1)))),
                                ),
                                buildAbroadGetEmailCode(),
                              ],
                            )),
                      ),
                    ],
                  )
                ],
              ),
        Row(
          children: [
            RichText(
                text: TextSpan(
                    text: '*',
                    style: TextStyle(color: Colors.red, fontSize: 14),
                    children: [
                  TextSpan(
                      text: '密       码',
                      style: TextStyle(fontSize: 14, color: Colors.black))
                ])),
            Expanded(
              child: Container(
                  margin: EdgeInsets.only(
                      left: getWidthPx(30), right: getWidthPx(30)),
                  height: 45,
                  decoration: BoxDecoration(border: borderBottom()),
                  child: TextField(
                    maxLength: 16,
                    controller: registerViewModel.selectData == '大陆用户'
                        ? registerViewModel.psdC
                        : registerViewModel.abroadPswC,
                    keyboardType: TextInputType.visiblePassword,
                    obscureText: registerViewModel.selectData == '大陆用户'
                        ? !registerViewModel.passwordShow
                        : !registerViewModel.abroadPasswordShow,
                    decoration: InputDecoration(
                        counterText: '',
                        border: InputBorder.none,
                        hintText: '输入8位以上有效密码',
                        hintStyle: TextStyle(
                            fontSize: getSp(26), color: Color(0xFF999999)),
                        suffixIcon: IconButton(
                            icon: registerViewModel.selectData == '大陆用户'
                                ? (registerViewModel.passwordShow
                                    ? visibilityIco(color: Colors.grey)
                                    : visibilityOffIco(color: Colors.grey))
                                : (registerViewModel.abroadPasswordShow
                                    ? visibilityIco(color: Colors.grey)
                                    : visibilityOffIco(color: Colors.grey)),
                            onPressed: () {
                              registerViewModel.setPasswordShow();
                            })),
                  )),
            ),
          ],
        ),
        // 确认密码
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            RichText(
                text: TextSpan(
                    text: '*',
                    style: TextStyle(color: Colors.red, fontSize: 14),
                    children: [
                  TextSpan(
                      text: '确认密码',
                      style: TextStyle(fontSize: 14, color: Colors.black))
                ])),
            Expanded(
              child: Container(
                margin: EdgeInsets.only(
                    left: getWidthPx(30), right: getWidthPx(30)),
                height: 45,
                width: double.maxFinite,
                decoration: BoxDecoration(border: borderBottom()),
                child: TextField(
                  maxLength: 16,
                  controller: registerViewModel.selectData == '大陆用户'
                      ? registerViewModel.psdAgainC
                      : registerViewModel.abroadPswAgainC,
                  keyboardType: TextInputType.text,
                  obscureText: registerViewModel.selectData == '大陆用户'
                      ? !registerViewModel.password2Show
                      : !registerViewModel.abroadPassword2Show,
                  decoration: InputDecoration(
                      counterText: '',
                      border: InputBorder.none,
                      hintText: '请再次输入密码',
                      hintStyle: TextStyle(
                          fontSize: getSp(26), color: Color(0xFF999999)),
                      suffixIcon: IconButton(
                          icon: registerViewModel.selectData == '大陆用户'
                              ? (registerViewModel.password2Show
                                  ? visibilityIco(color: Colors.grey)
                                  : visibilityOffIco(color: Colors.grey))
                              : registerViewModel.abroadPassword2Show
                                  ? visibilityIco(color: Colors.grey)
                                  : visibilityOffIco(color: Colors.grey),
                          onPressed: () {
                            registerViewModel.setPasswordAgainShow();
                          })),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 国内获取获取验证码
  Container buildGetEmailCode() {
    return Container(
        child: AButton.normal(
            onPressed: registerViewModel.codeCountDownIsEnable
                ? () {
                    registerViewModel.getImgCode(myContext: context);
                  }
                : null,
            child: Text(
              registerViewModel.codeCountdownStr,
              style: TextStyle(fontSize: getSp(26)),
            ),
            color: AppTheme.themeBlue));
  }

//  国外获取获取邮箱验证码
  Container buildAbroadGetEmailCode() {
    return Container(
        child: AButton.normal(
            onPressed: registerViewModel.abroadCodeCountDownIsEnable
                ? () {
                    registerViewModel.getImgCode(myContext: context);
                  }
                : null,
            child: Text(
              registerViewModel.abroadCodeCountdownStr,
              style: TextStyle(fontSize: getSp(26)),
            ),
            color: AppTheme.themeBlue));
  }

  Border borderBottom() {
    return Border(bottom: BorderSide(color: Color(0xFFD7D7D7), width: 1));
  }

  // phone alert widget
  //
  // void showPhoneAlert() {
  //   showDialog(
  //       context: context,
  //       builder: (context) {
  //         return StatefulBuilder(builder: (context, Function setState) {
  //           return Material(
  //             color: Colors.black.withAlpha(100),
  //             child: Align(
  //               alignment: Alignment.center,
  //               child: Container(
  //                 width: getWidthPx(550),
  //                 height: getWidthPx(570),
  //                 decoration: BoxDecoration(
  //                     image: DecorationImage(
  //                   image: AssetImage(
  //                     'lib/assets/images/phone_alert_bg.png',
  //                   ),
  //                   fit: BoxFit.fill,
  //                 )),
  //                 child: Column(
  //                   children: [
  //                     Row(
  //                       mainAxisAlignment: MainAxisAlignment.end,
  //                       children: [
  //                         IconButton(
  //                             icon: Icon(
  //                               Icons.close,
  //                               size: 20,
  //                             ),
  //                             onPressed: () {
  //                               Navigator.pop(context);
  //                             })
  //                       ],
  //                     ),
  //                     Text(
  //                       '提示',
  //                       style:
  //                           TextStyle(fontSize: getSp(40), color: Colors.black),
  //                     ),
  //                     Padding(
  //                       padding: EdgeInsets.only(
  //                           top: getWidthPx(26), bottom: getWidthPx(80)),
  //                       child: Text(
  //                         '请提交手机号码，并联系公证员',
  //                         style: TextStyle(
  //                             fontSize: getSp(26), color: Colors.black),
  //                       ),
  //                     ),
  //                     Padding(
  //                       padding: EdgeInsets.symmetric(
  //                         horizontal: getWidthPx(38),
  //                       ),
  //                       child: Column(
  //                         mainAxisSize: MainAxisSize.min,
  //                         children: [
  //                           Padding(
  //                             padding: EdgeInsets.symmetric(
  //                                 horizontal: getWidthPx(40)),
  //                             child: GestureDetector(
  //                               onTap: () {
  //                                 AlertView.showPhoneAreaAlert(
  //                                     context,
  //                                     G.phoneArea
  //                                         .map((e) => e['dictCode'])
  //                                         .toList(), (value) {
  //                                   setState(() {
  //                                     registerViewModel.dropDownValueFirst =
  //                                         value;
  //                                   });
  //                                 });
  //                               },
  //                               child: Row(
  //                                 children: [
  //                                   Container(
  //                                     width: 60,
  //                                     height: 30,
  //                                     alignment: Alignment.centerLeft,
  //                                     child: Row(
  //                                       mainAxisSize: MainAxisSize.min,
  //                                       children: [
  //                                         Text(
  //                                           registerViewModel
  //                                                       .dropDownValueFirst !=
  //                                                   null
  //                                               ? "+${registerViewModel.dropDownValueFirst}"
  //                                               : '',
  //                                           style: TextStyle(
  //                                               fontSize: 13,
  //                                               color: Colors.black),
  //                                         ),
  //                                         Icon(
  //                                           Icons.arrow_drop_down_sharp,
  //                                           size: 20,
  //                                         )
  //                                       ],
  //                                     ),
  //                                   ),
  //                                   Expanded(
  //                                       child: TextField(
  //                                     controller: registerViewModel.phoneO,
  //                                     decoration: InputDecoration(
  //                                         hintText: "请输入手机号",
  //                                         hintStyle: TextStyle(
  //                                             fontSize: getSp(26),
  //                                             color: Color(0xFF999999)),
  //                                         border: InputBorder.none),
  //                                   ))
  //                                 ],
  //                               ),
  //                             ),
  //                           ),
  //                           Padding(
  //                             padding: EdgeInsets.symmetric(
  //                                 horizontal: getWidthPx(40)),
  //                             child: Divider(
  //                               color: Color(0xFFD7D7D7),
  //                             ),
  //                           )
  //                         ],
  //                       ),
  //                     ),
  //                     Padding(
  //                       padding: EdgeInsets.only(
  //                           bottom: getWidthPx(40), top: getWidthPx(20)),
  //                       child: GestureDetector(
  //                         onTap: () {
  //                           Map<String, dynamic> params = {
  //                             'areaCode':
  //                                 "${registerViewModel.dropDownValueFirst}",
  //                             'mobile': registerViewModel.phoneO.text,
  //                             'mobileType':
  //                                 int.parse(registerViewModel.selectData),
  //                           };
  //                           EasyLoading.show();
  //                           HomeApi.getSingleton()
  //                               .appContactAdmin(params)
  //                               .then((value) {
  //                             EasyLoading.dismiss();
  //                             if (value != null && value['code'] == 200) {
  //                               ToastUtil.showSuccessToast("提交成功！");
  //                               Future.delayed(const Duration(seconds: 1), () {
  //                                 Navigator.pop(context);
  //                                 showConnetAuthenAlert();
  //                               });
  //                             } else {
  //                               ToastUtil.showWarningToast(value['data']);
  //                             }
  //                           });
  //                         },
  //                         child: Container(
  //                           alignment: Alignment.center,
  //                           width: getWidthPx(400),
  //                           height: getWidthPx(72),
  //                           decoration: BoxDecoration(
  //                               image: DecorationImage(
  //                                   image: AssetImage(
  //                             'lib/assets/images/conner_btn_bg.png',
  //                           ))),
  //                           child: Text(
  //                             '确定',
  //                             style: TextStyle(
  //                                 fontSize: getSp(30), color: Colors.white),
  //                           ),
  //                         ),
  //                       ),
  //                     )
  //                   ],
  //                 ),
  //               ),
  //             ),
  //           );
  //         });
  //       });
  // }
  //
  // void showConnetAuthenAlert() {
  //   showDialog(
  //       context: context,
  //       builder: (context) {
  //         return Material(
  //           color: Colors.black.withAlpha(100),
  //           child: Align(
  //             alignment: Alignment.center,
  //             child: Container(
  //               width: getWidthPx(550),
  //               height: getWidthPx(570),
  //               decoration: BoxDecoration(
  //                   image: DecorationImage(
  //                 image: AssetImage(
  //                   'lib/assets/images/phone_alert_bg.png',
  //                 ),
  //                 fit: BoxFit.fill,
  //               )),
  //               child: Column(
  //                 children: [
  //                   Row(
  //                     mainAxisAlignment: MainAxisAlignment.end,
  //                     children: [
  //                       IconButton(
  //                           icon: Icon(
  //                             Icons.close,
  //                             size: 20,
  //                           ),
  //                           onPressed: () {
  //                             Navigator.pop(context);
  //                           })
  //                     ],
  //                   ),
  //                   Text(
  //                     '提示',
  //                     style:
  //                         TextStyle(fontSize: getSp(40), color: Colors.black),
  //                   ),
  //                   const Spacer(),
  //                   Text(
  //                     '公证员联系方式：4008001820',
  //                     style:
  //                         TextStyle(fontSize: getSp(26), color: Colors.black),
  //                   ),
  //                   const Spacer(),
  //                   Padding(
  //                     padding: EdgeInsets.only(
  //                         bottom: getWidthPx(40), top: getWidthPx(20)),
  //                     child: GestureDetector(
  //                       onTap: () {
  //                         Navigator.pop(context);
  //                         registerViewModel.service.call("4008001820");
  //                       },
  //                       child: Container(
  //                         alignment: Alignment.center,
  //                         width: getWidthPx(400),
  //                         height: getWidthPx(72),
  //                         decoration: BoxDecoration(
  //                             image: DecorationImage(
  //                                 image: AssetImage(
  //                           'lib/assets/images/conner_btn_bg.png',
  //                         ))),
  //                         child: Text(
  //                           '立即联系',
  //                           style: TextStyle(
  //                               fontSize: getSp(30), color: Colors.white),
  //                         ),
  //                       ),
  //                     ),
  //                   )
  //                 ],
  //               ),
  //             ),
  //           ),
  //         );
  //       });
  // }
}
