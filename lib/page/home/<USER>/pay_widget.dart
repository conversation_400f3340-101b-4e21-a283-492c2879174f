import 'package:flutter/material.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/home/<USER>/pay_view_model.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/utils/debounce_button.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:provider/provider.dart';

class PayWidget extends StatefulWidget {
  Map<String, dynamic> data;

  PayWidget({Key key, this.data}) : super(key: key);

  @override
  State<PayWidget> createState() => _PayWidgetState();
}

class _PayWidgetState extends BaseState<PayWidget>
    with SingleTickerProviderStateMixin {
  int position = 0;

  int selectIndex = 2;

  PageController pageController = PageController(initialPage: 0);

  PayViewModel _payViewModel;

  /// 屏幕宽度
  double myWidth;

  /// 支付方式的宽度
  double topWidth;

  /// 费用
  double fee;

  @override
  void initState() {
    super.initState();
    fee = widget.data['fee'];
  }

  /// 顶部header部分布局
  Widget _headerWidget() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding:  EdgeInsets.only(top: getWidthPx(80),left: getWidthPx(48)),
          child: Text(
            "待支付",
            style: TextStyle(fontSize: getSp(24), color: Color(0xFF666666)),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: getHeightPx(30),left: getWidthPx(54)),
          child: Text(
            '￥${fee ?? 0.0}',
            style: TextStyle(
                fontSize: getSp(60),
                fontWeight: FontWeight.w500,
                color: AppTheme.darkText),
          ),
        ),

        // Padding(
        //     padding:
        //         EdgeInsets.only(top: getHeightPx(100), bottom: getHeightPx(20)),
        //     child: Text("技术支持：南京国础科学技术研究院有限公司",
        //         style: TextStyle(fontSize: getSp(30), color: AppTheme.bg_f)))
      ],
    );
  }

  // /// 切换支付方式
  // Widget _payWayWidget() {
  //   return Container(
  //     width: topWidth,
  //     height: getWidthPx(80),
  //     decoration: BoxDecoration(
  //       color: AppTheme.white,
  //       borderRadius: BorderRadius.circular(getWidthPx(40)),
  //       boxShadow: [
  //         const BoxShadow(
  //           color: AppTheme.bg_d,
  //           offset: Offset(0.0, 2.0),
  //           blurRadius: 8.0,
  //           spreadRadius: 0.0,
  //         )
  //       ],
  //     ),
  //     child: Stack(
  //       children: [
  //         Row(
  //           children: [
  //             InkWell(
  //               onTap: () {
  //                 setState(() {
  //                   position = 0;
  //                   pageController.jumpToPage(position);
  //                 });
  //               },
  //               child: Container(
  //                 width: topWidth / 2,
  //                 height: getWidthPx(80),
  //                 decoration: BoxDecoration(
  //                   color: position == 0 ? AppTheme.themeBlue : AppTheme.white,
  //                   borderRadius: BorderRadius.circular(getWidthPx(40)),
  //                   boxShadow: position == 0
  //                       ? [
  //                           const BoxShadow(
  //                             color: AppTheme.bg_d,
  //                             offset: Offset(0.0, 2.0),
  //                             blurRadius: 8.0,
  //                             spreadRadius: 0.0,
  //                           )
  //                         ]
  //                       : null,
  //                 ),
  //                 child: Center(
  //                   child: Text(
  //                     "普通支付",
  //                     style: TextStyle(
  //                         fontSize: getSp(30),
  //                         color: position == 0
  //                             ? AppTheme.white
  //                             : AppTheme.darkText),
  //                   ),
  //                 ),
  //               ),
  //             ),
  //             InkWell(
  //               onTap: () {
  //                 setState(() {
  //                   position = 1;
  //                   pageController.jumpToPage(position);
  //                 });
  //               },
  //               child: Container(
  //                 width: topWidth / 2,
  //                 height: getWidthPx(80),
  //                 decoration: BoxDecoration(
  //                   color: position == 1 ? AppTheme.themeBlue : AppTheme.white,
  //                   borderRadius: BorderRadius.circular(getWidthPx(40)),
  //                   boxShadow: position == 1
  //                       ? [
  //                           const BoxShadow(
  //                             color: AppTheme.bg_d,
  //                             offset: Offset(0.0, 2.0),
  //                             blurRadius: 8.0,
  //                             spreadRadius: 0.0,
  //                           )
  //                         ]
  //                       : null,
  //                 ),
  //                 child: Center(
  //                   child: Text(
  //                     "数币支付",
  //                     style: TextStyle(
  //                         fontSize: getSp(30),
  //                         color: position == 1
  //                             ? AppTheme.white
  //                             : AppTheme.darkText),
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           ],
  //         )
  //       ],
  //     ),
  //   );
  // }

  /// 底部支付方式展示
  Widget _showPayWayWidget() {
    return Column(
      children: [
        // _commonPay(),
        _coinPay(),
      ],
    );
  }

  /// 普通支付
  Widget _commonPay() {
    return Container(
      margin: EdgeInsets.only(
          left: getWidthPx(30), right: getWidthPx(30), top: getWidthPx(60)),
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(getWidthPx(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: getWidthPx(88),
            padding: EdgeInsets.only(left: getWidthPx(30)),
            alignment: Alignment.centerLeft,
            child: Text("普通支付",style: TextStyle(
              fontSize: getSp(30),
              color: AppTheme.Text_max
            ),),
          ),
          Divider(indent: getWidthPx(30),),
          MediaQuery.removePadding(
            context: context,
            removeBottom: true,
            removeTop: true,
            child: ListView(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _commonPayItem(0, '微信支付', 'lib/assets/images/wechat.png'),
                _commonPayItem(1,'支付宝支付','lib/assets/images/alipay.png'),
                // _commonPayItem(2, '银行卡支付', 'lib/assets/images/union_card.png'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 普通支付item
  Widget _commonPayItem(int index, String title, String imgName) {
    return InkWell(
      onTap: () {
        setState(() {
          selectIndex = index;
        });
      },
      child: Container(
        height: getWidthPx(120),
        padding: EdgeInsets.symmetric(horizontal: getWidthPx(30)),
        color: Colors.white,
        child: Column(
          children: [
            Expanded(
              child: Row(
                children: [
                  Image.asset(
                    imgName,
                    width: getWidthPx(60),
                    height: getWidthPx(60),
                  ),
                  Padding(
                      padding: EdgeInsets.only(left: getWidthPx(20)),
                      child: Text(
                        title,
                        style:
                            TextStyle(fontSize: getSp(30), color: AppTheme.darkText),
                      )),
                  const Spacer(),
                  Icon(
                      selectIndex == index
                          ? Icons.check_circle_rounded
                          : Icons.circle,
                      color:
                          selectIndex == index ? AppTheme.themeBlue : AppTheme.bg_b,
                      size: 20),
                ],
              ),
            ),
            Offstage(
              offstage: index == 1,
                child: Divider(indent: getWidthPx(70),))
          ],
        ),
      ),
    );
  }

  /// 数币支付
  Widget _coinPay() {
    return Container(
      margin: EdgeInsets.only(
          left: getWidthPx(30), right: getWidthPx(30), top: getWidthPx(30)),
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(getWidthPx(20)),
      ),
      child: Column(
        children: [
          Container(
            height: getWidthPx(88),
            padding: EdgeInsets.only(left: getWidthPx(30)),
            alignment: Alignment.centerLeft,
            child: Text("其他支付",style: TextStyle(
                fontSize: getSp(30),
                color: AppTheme.Text_max
            ),),
          ),
          Divider(indent: getWidthPx(30),),
          MediaQuery.removePadding(
            context: context,
            removeBottom: true,
            removeTop: true,
            child: ListView(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                GestureDetector(child: _coinPayItem(),onTap: (){
                  setState(() {
                    selectIndex = 2;
                  });
                },),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 数币支付item
  Widget _coinPayItem() {
    return Container(
      height: getWidthPx(120),
      padding: EdgeInsets.symmetric(horizontal: getWidthPx(30)),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(10))
      ),
      child: Row(
        children: [
          Image.asset(
            'lib/assets/images/tongqianicon.png',
            width: 30,
            height: 30,
          ),
          Padding(
              padding: EdgeInsets.only(left: getWidthPx(20)),
              child: Text(
                '数币支付',
                style: TextStyle(fontSize: getSp(30), color: AppTheme.darkText),
              )),
          const Spacer(),
          Icon(selectIndex == 2
              ? Icons.check_circle_rounded
              : Icons.circle,
              color:
              selectIndex == 2 ? AppTheme.themeBlue : AppTheme.bg_b, size: 20),
        ],
      ),
    );
  }

  //
  Widget _bodyWidget() {
    return  Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _headerWidget(),
        _showPayWayWidget(),
       const Spacer(),
        Center(
          child: Text("技术支持:南京国础科学技术研究院有限公司",style: TextStyle(
            fontSize: getSp(24),
            color: Color(0xFF666666)
          ),),
        ),
        Padding(
          padding:  EdgeInsets.only(left: getWidthPx(30),right: getWidthPx(30),bottom: getWidthPx(60),top: getWidthPx(50)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex:1,
                child: DebounceButton(
                  isEnable: _payViewModel.isEnable,
                  clickTap: () {
                    if(selectIndex == 2){
                      _payViewModel.addOrderByShuBi(context);
                    }else{
                      _payViewModel.addOrder(context);
                    }
                  },
                  padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                  borderRadius: BorderRadius.circular(10),
                  child: Text(
                    '确认',
                    style: TextStyle(
                        color: AppTheme.white,
                        fontSize: getSp(32),
                        fontWeight: FontWeight.w500),
                  ),
                ),
              ),
              SizedBox(width: getWidthPx(30),),
              Expanded(
                flex: 1,
                child: DebounceButton(
                  isEnable:true,
                  clickTap: () {
                   G.pop();
                  },
                  padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                  borderRadius: BorderRadius.circular(10),
                  backgroundColor: Colors.white,
                  border: Border.all(
                    color: Color(0xFFB9BBC2),
                    width: 0.5
                  ),
                  child: Text(
                    '取消',
                    style: TextStyle(
                        color: AppTheme.darkerText,
                        fontSize: getSp(32),
                        fontWeight: FontWeight.w500),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    myWidth = MediaQuery.of(context).size.width;
    topWidth = myWidth - 20;
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '收银台',
          style: TextStyle(color: AppTheme.darkText),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Color.fromRGBO(204, 230, 244, 1),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_outlined,
            color: AppTheme.themeBlue,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Consumer<UserViewModel>(
        builder: (context, userModel, child) {
          return ProviderWidget(
            model: PayViewModel(),
            onModelReady: (payViewModel) {
              _payViewModel = payViewModel;
              _payViewModel.userViewModel = userModel;
              _payViewModel.callBackUrl = widget.data['callBackUrl'];
              _payViewModel.unitGuid = widget.data['unitGuid'];
              _payViewModel.payLogo = widget.data['payLogo'];
              _payViewModel.orderNo = widget.data['orderNo'];
            },
            builder: (context, payViewModel, child) {
              return _bodyWidget();
            },
          );
        },
      ),
    );
  }
}
