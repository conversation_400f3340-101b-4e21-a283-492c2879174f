/*
 * @Author: <EMAIL> WSBwsb123!@#
 * @Date: 2023-10-24 10:40:01
 * @LastEditors: <EMAIL> WSBwsb123!@#
 * @LastEditTime: 2023-10-24 10:41:28
 * @FilePath: /remouldApp/lib/page/home/<USER>/notary_office_list_view_model.dart
 * @Description: 
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */

import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';

class NotaryOfficeListViewModel extends SingleViewStateModel {
  @override
  Future loadData() {
    // throw UnimplementedError();
  }

  @override
  onCompleted(data) {
    throw UnimplementedError();
  }

}
