/*
 * @Author: 王士博 <EMAIL>
 * @Date: 2023-07-25 09:36:47
 * @LastEditors: 王士博 <EMAIL>
 * @LastEditTime: 2023-08-15 17:27:11
 * @FilePath: /sc-remotenotarization-app/lib/page/home/<USER>/webview_page.dart
 * @Description: 
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */

import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:notarization_station_app/utils/javascript_channel_method.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebviewPage extends StatefulWidget {
  // 加载的url
  final String url;
  // 数币支付的需要的订单编号
  final String orderNo;
  // 数币支付的需要的订单来源 1:自助订单 2：视频相关
  final int source;
  const WebviewPage({Key key, this.url, this.orderNo, this.source})
      : super(key: key);

  @override
  State<WebviewPage> createState() => _WebviewPageState();
}

class _WebviewPageState extends BaseState<WebviewPage> {
  final Completer<WebViewController> _controller =
      Completer<WebViewController>();

  Timer _timer;

  // 五分钟查询时间
  int _count = 300;

  // 间隔时长
  int _duration = 3;

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    if (Platform.isAndroid) {
      WebView.platform = SurfaceAndroidWebView();
    }
  }

  /// 数币支付订单状态查询
  getShuBiData(BuildContext context, UserViewModel userViewModel,
      String orderNo, int source) async {
    var map = {
      "orderNo": orderNo,
    };
    HomeApi.getSingleton().queryShuBiOrder(map, errorCallBack: (e) {
      // ToastUtil.showSuccessToast("网络出错，请稍后再试");
      // _timer.cancel();
      // if (source == 1) {
      //   userViewModel.currentIndex = 1;
      //   Navigator.pushNamedAndRemoveUntil(
      //       context, RoutePaths.HomeIndex, (route) => false);
      // } else {
      //   G.pop();
      // }
    }, checkNet: true).then((value) {
      wjPrint("33333333333333$value");
      if (value["code"] == 200) {
        // payStatus	1、成功 2、未支付 3、支付超时 4、支付失败
        if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '1') {
          ToastUtil.showSuccessToast("支付成功");
          _timer.cancel();
          Future.delayed(const Duration(seconds: 2), () {
            if (source == 1) {
              userViewModel.currentIndex = 1;
              Navigator.pushNamedAndRemoveUntil(
                  context, RoutePaths.HomeIndex, (route) => false);
            } else {
              G.pop();
            }
          });
        } else if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '2') {
        } else if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '3') {
          ToastUtil.showSuccessToast("支付超时", second: 3);
          _timer.cancel();
          Future.delayed(const Duration(seconds: 2), () {
            if (source == 1) {
              userViewModel.currentIndex = 1;
              Navigator.pushNamedAndRemoveUntil(
                  context, RoutePaths.HomeIndex, (route) => false);
            } else {
              G.pop();
            }
          });
        } else {
          ToastUtil.showSuccessToast("支付失败", second: 3);
          _timer.cancel();
          Future.delayed(const Duration(seconds: 2), () {
            if (source == 1) {
              userViewModel.currentIndex = 1;
              Navigator.pushNamedAndRemoveUntil(
                  context, RoutePaths.HomeIndex, (route) => false);
            } else {
              G.pop();
            }
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: commonAppBar(title: ''),
      body: Consumer<UserViewModel>(
        builder: (context, userModel, child) {
          if (_timer != null) {
            _timer.cancel();
          }
          _count = 300;
          _timer = Timer.periodic(Duration(seconds: _duration), (timer) {
            _count -= _duration;
            getShuBiData(context, userModel, widget.orderNo, widget.source);
            if (_count <= 0) {
              _timer.cancel();
              ToastUtil.showToast("支付超时");
              if (widget.source == 1) {
                userModel.currentIndex = 1;
                Navigator.pushNamedAndRemoveUntil(
                    context, RoutePaths.HomeIndex, (route) => false);
              } else {
                G.pop();
              }
            }
          });
          return WebView(
            initialUrl: widget.url,
            javascriptMode: JavascriptMode.unrestricted,
            javascriptChannels: JavascriptChannelMethod().loadJavascriptChannel(
                context, userModel, widget.orderNo, widget.source, null),
            onWebViewCreated: (WebViewController webViewController) {
              _controller.complete(webViewController);
            },
            navigationDelegate: (nav)  {
              print("nav===========$nav");
              if(nav.url.contains("https://ch5.dcep.ccb.com/index")){
                G.pop();
                return NavigationDecision.prevent;
              }

              return NavigationDecision.navigate;

            },
          );
        },
      ),
    );
  }
}
