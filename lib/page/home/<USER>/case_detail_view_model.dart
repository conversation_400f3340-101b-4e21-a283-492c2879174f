/*
 * @Author: <EMAIL> WSBwsb123!@#
 * @Date: 2023-10-23 15:42:59
 * @LastEditors: <EMAIL> WSBwsb123!@#
 * @LastEditTime: 2023-10-23 15:44:04
 * @FilePath: /remouldApp/lib/page/home/<USER>/case_detail_view_model.dart
 * @Description: 案件详情
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */

import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';

class CaseDetailViewModel extends SingleViewStateModel {
  @override
  Future loadData() {
    // throw UnimplementedError();
  }

  @override
  onCompleted(data) {
    throw UnimplementedError();
  }
}
