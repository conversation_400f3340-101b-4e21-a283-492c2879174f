import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/helper_api.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/debounce_button.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:notarization_station_app/utils/javascript_channel_method.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../appTheme.dart';

class PayAlertView extends StatefulWidget {
  final Map data;

// 数币支付点击完成时，回到自己的 webView 界面三秒倒计时回调函数
  final Function callBack;

// 轮询 300 秒后超时回调函数
  final Function payTimeCallBack;

// 页面关闭回调函数
  final ValueChanged cancelCallBack;

// 成功下单后的回调函数
  final ValueChanged creatOrderSuccessCallBack;

  const PayAlertView(
      {Key key,
      this.data,
      this.callBack,
      this.payTimeCallBack,
      this.cancelCallBack,
      this.creatOrderSuccessCallBack})
      : super(key: key);

  @override
  State<PayAlertView> createState() => _PayAlertViewState();
}

class _PayAlertViewState extends BaseState<PayAlertView> {
  int position = 0;

  int selectIndex = 0;

  PageController pageController = PageController(initialPage: 0);

  /// 屏幕宽度
  double myWidth;

  /// 支付方式的宽度
  double topWidth;

  /// 费用
  double fee;

  bool isEnable = true;

  /// 回调地址
  String callBackUrl;

  /// 支付类型 1、公证费用 2、补缴费用
  int payLogo;

  /// 订单主键
  String unitGuid;

  /// 订单编号
  String orderNo;

  /// 保存临时变量
  String bnkPyOrdrNo;

  String cstmPyOrdrNo;

  String pyUrl;

  Timer _myTimer;

  /// 设置轮巡时间
  int _totalSecond = 300;

  bool isShowWeb = false;

  /// web 地址
  String webUrl;

  UserViewModel userViewModel;

  int webCount = 0;

  @override
  void dispose() {
    if (_myTimer != null) _myTimer.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    fee = widget.data['fee'];
    unitGuid = widget.data['unitGuid'];
    callBackUrl = widget.data['callBackUrl'];
    payLogo = widget.data['payLogo'];
    orderNo = widget.data['orderNo'];
    if (Platform.isAndroid)  WebView.platform = SurfaceAndroidWebView();
  }

  /// 下单
  void addOrder(BuildContext context) {
    isEnable = false;
    setState(() {});
    EasyLoading.show();
    String source = '';
    if (Platform.isIOS) {
      source = '2';
    } else if (Platform.isAndroid) {
      source = '1';
    }
    Map<String, dynamic> data = {
      "callBackUrl": callBackUrl,
      'payLogo': payLogo,
      "unitGuid": unitGuid,
      'pyChnlCd': "2",
      'paySources': source
    };
    HomeApi.getSingleton().placeOrder(data, errorCallBack: (e) {
      isEnable = true;
      EasyLoading.dismiss();
      setState(() {});
    }).then((value) {
      isEnable = true;
      EasyLoading.dismiss();
      setState(() {});
      if (value["code"] == 200) {
        if (value['data']['pyUrl'] != null) {
          cstmPyOrdrNo = value['data']['cstmPyOrdrNo'];
          bnkPyOrdrNo = value['data']['bnkPyOrdrNo'];
          setState(() {
            isShowWeb = true;
            webUrl = value["data"]["pyUrl"];
          });
          // Ccbpay.payWitWXApi(mainServiceHttp: value["data"]["pyUrl"])
          //     .then((value) {
          //   wjPrint("支付结果回调结果：$value");
          // });
          getPayStatus(context, eventCallBack: (context) {
            getData(context);
          });
        } else {
          ToastUtil.showErrorToast("获取得地址链接为空");
        }
      } else if (value["code"] == 11045) {
        ToastUtil.showSuccessToast(value['message']);
        G.pop();
        // userViewModel.currentIndex = 1;
      } else {
        ToastUtil.showErrorToast(value['message']);
      }
    });
  }

  /// 轮巡查询订单状态
  void getPayStatus(BuildContext context, {Function eventCallBack}) {
    if (_myTimer != null) {
      _myTimer.cancel();
    }
    _totalSecond = 300;
    _myTimer = Timer.periodic(Duration(seconds: 3), (timer) {
      _totalSecond -= 3;
      if (_totalSecond <= 0) {
        _myTimer.cancel();
        widget.payTimeCallBack();
      } else {
        eventCallBack(context);
      }
    });
  }

  getData(BuildContext context) async {
    var map = {
      "bnkPyOrdrNo": bnkPyOrdrNo,
      'cstmPyOrdrNo': cstmPyOrdrNo,
    };
    HelperApi.getSingleton()
        .queryOrderInfo(map, errorCallBack: (e) {}, checkNet: false)
        .then((value) {
      wjPrint("33333333333333$value");
      if (value["code"] == 200) {
        if (value['data'] != null &&
            value['data']['ordrStCd'] != null &&
            value['data']['ordrStCd'] == '2') {
          ToastUtil.showSuccessToast("支付成功");
          // userViewModel.currentIndex = 1;
          Navigator.pushNamedAndRemoveUntil(
              context, RoutePaths.HomeIndex, (route) => false);
          _myTimer.cancel();
        }
      }
    });
  }

  /// 数币支付下单
  void addOrderByShuBi(BuildContext context) {
    isEnable = false;
    setState(() {});
    EasyLoading.show();
    String source = '';
    if (Platform.isIOS) {
      source = '2';
    } else if (Platform.isAndroid) {
      source = '1';
    }
    Map<String, dynamic> data = {
      "callBackUrl": callBackUrl,
      'payLogo': payLogo,
      "unitGuid": unitGuid,
      'pyChnlCd': "2",
      'paySources': source
    };
    HomeApi.getSingleton().ccBisPlaceOrder(data, errorCallBack: (e) {
      isEnable = true;
      EasyLoading.dismiss();
      widget.creatOrderSuccessCallBack({"isSuccess": false});
      setState(() {});
    }).then((value) {
      isEnable = true;
      EasyLoading.dismiss();
      setState(() {});
      if (value["code"] == 200) {
        if (value['data'] != null) {
          setState(() {
            isShowWeb = true;
            webUrl = value["data"]["payUrl"];
          });
          widget.creatOrderSuccessCallBack({
            "isSuccess": true,
            "linkOverTime": value['data']['linkOverTime']
          });
          getPayStatus(context, eventCallBack: (context) {
            getShuBiData(context);
          });
        } else {
          widget.creatOrderSuccessCallBack({"isSuccess": false});
          ToastUtil.showErrorToast("获取得地址链接为空");
        }
      } else if (value["code"] == 11045) {
        widget.creatOrderSuccessCallBack({"isSuccess": false});
        ToastUtil.showSuccessToast(value['message']);
      } else {
        widget.creatOrderSuccessCallBack({"isSuccess": false});
        ToastUtil.showErrorToast(value['message']);
      }
    });
  }

  /// 数币支付订单状态查询
  getShuBiData(BuildContext context) async {
    var map = {
      "orderNo": orderNo,
    };
    HomeApi.getSingleton().queryShuBiOrder(map, errorCallBack: (e) {
      // ToastUtil.showToast("网络异常，请稍后再试");
    }, checkNet: true).then((value) {
      wjPrint("33333333333333$value");
      if (value["code"] == 200) {
        // payStatus	1、成功 2、未支付 3、支付超时 4、支付失败
        if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '1') {
          ToastUtil.showSuccessToast("支付成功");
          _myTimer.cancel();
          Future.delayed(const Duration(seconds: 2), () {
            G.pop();
          });
        } else if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '2') {
          // ToastUtil.showSuccessToast("未支付");
          // _myTimer.cancel();
        } else if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '3') {
          ToastUtil.showSuccessToast("支付超时");
          _myTimer.cancel();
          Future.delayed(const Duration(seconds: 2), () {
            G.pop();
          });
        } else {
          ToastUtil.showSuccessToast("支付失败");
          _myTimer.cancel();
          Future.delayed(const Duration(seconds: 2), () {
            G.pop();
          });
        }
      }
    });
  }

  /// 顶部header部分布局
  Widget _headerWidget() {
    return Container(
      width: double.maxFinite,
      height: getHeightPx(350),
      color: Color.fromRGBO(204, 230, 244, 1),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 30),
            child: Text(
              '￥${fee ?? 0.0}',
              style: TextStyle(
                  fontSize: 30,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.darkText),
            ),
          ),
          Text(
            "待支付",
            style: TextStyle(fontSize: 15, color: AppTheme.bg_f),
          ),
          Padding(
              padding: EdgeInsets.only(top: 50, bottom: 10),
              child: Text("技术支持：南京国础科学技术研究院有限公司",
                  style: TextStyle(fontSize: 15, color: AppTheme.bg_f)))
        ],
      ),
    );
  }

  /// 切换支付方式
  Widget _payWayWidget() {
    return Container(
      width: topWidth,
      height: 40,
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          const BoxShadow(
            color: AppTheme.bg_d,
            offset: Offset(0.0, 2.0),
            blurRadius: 8.0,
            spreadRadius: 0.0,
          )
        ],
      ),
      child: Stack(
        children: [
          Row(
            children: [
              InkWell(
                onTap: () {
                  setState(() {
                    position = 0;
                    pageController.jumpToPage(position);
                  });
                },
                child: Container(
                  width: topWidth / 2,
                  height: getWidthPx(80),
                  decoration: BoxDecoration(
                    color: position == 0 ? AppTheme.themeBlue : AppTheme.white,
                    borderRadius: BorderRadius.circular(getWidthPx(40)),
                    boxShadow: position == 0
                        ? [
                            const BoxShadow(
                              color: AppTheme.bg_d,
                              offset: Offset(0.0, 2.0),
                              blurRadius: 8.0,
                              spreadRadius: 0.0,
                            )
                          ]
                        : null,
                  ),
                  child: Center(
                    child: Text(
                      "传统支付",
                      style: TextStyle(
                          fontSize: getSp(30),
                          color: position == 0
                              ? AppTheme.white
                              : AppTheme.darkText),
                    ),
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  setState(() {
                    position = 1;
                    pageController.jumpToPage(position);
                  });
                },
                child: Container(
                  width: topWidth / 2,
                  height: getWidthPx(80),
                  decoration: BoxDecoration(
                    color: position == 1 ? AppTheme.themeBlue : AppTheme.white,
                    borderRadius: BorderRadius.circular(getWidthPx(40)),
                    boxShadow: position == 1
                        ? [
                            const BoxShadow(
                              color: AppTheme.bg_d,
                              offset: Offset(0.0, 2.0),
                              blurRadius: 8.0,
                              spreadRadius: 0.0,
                            )
                          ]
                        : null,
                  ),
                  child: Center(
                    child: Text(
                      "数币支付",
                      style: TextStyle(
                          fontSize: getSp(30),
                          color: position == 1
                              ? AppTheme.white
                              : AppTheme.darkText),
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  // getPosition(double dx){
  //   setState(() {
  //     if((dx - (MediaQuery.of(context).size.width - getWidthPx(300))/2) > getWidthPx(300) * 0.8) {
  //       setState(() {
  //         position = getWidthPx(300);
  //       });
  //     }else{
  //       setState(() {
  //         position = 0;
  //       });
  //     }
  //   });
  // }

  /// 底部支付方式展示
  Widget _showPayWayWidget() {
    return PageView(
      controller: pageController,
      scrollDirection: Axis.horizontal,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        // _commonPay(),
        _coinPay(),
      ],
    );
  }

  /// 传统支付
  Widget _commonPay() {
    return Container(
      margin: EdgeInsets.only(
          left: getWidthPx(30), right: getWidthPx(30), top: getWidthPx(40)),
      width: double.maxFinite,
      height: getHeightPx(360),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(getWidthPx(20)),
      ),
      child: MediaQuery.removePadding(
        context: context,
        removeBottom: true,
        removeTop: true,
        child: ListView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            _commonPayItem(0, '微信支付', 'lib/assets/images/wechat.png'),
            _commonPayItem(1, '支付宝支付', 'lib/assets/images/alipay.png'),
            _commonPayItem(2, '银行卡支付', 'lib/assets/images/union_card.png'),
          ],
        ),
      ),
    );
  }

  /// 传统支付item
  Widget _commonPayItem(int index, String title, String imgName) {
    return InkWell(
      onTap: () {
        setState(() {
          selectIndex = index;
        });
      },
      child: Container(
        height: getWidthPx(120),
        padding: EdgeInsets.symmetric(horizontal: getWidthPx(30)),
        color: Colors.white,
        child: Row(
          children: [
            Image.asset(
              imgName,
              width: getWidthPx(60),
              height: getWidthPx(60),
            ),
            Padding(
                padding: EdgeInsets.only(left: getWidthPx(20)),
                child: Text(
                  title,
                  style:
                      TextStyle(fontSize: getSp(30), color: AppTheme.darkText),
                )),
            const Spacer(),
            Icon(
                selectIndex == index
                    ? Icons.check_circle_rounded
                    : Icons.circle,
                color:
                    selectIndex == index ? AppTheme.themeBlue : AppTheme.bg_b,
                size: 20),
          ],
        ),
      ),
    );
  }

  /// 数币支付
  Widget _coinPay() {
    return Container(
      margin: EdgeInsets.only(
          left: getWidthPx(30), right: getWidthPx(30), top: getWidthPx(40)),
      width: double.infinity,
      height: getHeightPx(120),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(getWidthPx(20)),
      ),
      child: MediaQuery.removePadding(
        context: context,
        removeBottom: true,
        removeTop: true,
        child: ListView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            _coinPayItem(),
          ],
        ),
      ),
    );
  }

  /// 数币支付item
  Widget _coinPayItem() {
    return Container(
      height: getWidthPx(120),
      color: Colors.transparent,
      padding: EdgeInsets.symmetric(horizontal: getWidthPx(30)),
      child: Row(
        children: [
          Image.asset(
            'lib/assets/images/tongqianicon.png',
            width: 30,
            height: 30,
          ),
          Padding(
              padding: EdgeInsets.only(left: getWidthPx(20)),
              child: Text(
                '数币支付',
                style: TextStyle(fontSize: getSp(30), color: AppTheme.darkText),
              )),
          const Spacer(),
          Icon(Icons.check_circle_rounded, color: AppTheme.themeBlue, size: 25),
        ],
      ),
    );
  }

  //
  Widget _bodyWidget() {
    return Stack(
      children: [
        Container(
          width: double.maxFinite,
          height: getHeightPx(350),
          color: Color.fromRGBO(204, 230, 244, 1),
        ),
        Column(
          children: [
            _headerWidget(),
            // _payWayWidget(),
            Expanded(child: _showPayWayWidget()),
            DebounceButton(
              isEnable: isEnable,
              clickTap: () {
                // if(position==0) {
                //  addOrder(context);
                // }else{
                addOrderByShuBi(context);
                // }
              },
              margin: EdgeInsets.fromLTRB(getWidthPx(80), 0, getWidthPx(80),
                  MediaQuery.of(context).padding.bottom + getWidthPx(20)),
              padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
              borderRadius: BorderRadius.circular(30),
              child: Text(
                '确认',
                style: TextStyle(
                    color: AppTheme.white,
                    fontSize: getSp(32),
                    fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 展示 webView
  Widget _showWebView() {
    webCount++;
    wjPrint("webCount------$webCount");
    return WebView(
      initialUrl: webUrl,
      javascriptMode: JavascriptMode.unrestricted,
      javascriptChannels: JavascriptChannelMethod().loadJavascriptChannel(
          context, userViewModel, orderNo, 2, widget.callBack),
      navigationDelegate: (nav)  {
        print("nav===========$nav");
        if(nav.url.contains("https://ch5.dcep.ccb.com/index")){
          widget.cancelCallBack(isShowWeb);
          G.pop();
          return NavigationDecision.prevent;
        }

        return NavigationDecision.navigate;

      },
    );
  }

  /// 展示 webView
  // Widget _showWebView() {
  //   webCount++;
  //   wjPrint("webCount------$webCount");
  //   return InAppWebView(
  //     initialUrlRequest: URLRequest(url: Uri.parse(webUrl)),
  //     initialOptions: InAppWebViewGroupOptions(
  //         android: AndroidInAppWebViewOptions(
  //       useHybridComposition: false,
  //       cacheMode: AndroidCacheMode.LOAD_NO_CACHE,
  //       clearSessionCache: true,
  //           needInitialFocus: true,
  //     )),
  //     onLoadStart: (controller, url) {
  //       print("onLoadStart------url:$url");
  //       if (url.toString().contains("https://ch5.dcep.ccb.com/index")) {
  //         G.pop();
  //         widget.cancelCallBack(isShowWeb);
  //       }
  //     },
  //
  //     onLoadStop: (controller, url) {
  //       print("onLoadStop------url:$url");
  //     },
  //     onWindowFocus: (controller){
  //       controller.requestFocusNodeHref();
  //     },
  //     onWebViewCreated: (webviewContronller) async {
  //   //     await webviewContronller.evaluateJavascript(source: """
  //   //   document.querySelector('input').focus();
  //   // """);
  //       webviewContronller.getUrl().then((value) => FocusScope.of(context).requestFocus(FocusNode()));
  //       // webviewContronller.setOptions(
  //       //     options: InAppWebViewGroupOptions(
  //       //         android: AndroidInAppWebViewOptions(
  //       //           useHybridComposition: false,
  //       //           cacheMode: AndroidCacheMode.LOAD_NO_CACHE,
  //       //           clearSessionCache: true,
  //       //         )));
  //     },
  //     onLoadHttpError: (controller, uri, count, error) {
  //       wjPrint("uri-------$uri,count-------$count,error--------$error");
  //     },
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    myWidth = MediaQuery.of(context).size.width;
    topWidth = myWidth - 20;
    return Consumer<UserViewModel>(builder: (context, userModel, child) {
      userViewModel = userModel;
      return Scaffold(
        appBar: AppBar(
          title: Text(
            '收银台',
            style: TextStyle(color: AppTheme.darkText),
          ),
          centerTitle: true,
          elevation: 0,
          backgroundColor: Color.fromRGBO(204, 230, 244, 1),
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios_outlined,
              color: AppTheme.themeBlue,
            ),
            onPressed: () {
              widget.cancelCallBack(isShowWeb);
              G.pop();
            },
          ),
        ),
        body: isShowWeb ? _showWebView() : _bodyWidget(),
      );
    });
  }
}
