import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/components/throttleUtil.dart';
import 'package:notarization_station_app/page/home/<USER>/confirmApplyInfo_vm.dart';
import 'package:notarization_station_app/page/home/<USER>/num_change_widget.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/utils/debounce_button.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:provider/provider.dart';

import '../../appTheme.dart';

class ConfirmApplyInfoPage extends StatefulWidget {
  final arguments;
  const ConfirmApplyInfoPage({Key key, this.arguments}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ConfirmApplyInfoPageState();
  }
}

class ConfirmApplyInfoPageState extends BaseState<ConfirmApplyInfoPage>
    with AutomaticKeepAliveClientMixin {
  ConfirmApplyInfoModel confirmApplyModel;

  @override
  void initState() {
    super.initState();
  }

  //引入防抖
  ThrottleUtil throttleUtil = ThrottleUtil();

  Future<void> changeAddress() async {
    await showDialog<Map>(
        context: context,
        builder: (BuildContext context) {
          return SimpleDialog(
            title: Text('请选择邮寄地址'),
            children: confirmApplyModel.addressList.map((e) {
              return SimpleDialogOption(
                onPressed: () {
                  confirmApplyModel.orderAddressUpdate(e);
                  Navigator.pop(context);
                  confirmApplyModel.chooseAddress = e["address"];
                  confirmApplyModel.takeAddress = e["address"];
                  confirmApplyModel.unitId = e["unitGuid"];
                  confirmApplyModel.takePhone = e["phone"];
                },
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 6),
                  child: Text(e["address"]),
                ),
              );
            }).toList(),
          );
        });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: commonAppBar(title: "确认订单信息"),
        body: Consumer<UserViewModel>(
          builder: (ctx, userModel, child) {
            return ProviderWidget<ConfirmApplyInfoModel>(
              model: ConfirmApplyInfoModel(userModel, widget.arguments),
              onModelReady: (model) {
                confirmApplyModel = model;
                confirmApplyModel.mobile.text = userModel.mobile;
                confirmApplyModel.number.text = "1";
                model.getAddressList();
              },
              builder: (ctx, homeModel, child) {
                return SingleChildScrollView(
                  child: Column(
                    children: <Widget>[
                      Container(
                          color: Colors.white,
                          child: Column(
                            children: <Widget>[
                              SizedBox(
                                height: getHeightPx(10),
                              ),
                              Container(
                                width: double.maxFinite,
                                child: Image.asset(
                                  "lib/assets/images/stepThree.png",
                                  fit: BoxFit.fill,
                                ),
                              ),
                              SizedBox(
                                height: getHeightPx(40),
                              ),
                              Row(
                                children: <Widget>[
                                  Padding(
                                      padding: EdgeInsets.only(
                                          left: getWidthPx(40),
                                          right: getWidthPx(40)),
                                      child: Text(
                                        "申请信息",
                                        style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w700),
                                      )),
                                ],
                              ),
                              widget.arguments["isAgent"] == 0
                                  ? Row(
                                      children: <Widget>[
                                        Container(
                                          margin: EdgeInsets.only(
                                              bottom: getWidthPx(20),
                                              left: getWidthPx(40),
                                              top: getWidthPx(20)),
                                          height: getWidthPx(60),
                                          width: getWidthPx(120),
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                                width: 1,
                                                color: Colors.blueAccent),
                                            borderRadius: BorderRadius.circular(
                                                getWidthPx(40)),
                                          ),
                                          child: Text(
                                            "申请人",
                                            style: TextStyle(
                                                color: Colors.blueAccent),
                                          ),
                                        ),
                                        SizedBox(
                                          width: getWidthPx(20),
                                        ),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: <Widget>[
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: <Widget>[
                                                  Text(
                                                    userModel.userName != null
                                                        ? userModel.userName
                                                        : "",
                                                    style: TextStyle(
                                                        fontSize: getSp(32),
                                                        color:
                                                            AppTheme.dark_grey),
                                                  ),
                                                  SizedBox(
                                                    width: getWidthPx(10),
                                                  ),
                                                  Text(
                                                    userModel.mobile != null
                                                        ? userModel.mobile
                                                        : "",
                                                    style: TextStyle(
                                                        fontSize: getSp(32),
                                                        color:
                                                            AppTheme.dark_grey),
                                                  )
                                                ],
                                              ),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: <Widget>[
                                                  Text(
                                                    userModel.idCard ?? "",
                                                    style: TextStyle(
                                                        fontSize: getSp(28),
                                                        color:
                                                            AppTheme.dark_grey),
                                                  ),
                                                  SizedBox(
                                                    width: getWidthPx(10),
                                                  ),
                                                  Text(
                                                    userModel.gender != null
                                                        ? userModel.gender == 1
                                                            ? "男"
                                                            : "女"
                                                        : "",
                                                    style: TextStyle(
                                                        fontSize: getSp(28),
                                                        color:
                                                            AppTheme.dark_grey),
                                                  ),
                                                  SizedBox(
                                                    width: getWidthPx(10),
                                                  ),
                                                  Expanded(
                                                      child: Text(
                                                    userModel.birthday != null
                                                        ?  userModel
                                                                .birthday
                                                        : "",
                                                    maxLines: 2,
                                                    style: TextStyle(
                                                        fontSize: getSp(28),
                                                        color:
                                                            AppTheme.dark_grey),
                                                  ))
                                                ],
                                              ),
                                            ],
                                          ),
                                        )
                                      ],
                                    )
                                  : Container(
//                                  height: getHeightPx(100),
                                      child: ListView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              NeverScrollableScrollPhysics(),
                                          itemCount: widget
                                              .arguments["applyList"].length,
                                          itemBuilder: (ctx, int index) {
                                            return Row(
                                              children: <Widget>[
                                                Container(
                                                  margin: EdgeInsets.only(
                                                      bottom: getWidthPx(20),
                                                      left: getWidthPx(40),
                                                      top: getWidthPx(20)),
                                                  height: getWidthPx(50),
                                                  width: getWidthPx(120),
                                                  alignment: Alignment.center,
                                                  decoration: BoxDecoration(
                                                    border: Border.all(
                                                        width: 1,
                                                        color:
                                                            Colors.blueAccent),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            getWidthPx(40)),
                                                  ),
                                                  child: Text(
                                                    "申请人",
                                                    style: TextStyle(
                                                        color:
                                                            Colors.blueAccent),
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: getWidthPx(20),
                                                ),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: <Widget>[
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: <Widget>[
                                                        Text(
                                                          widget.arguments[
                                                                  "applyList"]
                                                              [index]["name"],
                                                          style: TextStyle(
                                                              fontSize: 16,
                                                              color: AppTheme
                                                                  .dark_grey),
                                                        ),
                                                        SizedBox(
                                                          width: getWidthPx(10),
                                                        ),
                                                        Text(
                                                          widget.arguments[
                                                                  "applyList"]
                                                              [index]["mobile"],
                                                          style: TextStyle(
                                                              fontSize: 16,
                                                              color: AppTheme
                                                                  .dark_grey),
                                                        )
                                                      ],
                                                    ),
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: <Widget>[
                                                        Text(
                                                          widget.arguments[
                                                                  "applyList"]
                                                              [index]["idCard"],
                                                          style: TextStyle(
                                                              fontSize: 14,
                                                              color: AppTheme
                                                                  .dark_grey),
                                                        ),
                                                        SizedBox(
                                                          width: getWidthPx(10),
                                                        ),
                                                        Text(
                                                          widget.arguments["applyList"]
                                                                          [
                                                                          index]
                                                                      [
                                                                      "gender"] ==
                                                                  "1"
                                                              ? "男"
                                                              : "女",
                                                          style: TextStyle(
                                                              fontSize: 14,
                                                              color: AppTheme
                                                                  .dark_grey),
                                                        ),
                                                        SizedBox(
                                                          width: getWidthPx(10),
                                                        ),
                                                        Text(
                                                          widget.arguments[
                                                                  "applyList"][
                                                              index]["birthday"],
                                                          style: TextStyle(
                                                              fontSize: 14,
                                                              color: AppTheme
                                                                  .dark_grey),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                )
                                              ],
                                            );
                                          }),
                                    )
                            ],
                          )),
                      SizedBox(
                        height: getHeightPx(30),
                      ),
                      Container(
                        color: Colors.white,
                        child: Column(
                          children: <Widget>[
                            SizedBox(
                              height: getHeightPx(30),
                            ),
                            Padding(
                                padding: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    right: getWidthPx(40)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Text(
                                      "公证费用",
                                      style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w700),
                                    ),
                                  ],
                                )),
                            SizedBox(
                              height: getHeightPx(30),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                  left: getWidthPx(40), right: getWidthPx(40)),
                              child: ListView.builder(
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: widget
                                    .arguments["notarizationMatters"].length,
                                itemBuilder: (BuildContext context, int index) {
                                  var item = widget
                                      .arguments["notarizationMatters"][index];
                                  return Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      Expanded(
                                        child: Container(
                                          margin: EdgeInsets.only(
                                            bottom: getWidthPx(10),
                                          ),
                                          child: Text(
                                            item["name"] + "×1",
                                            style: TextStyle(
                                                fontSize: 16,
                                                color: AppTheme.dark_grey),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        width: 10,
                                      ),
                                      Text(
                                        item["price"].toString() + "元",
                                        style: TextStyle(
                                            fontSize: 16,
                                            color: AppTheme.dark_grey),
                                      )
                                    ],
                                  );
                                },
                              ),
                            ),
                            SizedBox(
                              height: getHeightPx(30),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: getHeightPx(30),
                      ),
                      Container(
                        color: Colors.white,
                        child: Column(
                          children: <Widget>[
                            SizedBox(
                              height: getHeightPx(30),
                            ),
                            Padding(
                                padding: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    right: getWidthPx(40)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Text(
                                      "联系信息",
                                      style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w700),
                                    ),
                                  ],
                                )),
                            SizedBox(
                              height: getHeightPx(30),
                            ),
                            Padding(
                                padding: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    right: getWidthPx(40)),
                                child: Column(
                                  children: <Widget>[
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Text(
                                          "领取份数",
                                          style: TextStyle(
                                              fontSize: 16,
                                              color: AppTheme.dark_grey),
                                        ),
                                        Container(
                                          width: getWidthPx(270),
                                          height: getHeightPx(70),
//                                        padding: EdgeInsets.symmetric(vertical:getWidthPx(5)),
                                          color:
                                              Color.fromRGBO(254, 240, 240, 1),
                                          child: NumChangeWidget(
                                              num: 1,
                                              onValueChanged: (num) {
                                                confirmApplyModel.number.text =
                                                    num.toString();
                                              }),
                                        )
                                      ],
                                    ),
                                    SizedBox(
                                      height: getHeightPx(30),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(
                                        left: getWidthPx(0),
                                      ),
                                      child: Container(
                                          alignment: Alignment.centerLeft,
                                          child: Text(
                                            "公证书您需要一式几份？（默认1份）",
                                            style: TextStyle(
                                                color: Colors.black45,
                                                fontSize: 14),
                                          )),
                                    ),
                                    SizedBox(
                                      height: getHeightPx(30),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                          border: Border(
                                              bottom: BorderSide(
                                        width: 0.5, //宽度
                                        color: Colors.black12, //边框颜色
                                      ))),
                                    ),
                                  ],
                                )),
                            SizedBox(
                              height: getHeightPx(30),
                            ),
                            Padding(
                                padding: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    right: getWidthPx(40)),
                                child: Column(
                                  children: <Widget>[
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Text(
                                          "联系电话",
                                          style: TextStyle(
                                              fontSize: 16,
                                              color: AppTheme.dark_grey),
                                        ),
                                        Container(
                                          alignment: Alignment.center,
                                          width: getWidthPx(270),
                                          height: getHeightPx(70),
                                          color:
                                              Color.fromRGBO(254, 240, 240, 1),
                                          child: TextField(
                                              keyboardType:
                                                  TextInputType.number,
//                                          maxLength: 11,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .digitsOnly
                                              ],
                                              controller:
                                                  confirmApplyModel.mobile,
                                              decoration: InputDecoration(
                                                counterText: "",
                                                border: InputBorder.none,
                                                hintText: '',
                                                hintStyle: TextStyle(
                                                  fontSize: 12,
                                                ),
                                              ),
                                              onChanged: (value) {
                                                confirmApplyModel
                                                    .notifyListeners();
                                              }),
                                        )
                                      ],
                                    ),
                                    SizedBox(
                                      height: getHeightPx(30),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(
                                        left: getWidthPx(0),
                                      ),
                                      child: Container(
                                          alignment: Alignment.centerLeft,
                                          child: Text(
                                            "公证员将与你确认公证细节，请保持手机通畅",
                                            style: TextStyle(
                                                color: Colors.black45,
                                                fontSize: 14),
                                          )),
                                    ),
                                    SizedBox(
                                      height: getHeightPx(30),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                          border: Border(
                                              bottom: BorderSide(
                                        width: 0.5, //宽度
                                        color: Colors.black12, //边框颜色
                                      ))),
                                    ),
                                  ],
                                )),
                            SizedBox(
                              height: getHeightPx(30),
                            ),
                            Padding(
                                padding: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    right: getWidthPx(40)),
                                child: Column(
                                  children: <Widget>[
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Text(
                                          "领取方式",
                                          style: TextStyle(
                                              fontSize: 16,
                                              color: AppTheme.dark_grey),
                                        ),
                                        DropdownButton(
                                            value: confirmApplyModel.selectType,
                                            underline: Container(
                                                height: 0.0,
                                                color: Colors.green
                                                    .withOpacity(0.7)),
                                            items: [
                                              DropdownMenuItem(
                                                child: Text(
                                                  '邮寄',
                                                  style: TextStyle(
                                                      fontSize: 16,
                                                      color:
                                                          AppTheme.dark_grey),
                                                ),
                                                value: "邮寄",
                                              ),
                                              DropdownMenuItem(
                                                child: Text(
                                                  '自取',
                                                  style: TextStyle(
                                                      fontSize: 16,
                                                      color:
                                                          AppTheme.dark_grey),
                                                ),
                                                value: "自取",
                                              ),
                                            ],
                                            onChanged: (value) {
                                              confirmApplyModel
                                                  .setSelectType(value);
                                              if (value == "邮寄") {
                                                if (confirmApplyModel
                                                        .addressList.length ==
                                                    0) {
                                                  ToastUtil.showWarningToast(
                                                      "请新增地址");
                                                  Navigator.pushNamed(context,
                                                          RoutePaths.AddAddress)
                                                      .then((value) =>
                                                          confirmApplyModel
                                                              .getAddressList());
                                                } else {
//                                                changeAddress();
                                                }
                                                confirmApplyModel.takeStyle = 2;
                                              } else if (value == "自取") {
                                                confirmApplyModel.takeStyle = 1;
                                              }
                                            })
                                      ],
                                    ),
                                    confirmApplyModel.takeStyle == 1
                                        ? Padding(
                                            padding: EdgeInsets.only(
                                              left: getWidthPx(0),
                                            ),
                                            child: Container(
                                                alignment: Alignment.centerLeft,
                                                child: Text(
                                                  "领取时请携带公证时提供的原件去公证现场",
                                                  style: TextStyle(
                                                      color: Colors.black45,
                                                      fontSize: 14),
                                                )),
                                          )
                                        : Padding(
                                            padding: EdgeInsets.only(
                                              left: getWidthPx(0),
                                            ),
                                            child: confirmApplyModel
                                                        .defaultAddress ==
                                                    ""
                                                ? confirmApplyModel
                                                            .chooseAddress ==
                                                        ""
                                                    ? Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: <Widget>[
                                                          Container(
                                                              alignment: Alignment
                                                                  .centerLeft,
                                                              child: Text(
                                                                "请选择邮寄地址",
                                                                style: TextStyle(
                                                                    color: Colors
                                                                        .black45,
                                                                    fontSize:
                                                                        12),
                                                              )),
                                                          InkWell(
                                                            onTap: () {
                                                              changeAddress();
                                                            },
                                                            child: Icon(
                                                              Icons
                                                                  .chevron_right,
                                                              color: Colors
                                                                  .black38,
                                                            ),
                                                          )
                                                        ],
                                                      )
                                                    : Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: <Widget>[
                                                          Container(
                                                              alignment: Alignment
                                                                  .centerLeft,
                                                              child: Text(
                                                                confirmApplyModel
                                                                    .chooseAddress
                                                                    .toString(),
                                                                style: TextStyle(
                                                                    color: Colors
                                                                        .black45,
                                                                    fontSize:
                                                                        12),
                                                              )),
                                                        ],
                                                      )
                                                : Padding(
                                                    padding: EdgeInsets.only(
                                                      left: getWidthPx(0),
                                                    ),
                                                    child: Container(
                                                        alignment: Alignment
                                                            .centerLeft,
                                                        child: Text(
                                                          confirmApplyModel
                                                              .defaultAddress
                                                              .toString(),
                                                          style: TextStyle(
                                                              color: Colors
                                                                  .black45,
                                                              fontSize: 12),
                                                        )),
                                                  )),
                                    SizedBox(
                                      height: getHeightPx(30),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                          border: Border(
                                              bottom: BorderSide(
                                        width: 0.5, //宽度
                                        color: Colors.black12, //边框颜色
                                      ))),
                                    ),
                                  ],
                                )),
                          ],
                        ),
                      ),
                      DebounceButton(
                        clickTap: ThrottleUtil().throttle(() async {
//                        if (!RegexUtil.isMobileSimple(confirmApplyModel.mobile.text)) {
//                          ToastUtil.showErrorToast("请输入正确的手机号");
//                        } else {
                          if (confirmApplyModel.mobile.text == "") {
                            ToastUtil.showErrorToast("请输入联系电话");
                          } else {
                            if (confirmApplyModel.takeStyle == 2) {
                              if (confirmApplyModel.addressList.length == 0) {
                                ToastUtil.showWarningToast("请新增地址");
                                Navigator.pushNamed(
                                    context, RoutePaths.AddAddress)
                                    .then((value) =>
                                    confirmApplyModel.getAddressList());
                              } else {
                                if (confirmApplyModel.defaultAddress == "" &&
                                    confirmApplyModel.takeAddress == "") {
                                  ToastUtil.showWarningToast("请设置默认地址");
                                  G.pushNamed(RoutePaths.MineAddressList);
                                } else if (confirmApplyModel.defaultAddress ==
                                    "" &&
                                    confirmApplyModel.takeAddress != "") {
                                  confirmApplyModel.isEnable = false;
                                  confirmApplyModel.notifyListeners();
                                  confirmApplyModel.setTakeType();
                                } else if (confirmApplyModel.defaultAddress !=
                                    "") {
                                  if (confirmApplyModel.takeAddress == "") {
                                    confirmApplyModel.takeAddress =
                                        confirmApplyModel.defaultAddress;
                                  }
                                  confirmApplyModel.isEnable = false;
                                  confirmApplyModel.notifyListeners();
                                  confirmApplyModel.setTakeType();
                                }
                              }
                            } else {
                              confirmApplyModel.isEnable = false;
                              confirmApplyModel.notifyListeners();
                              confirmApplyModel.setTakeType();
                            }
                          }
                        }),
                        isEnable: confirmApplyModel.isEnable,
                          margin: EdgeInsets.only(
                              top: getWidthPx(50),
                              left: getWidthPx(40),
                              right: getWidthPx(40)),
                          padding: EdgeInsets.all(10),
                          borderRadius:
                          BorderRadius.all(Radius.circular(5)),
                        child: const Text('下一步',
                            style: TextStyle(
                                fontSize: 16,
                                color: AppTheme.nearlyWhite)),
                      ),
                      SizedBox(
                        height: getHeightPx(30),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        ));
  }

  @override
  bool get wantKeepAlive => true;
}
