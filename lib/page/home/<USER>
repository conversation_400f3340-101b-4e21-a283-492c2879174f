import 'package:amap_location_fluttify/amap_location_fluttify.dart';
/// FileName video_user_information
///
/// <AUTHOR>
/// @Date 2023/2/7 09:41
///
/// @Description 视频公证申请人信息界面
///
import 'package:flutter/material.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

import '../../appTheme.dart';

class VideoUserInformation extends StatefulWidget {
  const VideoUserInformation({Key key}) : super(key: key);

  @override
  State<VideoUserInformation> createState() => _VideoUserInformationState();
}

class _VideoUserInformationState extends BaseState<VideoUserInformation> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: commonAppBar(title: '视频公证'),
      body: Consumer<UserViewModel>(builder: (context, usermodel, child) {
        return Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: getWidthPx(20)),
                child: Text(
                  '申请人信息',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Color(0xff5496e0)),
                ),
              ),
              SizedBox(
                height: getHeightPx(20),
              ),
              Container(
                color: Colors.white,
                child: Column(
                  children: <Widget>[
                    Container(
                      height: getHeightPx(100),
                      decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                      ),
                      margin: EdgeInsets.symmetric(horizontal: getWidthPx(10)),
                      padding: EdgeInsets.fromLTRB(getWidthPx(40),
                          getWidthPx(20), getWidthPx(40), getWidthPx(20)),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Text('本人名字',
                                style: TextStyle(
                                    fontSize: 16, color: Colors.black87)),
                          ),
                          Text(
                            usermodel.userName == null ||
                                    usermodel.userName == ""
                                ? "当事人"
                                : usermodel.userName,
                            style: TextStyle(
                                fontSize: 16, color: AppTheme.Text_min),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      height: getHeightPx(100),
                      decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                      ),
                      margin: EdgeInsets.symmetric(horizontal: getWidthPx(10)),
                      padding: EdgeInsets.fromLTRB(getWidthPx(40),
                          getWidthPx(20), getWidthPx(40), getWidthPx(20)),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Text('身份证号',
                                style: TextStyle(
                                    fontSize: 16, color: Colors.black87)),
                          ),
                          Text(
                            usermodel.idCard,
                            style: TextStyle(
                                fontSize: 16, color: AppTheme.Text_min),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      height: getHeightPx(100),
                      decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                      ),
                      margin: EdgeInsets.symmetric(horizontal: getWidthPx(10)),
                      padding: EdgeInsets.fromLTRB(getWidthPx(40),
                          getWidthPx(20), getWidthPx(40), getWidthPx(20)),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Text('手机号码',
                                style: TextStyle(
                                    fontSize: 16, color: Colors.black87)),
                          ),
                          Text(
                            usermodel.mobile,
                            style: TextStyle(
                                fontSize: 16, color: AppTheme.Text_min),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  String city = "";
                  String adCode = "";
                  String latLng = "";
                  Permission.location
                      .request()
                      .isGranted
                      .then((value) {
                    if (value) {
                      try {
                        AmapLocation.instance
                            .fetchLocation()
                            .then((location) {
                          if (location.province != null &&
                              location.city != null) {
                            city =
                            "${location.province} ${location.city}";
                          }
                          if (location.adCode != null) {
                            adCode =
                            "${location.adCode.substring(0, 4)}00";
                          }

                          latLng =
                          "${location.latLng.longitude},${location.latLng.latitude}";
                          G.getCurrentState().pushReplacementNamed(
                              RoutePaths.VideoNotarize,
                              arguments: {
                                'city': city,
                                'adCode': adCode,
                                'latLng': latLng
                              });
                        });
                      } catch (e) {
                        G.getCurrentState().pushReplacementNamed(
                            RoutePaths.VideoNotarize,
                            arguments: {
                              'city': city,
                              'adCode': adCode,
                              'latLng': latLng
                            });
                      }
                    } else {
                      G.showPermissionDialog(str: "访问位置信息权限");
                    }
                  });
                },
                child: Container(
                  height: getWidthPx(80),
                  margin: EdgeInsets.only(
                      left: getWidthPx(40),
                      right: getWidthPx(40),
                      bottom: MediaQuery.of(context).padding.bottom +
                          getHeightPx(20)),
                  alignment: Alignment.center,
                  decoration: ShapeDecoration(
                      shape: StadiumBorder(), color: AppTheme.themeBlue),
                  child: Text(
                    '下一步',
                    style: TextStyle(
                      fontSize: getSp(28),
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}
