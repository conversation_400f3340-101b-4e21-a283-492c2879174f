import 'dart:math';

import 'package:flutter/material.dart';
import 'package:notarization_station_app/base_framework/utils/refresh_helper.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/home/<USER>/appointment_vm.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:notarization_station_app/utils/throttle_anti-shake.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../appTheme.dart';

class AppointmentPage extends StatefulWidget {
  @override
  _AppointmentPageState createState() => _AppointmentPageState();
}

class _AppointmentPageState extends BaseState<AppointmentPage> {
  AppointmentViewModel viewModel;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: commonAppBar(title: "预约公证"),
        body: Consumer<UserViewModel>(builder: (ctx, userModel, child) {
          return ProviderWidget<AppointmentViewModel>(
              model: AppointmentViewModel(userModel),
              onModelReady: (model) {
                viewModel = model;
                model.getAppointmentLists();
                model.getLocation();
              },
              builder: (ctx, helpModel, child) {
                return Column(
                  children: [
                    Expanded(
                      child: Container(
                          color: Color.fromRGBO(247, 247, 247, 1),
                          child: helpModel.busy
                              ? loadingWidget()
                              : viewModel.appointmentLists.length > 0
                                  ? SmartRefresher(
                                      enablePullDown: true,
                                      enablePullUp: true,
                                      header: HomeRefreshHeader(Colors.black),
                                      footer: RefresherFooter(),
                                      controller: helpModel.refreshController,
                                      onRefresh: helpModel.refresh,
                                      onLoading: helpModel.loadMore,
                                      child: ListView(
                                        children: viewModel.appointmentLists
                                            .map((item) {
                                          return Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              Container(
                                                color: Colors.white,
                                                margin: EdgeInsets.fromLTRB(
                                                    0, 0, 0, getWidthPx(20)),
                                                child: Container(
                                                  width: getWidthPx(700),
                                                  margin: EdgeInsets.fromLTRB(
                                                      getWidthPx(20),
                                                      getWidthPx(30),
                                                      getWidthPx(20),
                                                      getWidthPx(0)),
                                                  child: Column(
                                                    children: <Widget>[
                                                      Padding(
                                                        padding: EdgeInsets.only(top:getWidthPx(20),),
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                          children: [
                                                            Text(
                                                              "订单编号：",
                                                              style: TextStyle(
                                                                color: Color.fromRGBO(
                                                                    195, 195, 195, 1),
                                                              ),
                                                            ),
                                                            SizedBox(width: 10),
                                                            Expanded(
                                                                child: Text(
                                                                    "${item['orderNo']??''}",
                                                                  textAlign: TextAlign.end,)),
                                                          ],
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding: EdgeInsets.only(top:getWidthPx(20),),
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                          children: [
                                                            Text(
                                                              "公证处：",
                                                              style: TextStyle(
                                                                color: Color.fromRGBO(
                                                                    195, 195, 195, 1),
                                                              ),
                                                            ),
                                                            SizedBox(width: 10),
                                                            Expanded(
                                                                child: Text(
                                                                    "${item['notaryName']??''}",
                                                                  textAlign: TextAlign.end,)),
                                                          ],
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding: EdgeInsets.only(top:getWidthPx(20),),
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,

                                                          children: [
                                                            Text(
                                                              "申办人：",
                                                              style: TextStyle(
                                                                color: Color.fromRGBO(
                                                                    195, 195, 195, 1),
                                                              ),
                                                            ),
                                                            SizedBox(width: 10),
                                                            Expanded(
                                                                child: Text(
                                                                    "${item['userName']??''}",
                                                                  textAlign: TextAlign.end,
                                                                )),
                                                          ],
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding: EdgeInsets.only(top:getWidthPx(20),),
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,

                                                          children: <Widget>[
                                                            Text(
                                                              "创建时间：",
                                                              style: TextStyle(
                                                                color: Color.fromRGBO(
                                                                    195, 195, 195, 1),
                                                              ),
                                                            ),
                                                            SizedBox(width: 10),
                                                            Expanded(
                                                              child: Text(
                                                                "${item['createDate']??""}",
                                                                textAlign: TextAlign.end,
                                                                style: TextStyle(
                                                                    color:
                                                                    Colors.black),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding: EdgeInsets.only(top:getWidthPx(20),),
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                          children: <Widget>[
                                                            Text(
                                                              "预约时间：",
                                                              style: TextStyle(
                                                                color: Color.fromRGBO(
                                                                    195, 195, 195, 1),
                                                              ),
                                                            ),
                                                            SizedBox(width: 10),
                                                            Expanded(
                                                              child: Text(
                                                                "${item['processingTime']??''}",
                                                                textAlign: TextAlign.end,
                                                                style: TextStyle(
                                                                    color:
                                                                    Colors.black),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding: EdgeInsets.only(top:getWidthPx(20),),
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                          children: <Widget>[
                                                            Text(
                                                              "备注:",
                                                              style: TextStyle(
                                                                color: Color.fromRGBO(
                                                                    195, 195, 195, 1),
                                                              ),
                                                            ),
                                                            SizedBox(width: 10),
                                                            Expanded(
                                                              child: Text(
                                                                  "${item['remarks']??''}",
                                                                textAlign: TextAlign.end,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding: EdgeInsets.symmetric(vertical:getWidthPx(20),),
                                                        child: Row(
                                                          children: [
                                                            const Spacer(),
                                                            (item['status']== 1 || item['status']==0) ? InkWell(
                                                              onTap: debounce((){
                                                                showDialog(
                                                                    context: context,
                                                                    barrierDismissible: false,
                                                                    builder: (context){
                                                                      return Center(
                                                                        child: Container(
                                                                          height: 230,
                                                                          margin: EdgeInsets.symmetric(horizontal: 50),
                                                                          decoration: BoxDecoration(
                                                                            borderRadius: BorderRadius.circular(10),
                                                                            color: Colors.white,
                                                                          ),
                                                                          child: Column(
                                                                            crossAxisAlignment: CrossAxisAlignment.center,
                                                                            children: [
                                                                              Padding(
                                                                                padding: EdgeInsets.only(left: 10,top: 15),
                                                                                child: Row(
                                                                                  children: [
                                                                                    Icon(Icons.warning,color: Colors.red,size: 20,),
                                                                                    SizedBox(width: 10,),
                                                                                    Text('提示'),
                                                                                    const Spacer(),
                                                                                    IconButton(icon: Icon(Icons.close,size: 20,), onPressed: (){
                                                                                      G.pop();
                                                                                    })
                                                                                  ],
                                                                                ),
                                                                              ),
                                                                              Padding(padding: EdgeInsets.symmetric(horizontal: 10),child: Container(
                                                                                color: AppTheme.bg_e,
                                                                                height: 1.0,
                                                                              ),),
                                                                              const Spacer(flex: 1,),
                                                                              Text("确定要取消预约吗？"),
                                                                              const Spacer(flex: 1,),
                                                                              Padding(padding: EdgeInsets.only(bottom: 30),child: Row(
                                                                                mainAxisAlignment: MainAxisAlignment.center,
                                                                                children: [
                                                                                  InkWell(
                                                                                    onTap:(){
                                                                                      G.pop();
                                                                                    },
                                                                                    child: Container(
                                                                                      padding:EdgeInsets.symmetric(horizontal:15,vertical:8),
                                                                                      decoration: BoxDecoration(
                                                                                          borderRadius: BorderRadius.circular(5),
                                                                                          border: Border.all(color: AppTheme.bg_f,width: 1.0)
                                                                                      ),
                                                                                      child: Text(
                                                                                        '点错了',
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(width: 10,),
                                                                                  InkWell(
                                                                                    onTap:debounce((){
                                                                                      Navigator.of(context).pop(true);
                                                                                      viewModel.cancelAppointmentOrder(item);
                                                                                    }),
                                                                                    child: Container(
                                                                                      padding:EdgeInsets.symmetric(horizontal:25,vertical:8),
                                                                                      decoration: BoxDecoration(
                                                                                        color:AppTheme.themeBlue,
                                                                                        borderRadius: BorderRadius.circular(5),
                                                                                      ),
                                                                                      child: Text(
                                                                                        '确定',
                                                                                        style:TextStyle(
                                                                                          color: Colors.white,
                                                                                        ),
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                ],
                                                                              ),)
                                                                            ],
                                                                          ),

                                                                        ),
                                                                      );

                                                                    });

                                                              }),
                                                              child: Container(
                                                                padding:
                                                                EdgeInsets
                                                                    .fromLTRB(
                                                                    15,
                                                                    8,
                                                                    15,
                                                                    8),
                                                                decoration:
                                                                BoxDecoration(
                                                                  borderRadius:
                                                                  BorderRadius.all(
                                                                      Radius.circular(
                                                                          5)),
                                                                  border: Border.all(color: AppTheme.bg_f,width: 1.0),
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                                child: Text(
                                                                    '取消预约',
                                                                    style: TextStyle(
                                                                        color: AppTheme.darkText)),
                                                              ),
                                                            ) : SizedBox(),
                                                            const SizedBox(width: 10,),
                                                            item['status']==1 ? InkWell(
                                                              onTap: debounce((){
                                                                if (item['notaryPublicName'] !=
                                                                    null &&
                                                                    item['notaryPublicName'] !=
                                                                        "") {
                                                                  showDialog<
                                                                      bool>(
                                                                    context:
                                                                    context,
                                                                    builder:
                                                                        (context) {
                                                                      return AlertDialog(
                                                                        title: Text(
                                                                            "提示"),
                                                                        content:
                                                                        Text("我已与公证员确认当前时间进行视频公证，即将向公证员发起订单"),
                                                                        actions: <
                                                                            Widget>[
                                                                          // ignore: deprecated_member_use
                                                                          FlatButton(
                                                                            child:
                                                                            Text("取消"),
                                                                            onPressed: () =>
                                                                                Navigator.of(context).pop(), // 关闭对话框
                                                                          ),
                                                                          // ignore: deprecated_member_use
                                                                          FlatButton(
                                                                            child:
                                                                            Text("确定"),
                                                                            onPressed:debounce(() async {
                                                                              //关闭对话框并返回true
                                                                              if(await Permission.photos.request().isGranted && await Permission.speech.request().isGranted && await Permission.camera.request().isGranted && await Permission.storage.request().isGranted){
                                                                                Navigator.of(context).pop(true);
                                                                                viewModel.addOrder(item);
                                                                              }else{
                                                                                G.showPermissionDialog(str: '相机、相册、存储及麦克风权限');
                                                                              }
                                                                              // final status = await Permission.speech.request();
                                                                              // if (status == PermissionStatus.granted) {
                                                                              //   final cameraS = await Permission.camera.request();
                                                                              //   if (cameraS == PermissionStatus.granted) {
                                                                              //     final storageS = await Permission.storage.request();
                                                                              //     if (storageS == PermissionStatus.granted) {
                                                                              //
                                                                              //     }
                                                                              //   }
                                                                              // }
                                                                            }),
                                                                          ),
                                                                        ],
                                                                      );
                                                                    },
                                                                  );
                                                                } else {
                                                                  ToastUtil
                                                                      .showErrorToast(
                                                                      "该公证员已不再受理业务，请重新发起预约");
                                                                }
                                                              }),
                                                              child: Container(
                                                                padding:
                                                                EdgeInsets
                                                                    .fromLTRB(
                                                                    15,
                                                                    8,
                                                                    15,
                                                                    8),
                                                                decoration:
                                                                BoxDecoration(
                                                                  borderRadius:
                                                                  BorderRadius.all(
                                                                      Radius.circular(
                                                                          5)),
                                                                  color: AppTheme.themeBlue,
                                                                ),
                                                                child: Text(
                                                                    '开始公证',
                                                                    style: TextStyle(
                                                                        color: AppTheme
                                                                            .nearlyWhite)),
                                                              ),
                                                            ) : SizedBox()
                                                          ],
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Transform.rotate(
                                              angle: - (40 / pi),
                                                child: Image.asset(
                                                    'lib/assets/images/${item['status'].toString() == '0' ? "waiting_to_confirm_img.jpg" :(item['status'].toString() == '1' ? "appointment_success_img.jpg" : (item['status'].toString() == '2' ? "has_done_img.jpg":"cancel_img.jpg"))}',
                                                  width: 120,
                                                  height: 40,
                                                  fit: BoxFit.contain,
                                                ),
                                              )
                                            ],
                                          );
                                        }).toList(),
                                      ))
                                  : Center(
                                      child: Text(
                                        "暂无预约",
                                      ),
                                    )),
                    ),
                    InkWell(
                      onTap: debounce((){
                        Navigator.pushNamed(context, RoutePaths.AddAppointment)
                            .then((value) => viewModel.refresh());
                      }),
                      child: Container(
                        width: getWidthPx(670),
                        margin: EdgeInsets.fromLTRB(
                            getWidthPx(40), 0, getWidthPx(40), getWidthPx(20) + MediaQuery.of(context).padding.bottom),
                        padding: EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(50)),
                          color: AppTheme.themeBlue,
                        ),
                        child: Center(
                          child: Text('新增预约',
                              style: TextStyle(
                                  fontSize: 16, color: AppTheme.nearlyWhite)),
                        ),
                      ),
                    )
                  ],
                );
              });
        }));
  }
}
