import 'dart:ui';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
// import 'package:wakelock/wakelock.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:multi_image_picker/multi_image_picker.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/components/a_dialog/a_dialog.dart';
import 'package:notarization_station_app/page/home/<USER>/video_ing_model.dart';
import 'package:notarization_station_app/page/login/entity/user_info_entity.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:provider/provider.dart';

class VideoIngPage extends StatefulWidget {
  final arguments;

  VideoIngPage({Key key, this.arguments}) : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return VideoIngPageState();
  }
}

class VideoIngPageState extends BaseState<VideoIngPage>
    with WidgetsBindingObserver {
  VideoIngViewModel videoVm;
  UserInfoEntity userInfo;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    wjPrint("--" + state.toString());
    // switch (state) {
    //   case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
    //     videoVm.engine.enableLocalVideo(false);
    //     break;
    //   case AppLifecycleState.resumed: // 应用程序可见，前台
    //     videoVm.engine.enableLocalVideo(true);
    //     break;
    //   case AppLifecycleState.paused: // 应用程序不可见，后台
    //     break;
    //   case AppLifecycleState.detached: // 申请将暂时暂停
    //     break;
    // }
  }

  @override
  void dispose() {
    super.dispose();
    EasyLoading.dismiss();
    videoVm.closeRoom();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: WillPopScope(
        onWillPop: () {
          ADialog.confirm(context,
              content: "是否确认退出视频公证？",
              cancelButtonText: Text("取消"),
              confirmButtonText: Text("确认"), cancelButtonPress: () {
            Navigator.of(context).pop();
          }, confirmButtonPress: () {
            videoVm.closeSocket();
            G.pop();
            G
                .getCurrentState()
                .popUntil(ModalRoute.withName(RoutePaths.HomeIndex));
          });
          return Future.value(false);
        },
        child: Consumer<UserViewModel>(
          builder: (ctx, userModel, child) {
            return ProviderWidget<VideoIngViewModel>(
                model: VideoIngViewModel(userModel, widget.arguments['roomId'],
                    widget.arguments['orderInfo'], context),
                onModelReady: (model) async {
                  videoVm = model;
                  model.videoHeight = getHeightPx(1334);
                  model.initData();
                },
                builder: (ctx, vm, child) {
                  return Column(
                    children: <Widget>[
                      Container(
                          // height: getWidthPx(375),
                          color: Colors.black26,
                          child: _renderWidget()),
                      Container(
                        width: double.maxFinite,
                        height: getScreenWidth() / 8.5,
                        color: AppTheme.bg_d,
                        child: Image.asset(
                          vm.showType(),
                          fit: BoxFit.fill,
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      showContentPage(vm),
                    ],
                  );
                });
          },
        ),
      ),
    );
  }

  Widget releaseImage(bool isDef, Asset asset) {
    return isDef && videoVm.showUpload
        ? InkWell(
            onTap: () {
              videoVm.requestCameraPermission();
            },
            child: Image.asset("lib/assets/images/add_img.png"))
        : ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: AssetThumb(
              asset: asset,
              width: 500,
              height: 500,
            ),
          );
  }

  Widget _renderWidget() {
    wjPrint("++++videoVm.userList+++++++++${videoVm.userList}");
    if (videoVm.userList == null || videoVm.userList.isEmpty) {
      return SizedBox();
    } else {
      return MediaQuery.removePadding(
          context: context,
          removeBottom: true,
          child: GridView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2, //每行三列
                childAspectRatio: 1.0, //显示区域宽高相等
                mainAxisSpacing: 0,
                crossAxisSpacing: 0,
              ),
              itemCount: videoVm.userList.length,
              itemBuilder: (context, index) {
                // return videoVm.userList[index]['widget']!=null?videoVm.userList[index]['widget']:SizedBox();
                return Stack(
                  children: [
                    videoVm.userList[index]['widget'] != null
                        ? videoVm.userList[index]['widget']
                        : SizedBox(),
                    videoVm.userList[index]['userId'] ==
                            videoVm.userViewModel.idCard
                        ? Positioned(
                            right: 0,
                            child: InkWell(
                              onTap: () {
                                videoVm.txDeviceManager.switchCamera(
                                    videoVm.camerasNum == 0 ? true : false);
                                videoVm.camerasNum =
                                    videoVm.camerasNum == 0 ? 1 : 0;
                                // videoVm.trtcCloud.stopScreenCapture();
                                setState(() {});
                              },
                              child: Container(
                                color: Color(0x66ffffff),
                                child: Icon(Icons.autorenew),
                              ),
                            ),
                          )
                        : SizedBox()
                  ],
                );
              }));
    }
  }

  Widget showContentPage(VideoIngViewModel vm) {
    Widget child;
    switch (vm.showTab) {
      case 0:
        child = Expanded(
            child: SingleChildScrollView(
          child: Column(
            children: <Widget>[
              Container(
                padding: EdgeInsets.all(getWidthPx(20)),
                decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                ),
                child: Row(
                  children: <Widget>[
                    Container(
                      width: getWidthPx(10),
                      decoration: BoxDecoration(
                        color: AppTheme.themeBlue,
                        borderRadius: BorderRadius.vertical(
                            top: Radius.circular(10),
                            bottom: Radius.circular(10)),
                      ),
                      height: getHeightPx(50),
                    ),
                    Text(' 申请表')
                  ],
                ),
              ),
              // Text(videoVm.userList.toString()),
              MediaQuery.removePadding(
                removeTop: true,
                context: context,
                child: ListView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: vm.applicationForm.length,
                    itemBuilder: (ctx, a) {
                      return Column(
                        children: <Widget>[
                          Container(
                            decoration: BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                                      width: 1, color: AppTheme.bg_c)),
                            ),
                            margin: EdgeInsets.symmetric(
                                horizontal: getWidthPx(10)),
                            padding: EdgeInsets.fromLTRB(getWidthPx(40),
                                getWidthPx(20), getWidthPx(40), getWidthPx(20)),
                            child: Row(
                              children: <Widget>[
                                Text('姓名',
                                    style: TextStyle(
                                      fontSize: 16,
                                    )),
                                Expanded(
                                    child: Text(
                                  "${vm.applicationForm[a]['name'] ?? ""}",
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: AppTheme.deactivatedText),
                                  textAlign: TextAlign.right,
                                )),
                              ],
                            ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                                      width: 1, color: AppTheme.bg_c)),
                            ),
                            margin: EdgeInsets.symmetric(
                                horizontal: getWidthPx(10)),
                            padding: EdgeInsets.fromLTRB(getWidthPx(40),
                                getWidthPx(20), getWidthPx(40), getWidthPx(20)),
                            child: Row(
                              children: <Widget>[
                                Text('身份证号',
                                    style: TextStyle(
                                      fontSize: 16,
                                    )),
                                Expanded(
                                    child: Text(vm.applicationForm[a]['idCard'],
                                        style: TextStyle(
                                            fontSize: 16,
                                            color: AppTheme.deactivatedText),
                                        textAlign: TextAlign.right)),
                              ],
                            ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                                      width: 1, color: AppTheme.bg_c)),
                            ),
                            margin: EdgeInsets.symmetric(
                                horizontal: getWidthPx(10)),
                            padding: EdgeInsets.fromLTRB(getWidthPx(40),
                                getWidthPx(20), getWidthPx(40), getWidthPx(20)),
                            child: Row(
                              children: <Widget>[
                                Text("地址"),
                                Expanded(
                                  child: Text(
                                    "${vm.applicationForm[a]['address'] ?? ""}",
                                    style: TextStyle(
                                        fontSize: 16,
                                        color: AppTheme.deactivatedText),
                                    textAlign: TextAlign.right,
                                  ),
                                )
                              ],
                            ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                                      width: 1, color: AppTheme.bg_c)),
                            ),
                            margin: EdgeInsets.symmetric(
                                horizontal: getWidthPx(10)),
                            padding: EdgeInsets.fromLTRB(getWidthPx(40),
                                getWidthPx(20), getWidthPx(40), getWidthPx(20)),
                            child: Row(
                              children: <Widget>[
                                Text('联系电话',
                                    style: TextStyle(
                                      fontSize: 16,
                                    )),
                                Expanded(
                                    child: Text(vm.applicationForm[a]['mobile'],
                                        style: TextStyle(
                                            fontSize: 16,
                                            color: AppTheme.deactivatedText),
                                        textAlign: TextAlign.right)),
                              ],
                            ),
                          ),
                          Container(
                            color: AppTheme.bg_e,
                            height: getWidthPx(20),
                          ),
                        ],
                      );
                    }),
              ),

              vm.agentForm.length != 0
                  ? MediaQuery.removePadding(
                      removeTop: true,
                      context: context,
                      child: ListView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: vm.agentForm.length,
                          itemBuilder: (ctx, a) {
                            return Column(
                              children: <Widget>[
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            width: 1, color: AppTheme.bg_c)),
                                  ),
                                  margin: EdgeInsets.symmetric(
                                      horizontal: getWidthPx(10)),
                                  padding: EdgeInsets.fromLTRB(
                                      getWidthPx(40),
                                      getWidthPx(20),
                                      getWidthPx(40),
                                      getWidthPx(20)),
                                  child: Row(
                                    children: <Widget>[
                                      Text('姓名',
                                          style: TextStyle(
                                            fontSize: 16,
                                          )),
                                      Expanded(
                                          child: Text(vm.agentForm[a]['name'],
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color:
                                                      AppTheme.deactivatedText),
                                              textAlign: TextAlign.right)),
                                    ],
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            width: 1, color: AppTheme.bg_c)),
                                  ),
                                  margin: EdgeInsets.symmetric(
                                      horizontal: getWidthPx(10)),
                                  padding: EdgeInsets.fromLTRB(
                                      getWidthPx(40),
                                      getWidthPx(20),
                                      getWidthPx(40),
                                      getWidthPx(20)),
                                  child: Row(
                                    children: <Widget>[
                                      Expanded(
                                        child: Text('性别',
                                            style: TextStyle(
                                              fontSize: 16,
                                            )),
                                      ),
                                      Text(
                                        vm.agentForm[a]['gender'] == 1
                                            ? '男'
                                            : vm.agentForm[a]['gender'] == 0
                                                ? '女'
                                                : '',
                                        style: TextStyle(
                                            fontSize: 16,
                                            color: AppTheme.deactivatedText),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            width: 1, color: AppTheme.bg_c)),
                                  ),
                                  margin: EdgeInsets.symmetric(
                                      horizontal: getWidthPx(10)),
                                  padding: EdgeInsets.fromLTRB(
                                      getWidthPx(40),
                                      getWidthPx(20),
                                      getWidthPx(40),
                                      getWidthPx(20)),
                                  child: Row(
                                    children: <Widget>[
                                      Text('身份证号',
                                          style: TextStyle(
                                            fontSize: 16,
                                          )),
                                      Expanded(
                                          child: Text(vm.agentForm[a]['idCard'],
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color:
                                                      AppTheme.deactivatedText),
                                              textAlign: TextAlign.right)),
                                    ],
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            width: 1, color: AppTheme.bg_c)),
                                  ),
                                  margin: EdgeInsets.symmetric(
                                      horizontal: getWidthPx(10)),
                                  padding: EdgeInsets.fromLTRB(
                                      getWidthPx(40),
                                      getWidthPx(20),
                                      getWidthPx(40),
                                      getWidthPx(20)),
                                  child: Row(
                                    children: <Widget>[
                                      Text('被代理人',
                                          style: TextStyle(
                                            fontSize: 16,
                                          )),
                                      Expanded(
                                          child: Text(
                                              vm.agentForm[a]['principal'],
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color:
                                                      AppTheme.deactivatedText),
                                              textAlign: TextAlign.right)),
                                    ],
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            width: 1, color: AppTheme.bg_c)),
                                  ),
                                  margin: EdgeInsets.symmetric(
                                      horizontal: getWidthPx(10)),
                                  padding: EdgeInsets.fromLTRB(
                                      getWidthPx(40),
                                      getWidthPx(20),
                                      getWidthPx(40),
                                      getWidthPx(20)),
                                  child: Row(
                                    children: <Widget>[
                                      Text('联系电话',
                                          style: TextStyle(
                                            fontSize: 16,
                                          )),
                                      Expanded(
                                          child: Text(vm.agentForm[a]['mobile'],
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color:
                                                      AppTheme.deactivatedText),
                                              textAlign: TextAlign.right)),
                                    ],
                                  ),
                                ),
                                Container(
                                  color: AppTheme.bg_e,
                                  height: getWidthPx(20),
                                ),
                              ],
                            );
                          }),
                    )
                  : SizedBox(),

              Container(
                color: AppTheme.bg_e,
                height: getWidthPx(30),
              ),
              vm.enterpriseForm.length != 0
                  ? Container(
                      padding: EdgeInsets.all(getWidthPx(20)),
                      decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                      ),
                      child: Row(
                        children: <Widget>[
                          Container(
                            width: getWidthPx(10),
                            decoration: BoxDecoration(
                              color: AppTheme.themeBlue,
                              borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(10),
                                  bottom: Radius.circular(10)),
                            ),
                            height: getHeightPx(50),
                          ),
                          Text(' 企业内容')
                        ],
                      ),
                    )
                  : SizedBox(),
              vm.enterpriseForm.length != 0
                  ? MediaQuery.removePadding(
                      removeTop: true,
                      context: context,
                      child: ListView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: vm.enterpriseForm.length,
                          itemBuilder: (ctx, a) {
                            return Column(
                              children: <Widget>[
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            width: 1, color: AppTheme.bg_c)),
                                  ),
                                  margin: EdgeInsets.symmetric(
                                      horizontal: getWidthPx(10)),
                                  padding: EdgeInsets.fromLTRB(
                                      getWidthPx(40),
                                      getWidthPx(20),
                                      getWidthPx(40),
                                      getWidthPx(20)),
                                  child: Row(
                                    children: <Widget>[
                                      Text('单位名称',
                                          style: TextStyle(
                                            fontSize: 16,
                                          )),
                                      Expanded(
                                          child: Text(
                                              vm.enterpriseForm[a]
                                                  ['companyName'],
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color:
                                                      AppTheme.deactivatedText),
                                              textAlign: TextAlign.right)),
                                    ],
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            width: 1, color: AppTheme.bg_c)),
                                  ),
                                  margin: EdgeInsets.symmetric(
                                      horizontal: getWidthPx(10)),
                                  padding: EdgeInsets.fromLTRB(
                                      getWidthPx(40),
                                      getWidthPx(20),
                                      getWidthPx(40),
                                      getWidthPx(20)),
                                  child: Row(
                                    children: <Widget>[
                                      Text('法定代表人',
                                          style: TextStyle(
                                            fontSize: 16,
                                          )),
                                      Expanded(
                                          child: Text(
                                              vm.enterpriseForm[a]
                                                  ['statutoryPerson'],
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color:
                                                      AppTheme.deactivatedText),
                                              textAlign: TextAlign.right)),
                                    ],
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            width: 1, color: AppTheme.bg_c)),
                                  ),
                                  margin: EdgeInsets.symmetric(
                                      horizontal: getWidthPx(10)),
                                  padding: EdgeInsets.fromLTRB(
                                      getWidthPx(40),
                                      getWidthPx(20),
                                      getWidthPx(40),
                                      getWidthPx(20)),
                                  child: Row(
                                    children: <Widget>[
                                      Text('电话',
                                          style: TextStyle(
                                            fontSize: 16,
                                          )),
                                      Expanded(
                                          child: Text(
                                              vm.enterpriseForm[a]
                                                  ['statutoryMobile'],
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color:
                                                      AppTheme.deactivatedText),
                                              textAlign: TextAlign.right)),
                                    ],
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            width: 1, color: AppTheme.bg_c)),
                                  ),
                                  margin: EdgeInsets.symmetric(
                                      horizontal: getWidthPx(10)),
                                  padding: EdgeInsets.fromLTRB(
                                      getWidthPx(40),
                                      getWidthPx(20),
                                      getWidthPx(40),
                                      getWidthPx(20)),
                                  child: Row(
                                    children: <Widget>[
                                      Text('地址',
                                          style: TextStyle(
                                            fontSize: 16,
                                          )),
                                      Expanded(
                                          child: Text(
                                              vm.enterpriseForm[a]
                                                  ['companyAdd'],
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color:
                                                      AppTheme.deactivatedText),
                                              textAlign: TextAlign.right)),
                                    ],
                                  ),
                                ),
                                Container(
                                  color: AppTheme.bg_e,
                                  height: getWidthPx(20),
                                ),
                              ],
                            );
                          }),
                    )
                  : SizedBox(),

              Container(
                color: AppTheme.bg_e,
                height: vm.enterpriseForm.length != 0 ? getWidthPx(30) : 0,
              ),
              Container(
                padding: EdgeInsets.all(getWidthPx(20)),
                decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                ),
                child: Row(
                  children: <Widget>[
                    Container(
                      width: getWidthPx(10),
                      decoration: BoxDecoration(
                        color: AppTheme.themeBlue,
                        borderRadius: BorderRadius.vertical(
                            top: Radius.circular(10),
                            bottom: Radius.circular(10)),
                      ),
                      height: getHeightPx(50),
                    ),
                    Text(' 公证内容')
                  ],
                ),
              ),

              Container(
                decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                ),
                margin: EdgeInsets.symmetric(horizontal: getWidthPx(10)),
                padding: EdgeInsets.fromLTRB(getWidthPx(40), getWidthPx(20),
                    getWidthPx(40), getWidthPx(20)),
                child: Row(
                  children: <Widget>[
                    Text('证书用途',
                        style: TextStyle(
                          fontSize: 16,
                        )),
                    Expanded(
                        child: Text(
                            vm.contentForm['yongtu'] == null
                                ? ""
                                : vm.contentForm['yongtu'],
                            style: TextStyle(
                                fontSize: 16, color: AppTheme.deactivatedText),
                            textAlign: TextAlign.right)),
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                ),
                margin: EdgeInsets.symmetric(horizontal: getWidthPx(10)),
                padding: EdgeInsets.fromLTRB(getWidthPx(40), getWidthPx(20),
                    getWidthPx(40), getWidthPx(20)),
                child: Row(
                  children: <Widget>[
                    Text('使用地',
                        style: TextStyle(
                          fontSize: 16,
                        )),
                    Expanded(
                        child: Text(
                            vm.contentForm['area'] == null
                                ? ""
                                : vm.contentForm['area'],
                            style: TextStyle(
                                fontSize: 16, color: AppTheme.deactivatedText),
                            textAlign: TextAlign.right)),
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                ),
                margin: EdgeInsets.symmetric(horizontal: getWidthPx(10)),
                padding: EdgeInsets.fromLTRB(getWidthPx(40), getWidthPx(20),
                    getWidthPx(40), getWidthPx(20)),
                child: Row(
                  children: <Widget>[
                    Text('翻译语种',
                        style: TextStyle(
                          fontSize: 16,
                        )),
                    Expanded(
                        child: Text(
                            vm.contentForm['lang'] == null
                                ? ""
                                : vm.contentForm['lang'],
                            style: TextStyle(
                                fontSize: 16, color: AppTheme.deactivatedText),
                            textAlign: TextAlign.right)),
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                ),
                margin: EdgeInsets.symmetric(horizontal: getWidthPx(10)),
                padding: EdgeInsets.fromLTRB(getWidthPx(40), getWidthPx(20),
                    getWidthPx(40), getWidthPx(20)),
                child: Row(
                  children: <Widget>[
                    Text('公证事项',
                        style: TextStyle(
                          fontSize: 16,
                        )),
                    Expanded(
                        child: Text(
                            vm.contentForm['gongzhengValue'] == null
                                ? ""
                                : vm.contentForm['gongzhengValue'],
                            style: TextStyle(
                                fontSize: 16, color: AppTheme.deactivatedText),
                            textAlign: TextAlign.right)),
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                ),
                margin: EdgeInsets.symmetric(horizontal: getWidthPx(10)),
                padding: EdgeInsets.fromLTRB(getWidthPx(40), getWidthPx(20),
                    getWidthPx(40), getWidthPx(20)),
                child: Row(
                  children: <Widget>[
                    Text('公证材料',
                        style: TextStyle(
                          fontSize: 16,
                        )),
                    Expanded(
                        child: Text(
                            vm.contentForm['cailiaoValue'] == null
                                ? ""
                                : vm.contentForm['cailiaoValue'],
                            style: TextStyle(
                                fontSize: 16, color: AppTheme.deactivatedText),
                            textAlign: TextAlign.right)),
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppTheme.bg_c)),
                ),
                margin: EdgeInsets.symmetric(horizontal: getWidthPx(10)),
                padding: EdgeInsets.fromLTRB(getWidthPx(40), getWidthPx(20),
                    getWidthPx(40), getWidthPx(20)),
                child: Row(
                  children: <Widget>[
                    Text('备注',
                        style: TextStyle(
                          fontSize: 16,
                        )),
                    Expanded(
                        child: Text(
                            vm.contentForm['remark'] == null
                                ? ""
                                : vm.contentForm['remark'],
                            style: TextStyle(
                                fontSize: 16, color: AppTheme.deactivatedText),
                            textAlign: TextAlign.right)),
                  ],
                ),
              ),
            ],
          ),
        ));
        return child;
      case 1:
        child = Expanded(
            child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: MediaQuery.removePadding(
                context: context,
                removeTop: true,
                child: GridView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3, //每行三列
                      childAspectRatio: 1.0, //显示区域宽高相等
                      mainAxisSpacing: 10,
                      crossAxisSpacing: 10,
                    ),
                    itemCount: vm.imgList.length + 1,
                    itemBuilder: (context, index) {
                      return releaseImage(
                          index == vm.imgList.length,
                          index != vm.imgList.length
                              ? vm.imgList[index]
                              : null);
                    })),
          ),
        ));
        return child;

      case 2:
        child = Padding(
          padding: EdgeInsets.all(20),
          child: const Text(
            '公证员正在编辑文书，请稍等',
            style: TextStyle(
                fontSize: 16, fontWeight: FontWeight.bold),
          ),
        );
        return child;
      case 3:
        child = Padding(
          padding: EdgeInsets.all(20),
          child: const Text(
            '请如实回答公证员的提问',
            style: TextStyle(
                fontSize: 16, fontWeight: FontWeight.bold),
          ),
        );
        return child;

      default:
        child = Expanded(
            child: SingleChildScrollView(
                child: Padding(
          padding: EdgeInsets.symmetric(horizontal: getWidthPx(8)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Container(
                width: getWidthPx(750),
                height: 50,
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                decoration: new BoxDecoration(
                  color: Colors.white,
                  border: new Border.all(width: 1, color: Colors.black),
                ),
                child: Text('受理单号:   ${vm.orderInfo.unitGuid.orderNo}'),
              ),
              Container(
                width: getWidthPx(750),
                height: 50,
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                        left: BorderSide(width: 1, color: Colors.black),
                        right: BorderSide(width: 1, color: Colors.black),
                        bottom: BorderSide(width: 1, color: Colors.black))),
                child: Text('受理日期:   ${vm.orderInfo.unitGuid.createDate != null && vm.orderInfo.unitGuid.createDate.length > 10 ? vm.orderInfo.unitGuid.createDate.substring(0,10):vm.orderInfo.unitGuid.createDate}'),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    width: getWidthPx(183.5),
                    height: 60,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                            left: BorderSide(width: 1, color: Colors.black),
                            right: BorderSide(width: 1, color: Colors.black),
                            bottom: BorderSide(width: 1, color: Colors.black))),
                    child: Text('申请人'),
                  ),
                  Container(
                    width: getWidthPx(183.5),
                    height: 60,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                          right: BorderSide(width: 1, color: Colors.black),
                          bottom: BorderSide(width: 1, color: Colors.black)),
                    ),
                    child: AutoSizeText(
                      "${vm.getApplication()}",
                      overflow: TextOverflow.ellipsis,
                      maxLines: 6,
                    ),
                  ),
                  Container(
                    width: getWidthPx(183.5),
                    height: 60,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                          right: BorderSide(width: 1, color: Colors.black),
                          bottom: BorderSide(width: 1, color: Colors.black)),
                    ),
                    child: Text('承办人'),
                  ),
                  Container(
                    width: getWidthPx(183.5),
                    height: 60,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                          right: BorderSide(width: 1, color: Colors.black),
                          bottom: BorderSide(width: 1, color: Colors.black)),
                    ),
                    child: AutoSizeText(
                      widget.arguments['greffierName'],
                      overflow: TextOverflow.ellipsis,
                      maxLines: 6,
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    width: getWidthPx(183.5),
                    height: 50,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                            left: BorderSide(width: 1, color: Colors.black),
                            right: BorderSide(width: 1, color: Colors.black),
                            bottom: BorderSide(width: 1, color: Colors.black))),
                    child: Text('公证用途'),
                  ),
                  Expanded(
                    child: Container(
                      height: 50,
                      alignment: Alignment.centerLeft,
                      padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                            right: BorderSide(width: 1, color: Colors.black),
                            bottom: BorderSide(width: 1, color: Colors.black)),
                      ),
                      child: AutoSizeText(
                        vm.contentForm['yongtu'] == null
                            ? ""
                            : vm.contentForm['yongtu'],
                        overflow: TextOverflow.ellipsis,
                        maxLines: 6,
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    width: getWidthPx(183.5),
                    height: 80,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                            left: BorderSide(width: 1, color: Colors.black),
                            right: BorderSide(width: 1, color: Colors.black),
                            bottom: BorderSide(width: 1, color: Colors.black))),
                    child: Text('发证地点'),
                  ),
                  Expanded(
                    child: Container(
                      height: 80,
                      alignment: Alignment.centerLeft,
                      padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                            right: BorderSide(width: 1, color: Colors.black),
                            bottom: BorderSide(width: 1, color: Colors.black)),
                      ),
                      child: AutoSizeText(
                        '${vm.costMap['address'] == null ? "" : vm.costMap['address']}',
                        overflow: TextOverflow.ellipsis,
                        maxLines: 6,
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    width: getWidthPx(183.5),
                    height: 80,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                            left: BorderSide(width: 1, color: Colors.black),
                            right: BorderSide(width: 1, color: Colors.black),
                            bottom: BorderSide(width: 1, color: Colors.black))),
                    child: Text('公证事项'),
                  ),
                  Expanded(
                    child: Container(
                      height: 80,
                      alignment: Alignment.centerLeft,
                      padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                            right: BorderSide(width: 1, color: Colors.black),
                            bottom: BorderSide(width: 1, color: Colors.black)),
                      ),
                      child: AutoSizeText(
                        vm.contentForm['gongzhengValue'] == null
                            ? ""
                            : vm.contentForm['gongzhengValue'],
                        overflow: TextOverflow.ellipsis,
                        maxLines: 6,
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    width: getWidthPx(180),
                    height: 50,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                            left: BorderSide(width: 1, color: Colors.black),
                            right: BorderSide(width: 1, color: Colors.black),
                            bottom: BorderSide(width: 1, color: Colors.black))),
                    child: Text('公证费'),
                  ),
                  Container(
                    width: getWidthPx(180),
                    height: 50,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                          right: BorderSide(width: 1, color: Colors.black),
                          bottom: BorderSide(width: 1, color: Colors.black)),
                    ),
                    child: Text('法律服务费'),
                  ),
                  Container(
                    width: getWidthPx(194),
                    height: 50,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                          right: BorderSide(width: 1, color: Colors.black),
                          bottom: BorderSide(width: 1, color: Colors.black)),
                    ),
                    child: Text('摄像、拍照费'),
                  ),
                  Container(
                    width: getWidthPx(180),
                    height: 50,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                          right: BorderSide(width: 1, color: Colors.black),
                          bottom: BorderSide(width: 1, color: Colors.black)),
                    ),
                    child: Text('上门服务费'),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    width: getWidthPx(180),
                    height: 50,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                            left: BorderSide(width: 1, color: Colors.black),
                            right: BorderSide(width: 1, color: Colors.black),
                            bottom: BorderSide(width: 1, color: Colors.black))),
                    child: Text(vm.costMap['notarize'] == ''
                        ? '  0 元'
                        : '  ${vm.costMap['notarize']}元'),
                  ),
                  Container(
                    width: getWidthPx(180),
                    height: 50,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                          right: BorderSide(width: 1, color: Colors.black),
                          bottom: BorderSide(width: 1, color: Colors.black)),
                    ),
                    child: Text(vm.costMap['law'] == ''
                        ? '  0 元'
                        : '  ${vm.costMap['law']}元'),
                  ),
                  Container(
                    width: getWidthPx(194),
                    height: 50,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                          right: BorderSide(width: 1, color: Colors.black),
                          bottom: BorderSide(width: 1, color: Colors.black)),
                    ),
                    child: Text(vm.costMap['photograph'] == ''
                        ? '  0 元'
                        : '  ${vm.costMap['photograph']}元'),
                  ),
                  Container(
                    width: getWidthPx(180),
                    height: 50,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                          right: BorderSide(width: 1, color: Colors.black),
                          bottom: BorderSide(width: 1, color: Colors.black)),
                    ),
                    child: Text(vm.costMap['visit'] == ''
                        ? '  0 元'
                        : '  ${vm.costMap['visit']}元'),
                  ),
                ],
              ),
              Container(
                width: getWidthPx(750),
                height: 50,
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                        left: BorderSide(width: 1, color: Colors.black),
                        right: BorderSide(width: 1, color: Colors.black),
                        bottom: BorderSide(width: 1, color: Colors.black))),
                child: Text('其他收费：${vm.costMap['others']}元'),
              ),
              Container(
                width: getWidthPx(750),
                height: 50,
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.symmetric(vertical: getWidthPx(20)),
                decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                        left: BorderSide(width: 1, color: Colors.black),
                        right: BorderSide(width: 1, color: Colors.black),
                        bottom: BorderSide(width: 1, color: Colors.black))),
                child: Text('合计：${vm.total()}元'),
              ),
            ],
          ),
        )));
        return child;
    }
  }
}
