import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget/release_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/components/throttleUtil.dart';
import 'package:notarization_station_app/page/home/<USER>/uploadMaterial_vm.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/utils/debounce_button.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:provider/provider.dart';

import '../../appTheme.dart';

class UploadMaterialPage extends StatefulWidget {
  final arguments;

  const UploadMaterialPage({Key key, this.arguments}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return UploadMaterialPageState();
  }
}

class UploadMaterialPageState extends BaseState<UploadMaterialPage>
    with AutomaticKeepAliveClientMixin {
  UploadMaterialModel uploadMaterialViewModel;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
          backgroundColor: AppTheme.themeBlue,
          centerTitle: true,
          title: Text(
            "上传材料",
            style: TextStyle(color: Colors.white, fontSize: 18),
          ),
          leading: IconButton(
              icon: Icon(
                Icons.navigate_before,
                color: Colors.white,
                size: 30,
              ),
              onPressed: () {
                G.pushNamed(RoutePaths.HomeIndex);
              }),
        ),
        body: WillPopScope(
          onWillPop: () {
            return Future.value(false);
          },
          child: Consumer<UserViewModel>(
            builder: (ctx, userModel, child) {
              return ProviderWidget<UploadMaterialModel>(
                model: UploadMaterialModel(userModel, widget.arguments),
                onModelReady: (model) {
                  model.videoHeight = getHeightPx(1334);
                  uploadMaterialViewModel = model;
                  model.loadData();
                },
                builder: (ctx, homeModel, child) {
                  return SingleChildScrollView(
                    child: Column(
                      children: <Widget>[
                        SizedBox(
                          height: getHeightPx(10),
                        ),
                        Container(
                          width: double.maxFinite,
                          child: Image.asset(
                            "lib/assets/images/stepFour.png",
                            fit: BoxFit.fill,
                          ),
                        ),
                        SizedBox(
                          height: getHeightPx(40),
                        ),
                        Padding(
                            padding: EdgeInsets.only(
                                left: getWidthPx(40), right: getWidthPx(40)),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: <Widget>[
                                Text(
                                  "上传材料",
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w700),
                                ),
                              ],
                            )),
                        SizedBox(
                          height: getHeightPx(30),
                        ),
                        Padding(
                            padding: EdgeInsets.only(
                                left: getWidthPx(40), right: getWidthPx(40)),
                            child: Row(
                              children: <Widget>[
                                Text(
                                  "申请人需上传的材料",
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black45),
                                ),
                              ],
                            )),
                        SizedBox(
                          height: getHeightPx(30),
                        ),
                        Padding(
                            padding: EdgeInsets.only(
                                left: getWidthPx(40), right: getWidthPx(40)),
                            child: Row(
                              children: <Widget>[
                                Text(
                                  "1.申请的证件的正反两面，都需要拍照或者扫描",
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black45),
                                ),
                              ],
                            )),
                        SizedBox(
                          height: getHeightPx(30),
                        ),
                        Padding(
                            padding: EdgeInsets.only(
                                left: getWidthPx(40), right: getWidthPx(40)),
                            child: Row(
                              children: <Widget>[
                                Text(
                                  "2.检查是否在公安部门的年检有效期内",
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black45),
                                ),
                              ],
                            )),
                        SizedBox(
                          height: getHeightPx(30),
                        ),
                        Padding(
                            padding: EdgeInsets.only(
                                left: getWidthPx(40), right: getWidthPx(40)),
                            child: Row(
                              children: <Widget>[
                                Text(
                                  "3.文件可以不上传，等待公证员联系您",
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black45),
                                ),
                              ],
                            )),
                        SizedBox(
                          height: getHeightPx(40),
                        ),
                        uploadMaterialViewModel.materialList.length > 0
                            ? ListView.builder(
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                itemCount:
                                    uploadMaterialViewModel.materialList.length,
                                itemBuilder: (context, index) {
                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: <Widget>[
                                      Padding(
                                          padding: EdgeInsets.only(
                                              left: getWidthPx(40),
                                              right: getWidthPx(40)),
                                          child: Text(
                                            uploadMaterialViewModel
                                                .materialList[index]["name"],
                                            maxLines: 2,
                                            style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500),
                                          )),
                                      Padding(
                                        padding: EdgeInsets.only(
                                          left: getWidthPx(20),
                                        ),
                                        child: ReleaseWidget(
                                          isEdit:
                                              uploadMaterialViewModel.isEdite,
                                          con:
                                              uploadMaterialViewModel.otherList,
                                          callback: (MultipartFile file) {
                                            uploadMaterialViewModel
                                                .updateOtherImage(
                                                    uploadMaterialViewModel
                                                            .materialList[index]
                                                        ["unitGuid"],
                                                    file);
                                          },
                                          deleteImgCallback: (int a) {
                                            uploadMaterialViewModel.imageMap[
                                                    uploadMaterialViewModel
                                                            .materialList[index]
                                                        ["unitGuid"]]
                                                .removeAt(a);
                                          },
                                        ),
                                      ),
                                    ],
                                  );
                                })
                            : Container(
                                alignment: Alignment.center,
                                child: Text("暂无可上传的材料"),
                              ),
                        DebounceButton(
                          clickTap: ThrottleUtil().throttle(() {
                            if (!uploadMaterialViewModel.alreadyRead) {
                              // if (!uploadMaterialViewModel.clickNext) {
                              // uploadMaterialViewModel.clickNext = true;

                              uploadMaterialViewModel.previewFile();

                              // }
                            } else {
                              uploadMaterialViewModel.showSignature(
                                  userModel.userName,
                                  userModel.idCard,
                                  "zizhu,手印",
                                  "1",
                                  context);
                            }
                          }),
                          isEnable: uploadMaterialViewModel.bottomBtnEnable,
                          margin: EdgeInsets.only(
                              top: getWidthPx(50),
                              left: getWidthPx(40),
                              right: getWidthPx(40)),
                          padding: EdgeInsets.all(10),
                          borderRadius: BorderRadius.all(Radius.circular(5)),
                          backgroundColor: AppTheme.themeBlue,
                          child: Text(uploadMaterialViewModel.alreadyRead == false ? '下一步' : (uploadMaterialViewModel.isName == true ? "请签字" : ""),
                              style: TextStyle(
                                  fontSize: 16, color: AppTheme.nearlyWhite)),
                        ),
                        // InkWell(
                        //   onTap: debounce(() {
                        //     if (!uploadMaterialViewModel.alreadyRead) {
                        //       // if (!uploadMaterialViewModel.clickNext) {
                        //       // uploadMaterialViewModel.clickNext = true;
                        //
                        //       uploadMaterialViewModel.previewFile();
                        //
                        //       // }
                        //     } else {
                        //       uploadMaterialViewModel.showSignature(
                        //           userModel.userName,
                        //           userModel.idCard,
                        //           "zizhu,手印",
                        //           "1",
                        //           context);
                        //     }
                        //   }),
                        //   child: uploadMaterialViewModel.alreadyRead == false
                        //       ? Container(
                        //           alignment: Alignment.center,
                        //           margin: EdgeInsets.only(
                        //               top: getWidthPx(50),
                        //               left: getWidthPx(40),
                        //               right: getWidthPx(40)),
                        //           padding: EdgeInsets.all(10),
                        //           decoration: BoxDecoration(
                        //             borderRadius:
                        //                 BorderRadius.all(Radius.circular(5)),
                        //             color: AppTheme.themeBlue,
                        //           ),
                        //           child: Text('下一步',
                        //               style: TextStyle(
                        //                   fontSize: 16,
                        //                   color: AppTheme.nearlyWhite)))
                        //       : uploadMaterialViewModel.isName == true
                        //           ? Container(
                        //               alignment: Alignment.center,
                        //               margin: EdgeInsets.only(
                        //                   top: getWidthPx(50),
                        //                   left: getWidthPx(40),
                        //                   right: getWidthPx(40)),
                        //               padding: EdgeInsets.all(10),
                        //               decoration: BoxDecoration(
                        //                 borderRadius: BorderRadius.all(
                        //                     Radius.circular(5)),
                        //                 color: AppTheme.themeBlue,
                        //               ),
                        //               child: Text('请签字',
                        //                   style: TextStyle(
                        //                       fontSize: 16,
                        //                       color: AppTheme.nearlyWhite)))
                        //           : Container(),
                        // ),
                        SizedBox(
                          height: getHeightPx(30),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ));
  }

  @override
  bool get wantKeepAlive => true;
}
