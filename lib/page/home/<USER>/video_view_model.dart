import 'dart:async';
import 'dart:convert';

import 'package:amap_location_fluttify/amap_location_fluttify.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';
import 'package:notarization_station_app/config.dart';
import 'package:notarization_station_app/generated/json/base/json_convert_content.dart';
import 'package:notarization_station_app/page/home/<USER>/notarial_office_entity.dart';
import 'package:notarization_station_app/page/home/<USER>/video_entity.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/service_api/mine_api.dart';
import 'package:notarization_station_app/socket/mqtt_client.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:permission_handler/permission_handler.dart';

class VideoViewModel extends SingleViewStateModel with ClientCallback {
  final UserViewModel userViewModel;
  VideoViewModel(this.userViewModel);
  List notarialList = [];
  List notarialStatusList = [];
  List<String> notarialOfficeNameList = []; //公证处名称列表

  Map notarialInfo;
  VideoEntity orderInfo;
  Timer timer2;
  bool isInform = true;
  String adCode = '';
  String city;
  String latLng = '';

  bool isEnable = true;

  Location location;

  /// 获取当前ip信息
  String ipAddress = '';

  @override
  onCompleted(data) {}

  @override
  Future loadData() {
    MqttClientMsg.instance.setCallback(this);
    Future.delayed(Duration.zero, () {
      getLocation();
    });
    return null;
  }

  notarialUpdate(info) {
    notarialInfo = {
      "notarialName": info.notarialName,
      "unitGuid": info.unitGuid
    };
    notifyListeners();
  }

  getLocation() async {
    if (await Permission.location.request().isGranted) {
      Location location = await AmapLocation.instance.fetchLocation();
      wjPrint("位置信息：-----$location");
      if(location.province!=null && location.city!=null){
        city = "${location.province} ${location.city}";
      }
      if (location.adCode != null) {
        adCode = "${location.adCode.substring(0, 4)}00";
      }
      if (location.latLng.longitude != null && location.latLng.longitude != 0.0 &&
          location.latLng.latitude != null && location.latLng.latitude != 0.0) {
        latLng = "${location.latLng.longitude},${location.latLng.latitude}";
      } else {
        ipAddress = await G.getDeviceIPAddress();
      }
      getNotarial();
      getHistoryNotarial();
      notifyListeners();
    } else {
      ipAddress = await G.getDeviceIPAddress();
      G.showPermissionDialog(str: "访问位置信息权限");
    }
  }

  getHistoryNotarial() {
    Map<String, String> map = {
      //"userId":userViewModel.unitGuid
    };
    HomeApi.getSingleton().queryHistoryOffice(map).then((res) {
      if (res != null && res["code"] == 200 && res['items'] != null) {
        notarialList.forEach((element) {
          if (element.unitGuid == res['items']['notaryId']) {
            notarialInfo = {
              "notarialName": res['items']['notarialName'],
              "unitGuid": res['items']['notaryId']
            };
          }
        });
        wjPrint(".......$notarialInfo");
        notifyListeners();
      }
    });
  }

  addHistoryNotarial() {
    Map<String, String> map = {
      "notaryId": notarialInfo['unitGuid'],
      //"userId":userViewModel.unitGuid,
    };
    HomeApi.getSingleton().historyNotarial(map);
  }

  getNotarial() {
    notarialList.clear();
    notarialStatusList.clear();
    notarialOfficeNameList.clear();
    EasyLoading.show(status: "正在获取公证处列表");
    Map<String, String> map = {
      "institutionType": "1",
      "cityCode": adCode,
    };
    wjPrint(".......$map");
    HomeApi.getSingleton().getNotarialOffice(map).then((res) {
      EasyLoading.dismiss();
      if (res != null) {
        NotarialOfficeEntity notarialInfo1 = JsonConvert.fromJsonAsT(res);
        if (notarialInfo1.code != 200) {
          return ToastUtil.showWarningToast(notarialInfo1.msg);
        }
        notarialList = notarialInfo1.items ?? [];
        if(notarialList!=null && notarialList.isNotEmpty){
          notarialList.forEach((element) {
            notarialOfficeNameList.add(element.notarialName);
          });
        }
        if (notarialList.length == 0) {
          ToastUtil.showNormalToast("此地区暂无公证处");
          notarialInfo = null;
          notifyListeners();
        }
      } else {
        ToastUtil.showWarningToast("请求失败，稍后再试！");
      }
    });
  }

  addOrder({Function success,Function failure}) async {
    if (notarialInfo == null) {
      return ToastUtil.showWarningToast("请选择公证处！");
    }
    String phoneInfo = await G.phoneInfo(latLng);
    // EasyLoading.show(status: "正在安排公证员");
    DateTime now = new DateTime.now();
    Map<String, String> map = {
      "nei": phoneInfo,
      //"userId": userViewModel.unitGuid,
      "name": userViewModel.userName,
      "useArea": "",
      "useLanguage": "",
      "purposeName": "",
      "notaryId": notarialInfo['unitGuid'],
      "isDaiBan": "0",
      "terminalType": "1",
      "isIos": "1",
      "notaryForm": "2",
      "description": "",
      "videolog": json.encode({"planDate": now.toString().substring(0, 10)}),
      "geoLongitude":latLng.isEmpty?'':latLng.split(',')[0],
      "geoLatitude":latLng.isEmpty?'':latLng.split(',')[1],
      "geoIp": ipAddress,
      "applyUsers": json.encode([
        {
          "name": userViewModel.userName,
          "gender": userViewModel.gender,
          "birthday": userViewModel.birthday,
          "idCard": userViewModel.idCard,
          "mobile": userViewModel.mobile,
          "address": userViewModel.address
        }
      ])
    };
    wjPrint('公证入参$map');
    isEnable = false;
    notifyListeners();
    HomeApi.getSingleton().addOrder(map,errorCallBack: (e){
      // EasyLoading.dismiss();
      isEnable = true;
      notifyListeners();
    }).then((res) {
      if (res != null) {
        orderInfo = JsonConvert.fromJsonAsT(res);
        if (orderInfo.code != 200) {
          isEnable = true;
          notifyListeners();
          // EasyLoading.dismiss();
          return ToastUtil.showWarningToast(orderInfo.msg);
        }
        success();
        try{
          MqttClientMsg.instance
              .subscribe("/topic/shiping/${userViewModel.idCard}");
        }catch(e){
          log("订阅报错了$e");
        }
        addHistoryNotarial();
        timer2 = Timer(new Duration(seconds: 60), () {
          isEnable = true;
          notifyListeners();
          failure();
          //如果60s内不接单

          try {
            MqttClientMsg.instance
                ?.unsubscribe("/topic/shiping/${userViewModel.idCard}");
          } catch (e) {
            log("结束报错了$e");
          }
          cancelOrder();
          // EasyLoading.dismiss();
        });
      } else {
        isEnable = true;
        notifyListeners();
        // EasyLoading.dismiss();
        ToastUtil.showWarningToast("请求失败，稍后再试！");
      }
    });
  }

  cancelOrder() {
    Map<String, String> map2 = {"unitGuid": orderInfo.unitGuid.unitGuid};
    HomeApi.getSingleton().shutVideoOrder(map2).then((res2) {
      isEnable = true;
      notifyListeners();
      if (res2 != null) {
        if (res2['code'] != 200) {
          return ToastUtil.showWarningToast(orderInfo.msg);
        }
        // ToastUtil.showNormalToast("公证员正在忙碌请稍后再试！");
      } else {
        ToastUtil.showWarningToast("请求失败，稍后再试！");
      }
    });
  }

  @override
  void clientDataHandler(onData, topic) {
    wjPrint('MQTT的消息...$onData....$topic');
    // Wakelock.enable();
    if (topic == "shiping") {
      var orderData = json.decode(onData);
      if (orderData['code'] == 'dontWiting' && isInform) {
        isInform = false;
        timer2.cancel();
        EasyLoading.dismiss();
        isEnable = true;
        notifyListeners();
        // MqttClientMsg.instance.postMessage("我是我",".topic.shiping.collect");
        Map<String, Object> map = {"channelName": orderData["roomId"]};
        MineApi.getSingleton().getToken(map).then((res) {
          if (res["code"] == 200) {
            Config.aPPId = res['appId'];
            Config.token = res['token'];
            // 直播
            G
                .getCurrentState()
                .pushReplacementNamed(RoutePaths.VideoIng, arguments: {
              "roomId": orderData["roomId"],
              "orderInfo": orderInfo,
              "greffierName": orderData["greffierName"]
            });
          }
        });
      }
    }
  }
}
