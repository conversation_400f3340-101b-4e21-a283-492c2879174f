import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/helper_api.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/utils/common_tools.dart';

class SubmitOrderListModel extends SingleViewStateModel {
  final UserViewModel userViewModel;
  final arguments;
  double totalMoney = 0.0;
  String orderNumber;
  String number = "0"; //领取份数
  Timer timerPay;
  int numPay = 1;
  bool isShow = true;
  // 该公证处是否支持线上支付
  bool isSupportOnlinePay = false;

  List orderLogsList = [];

  SubmitOrderListModel(this.userViewModel, this.arguments);

//   goPay() {
//     showModalBottomSheet(
//       context: G.getCurrentState().overlay.context,
//       backgroundColor: Colors.transparent,
// //      isDismissible:false,
//       builder: (BuildContext context) {
//         return Container(
//           height: 150,
//           decoration: BoxDecoration(
//             color: Colors.white,
//             borderRadius: BorderRadius.only(
//               topLeft: Radius.circular(15),
//               topRight: Radius.circular(15),
//             ),
//           ),
//           width: double.infinity,
//           child: Column(
//             children: <Widget>[
//               // InkWell(
//               //   onTap: () {
//               //     Map<String, Object> map = {
//               //       "totalAmount": totalMoney.toString(),
//               //       "outTradeNo": this.orderNumber,
//               //       "subject": '公证费用'
//               //     };
//               //     HomeApi.getSingleton().aliPay(map).then((res) {
//               //       if (res != null) {
//               //         if (res['code'] == 200) {
//               //           _alipay(res['item']);
//               //         }
//               //       }
//               //     });
//               //   },
//               //   child: Container(
//               //     decoration: BoxDecoration(
//               //         border: Border(
//               //             bottom: BorderSide(width: 1, color: AppTheme.bg_e))),
//               //     padding: EdgeInsets.fromLTRB(30, 30, 10, 20),
//               //     child: Row(
//               //       children: <Widget>[
//               //         aliPayIco(
//               //           color: Color(0xff3CA4FB),
//               //           size: 30,
//               //         ),
//               //         SizedBox(
//               //           width: 20,
//               //         ),
//               //         Text('支付宝支付',
//               //             style: TextStyle(
//               //                 fontSize: 16, color: AppTheme.dark_grey)),
//               //       ],
//               //     ),
//               //   ),
//               // ),
//               Container(
//                 decoration: BoxDecoration(
//                     border: Border(
//                         bottom: BorderSide(width: 1, color: AppTheme.bg_e))),
//                 padding: EdgeInsets.fromLTRB(30, 20, 10, 10),
//                 child: Row(
//                   children: <Widget>[
//                     weChatIco(
//                       color: Colors.green,
//                       size: 30,
//                     ),
//                     SizedBox(
//                       width: 20,
//                     ),
//                     Expanded(
//                       child: SizedBox(
//                         height: 30,
//                         child: H5PayWidget(
//                           timeout: Duration(seconds: 30),
//                           refererScheme: Platform.isAndroid
//                               ? "https://sc.njguochu.com"
//                               : "sc.njguochu.com://",
//                           builder: (ctx, controller) {
//                             return InkWell(
//                                 onTap: throttle(() async {
//                                   EasyLoading.show();
//                                   numPay = 1;
//                                   await Future.delayed(
//                                       Duration(milliseconds: 1000));
//                                   Map<String, Object> map = {
//                                     "orderGuid": orderNumber
//                                   };
//                                   wjPrint("-----------订单号$orderNumber");
//                                   HomeApi.getSingleton().wxPay(map).then((res) {
//                                     EasyLoading.dismiss();
//                                     if (res != null) {
//                                       if (res['code'] == 200) {
//                                         wjPrint("----------------------$res");
//                                         controller
//                                             .pay(res['result']['mweb_url'],
//                                                 jumpPayResultCallback: (p) {
//                                           wjPrint("是否进行了微信支付 ->$p");
//                                           if (p == JumpPayResult.SUCCESS) {
//                                             wjPrint("进行了支付跳转,但是我不知道用户到底有没有进行支付");
//                                             timerPay?.cancel();
//                                             timerPay = Timer.periodic(
//                                                 Duration(seconds: 5), (t) {
//                                               numPay++;
//                                               wjPrint(
//                                                   "++++++++++++++++++++++++++计数器++$numPay");
//                                               Map<String, Object> map1 = {
//                                                 "outTradeNo": orderNumber
//                                               };
//                                               HomeApi.getSingleton()
//                                                   .getPay(map1)
//                                                   .then((value) {
//                                                 if (value['code'] == 200 &&
//                                                     value['item']
//                                                             ['tradeStatus'] ==
//                                                         "SUCCESS") {
//                                                   t.cancel();
//                                                   ToastUtil.showSuccessToast(
//                                                       "付款成功");
//                                                   isShow = false;
//                                                   notifyListeners();
//                                                   // Future.delayed(Duration.zero).then((value) => G.pushNamed(RoutePaths.HomeIndex));
//                                                 }
//                                               });
//                                               if (numPay > 17) {
//                                                 t.cancel();
//                                               }
//                                             });
//                                           } else if (p ==
//                                               JumpPayResult.TIMEOUT) {
//                                             wjPrint("支付跳转失败");
//                                             ToastUtil.showNormalToast("支付失败");
//                                           } else if (p == JumpPayResult.FAIL) {
//                                             wjPrint("没有安装或者不允许本应用打开微信支付");
//                                             ToastUtil.showNormalToast(
//                                                 "没有安装微信或者不允许本应用打开微信支付");
//                                           }
//                                           G.pop();
//                                         });
//                                       } else if (res['code'] == 500) {
//                                         ToastUtil.showNormalToast(res['msg']);
//                                       } else {
//                                         ToastUtil.showNormalToast("支付失败");
//                                       }
//                                     }
//                                   });
//                                 }),
//                                 child: Text("微信支付",
//                                     style: TextStyle(
//                                         fontSize: 16,
//                                         color: AppTheme.dark_grey)));
//                           },
//                         ),
//                       ),
//                     )
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }
//
//   _alipay(payInfo) async {
//     EasyLoading.show(status: "付款中...");
//     var result = await aliPay(payInfo);
//     EasyLoading.dismiss();
//     wjPrint('...........付款$result');
//     if (result['resultStatus'] == "9000") {
//       ToastUtil.showSuccessToast("付款成功");
//       isShow = false;
//       notifyListeners();
//     } else if (result['resultStatus'] == "6001") {
//       ToastUtil.showErrorToast("付款失败");
//     }
//     G.pop();
//   }

  @override
  onCompleted(data) {}

  @override
  Future loadData() {
    this.arguments["notarizationMatters"].forEach((data) {
      totalMoney += data["price"];
    });

    getOneNotarizationData();
    // doSetState();
    return null;
  }

  /// 获取支付配置信息
  getPayConfig(String notaryId) {
    Map<String, dynamic> data = {"notaryId": notaryId};
    HomeApi.getSingleton()
        .getPayConfig(data, errorCallBack: (e) {}, checkNet: true)
        .then((value) {
      wjPrint("获取支付配置信息------$value");
      if (value['code'] == 200) {
        isSupportOnlinePay = true;
        notifyListeners();
      } else if (value['code'] == 11047) {
        isSupportOnlinePay = false;
        notifyListeners();
      }
    });
  }

  goPay(BuildContext context) {
    getShuBiData(() {
      if (totalMoney != null &&
          totalMoney <= 0 &&
          number != null &&
          int.parse(number) <= 0) {
        doSetState(context);
      } else {
        if (totalMoney != null && totalMoney > 0 ||
            number != null && int.parse(number) > 0) {
          Navigator.pushNamed(context, RoutePaths.payWidget, arguments: {
            "unitGuid": arguments["orderId"],
            'callBackUrl': "/index",
            'payLogo': 1,
            'fee': totalMoney + 20 * int.parse(number),
            "orderNo": this.orderNumber
          }).then((value) {
            getOneNotarizationData();
          });
        }
      }
    });
  }

  /// 数币支付订单状态查询
  getShuBiData(Function callBack) async {
    var map = {
      "orderNo": orderNumber,
    };
    HomeApi.getSingleton()
        .queryShuBiOrder(map, errorCallBack: (e) {}, checkNet: true)
        .then((value) {
      wjPrint("33333333333333$value");
      if (value["code"] == 200) {
        // payStatus	1、成功 2、未支付 3、支付超时 4、支付失败
        if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '1') {
          ToastUtil.showSuccessToast("该笔订单已支付成功");
        } else if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '2') {
          callBack();
        } else if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '3') {
          callBack();
        } else {
          callBack();
        }
      } else {
        callBack();
      }
    });
  }

  // 判断是否显示线下支付
  bool isShowOfflinePay() {
    if (orderLogsList.isEmpty) {
      return false;
    } else {
      if (orderLogsList[0]['notaryState'] == 102) {
        return true;
      } else {
        return false;
      }
    }
  }

  // 判断是否显示线上支付
  bool isShowOnlinePay() {
    if (orderLogsList.isEmpty) {
      return false;
    } else {
      if (orderLogsList[0]['notaryState'] == 102) {
        if (isSupportOnlinePay) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    }
  }

  void getOneNotarizationData() {
    var map = {
      "unitGuid": arguments["orderId"],
    };
    wjPrint('unitGuid-------${arguments["orderId"]}');
    HelperApi.getSingleton().getOneNotarizationData(map).then((value) {
      setBusy(false);
      if (value["code"] == 200) {
        this.orderNumber = value["item"]["order"]["orderNo"];
        number = value["item"]["order"]["notaryNum"]==null ? '0' : value["item"]["order"]["notaryNum"].toString();
        List tempData = value['item']['orderLogs'];
        if (tempData != null && tempData.isNotEmpty) {
          orderLogsList = tempData;
        }
        wjPrint("获取订单详情信息------$value");
        getPayConfig(value['item']['order']['notaryId']);
        notifyListeners();
        wjPrint(
            'orderLogsList[orderLogsList.length -1].notaryState ---- ${orderLogsList[orderLogsList.length - 1].notaryState}');



        wjPrint('orderLogs-----${value['item']['orderLogs']}');
      }
    });
  }

  doSetState(BuildContext context) {
    // if (NotaryData.notaryId != "666c2421-b2e1-4076-80aa-8e63cf287de4") {
    getShuBiData(() {
      Navigator.of(context).pushNamedAndRemoveUntil(
          RoutePaths.HomeIndex, (Route route) => false);
      var map = {
        "unitGuid": arguments["orderId"],
        "notaryState": 101,
        "payType": "pt006"
      };
      HomeApi.getSingleton().setTakeType(map, errorCallBack: (r) {
        ToastUtil.showErrorToast(r.toString());
      }).then((value) {
        if (value['code'] == 200) {
        } else {
          ToastUtil.showNormalToast(value['message']);
        }
      });
    });
  }
}
