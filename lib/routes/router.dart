import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:notarization_station_app/page/helper/widget/duigong_detail.dart';
import 'package:notarization_station_app/page/helper/widget/hetong_detail.dart';
import 'package:notarization_station_app/page/helper/widget/shipin_detail.dart';
import 'package:notarization_station_app/page/helper/widget/zaixian_detail.dart';
import 'package:notarization_station_app/page/helper/widget/zizhu_detail.dart';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>/bank_ing.dart';
import 'package:notarization_station_app/page/home/<USER>/bank_list.dart';
import 'package:notarization_station_app/page/home/<USER>/ious_list.dart';
import 'package:notarization_station_app/page/home/<USER>/conference_ing.dart';
import 'package:notarization_station_app/page/home/<USER>/video_conference.dart';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>/demo.dart';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>/pay_widget.dart';
import 'package:notarization_station_app/page/home/<USER>/protocol.dart';
import 'package:notarization_station_app/page/home/<USER>/protocol_ing.dart';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>/small_money.dart';
import 'package:notarization_station_app/page/home/<USER>/small_new.dart';
import 'package:notarization_station_app/page/home/<USER>/small_video_auth.dart';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>';
import 'package:notarization_station_app/page/home/<USER>/dialog/demo.dart';
import 'package:notarization_station_app/page/home/<USER>/conference_ing.dart';
import 'package:notarization_station_app/page/home/<USER>/dianzi_name.dart';
import 'package:notarization_station_app/page/home/<USER>/pay_alert_view.dart';
import 'package:notarization_station_app/page/home/<USER>/rule_detail.dart';
import 'package:notarization_station_app/page/home/<USER>/webview_page.dart';
import 'package:notarization_station_app/page/index.dart';
import 'package:notarization_station_app/page/infomation/add_friend.dart';
import 'package:notarization_station_app/page/infomation/chat.dart';
import 'package:notarization_station_app/page/infomation/guide.dart';
import 'package:notarization_station_app/page/infomation/matter_List.dart';
import 'package:notarization_station_app/page/infomation/message.dart';
import 'package:notarization_station_app/page/infomation/msg_inform.dart';
import 'package:notarization_station_app/page/infomation/news.dart';
import 'package:notarization_station_app/page/infomation/notarized_matter_widget.dart';
import 'package:notarization_station_app/page/infomation/notary_Office.dart';
import 'package:notarization_station_app/page/infomation/notary_detail.dart';
import 'package:notarization_station_app/page/infomation/question_detail.dart';
import 'package:notarization_station_app/page/infomation/quiz.dart';
import 'package:notarization_station_app/page/infomation/search.dart';
import 'package:notarization_station_app/page/infomation/widget/guideDetail.dart';
import 'package:notarization_station_app/page/infomation/widget/infoDetail.dart';
import 'package:notarization_station_app/page/login/bind_email_address.dart';
import 'package:notarization_station_app/page/login/code_login.dart';
import 'package:notarization_station_app/page/login/login_page.dart';
import 'package:notarization_station_app/page/login/modify_password_page.dart';
import 'package:notarization_station_app/page/login/register.dart';
import 'package:notarization_station_app/page/login/register_explain.dart';
import 'package:notarization_station_app/page/mine/add_address_page.dart';
import 'package:notarization_station_app/page/mine/attestation_page.dart';
import 'package:notarization_station_app/page/mine/edit_head_portrait_page.dart';
import 'package:notarization_station_app/page/mine/face_compare.dart';
import 'package:notarization_station_app/page/mine/mine_address_list.dart';
import 'package:notarization_station_app/page/mine/mine_info_page.dart';
import 'package:notarization_station_app/page/mine/privacy_page.dart';
import 'package:notarization_station_app/page/mine/settings_page.dart';
import 'package:notarization_station_app/page/mine/user_agreement_page.dart';
import 'package:notarization_station_app/page/mine/widget/real_name_page.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';

class RoutePaths {
  static const String LOGIN = 'login';
  static const String HomeIndex = 'mainIndex';
  //用户协议，隐私政策
  static const String Privacy = 'privacy';
  static const String UserAgreement = 'userAgreement';
  static const String settings = 'settings';
  static const String MineAddressList = 'addressList';
  static const String AddAddress = 'addAddress';
  static const String MineInfo = 'mineInfo';
  static const String EditPortrait = 'editPortrait';
  static const String Attestation = 'attestation';
  static const String ModifyPsd = 'modifyPsd';
  static const String CodeLogin = 'codeLogin';
  static const String ShiPinDetail = 'shiPinDetail';
  static const String ZaiXianDetail = 'zaiXianDetail';
  static const String Explain = 'explain';
  static const String VideoNotarize = 'videoNotarize';
  static const String VideoIng = 'videoIng';
  static const String HeTongDetail = 'heTongDetail';
  static const String Register = 'register';
  static const String RealName = 'realName';
  static const String RuleDetail = 'ruleDetail';
  static const String DianziName = 'dianziName';
  static const String InfoDetail = 'infoDetail';
  static const String Face = 'face';
  static const String SelectPublicContent = 'selectPublicContent';
  static const String ApplyInfo = 'applyInfo';
  static const String ConfirmApplyInfo = 'confirmApplyInfo';
  static const String UploadMaterial = 'uploadMaterial';
  static const String SubmitOrderList = 'submitOrderList';
  static const String ContractPeople = 'contractPeople';
  static const String AskAndQuestion = 'askAndQuestion';
  static const String ContractIntroduce = 'contractIntroduce';
  static const String ContractIng = 'contractIng';
  static const String Upload = 'upload';
  static const String Test = 'test';
  static const String Video = 'video';
  static const String MemberList = 'memberList';
  static const String ZiZhuDetail = 'ziZhuDetail';
  static const String VideoConference = 'videoConference';
  static const String ConferenceDetails = 'conferenceDetails';
  static const String ConferenceIng = 'conferenceIng';
  static const String QuestionDetail = 'questionDetail';
  static const String Guide = 'guide';
  static const String MatterList = 'matterList';
  static const String Quiz = 'quiz';
  static const String NotaryOffice = 'notaryOffice';
  static const String NotarizedMatter = 'NotarizedMatter';
  static const String NotaryDetail = 'notaryDetail';
  static const String Message = 'message';
  static const String Search = 'search';
  static const String News = 'news';
  static const String MsgInform = 'msgInform';
  static const String AddFriend = 'addFriend';
  static const String Chat = 'chat';
  static const String Sound = 'sound';
  static const String duiGongDetail = 'duiGongDetail';
  static const String Appointment = 'Appointment';
  static const String AddAppointment = 'addAppointment';
  static const String Protocol = 'Protocol';
  static const String ProtocolIng = 'ProtocolIng';
  static const String DomeIngPage = 'DomeIngPage';
  static const String GuideDetailPage = 'GuideDetailPage';
  static const String SmallMoneyPage = 'SmallMoneyPage';
  static const String SmallNew = 'smallNew';
  static const String SmallVideo = 'smallVideo';
  static const String FaceFail = 'faceFail';
  // 金融赋强
  static const String BankList = 'BankList';
  static const String Demo = 'Demo';
  static const String IndexDemo = 'IndexDemo';
  static const String BankIng = 'BankIng';
  static const String IousList = 'IousList';
  static const String financialNotarization = 'financialNotarization';
  static const String liveBroadcastRoom = 'liveBroadcastRoom';
  static const String receptionRoom = 'receptionRoom';
  static const String selectRoomHistory = 'selectRoomHistory';
  static const String liveRoomAndIntroduce = 'liveRoomAndIntroduce';
  static const String registerExplain = 'registerExplain';
  static const String faceCompare = 'faceCompare';
  static const String videoUserInformation = 'videoUserInformation';
  static const String payWidget = "payWidget";
  static const String payAlertWidget = "payAlertWidget";
  static const String webView = "webView";
  static const String bindEmailAddress = 'bindEmailAddress';
  static const String faceCompareIdentifyWidget= "faceCompareIdentifyWidget";
}

class Router {
  //路由列表
  static final _routes = {
    'login': (BuildContext context, {Object args}) => LoginPage(arguments: args),
    'mainIndex': (BuildContext context, {Object args}) => MainIndexPage(),
    'privacy': (BuildContext context, {Object args}) => PrivacyPage(),
    'userAgreement': (BuildContext context, {Object args}) =>
        UserAgreementPage(),
    'settings': (BuildContext context, {Object args}) => SettingsPage(),
    'addressList': (BuildContext context, {Object args}) =>
        MineAddressListPage(),
    'addAddress': (BuildContext context, {Object args}) =>
        AddAddressPage(addressInfo: args),
    'mineInfo': (BuildContext context, {Object args}) => MineInfoPage(),
    'editPortrait': (BuildContext context, {Object args}) => EditPortraitPage(),
    'attestation': (BuildContext context, {Object args}) =>
        AttestationPage(arguments: args),
    'modifyPsd': (BuildContext context, {Object args}) {
      String phoneNumber = (args as Map)['phoneNumber'];
      wjPrint("modifyPsd-----$args,phoneNumber-----$phoneNumber");
      return ModifyPasswordPage(
        title: (args as Map)['title'],
        isAbroad:(args as Map)['isAbroad'],
        phoneNumber: (args as Map)['phoneNumber'],
      );
    },
    'codeLogin': (BuildContext context, {Object args}) =>
        CodeLoginPage(),
    'shiPinDetail': (BuildContext context, {Object args}) =>
        ShiPinDetailPage(arguments: args),
    'zaiXianDetail': (BuildContext context, {Object args}) =>
        ZaiXianDetailPage(arguments: args),
    'explain': (BuildContext context, {Object args}) =>
        ExplainPage(arguments: args),
    'videoNotarize': (BuildContext context, {Object args}) =>
        VideoNotarizePage(),
    'videoIng': (BuildContext context, {Object args}) =>
        VideoIngPage(arguments: args),
    'heTongDetail': (BuildContext context, {Object args}) =>
        HeTongDetailPage(arguments: args),
    'register': (BuildContext context, {Object args}) =>
        RegisterPage(
          phone: (args as Map)['phoneNumber'],
          isAbroad: (args as Map)['isAbroad'],
        ),
    'payWidget': (BuildContext context, {Object args}) => PayWidget(
      data: args,
    ),
    'payAlertWidget': (BuildContext context, {Object args}) => PayAlertView(
      data: args,
    ),
    'webView': (BuildContext context, {Object args}) => WebviewPage(
      url: (args as Map)['url'],
      orderNo: (args as Map)['orderNo'],
      source: (args as Map)['source'],
    ),
    'realName': (BuildContext context, {Object args}) => RealNamePage(
      comeFrom: (args as Map)['comeFrom'],
    ),
    'ruleDetail': (BuildContext context, {Object args}) => RuleDetailPage(),
    'dianziName': (BuildContext context, {Object args}) => DianziNamePage(),
    'infoDetail': (BuildContext context, {Object args}) =>
        InfoDetailPage(arguments: args),
    'face': (BuildContext context, {Object args}) => FacePage(arguments: args),
    'selectPublicContent': (BuildContext context, {Object args}) =>
        SelectPublicContentPage(arguments: args),
    'applyInfo': (BuildContext context, {Object args}) =>
        ApplyInfoPage(arguments: args),
    'confirmApplyInfo': (BuildContext context, {Object args}) =>
        ConfirmApplyInfoPage(arguments: args),
    'uploadMaterial': (BuildContext context, {Object args}) =>
        UploadMaterialPage(arguments: args),
    'submitOrderList': (BuildContext context, {Object args}) =>
        SubmitOrderListPage(arguments: args),
    'contractPeople': (BuildContext context, {Object args}) =>
        ContractPeoplePage(),
    'contractIng': (BuildContext context, {Object args}) =>
        ContractIngPage(arguments: args),
    // 'upload': (BuildContext context, {Object args}) => UploadPage(),
    'ziZhuDetail': (BuildContext context, {Object args}) =>
        ZiZhuDetailPage(arguments: args),
    'questionDetail': (BuildContext context, {Object args}) =>
        QuestionDetailPage(arguments: args),
    'guide': (BuildContext context, {Object args}) => GuidePage(),
    'matterList': (BuildContext context, {Object args}) =>
        MatterListPage(arguments: args),
    'quiz': (BuildContext context, {Object args}) => QuizPage(),
    'notaryOffice': (BuildContext context, {Object args}) => NotaryOfficePage(),
    'NotarizedMatter': (BuildContext context, {Object args}) => NotarizedMatterWidget(),
    'notaryDetail': (BuildContext context, {Object args}) =>
        NotaryDetailPage(arguments: args),
    'message': (BuildContext context, {Object args}) => MessagePage(),
    'search': (BuildContext context, {Object args}) => SearchBarPage(),
    'news': (BuildContext context, {Object args}) => NewsPage(arguments: args),
    'msgInform': (BuildContext context, {Object args}) =>
        MsgInformPage(arguments: args),
    'addFriend': (BuildContext context, {Object args}) => AddFriendPage(),
    'chat': (BuildContext context, {Object args}) => ChatPage(arguments: args),
    // 'sound': (BuildContext context, {Object args}) => RecordPage(),
    'duiGongDetail': (BuildContext context, {Object args}) =>
        DuiGongDetailPage(arguments: args),
    'Appointment': (BuildContext context, {Object args}) => AppointmentPage(),
    'addAppointment': (BuildContext context, {Object args}) =>
        AddAppointmentPage(),
    'videoConference': (BuildContext context, {Object args}) =>
        VideoConferencePage(),
    "conferenceIng": (BuildContext context, {Object args}) =>
        ConferenceIngPage(),
    'Protocol': (BuildContext context, {Object args}) => ProtocolPage(),
    'ProtocolIng': (BuildContext context, {Object args}) =>
        ProtocolIngPage(arguments: args),
    'DomeIngPage': (BuildContext context, {Object args}) => DomeIngPage(),
    'GuideDetailPage': (BuildContext context, {Object args}) =>
        GuideDetailPage(arguments: args),
    'SmallMoneyPage': (BuildContext context, {Object args}) => SmallMoneyPage(),
    'smallNew': (BuildContext context, {Object args}) =>
        SmallNewPage(route: args),
    'smallVideo': (BuildContext context, {Object args}) =>
        SmallVideoAuthPage(arguments: args),
    'faceFail': (BuildContext context, {Object args}) => FaceFailPage(),
    'BankList': (BuildContext context, {Object args}) => BankListPage(),
    'Demo': (BuildContext context, {Object args}) => DemoPage(),
    'IndexDemo': (BuildContext context, {Object args}) => IndexDemoPage(),
    'BankIng': (BuildContext context, {Object args}) =>
        BankIngPage(arguments: args),
    'IousList': (BuildContext context, {Object args}) => IousListPage(),
    'financialNotarization': (BuildContext context, {Object args}) =>
        FinancialNotarizationWidget(),
    'liveBroadcastRoom': (BuildContext context, {Object args}) =>
        LiveBroadcastRoomSelectionWidget(),
    'receptionRoom': (BuildContext context, {Object args}) =>
        ReceptionRoomWidget(
          data: args,
        ),
    'selectRoomHistory': (BuildContext context, {Object args}) =>
        SelectRoomHistoryWidget(),
    'liveRoomAndIntroduce': (BuildContext context, {Object args}) =>
        LiveRoomAndIntroduceWidget(),
    'registerExplain': (BuildContext context, {Object args}) =>
        RegisterExplainWidget(),
    'videoUserInformation': (BuildContext context, {Object args}) =>
        VideoUserInformation(),
    'faceCompare': (BuildContext context, {Object args}) => FaceCompareWidget(
      data: args,
    ),
    "bindEmailAddress" : (BuildContext context, {Object args}) =>
        BindEmailAddress(),
    "faceCompareIdentifyWidget" : (BuildContext context, {Object args}) =>
        FaceCompareIdentifyWidget(
          orderId: (args as Map)['orderId'],
        ),
  };

  //单例模式
  static Router _singleton;

  Router._internal();

  factory Router() {
    if (_singleton == null) {
      _singleton = Router._internal();
    }
    return _singleton;
  }

  //路由监听
  static Route<dynamic> getRoutes(RouteSettings settings) {
    String routeName = settings.name;
    final Function builder = Router._routes[routeName];
    wjPrint("CurrentPage：$settings");
    G.currentPath = settings.name;

    if (builder == null) {
      return MaterialPageRoute(
          builder: (_) => Scaffold(
                body: Center(
                  child: Text('没有找到对应的页面：${settings.name}'),
                ),
              ));
    } else {
      return MaterialPageRoute(
          settings: settings,
          builder: (BuildContext context) =>
              builder(context, args: settings.arguments));
    }
  }
}
