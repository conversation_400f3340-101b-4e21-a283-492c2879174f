/*
 * @Author: wang<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-27 14:57:57
 * @LastEditors: 王士博 <EMAIL>
 * @LastEditTime: 2023-08-15 17:29:05
 * @FilePath: /sc-remotenotarization-app/lib/utils/javascript_channel_method.dart
 * @Description: 
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'common_tools.dart';
import 'global.dart';

class JavascriptChannelMethod {
  JavascriptChannel _baseJavaChannel(
      BuildContext context, String methodName, ValueChanged<String> received) {
    String eventName = methodName;
    return JavascriptChannel(
        name: eventName,
        onMessageReceived: (JavascriptMessage message) {
          if (message.message.isNotEmpty) {
            received(message.message);
          } else {
            EasyLoading.showError("居然什么都没有给我😭");
          }
        });
  }

  Set<JavascriptChannel> loadJavascriptChannel(
      BuildContext context,
      UserViewModel userViewModel,
      String orderNo,
      int source,
      Function resultCallBack) {
    // _webViewController = controller;
    final Set<JavascriptChannel> channels = <JavascriptChannel>{};
    channels.add(
        payFinished(context, userViewModel, orderNo, source, resultCallBack));
    return channels;
  }

  Future<void> evaluateJavascriptEvent(
      WebViewController controller, String prefixName) async {}

  JavascriptChannel payFinished(
      BuildContext context,
      UserViewModel userViewModel,
      String orderNo,
      int source,
      Function resultCallBack) {
    return _baseJavaChannel(context, "toFlutterMessage", (value) {
      // userViewModel.currentIndex = 1;
      // Navigator.pushNamedAndRemoveUntil(
      //     context, RoutePaths.HomeIndex, (route) => false);
      if (resultCallBack == null) {
        getShuBiData(context, userViewModel, orderNo, source);
      } else {
        resultCallBack();
      }
    });
  }

  /// 数币支付订单状态查询
  getShuBiData(BuildContext context, UserViewModel userViewModel,
      String orderNo, int source) async {
    var map = {
      "orderNo": orderNo,
    };
    HomeApi.getSingleton().queryShuBiOrder(map, errorCallBack: (e) {
      // ToastUtil.showSuccessToast("支付失败");
      if (source == 1) {
        userViewModel.currentIndex = 1;
        Navigator.pushNamedAndRemoveUntil(
            context, RoutePaths.HomeIndex, (route) => false);
      } else {
        G.pop();
      }
    }, checkNet: true).then((value) {
      wjPrint("33333333333333$value");
      if (value["code"] == 200) {
        // payStatus	1、成功 2、未支付 3、支付超时 4、支付失败
        if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '1') {
          ToastUtil.showSuccessToast("支付成功");
        } else if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '2') {
          ToastUtil.showSuccessToast("未支付");
        } else if (value['data'] != null &&
            value['data']['payStatus'] != null &&
            value['data']['payStatus'] == '3') {
          ToastUtil.showSuccessToast("支付超时");
        } else {
          ToastUtil.showSuccessToast("支付失败");
        }
        if (source == 1) {
          userViewModel.currentIndex = 1;
          Navigator.pushNamedAndRemoveUntil(
              context, RoutePaths.HomeIndex, (route) => false);
        } else {
          G.pop();
        }
      }
    });
  }
}
