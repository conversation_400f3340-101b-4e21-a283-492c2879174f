import 'package:flutter/material.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:notarization_station_app/utils/search_text.dart';

import 'common_tools.dart';

// ignore: must_be_immutable
class BottomAlertSearchList extends StatefulWidget {
  Function selectValueCallBack;
  String holderString;
  List<String> dataSource;
  BottomAlertSearchList(
      {Key key,
      this.selectValueCallBack,
      this.dataSource,
      this.holderString = '请输入你想搜索的国籍'})
      : super(key: key);

  @override
  State<BottomAlertSearchList> createState() => _BottomAlertSearchListState();
}

class _BottomAlertSearchListState extends State<BottomAlertSearchList>
    with WidgetsBindingObserver {
  List<String> tempData = [];

  String searchValue = '';

  List<String> saveData = [];

  /// 键盘的高度
  double keyboardHeight = 0.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    tempData.addAll(widget.dataSource);
    saveData.addAll(widget.dataSource);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    // 键盘高度
    final double viewInsetsBottom = EdgeInsets.fromWindowPadding(
        WidgetsBinding.instance.window.viewInsets,
        WidgetsBinding.instance.window.devicePixelRatio)
        .bottom;

    wjPrint(viewInsetsBottom);

    setState(() {
      keyboardHeight = viewInsetsBottom;
    });
  }

  // 所搜结果
  void _search(value) {
    tempData.clear();
    wjPrint('saveData-------$saveData');
    saveData.forEach((element) {
      if (element.contains(value) ||
          PinyinHelper.getPinyin(element).contains(value)) {
        tempData.add(element);
      }
    });
    wjPrint("tempData-------$tempData");
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Spacer(),
        Container(
          // constraints: BoxConstraints(
          //   maxHeight: 400,
          // ),
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10), topRight: Radius.circular(10))),
          child: Column(
            children: [
              SearchText(
                hintText: widget.holderString,
                onTextCommit: (value) {
                  _search(value);
                },
                onTextChange: (value) {
                  if (value == null || value.isEmpty) {
                    setState(() {
                      tempData.clear();
                      tempData.addAll(saveData);
                    });
                  }
                },
              ),
              Container(
                constraints: BoxConstraints(
                    maxHeight: keyboardHeight == 0.0 || keyboardHeight == null
                        ? 360
                        : keyboardHeight),
                child: tempData.isNotEmpty
                    ? MediaQuery.removeViewPadding(
                        context: context,
                        removeTop: true,
                        child: ListView.builder(
                            itemCount: tempData.length,
                            itemBuilder: (context, index) {
                              return GestureDetector(
                                onTap: () {
                                  if (widget.selectValueCallBack != null) {
                                    widget.selectValueCallBack(tempData[index]);
                                    G.pop();
                                  }
                                },
                                child: Container(
                                  height: 50,
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 15),
                                  decoration: BoxDecoration(
                                      border: Border(
                                          bottom: BorderSide(
                                              color: Colors.grey[100],
                                              width: 1))),
                                  child: Row(
                                    children: [
                                      Expanded(
                                          child: Text(
                                        tempData[index],
                                        overflow: TextOverflow.ellipsis,
                                      )),
                                    ],
                                  ),
                                ),
                              );
                            }),
                      )
                    : Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.asset(
                              'lib/assets/images/empty.png',
                              width: 100,
                              height: 150,
                            ),
                            Text('无相关搜索数据',style: TextStyle(
                                color: AppTheme.lightText
                              ))
                          ],
                        ),
                      ),
              )
            ],
          ),
        ),
      ],
    );
  }
}
