/*
 * @Author: 王士博 <EMAIL>
 * @Date: 2023-04-10 09:51:30
 * @LastEditors: 王士博 <EMAIL>
 * @LastEditTime: 2023-04-10 09:51:43
 * @FilePath: /sc-remotenotarization-app/lib/utils/debounce_button.dart
 * @Description: 防抖按钮
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */

import 'package:flutter/material.dart';
import 'package:notarization_station_app/appTheme.dart';

class DebounceButton extends StatefulWidget {
  bool isEnable;
  Function clickTap;
  Widget child;
  EdgeInsets margin;
  EdgeInsets padding;
  BorderRadiusGeometry borderRadius;
  Color backgroundColor;
  Color disableColor;
  BoxBorder border;
  DebounceButton(
      {Key key,
      this.isEnable,
      this.clickTap,
      this.child,
      this.margin,
      this.padding,
      this.borderRadius,
      this.backgroundColor,
        this.border,
      this.disableColor})
      : super(key: key);

  @override
  State<DebounceButton> createState() => _DebounceButtonState();
}

class _DebounceButtonState extends State<DebounceButton> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.margin ?? EdgeInsets.all(0),
      child: InkWell(
        onTap: widget.isEnable
            ? () {
                if (widget.clickTap != null) {
                  widget.clickTap.call();
                }
              }
            : null,
        child: Container(
          alignment: Alignment.center,
          child: widget.child,
          padding: widget.padding,
          decoration: BoxDecoration(
            color: widget.isEnable
                ? (widget.backgroundColor ?? AppTheme.themeBlue)
                : (widget.disableColor ?? AppTheme.textBlack_4),
            borderRadius: widget.borderRadius,
            border: widget.border??Border.all(
              color: Colors.transparent,
              width: 1.0
            ),
          ),
        ),
      ),
    );
  }
}
