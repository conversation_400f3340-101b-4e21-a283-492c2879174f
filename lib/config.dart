class Config {
  /// 生产地址  江苏省远程公证项目 --------------------------------------

  // // // 江苏省远程公证
  // static final String hostUrl = 'https://jsnotary.njguochu.com:9228/';
  //
  // // 生产 系统配置
  // static final String systemSetting =
  //     'https://jsnotary.njguochu.com:9228/apistatus/';
  // // ca地址
  // static String caUrl = "https://scgzdt.com:9007/";
  //
  // // 江苏省远程公证生产
  // static String mqttPath = "wss://jsnotary.njguochu.com/ws";
  // static int mqttPort = 9229;
  // static String mqttAccount = "njgc";
  // static String mqttPassword = "C6wfaMgy";

  /// 测试地址 ------------------------------------------------
//
  // 测试地址
  // static final String hostUrl = 'https://testgz.njguochu.com:29913';

  // 测试 系统配置
  static final String systemSetting =
      'https://testgz.njguochu.com:29913/apistatus/';

  // ca地址
  static  String caUrl= "https://testgz.njguochu.com:49287/";

//   测试mqtt地址
  static String mqttPath = "wss://testgz.njguochu.com:29914/ws";
  static int mqttPort = 29914;
  static String mqttAccount = "admin";
  static String mqttPassword = "njguochu@123";




  // static  String caUrl = "https://scgzdt.com:9005/";
  // static  String caUrl = "https://shicheng.njguochu.com:9223/";



  /// 本地地址 --------------------------------------------------------
  // 接口地址
  // 化文川
  static String hostUrl = 'http://*************:9000';
  // static final String hostUrl = 'https://testgz.njguochu.com:22203';
  // static final String hostUrl = 'http://************:9000';
  // static final String hostUrl = 'http://************:9000';
  // 包
  // static final String hostUrl = 'http://************:9000';
  // static final String hostUrl = 'https://njscgzc.com:9000';
  // static final String hostUrl = 'http://**************:9000';
  // static final String hostUrl = 'http://**************:9000';
  // static final String hostUrl = 'http://*************:9000';
  // static final String hostUrl = 'http://************:9000';
  // 顾
  //  static final String hostUrl = 'http://************:9000';

  //mqtt地址
  //  static String mqttPath = "ws://*************:15675/ws";
  //  static int mqttPort = 15675;
  //  static String mqttAccount = "admin";
  //  static String mqttPassword = "12345";

  // static String mqttPath = "ws://*************:15675/ws";
  // static int mqttPort = 15675;
  // static String mqttAccount = "guest";
  // static String mqttPassword = "guest";

  // //
  // static String mqttPath = "wss://testgz.njguochu.com:33672/ws";
  // static int mqttPort = 33672;
  // static String mqttAccount = "admin";
  // static String mqttPassword = "aaaaaa";

  // static String mqttPath = "wss://www.scgzdt.com/ws";
  // static int mqttPort = 15679;
  // static String mqttAccount = "gc";
  // static String mqttPassword = "Iml5imyb";

// //  江苏省远程石城公证测试
//   static String mqttPath = "wss://sc.njguochu.com:9229/ws";
//   static int mqttPort = 9229;
//   static String mqttAccount = "njgc";
//   static String mqttPassword = "C6wfaMgy";

  /// 公共参数------------------------------------------------------

  //缓存对象
  static final String lastUserAcc = 'LastUserAccount';

  // 缓存用户手机号
  static final String lastUserPhoneNumber  = "lastUserPhoneNumber";

  // 缓存用户密码
  static final String lastUserPassWord  = "lastUserPassWord";

 /*
  * 全局静态变量配置
  */

  //声网RTC 的 APP_ID 和 Token
  static String aPPId = '';
  static String token = '';

  //腾讯RTC 的 _sdkAppId 和 _secretKey
  static int sdkAppId = **********;
  static String secretKey =
      '134d9892e8eb661c7d34f3e7476369ee9280e76f02157067c1ad5c10c6479ac5';
  static String wechatAppId = "wx5c765fa201060140";

  //模块
  static String userModule = "/user-api";
  static String notaryModule = "/notarization";
  static String annexModule = "/annex";
  static String voteModule = "/vote";
  static String buyersModule = "/buyers-api";
  static String parkModule = "/park";
  static String identificationModule = "/identification";
  static String bankModule = "/bank";
  static String caModule = "/cgz";
  static String enforcement = '/enforcement-api';
  static String easyEnforcement = '/easy-enforcement-api';
  static String room = '/yxroom-api';

  ///头像地址
  static String splicingImageUrl(String i) {
    //测试 https://shicheng.njguochu.com:9214/ //生产  https://scoss.njguochu.com:45/
    // String imageUrl = "https://shicheng.njguochu.com:9214/";
    // if (imageUrl.isEmpty) {
    //   imageUrl = 'https://scoss.njguochu.com:45/';
    // }
    // imageUrl = imageUrl.replaceAll("\n", "");
    // if (i != null && !i.startsWith("http")) {
    //   return imageUrl.endsWith("/") ? imageUrl + i : imageUrl + "/" + i;
    // } else {
    //   return i;
    //

    // }
    return i;
  }
}
