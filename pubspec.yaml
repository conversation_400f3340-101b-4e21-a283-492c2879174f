name: notarization_station_app
description: A new Flutter application.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html

version: 2.4.5+77
#version: 1.2.1+32

environment:
  sdk: ">=2.7.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
    # 添加国际化
  flutter_localizations:
    sdk: flutter

  flutter_downloader: 1.6.1
  flutter_easyloading: ^1.1.3
  provider: ^4.1.0
  pull_to_refresh: 1.6.4-nullsafety.0
  amap_location_fluttify: ^0.20.0
  flustars: ^0.3.2
  open_file: ^2.1.1
  package_info: ^0.4.0+6
  permission_handler: ^5.1.0+2
  url_launcher: ^5.1.2
  get_it: ^1.0.3+2
  web_socket_channel: ^1.1.0
  dio: ^3.0.9
  flutter_swiper: ^1.1.6
  color_dart: ^0.1.0
  photo_view: ^0.13.0
  extended_image: ^0.8.0
  fluttertoast: ^7.0.2
  connectivity: ^3.0.3
  video_player: ^2.1.1
  chewie: 
    path: plugins/chewie-1.0.0
  # video_player: ^1.0.1
  # chewie: ^0.10.4
#  flutter_h5pay: ^0.1.0
#  screen_recorder_flutter: ^0.0.1
  flutter_image_compress: ^1.1.3
  flutter_native_image: ^0.0.6
  tobias: ^1.7.1+1
  image_picker: ^0.6.2+3
  image_cropper: 1.2.0
  camera: ^0.9.1
#  flui: ^0.9.2
  flutter_html: 0.11.1
#  flutter_inappwebview: 5.6.0+2
  webview_flutter_plus: any
#  shared_preferences_ios:
#    git: https://e.coding.net/wejeson/wj/wjshared_preferences_ios.git
  flutter_datetime_picker: ^1.5.1
  agora_rtc_engine:
    path: plugins/agora_rtc_engine-3.4.6
  multi_image_picker: ^4.8.0
  flutter_slidable: ^0.5.7
  fluwx: ^2.6.0+2
#  flutter_sound: ^7.8.4
  just_audio: any
  audioplayers: ^0.17.4
  mqtt_client: ^8.0.0 #Mqtt
  flutter_cache_manager: ^1.4.1
  flutter_picker: ^2.0.2
  tencent_trtc_cloud: 2.4.2 #腾讯RTC
  city_pickers: ^1.0.1
  device_info: ^2.0.2
  video_compress: ^3.1.0
  pdf_flutter: 1.1.3
  # flutter_xupdate: 1.0.2  # 暂时注释掉，存在兼容性问题
  webview_flutter: 2.0.13
  replay_kit_launcher: ^0.3.0
  event_bus: ^1.1.1
  scrollable_positioned_list: ^0.2.3
  auto_size_text: ^2.1.0
#  sentry_flutter: any
  ## 友盟基础组件
  umeng_common_sdk: 1.2.7
  ## 友盟报错日志收集
  umeng_apm_sdk: 2.2.1
    ## 国密加密插件
  dart_sm: 
    git: https://e.coding.net/wejeson/plugins/sm_not_null.git
  flutter_network_speed:
    git:
      url: https://e.coding.net/wejeson/flutter_network_speed/network_speed.git
      ref: master
#  ccbpay:
#    git:
#      url: https://e.coding.net/wejeson/ccbpayflytter/ccbpay.git
#      ref: master
  visibility_detector: ^0.2.2
  network_info_plus: any
#  internet_speed_test:
#    path: plugins/internet_speed_test-1.5.0
#  flutter_network_connection: 0.0.4
#  custom_pop_up_menu: ^1.1.2
#  dropdown_button2: ^1.3.0
#  location: ^3.2.4
#  geocode: ^0.1.3
#  amap_flutter_location: ^1.0.1
  # wakelock: any

dependency_overrides:
  http: ^0.12.0+2
#  path_provider: 2.0.0
#  crypto: 3.0.0
#  convert: ^2.0.0
#  uuid: ^2.2.2
  common_utils: 1.2.0
#  shared_preferences:
#    path: plugins/shared_preferences-2.0.3
dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - lib/assets/
    - lib/assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    #   - family: Schyler
    #     fonts:
    #       - asset: fonts/Schyler-Regular.ttf
    #       - asset: fonts/Schyler-Italic.ttf
    #         style: italic
    - family: iconfont
      fonts:   #    图标文件只增不减
        - asset: lib/iconfont/iconfont.ttf     #    图标库
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
