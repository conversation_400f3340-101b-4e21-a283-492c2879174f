Index: app/src/main/res/layout/dialog_divers.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- app/src/main/res/layout/dialog_divers.xml	(date 1632447997789)
+++ app/src/main/res/layout/dialog_divers.xml	(date 1632447997789)
@@ -1,57 +1,31 @@
 <?xml version="1.0" encoding="utf-8"?>
 <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
     xmlns:app="http://schemas.android.com/apk/res-auto"
-    android:id="@+id/main"
-    android:layout_width="@dimen/space_400"
+    android:layout_width="@dimen/space_300"
     android:layout_height="match_parent"
     android:background="@color/gray_f2">
 
-    <TextView
-        android:id="@+id/change_location"
-        android:layout_width="wrap_content"
-        android:layout_height="wrap_content"
-        android:layout_marginLeft="@dimen/dp_10"
-        android:layout_marginTop="@dimen/dp_10"
-        android:drawableRight="@drawable/zhcc"
-        android:gravity="center"
-        android:onClick="changeLocation"
-        android:text="南京"
-        android:textColor="@color/black"
-        android:textSize="15sp"
-        app:layout_constraintStart_toStartOf="parent"
-        app:layout_constraintTop_toTopOf="parent" />
-
-    <include
-        android:id="@+id/city_include"
-        layout="@layout/city"
-        android:layout_width="match_parent"
-        android:layout_height="wrap_content"
-        android:visibility="gone"
-        app:layout_constraintTop_toBottomOf="@+id/change_location"/>
-
     <TextView
         android:id="@+id/tivle"
         android:layout_width="match_parent"
         android:layout_height="wrap_content"
-        android:layout_marginTop="@dimen/space_40"
-        android:background="@color/white"
-        android:gravity="center"
         android:padding="@dimen/space_20"
         android:text="绑定公证处"
+        android:gravity="center"
+        android:background="@color/white"
         android:textSize="@dimen/font_size_22"
         app:layout_constraintEnd_toEndOf="parent"
         app:layout_constraintStart_toStartOf="parent"
-        app:layout_constraintTop_toBottomOf="@id/change_location"
-       />
+        app:layout_constraintTop_toTopOf="parent" />
 
     <androidx.recyclerview.widget.RecyclerView
         android:id="@+id/rvDevices"
         android:layout_width="match_parent"
         android:layout_height="0dp"
-        android:layout_marginBottom="50dp"
         app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
         app:layout_constraintBottom_toBottomOf="parent"
         app:layout_constraintEnd_toEndOf="parent"
         app:layout_constraintStart_toStartOf="parent"
-        app:layout_constraintTop_toBottomOf="@id/tivle" />
+        app:layout_constraintTop_toBottomOf="@id/tivle"
+        android:layout_marginBottom="50dp"/>
 </androidx.constraintlayout.widget.ConstraintLayout>
\ No newline at end of file
